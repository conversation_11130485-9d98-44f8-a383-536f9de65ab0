/* eslint-disable react/no-array-index-key */
/* eslint-disable no-nested-ternary */
/* eslint-disable no-continue */
/* eslint-disable no-restricted-syntax */
import { setConfig } from '@action/config';
import { Breadcrumb, Form, Row, Table, Col, Modal, Radio, Icon, Tooltip } from 'antd';
import { ModalFuncProps, ModalProps } from 'antd/lib/modal';
import moment from 'moment';
import React, { ReactNode, useState } from 'react';
import ReactDOM from 'react-dom';

declare const BUILD_ENV: 'dev' | 'test' | 'prev' | 'prod' | 'testb';

export const iMReportTypeMap: any = {
  1: '垃圾广告',
  2: '色情低俗',
  3: '涉嫌欺诈',
  4: '违法犯罪',
  5: '攻击谩骂',
  6: '不实信息',
  7: '政治敏感',
  8: '其他恶意行为',
};

export function previewPic(url: string) {
  Modal.info({
    icon: null,
    width: 660,
    content: <img src={url} alt="预览" style={{ width: 600 }} />,
    okText: '关闭',
  });
}

export function requirePerm(caller: any, perm: any) {
  const { permissions } = caller.props.session;
  let disabled = false;
  if (typeof perm == 'string') {
    disabled = perm !== '' && permissions.indexOf(perm) === -1;
  } else {
    disabled =
      perm?.filter((item: string) => {
        return item !== '' && permissions.indexOf(item) === -1;
      })?.length > 0;
  }

  // const disabled = false;
  return (elem: React.ReactElement | any) => {
    if (disabled) {
      return React.cloneElement(elem, { ...elem.props, disabled: true });
    }
    return React.cloneElement(elem, { ...elem.props });
  };
}

export function requirePerm4Function(session: any, perm: any) {
  return requirePerm({ props: { session } }, perm);
}

export function setMenu(caller: any) {
  caller.props.dispatch(
    setConfig({
      selectKeys: caller.props.selectKeys,
      openKeys: caller.props.openKeys,
    })
  );
}

export function setMenuHook(dispatch: any, props: any) {
  console.log('setMenuHook', props);
  dispatch(
    setConfig({
      selectKeys: props.selectKeys,
      openKeys: props.openKeys,
    })
  );
}

export function setLoading(caller: any, loading: boolean) {
  caller.props.dispatch(setConfig({ loading }));
}

export function setMLoading(caller: any, loading: boolean) {
  caller.props.dispatch(setConfig({ mLoading: loading }));
}

export function searchToObject() {
  const pairs = window.location.search.substring(1).split('&');
  const obj: { [key: string]: any } = {};
  let pair;
  let i;
  for (i in pairs) {
    if (pairs[i] === '') {
      continue;
    }
    pair = pairs[i].split('=');
    obj[decodeURIComponent(pair[0])] = decodeURIComponent(pair[1]);
  }
  return obj;
}

export function objectToQueryString(obj: any) {
  return Object.keys(obj)
    .map((key) => encodeURIComponent(key) + '=' + encodeURIComponent(obj[key]))
    .join('&');
}

export function getCrumb(crumb: string[] = []) {
  return (
    <Breadcrumb separator=">">
      <Breadcrumb.Item>读嘉</Breadcrumb.Item>
      {crumb.map((item: string, i: number) => (
        <Breadcrumb.Item key={i}>{item}</Breadcrumb.Item>
      ))}
    </Breadcrumb>
  );
}

export function resolveTopPushed(status: number) {
  switch (status) {
    case 1:
      return '头条未用';
    case 2:
      return '头条已用';
    default:
      return ' ';
  }
}

// moduleType为1是潮客与潮鸣号
export function resolveNewsType(type: number, moduleType: number = 0) {
  switch (type) {
    case -1:
      return '推荐位';
    case 2:
      return '新闻';
    case 3:
      return '链接';
    case 4:
      return '图集';
    case 5:
      return '专题';
    case 6:
      return '话题';
    case 7:
      return '活动';
    case 8:
      return '直播';
    case 9:
      return '视频';
    case 10:
      return '视频帖';
    case 11:
      return '视频专题';
    case 12:
      return '图文帖';
    default:
      return '未知';
  }
}

export function UserDetail(props: { detail: any; isTM?: boolean }) {
  const formLayout = {
    labelCol: { span: 5 },
    wrapperCol: { span: 17 },
  };
  let { detail } = props;
  const { account_delivery_address: delivery_info = {} } = detail;
  const [infoStatus, setInfoStatus] = useState(0);
  const resolveAccountType = (type: any) => {
    switch (type) {
      case 'wei_xin':
        return '微信';
      case 'qq':
        return 'QQ';
      case 'wei_bo':
        return '微博';
      case 'apple':
        return '苹果';
      default:
        return `未知绑定信息-${type}`;
    }
  };
  const columns = [
    {
      title: '序号',
      key: 'seq',
      render: (text: any, record: any, i: number) => <span>{i + 1}</span>,
      width: 70,
    },
    {
      title: '任务ID',
      key: 'taskid',
      dataIndex: 'task_id',
    },
    {
      title: '任务名称',
      key: 'taskname',
      dataIndex: 'task_name',
    },
    {
      title: '时间',
      key: 'created_at',
      dataIndex: 'created_at',
      render: (text: any) => <span>{moment(text).format('YYYY-MM-DD')}</span>,
    },
    {
      title: '次数',
      dataIndex: 'execute_number',
      key: 'execnumber',
    },
    {
      title: '得分',
      key: 'score',
      dataIndex: 'total_score',
    },
  ];
  return (
    <Form {...formLayout}>
      <div style={{ width: '100%', textAlign: 'center' }}>
        <Radio.Group
          value={infoStatus}
          style={{ margin: '0 auto' }}
          size="large"
          onChange={(e) => {
            setInfoStatus(e.target.value);
          }}
        >
          <Radio.Button value={0}>基础信息</Radio.Button>
          <Radio.Button value={1}>个人信息</Radio.Button>
          <Radio.Button value={2}>活跃数据</Radio.Button>
          <Radio.Button value={3}>积分情况</Radio.Button>
        </Radio.Group>
      </div>
      {infoStatus === 0 && (
        <>
          <Form.Item label={'用户昵称'}>{detail.nick_name}</Form.Item>
          <Form.Item label="用户ID">{detail.chao_id}</Form.Item>
          <Form.Item label="用户长ID">{detail.id}</Form.Item>
          <Form.Item label="通行证ID">{detail.zbtxz_id}</Form.Item>
          <Form.Item label="状态">
            {detail.status === 1 ? '注销' : detail.forbidden ? '禁言' : '正常'}
          </Form.Item>
          <Form.Item label="个推CID">{detail.gt_cid}</Form.Item>
          <Form.Item label={'官方认证'}>
            {['无', '主播', '圈主', '通用'][detail.official_cert_status]}
          </Form.Item>
          <Form.Item label="注册时间">
            {moment(detail.created_at).format('YYYY-MM-DD HH:mm:ss')}
          </Form.Item>
          <Form.Item label="绑定手机号">{detail.phone_number || '未绑定'}</Form.Item>
          <Form.Item label="第三方绑定信息">
            {!!detail.type_set &&
              detail.type_set.length > 0 &&
              detail.type_set.map(
                (v: any) =>
                  v !== 'phone_number' && <Row key={v}>类型：{resolveAccountType(v)}；</Row>
              )}
          </Form.Item>
          <Form.Item label="设备">
            {!!detail.device_list &&
              detail.device_list.length > 0 &&
              detail.device_list.map((v: any, i: any) => (
                <Row key={i}>
                  {v.model}-{v.platform}
                  {v.os_version}&emsp;客户端版本：{v.app_version}
                </Row>
              ))}
          </Form.Item>
        </>
      )}
      {infoStatus === 1 && (
        <>
          <Form.Item label="用户简介">{detail.information}</Form.Item>
          <Form.Item label="联系信息">
            <div>
              <span>姓名：</span>
              <span>{delivery_info.delivery_user_name}</span>
            </div>
            <div>
              <span>手机号：</span>
              <span>{delivery_info.delivery_phone_number}</span>
            </div>
            <div style={{ whiteSpace: 'pre-wrap', lineHeight: '18px' }}>
              <span>联系地址：</span>
              <span>{delivery_info.delivery_address}</span>
            </div>
          </Form.Item>
        </>
      )}
      {infoStatus === 2 && (
        <>
          <Form.Item label="上次访问客户端">
            {moment(detail.last_accessed).format('YYYY-MM-DD HH:mm:ss')}
          </Form.Item>
          <Form.Item label="连登录天数">{detail.continuous_days}</Form.Item>
          <Form.Item key={3} label="已过审视频帖" style={{ marginBottom: 0 }}>
            {detail.on_works_count || 0}
          </Form.Item>
          ,
          <Form.Item key={6} label="已过审短图文帖">
            {detail.on_short_image_count || 0}
          </Form.Item>
          <Form.Item label="用户粉丝数">{detail.fans_count || 0}</Form.Item>
          <Form.Item label="用户关注数">{detail.follow_count || 0}</Form.Item>
        </>
      )}
      {infoStatus === 3 && (
        <>
          <Form.Item label="当前总积分">{detail.score}</Form.Item>
          <Form.Item label="最近50条积分记录">
            <Table dataSource={detail.score_task_dtolist} pagination={false} columns={columns} />
          </Form.Item>
        </>
      )}
    </Form>
  );
}
export function geBoostDetail(detail: any) {
  const formLayout = {
    labelCol: { span: 4 },
    wrapperCol: { span: 18 },
  };
  console.log('detail', detail);
  return (
    <Form {...formLayout}>
      {/*<Form.Item label="助推内容">{detail?.list_title}</Form.Item>*/}
      <Form.Item label="开始时间">{moment(detail?.use_at).format('YYYY-MM-DD HH:mm:ss')}</Form.Item>
      <Form.Item label="结束时间">
        {detail.end_status !== 0 ? moment(detail?.end_at).format('YYYY-MM-DD HH:mm:ss') : ''}
      </Form.Item>
      <Form.Item label="目标次数">{detail?.target_number}</Form.Item>
      <Form.Item label="已助推次数">{detail?.recommend_number}</Form.Item>
      <Form.Item label="累计展示">{detail?.show_number}</Form.Item>
      <Form.Item label="结束原因">
        {
          [
            '',
            '已达助推目标',
            '助推有效期结束',
            '作者提前终止',
            `${detail.stop_by}后台操作终止(${detail.stop_reason})`,
            '内容审核状态变化',
            '内容变为沉底通过',
            '内容被作者/管理员删除',
            '作者已注销',
          ][detail.end_status]
        }
      </Form.Item>
    </Form>
  );
}
export function geOnStateDetail(detail: any) {
  const formLayout = {
    labelCol: { span: 4 },
    wrapperCol: { span: 18 },
  };
  return (
    <Form {...formLayout}>
      {detail.status == 1 ? (
        <>
          <Form.Item label="助推内容">{detail.nick_name}</Form.Item>
          <Form.Item label="ID">{detail.chao_id}</Form.Item>
          <Form.Item label="创作者平台ID">{detail.id}</Form.Item>
          <Form.Item label="使用时间">{detail.zbtxz_id}</Form.Item>
        </>
      ) : (
        <>
          <Form.Item label="失效时间">{detail.nick_name}</Form.Item>
          <Form.Item label="操作人">{detail.chao_id}</Form.Item>
        </>
      )}
    </Form>
  );
}

export function showIDDetailModal({ id, uuid, metadata_id }: any) {
  document.activeElement?.blur();
  const formLayout = {
    labelCol: { span: 8 },
    wrapperCol: { span: 16 },
  };

  Modal.info({
    title: 'id详情',
    content: (
      <Form {...formLayout} style={{ marginLeft: '-38px' }}>
        <Form.Item label="ID">{id}</Form.Item>
        <Form.Item label="uuid">{uuid}</Form.Item>
      </Form>
    ),
  });
}

export function showCommonIDDetailModal(ids: any = []) {
  document.activeElement?.blur();
  const formLayout = {
    labelCol: { span: 8 },
    wrapperCol: { span: 16 },
  };

  Modal.info({
    title: 'id详情',
    content: (
      <Form {...formLayout} style={{ marginLeft: '-38px' }}>
        {ids.map((id: any) => (
          <Form.Item label={id.key}>{id.value}</Form.Item>
        ))}
      </Form>
    ),
  });
}

export function showReportIDDetailModal({ number, id }: any) {
  document.activeElement?.blur();
  const formLayout = {
    labelCol: { span: 8 },
    wrapperCol: { span: 16 },
  };

  Modal.info({
    title: 'id详情',
    content: (
      <Form {...formLayout} style={{ marginLeft: '-38px' }}>
        <Form.Item label="报料编号">{number}</Form.Item>
        <Form.Item label="报料id">{id}</Form.Item>
      </Form>
    ),
  });
}

export function showReadCountDetailModal(article: any) {
  document.activeElement?.blur();
  const formLayout = {
    labelCol: { span: 8 },
    wrapperCol: { span: 16 },
  };

  Modal.info({
    title: '阅读数详情',
    content: (
      <Form {...formLayout} style={{ marginLeft: '-38px' }}>
        <Form.Item label="总阅读数">{article.fake_count || 0}</Form.Item>
        <Form.Item label="ugc内容阅读数">{article.self_read_count || 0}</Form.Item>
        <Form.Item label="取稿稿件阅读数">{article.related_read_count || 0}</Form.Item>
        <Form.Item label="迁移阅读数">{article.migrate_total_pv || 0}</Form.Item>
      </Form>
    ),
  });
}

export function showDataSetModal(article: any, type: number) {
  document.activeElement?.blur();
  const formLayout = {
    labelCol: { span: 8 },
    wrapperCol: { span: 16 },
  };
  let columns: any = [];
  let title: any = '';
  switch (type) {
    case 0: // 点赞数
      title = '点赞数详情';
      columns = [
        { label: '总点赞数', key: 'like_count' },
        { label: 'ugc内容点赞数', key: 'self_like_count' },
        { label: '取稿稿件点赞数', key: 'related_like_count' },
      ];
      break;
    default:
      break;
  }
  Modal.info({
    title,
    content: (
      <Form {...formLayout} style={{ marginLeft: '-38px' }}>
        {columns.map((item: any) => {
          return <Form.Item label={item.label}>{article[item.key] || 0}</Form.Item>;
        })}
      </Form>
    ),
  });
}

// 评论、地方号、用户、广告、数据、话题
const RecommendTypes: any = {
  21: '评论',
  22: '地方号',
  23: '用户',
  24: '广告',
  25: '数据',
  26: '话题',
  27: '用户内容',
  28: '用户内容',
  29: '图片',
  30: '圈子',
  31: '赛事日程',
  32: '读报',
  33: '通用',
  34: '早晚报',
  35: '聚合',
  36: '榜单',
  37: '报料',
  38: '投票',
  39: '音频',
  40: '直播预告',
  41: '活动',
};

export const RecommendTypeNames: any = {
  11: '视频轮播推荐位',
  12: '新闻汇总推荐位',
  13: '热点新闻推荐位',
  14: '直播汇总推荐位',
  // 21: '潮客潮语',
  21: '评论推荐位',
  22: '地方号推荐位',
  23: '用户推荐位',
  24: '广告位',
  25: 'H5推荐位',
  26: '话题推荐位',
  27: '帖子汇总推荐位',
  29: '图片推荐位',
  30: '圈子推荐位',
  31: '赛事推荐位',
  32: '读报推荐位',
  33: '通用推荐位',
  34: '早晚报推荐位',
  35: '聚合推荐位',
  36: '榜单推荐位',
  38: '投票推荐位',
  41: '活动推荐位',
};

export function recommendTypeText(refType: any) {
  const text = RecommendTypes[refType];
  if (text) {
    return text;
  }
  return refType > 10 && refType <= 14 ? '稿件' : '未知';
}

export function getSupportRecommend(channel?: any, nav_target_type = '') {
  // category_id === 2是城市
  const recommendList = [];
  if (channel) {
    // 频道
    const subtypes: any = [
      {
        name: '视频轮播推荐位',
        code: 11,
      },
      {
        name: '新闻汇总推荐位',
        code: 12,
      },
    ];
    recommendList.push({
      name: '稿件',
      subtypes,
    });
    subtypes.push({
      name: '直播汇总推荐位',
      code: 14,
    });

    const { name } = channel;
    if (name === '直播') {
    } else {
      subtypes.push({
        name: '热点新闻推荐位',
        code: 13,
      });
    }
    recommendList.push(
      {
        name: '评论',
        hideMenu: true,
        subtypes: [
          {
            name: '评论推荐位',
            code: 21,
          },
        ],
      },
      {
        name: '地方号',
        subtypes: [
          {
            name: '地方号推荐位',
            code: 22,
          },
        ],
      },
      {
        name: '用户',
        hideMenu: true,
        subtypes: [
          {
            name: '用户推荐位',
            code: 23,
          },
        ],
      },
      {
        name: '广告',
        subtypes: [
          {
            name: '广告位',
            code: 24,
          },
        ],
      },
      {
        name: 'H5',
        hideMenu: true,
        subtypes: [
          {
            name: 'H5推荐位',
            code: 25,
          },
        ],
      },
      {
        name: '帖子',
        subtypes: [
          {
            name: '帖子汇总推荐位',
            code: 27,
          },
        ],
      }
    );
    // if (name === '亚运' || nav_target_type == 'event') {
    //   recommendList.push({
    //     name: '赛事',
    //     hideMenu: true,
    //     subtypes: [
    //       {
    //         name: '赛事推荐位',
    //         code: 31,
    //       },
    //     ],
    //   });
    // }
    if (name !== '直播') {
      recommendList.push({
        name: '话题',
        hideMenu: true,
        subtypes: [
          {
            name: '话题推荐位',
            code: 26,
          },
        ],
      });
      // recommendList.push({
      //   name: '读报',
      //   hideMenu: true,
      //   subtypes: [
      //     {
      //       name: '读报推荐位',
      //       code: 32,
      //     },
      //   ],
      // });
      // recommendList.push({
      //   name: '榜单',
      //   hideMenu: true,
      //   subtypes: [
      //     {
      //       name: '榜单推荐位',
      //       code: 36,
      //     },
      //   ],
      // });
      // recommendList.push({
      //   name: '音频',
      //   hideMenu: true,
      //   subtypes: [
      //     {
      //       name: '音频推荐位',
      //       code: 39,
      //     },
      //   ],
      // });
    }
    recommendList.push(
      {
        name: '图片',
        subtypes: [
          {
            name: '图片推荐位',
            code: 29,
          },
        ],
      },
      {
        name: '通用',
        subtypes: [
          {
            name: '通用推荐位',
            code: 33,
          },
        ],
      },
      {
        name: '聚合',
        subtypes: [
          {
            name: '聚合推荐位',
            code: 35,
          },
        ],
      }
    );
    recommendList.push({
      name: '活动',
      hideMenu: true,
      subtypes: [
        {
          name: '活动推荐位',
          code: 41,
        },
      ],
    });
  } else {
    // 社区
    recommendList.push(
      {
        name: '图片',
        subtypes: [
          {
            name: '图片推荐位',
            code: 29,
          },
        ],
      },
      {
        name: '圈子',
        subtypes: [
          {
            name: '圈子推荐位',
            code: 30,
          },
        ],
      },
      {
        name: '用户',
        subtypes: [
          {
            name: '用户推荐位',
            code: 23,
          },
        ],
      },
      {
        name: '话题',
        subtypes: [
          {
            name: '话题推荐位',
            code: 26,
          },
        ],
      },
      {
        name: '视频',
        subtypes: [
          {
            name: '视频轮播推荐位',
            code: 11,
          },
        ],
      },
      {
        name: '帖子',
        subtypes: [
          {
            name: '帖子汇总推荐位',
            code: 27,
          },
        ],
      }
    );
  }
  return recommendList;
}

export function getRecommendHideMenuList(supportRecommend: any) {
  console.log(supportRecommend);
  return supportRecommend
    .filter((item: any) => item.hideMenu)
    .flatMap((item: any) => item.subtypes);
}

export const HzMaterialText: any = {
  0: '未知',
  1: '图片+文字',
  2: '图片+视频',
  3: '文字+视频',
  4: '图片+文字+视频',
  5: '纯图',
};

export const ShowModal = (props: ModalProps, children: ReactNode) => {
  const div = document.createElement('div');
  document.body.appendChild(div);

  let showStatus = 0;

  function destoryFn(immediate = false) {
    if (showStatus === 2) {
      updateFn({ visible: false });
      const removeDiv = () => document.body.removeChild(div);
      if (immediate) {
        removeDiv();
      } else {
        setTimeout(removeDiv, 250);
      }
    }
  }

  function updateFn(props: ModalProps) {
    if (showStatus === 0 && props.visible) {
      showStatus = 1;
      setTimeout(() => {
        showStatus = 2;
      }, 250);
    }
    ReactDOM.render(
      <Modal
        destroyOnClose
        getContainer={div}
        onCancel={() => {
          destoryFn();
        }}
        {...props}
      >
        {children}
      </Modal>,
      div
    );
  }
  setTimeout(() => {
    const doc = document.getElementsByClassName('ant-modal-content');
    if (doc.length > 0) {
      destoryFn();
    } else {
      updateFn({ visible: true, ...props });
    }
  }, 500);
  return {
    destoryFn,
  };
};

export function getDocTypeString(docType: Number) {
  const docTypeMap: any = {
    3: '链接',
    4: '图集',
    5: '专题',
    8: '直播',
    9: '视频',
    10: '小视频',
    12: '短图文',
    13: '长文章',
  };

  return docTypeMap[`${docType}`] || '新闻';
}

// 0 相等  1 符合条件  -1 不符合条件
export function compareVersions(min: string, max: string) {
  // 将版本号拆分为数组
  const v1 = min.split('.');
  const v2 = max.split('.');

  // 逐个比较每个字段
  for (let i = 0; i < Math.max(v1.length, v2.length); i++) {
    // 如果某个字段不存在,默认为0
    const v1Part = parseInt(v1[i] || '0', 10);
    const v2Part = parseInt(v2[i] || '0', 10);

    // 比较当前字段
    if (v1Part < v2Part) return 1;
    if (v1Part > v2Part) return -1;
  }

  // 如果所有字段都相等,则版本号相同
  return 0;
}

/* 简单数据拷贝 */
export function simpleCopy<T extends object = object>(b: T): T {
  return JSON.parse(JSON.stringify(b));
}

// 获取图片比例
export const getImageRatio = async (imageUrl: any) => {
  return new Promise((resolve, reject) => {
    const img = new Image();

    img.onload = () => {
      const width = img.width;
      const height = img.height;
      const ratio = width / height;
      resolve(ratio);
    };

    img.onerror = () => {
      resolve(2);
    };

    img.src = imageUrl;
  });
};

// 根据links数组生成带链接的文字
export const generateTextWithLink = (content: any, links: any) => {
  let str = content || '';

  let result = '';
  let index = 0;
  for (let link of links || []) {
    let { start, end, url } = link;
    // href="${url}"
    result +=
      str.slice(index, start) +
      `<a href="${url}" target="_blank" style=color:#0F63F8>${str.slice(start, end)}</a>`;
    index = end;
  }

  result += str.slice(index);
  return result;
};

export function copy(text: any) {
  return new Promise((resolve, reject) => {
    if (
      typeof navigator !== 'undefined' &&
      typeof navigator.clipboard !== 'undefined' &&
      navigator.permissions !== 'undefined'
    ) {
      const type = 'text/plain';
      const blob = new Blob([text], { type });
      const data = [new ClipboardItem({ [type]: blob })];
      navigator.permissions.query({ name: 'clipboard-write' }).then((permission) => {
        if (permission.state === 'granted' || permission.state === 'prompt') {
          navigator.clipboard.write(data).then(resolve, reject).catch(reject);
        } else {
          reject(new Error('Permission not granted!'));
        }
      });
    } else if (document.queryCommandSupported && document.queryCommandSupported('copy')) {
      var textarea = document.createElement('textarea');
      textarea.textContent = text;
      textarea.style.position = 'fixed';
      textarea.style.width = '2em';
      textarea.style.height = '2em';
      textarea.style.padding = 0;
      textarea.style.border = 'none';
      textarea.style.outline = 'none';
      textarea.style.boxShadow = 'none';
      textarea.style.background = 'transparent';
      document.body.appendChild(textarea);
      textarea.focus();
      textarea.select();
      try {
        document.execCommand('copy');
        document.body.removeChild(textarea);
        resolve();
      } catch (e) {
        document.body.removeChild(textarea);
        reject(e);
      }
    } else {
      reject(new Error('None of copying methods are supported by this browser!'));
    }
  });
}

// 报料类型转义
export const reportTypeMap = (val: any, defaultValue = '未知') => {
  return ['', '广场', '报料', '提问'][val] || defaultValue;
};

export const bizTypeMap = (val: any, defaultValue = '未知') => {
  let name = defaultValue;
  switch (val) {
    case 1:
      name = '问政';
      break;
    case 2:
      name = '帮办';
      break;
    case 3:
      name = '帮帮团';
      break;
    case 4:
      name = '应急求助';
      break;
    case 5:
      name = '小店帮';
      break;
  }
  return name;
};

export const jumpToEdit = (token: any, fileType: any) => {
  const url: any = {
    dev: 'http://localhost:5173/creatorPlatform/#/approve/',
    test: 'http://***********/creatorPlatform/#/approve/',
    prev: 'https://cscmh.8531.cn/creatorPlatform/#/approve/',
    prod: 'https://cmh.8531.cn/creatorPlatform/#/approve/',
  };

  const type: any = {
    '13': 'long_news',
    '10': 'video',
    '12': 'short_news',
  };

  const link =
    url[BUILD_ENV] + type[`${fileType}`] + '?token=' + token + '&from=' + window.location.origin;
  window.open(link, '_blank');
};

// 获取敏感场景映射
const mediaSensitiveType: any = {
  porn_porn: '涉黄',
  porn_sexy: '涉黄',
  porn_intimacy: '涉黄',
  porn_vulgar: '涉黄',
  porn_special: '涉黄',
  terrorism_group: '暴恐',
  terrorism: '暴恐',
  terrorism_event: '暴恐',
  politician: '涉政',
  political_event: '涉政',
  political_group: '涉政',
  politics_politician: '涉政',
  ad_brand: '广告',
  ad_marketing: '广告',
  bad_behavior: '违禁',
  prohibited: '违禁',
  prohibited_gamble: '违禁',
  prohibited_forgery: '违禁',
  prohibited_trade: '违禁',
  prohibited_privacy: '质量',
  disgust: '质量',
  official_text_black_lib: '官方文本黑库',
  image_black_lib: '官方图片黑库',
};

const mediaSubtypeMap: any = {
  behavior: '性行为及露点及招嫖',
  sm: 'SM',
  products: '性用品及性玩具',
  children: '儿童',
  art: '艺术品色情',
  male: '男性衣着暴露',
  female: '女性衣着暴露',
  intimacy: '亲密行为',
  vulgar: '低俗行为',
  pregnant: '孕肚裸露',
  terrorist_group: '恐怖组织',
  terrorist: '暴恐人物',
  blood: '血腥',
  corpse: '尸体',
  murder: '绑架及杀人',
  explosion: '爆炸火灾',
  riot: '暴乱',
  weapon: '军事武器',
  police: '警察部队',
  politician_positive: '涉政正面人物',
  politician_negative: '涉政负面人物',
  bad_artist: '劣迹艺人',
  politics_event_positive: '涉政正面事件',
  politics_event_negative: '涉政负面事件',
  politics_group_positive: '涉政正面组织',
  politics_group_negative: '涉政负面组织',
  brand: '品牌标识',
  qrcode: '二维码',
  contact: '联系方式',
  website: '网址',
  commercial: '软文推广',
  watermark: '水印',
  smoke: '吸烟',
  drink: '喝酒',
  bad_behavior_gamble: '赌博',
  drug: '毒品',
  illegal: '其他非法的或者更多的统称',
  other: '其他非法的或者更多的统称',
  gamble: '赌博',
  forgery: '假冒伪劣及造假盗窃',
  trade: '非法交易',
  privacy: '非法获取私人信息',
  disgust_image: '恶心图',
  baidu_illegal_textlib: '违禁词库',
  default_image_black_lib: '百度云风控',
  meg_image_lib: '互联网安全管控',
};

export const getSensitiveWords = (data: any) => {
  let sensitiveList: any[] = data.map((item: any) => JSON.parse(item)) || [];

  const result = sensitiveList.flatMap((element: any) => {
    return element.checkResult.map((item: any) => {
      let typeValue = mediaSensitiveType[item.type] ?? '其他';

      const list = item?.items?.map((child: any) => {
        return {
          ...child,
          type: mediaSubtypeMap[child.subType] ?? '其他',
          imgUrl: element.imgUrl,
        };
      });

      return {
        ...item,
        type: typeValue,
        items: list,
      };
    });
  });

  // 合并相同类型
  const mergeResult = result.reduce((acc: any, item: any) => {
    const index = acc.findIndex((i: any) => i.type === item.type);
    if (index !== -1) {
      acc[index].items.push(...item.items);
    } else {
      acc.push(item);
    }
    return acc;
  }, []);

  return mergeResult;
};

const formatSeconds = function (second: any) {
  if (isNaN(second)) return '00:00:00';
  // 格式化为 00:00:00
  const hours = Math.floor(second / 3600);
  const minutes = Math.floor((second % 3600) / 60);
  const seconds = second % 60;
  return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds
    .toString()
    .padStart(2, '0')}`;
};

export const formatVideoCheckResult = (data: any) => {
  return data.checkResult.map((item: any) => {
    let typeValue = mediaSensitiveType[item.type] ?? '其他';

    const list = item?.items?.map((child: any) => {
      return {
        ...child,
        type: mediaSubtypeMap[child.subType] ?? '其他',
        imgUrl: child.evidence.thumbnail,
        time: formatSeconds(child.timeInSeconds),
      };
    });

    return {
      ...item,
      type: typeValue,
      items: list,
    };
  });
};

export const formatTextCheckResult = (data: any, platform: any) => {
  const sensitiveType: any = {
    sexual: '涉黄',
    terrorist: '暴恐',
    terror: '暴恐',
    politician: '涉政',
    political: '涉政',
    politics: '涉政',
    ad: '广告',
    bad: '违禁',
    illegal: '违禁',
    disgust: '质量',
    prohibition: '黑屏/重复画面',
  };

  const result = data
    .map((item: any) => {
      const words = item.words?.flatMap((word: any) => {
        return (
          word.positions?.map((position: any) => {
            return {
              ...position,
              label: word.label,
              word: word.word,
            };
          }) || []
        );
      });

      let typeValue = sensitiveType[item.label.toLowerCase()] ?? '其他';

      return {
        words,
        score: item.score,
        label: typeValue,
      };
    })
    .filter((item: any) => {
      return item.words?.length > 0;
    });

  return result;
};
