import React, { useEffect, useState } from "react"
import { Row, Col, Button, Divider, Modal, message } from 'antd';
import { useHistory } from 'react-router-dom';
import { useDispatch, useSelector, useStore } from 'react-redux';
import { Table } from '@components/common';
import { getTableList } from '@app/action/tableList';
import { PermA } from '@app/components/permItems';
import { opApi } from '@app/api';
import BusinessAdCodeDrawer from '@app/components/business/BusinessAdCodeDrawer';

export default function CommercialAd() {
  const history = useHistory();
  const dispatch = useDispatch();
  const { current, size } = useSelector((state: any) => state.tableList)
  const getSeq = (i: number) => (current - 1) * size + i + 1;
  const columns: any = [
    {
      title: '序号',
      key: 'seq',
      render: (a: any, b: any, c: number) => getSeq(c),
      width: 70,
      align: 'center'
    },
    {
      title: '广告来源',
      dataIndex: 'ad_type',
      render: (ad_type: number) => ad_type === 1 ? '洪泽' : '读嘉'
    },
    {
      title: '广告位名称',
      dataIndex: 'name',
      align: 'center'
    },
    {
      title: '系统类型',
      dataIndex: 'os_type',
      render: (os_type: number = 0) => ['\\', '安卓', 'iOS'][os_type]
    },
    {
      title: '广告位编码',
      dataIndex: 'code',
      align: 'center'
    },
    {
      title: '投放页面',
      dataIndex: 'space_desc',
      align: 'center'
    },
    {
      title: '广告位位置',
      dataIndex: 'position',
      align: 'center',
      render: (position: any) => position || '\\'
    },
    {
      title: '编辑时间',
      dataIndex: 'updated_at',
      align: 'center'
    },
    {
      title: '操作',
      key: 'op',
      align: 'center',
      render(record: any) {
        return <span>
          <PermA perm="advertise:edit_space" onClick={() => editRecord(record)}>编辑</PermA>
          <Divider type="vertical" />
          <PermA perm="advertise:delete_space" onClick={() => deleteRecord(record)}>删除</PermA>
        </span>
      }
    },
  ]
  const [adCodeDrawerVisible, setAdCodeDrawerVisible] = useState(false)
  const [drawerEditRecord, setDrawerEditRecord] = useState(null)

  const handleCreateAdCode = () => {
    setDrawerEditRecord(null)
    setAdCodeDrawerVisible(true)
  }

  const editRecord = (record: any) => {
    setDrawerEditRecord(record)
    setAdCodeDrawerVisible(true)
  }

  const getData = (goToFirstPage = false) => {
    let cur = goToFirstPage ? 1 : current
    dispatch(getTableList('getCommercialAdCodeList', 'list', { current: cur, size }));
  }

  const deleteRecord = (record: any) => {
    Modal.confirm({
      title: `是否确定删除【${record.name}】？`,
      onOk: () => {
        opApi.deleteCommercialAdCode({ id: record.id }).then(() => {
          message.success('删除成功');
          getData()
        }).catch(() => { })
      },
    });
  }

  const submitEnd = (goToFirstPage: boolean) => {
    getData(goToFirstPage)
    setAdCodeDrawerVisible(false)
  }

  useEffect(() => {
    getData()
  }, [])

  const { permissions } = useStore().getState().session;
  const canAddCode = permissions.indexOf('advertise:add_space') > 0;

  return (
    <>
      <Row className="layout-infobar">
        <Col span={24}>
          <Button onClick={() => history.go(-1)}>返回</Button>
          <Button style={{ marginLeft: 8 }} onClick={handleCreateAdCode} disabled={!canAddCode}>创建广告位编码</Button>
        </Col>
      </Row>
      <div className="component-content">
        <Table
          columns={columns}
          rowKey="id"
          func="getCommercialAdCodeList"
          index="list"
          pagination={true}
        />
        <BusinessAdCodeDrawer record={drawerEditRecord} visible={adCodeDrawerVisible} onClose={() => setAdCodeDrawerVisible(false)} onEnd={submitEnd} />
      </div>
    </>
  )
}