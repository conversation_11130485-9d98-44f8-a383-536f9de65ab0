import React, { useState, useEffect, useCallback, useMemo, useRef } from 'react';
import { useStore, useDispatch, useSelector } from 'react-redux';
import { useHistory } from 'react-router-dom';
import { Table, A, OrderColumn, Drawer } from '@components/common';
import { CommonObject } from '@app/types';
import { getCrumb, setMenuHook } from '@app/utils/utils';
import {
  Row,
  Col,
  Input,
  Divider,
  Button,
  Select,
  Icon,
  Modal,
  message,
  InputNumber,
  Tooltip,
  Timeline,
  Switch,
} from 'antd';
import { getTableList } from '@app/action/tableList';
import { opApi as api, releaseListApi } from '@app/api';
import useXHR from '@utils/useXhr';
import { PermA, PermButton } from '@components/permItems';
import Radio from 'antd/es/radio';
import { setConfig } from '@app/action/config';
import moment from 'moment';
import AssistantForm from './assistantForm';
import ConfigGPTStyleDrawer from './configGPTStyleDrawer';
import '@components/business/styles/business.scss';
import AddArticleModal from './addArticleModal';
import AddAudioArticleModal from './addAudioArticleModal';
import AddAudioColumnModal from './addAudioColumnModal';
import AudioRedPacketModal from './audioRedPacketModal';
import { render } from 'react-dom';
import AudioBroadcastDrawer from './audioBroadcastDrawer';
import VideoResourceConfigModal from './videoResourceConfigModal';

const defaultSize = 10;
export default function AudioManager(props: any) {
  const [filter, setFilter] = useState<any>({
    // type: 0,
    // status: 2,
    status: '',
    keyword: '',
  });

  const [search, setSearch] = useState({
    keyword: '',
  });
  const dispatch = useDispatch();
  const store = useStore();
  const history = useHistory();
  const {
    total,
    current,
    size,
    records = [],
    allData,
  } = useSelector((state: any) => state.tableList);
  const { loading, run } = useXHR();
  const formRef = useRef<any>(null);
  const addRef = useRef<any>(null);
  const [drawer, setDrawer] = useState({
    visible: false,
    key: 0,
    record: null,
  });

  const [videoPreview, setVideoPreview] = useState({
    visible: false,
    url: '',
    key: null,
  });

  const [broadcastDrawerState, setBroadcastDrawerState] = useState({
    visible: false,
    key: 0,
    record: null,
  });

  const [logs, setLogs] = useState<any>({
    visible: false,
    logs: [],
    title: '',
  });

  const [addColumnState, setAddColumnState] = useState({
    visible: false,
    key: 0,
    column_list: null,
  });

  const [redpacketState, setRedpacketState] = useState({
    visible: false,
    key: 0,
    record: null,
  });

  const [videoConfigModal, setVideoConfigModal] = useState({
    visible: false,
    key: 0,
    record: null,
  });

  const getSeq = (i: number) => (current - 1) * size + i + 1;

  const f = useMemo(() => {
    const x: CommonObject = { ...filter };
    if (!x.keyword) {
      delete x.keyword;
      delete x.search_type;
    }
    return x;
  }, [filter]);

  const getList = useCallback(
    (overlap: CommonObject = {}) => {
      const { current, size = defaultSize } = store.getState().tableList;
      dispatch(
        getTableList('getVideoResourceList', 'list', {
          ...f,
          current,
          size,
          ...overlap,
        })
      );
    },
    [f]
  );

  const editRecord = (record: any) => {
    run(api.gptQuestionDetail, { id: record.id }, true)
      .then((data: any) => {
        console.log('gptQuestionDetail', data);
        const {
          data: { detail, article_list },
        } = data;
        const body = {
          ...detail,
          articles: article_list,
        };

        setDrawer({
          visible: true,
          key: Date.now(),
          record: body,
        });
      })
      .catch((e: any) => {});
  };
  const deleteRecord = (record: any) => {
    Modal.confirm({
      title: `确定要删除吗？`,
      onOk: () => {
        run(api.deleteAudioPlazaArticle, { recommend_id: record.recommend_id }, true).then(() => {
          message.success('操作成功');
          getList();
        });
      },
    });
  };

  const updateRecordStatus = (record: any) => {
    run(api.updateVideoResourceVisible, { id: record.id, hidden: record.status == 0 }, true).then(
      () => {
        message.success('操作成功');
        getList();
      }
    );
  };

  const changeOrder = (record: any, i: any) => {
    let position = i;
    const WAIT_TIME = 1000;
    const positionChange = (v: any) => {
      position = v;
    };

    Modal.confirm({
      title: (
        <p
          style={{
            whiteSpace: 'nowrap',
            overflow: 'hidden',
            textOverflow: 'ellipsis',
          }}
        >
          排序：{record.list_title}
        </p>
      ),
      icon: <Icon type="info-circle" />,
      content: (
        <div>
          <span>请输入位置：</span>
          <InputNumber min={1} max={100} defaultValue={position} onChange={positionChange} />
          <p>最大可输入100</p>
        </div>
      ),
      onOk: (closeFunc: Function) => {
        if (!position) {
          message.error('请填写位置');
          return;
        }

        dispatch(setConfig({ loading: true }));
        api
          .videoResourceSort({ id: record.id, number: position, sort_flag: 2 })
          .then((res: any) => {
            message.success('操作成功');
            getList();
            dispatch(setConfig({ loading: false }));
            closeFunc();
          })
          .catch(() => {
            dispatch(setConfig({ loading: false }));
          });
      },
    });
  };

  const getOperateLog = (record: any) => {
    run(
      releaseListApi.getRecommendOperateLog,
      { id: record.recommend_id, type: 'AudioHomeRecommend' },
      true
    )
      .then((r: any) => {
        setLogs({
          visible: true,
          logs: r.data.logs,
          title: record.list_title,
          key: Date.now(),
        });
      })
      .catch();
  };

  const titleClick = (record: any) => {
    if (!!record.article_url) {
      window.open(record.article_url, '_blank');
    }
  };

  // 排序
  const exchangeOrder = (id: number, sort_flag: number) => {
    dispatch(setConfig({ loading: true }));
    api
      .videoResourceSort({ id, sort_flag })
      .then(() => {
        message.success('操作成功');
        getList();
        dispatch(setConfig({ loading: false }));
      })
      .catch(() => {
        dispatch(setConfig({ loading: false }));
      });
  };

  const getColumns = () => {
    let values = [
      {
        title: '排序',
        key: 'order',
        render: (text: any, record: any, i: number) => {
          const pos = getSeq(i);
          return (
            <OrderColumn
              pos={pos}
              start={1}
              end={total}
              perm="video_resource:sort"
              disable={filter.status != '' || filter.keyword != ''}
              onUp={() => exchangeOrder(record.id, 0)}
              onDown={() => exchangeOrder(record.id, 1)}
            />
          );
        },
        width: 70,
      },
      {
        title: '序号',
        key: 'seq',
        render: (text: any, record: any, i: number) => {
          return <span>{getSeq(i)}</span>;
        },
        width: 70,
      },
      {
        title: '视频标题',
        dataIndex: 'title',
        render: (text: any, record: any) => {
          return (
            <A className="line-max-2" onClick={() => titleClick(record)}>
              {text}
            </A>
          );
        },
      },
      {
        title: '视频id',
        dataIndex: 'video_id',
        width: 140,
      },
      {
        title: '视频预览',
        dataIndex: 'cover_url',
        width: 120,
        render: (text: any, record: any) => {
          return (
            <div
              style={{
                height: '60px',
                position: 'relative',
                cursor: 'pointer',
              }}
              onClick={() =>
                setVideoPreview({ visible: true, url: record.video_url, key: Date.now() })
              }
            >
              <video
                style={{ width: '100%', height: '100%', objectFit: 'contain' }}
                // className="list-pic"
                src={record.video_url}
                poster={text}
              ></video>

              <img
                src="/assets/play_icon.png"
                style={{
                  position: 'absolute',
                  top: '50%',
                  left: '50%',
                  transform: 'translate(-50%, -50%)',
                  width: '30px',
                  height: '30px',
                }}
              ></img>
            </div>
          );
        },
      },
      {
        title: '所属频道',
        dataIndex: 'channel_name',
        width: 120,
      },
      {
        title: '发布时间',
        dataIndex: 'published_at',
        width: 100,
        render: (text: any, record: any) => {
          return (
            <div style={{ whiteSpace: 'pre-wrap' }}>
              {moment(text).format('YYYY-MM-DD\nHH:mm:ss')}
            </div>
          );
        },
      },
      {
        title: '阅读数',
        dataIndex: 'read_count',
        width: 120,
      },
      {
        title: '点赞数',
        dataIndex: 'like_count',
        width: 120,
      },
      {
        title: '转发数',
        dataIndex: 'share_count',
        width: 120,
      },
      {
        title: '评论数',
        dataIndex: 'comment_count',
        width: 120,
      },
      {
        title: '状态',
        dataIndex: 'status',
        width: 120,
        render: (text: any, record: any) => {
          return text == 0 ? '展示' : '隐藏';
        },
      },
      {
        title: '操作人',
        dataIndex: 'updated_by',
        width: 120,
      },
      {
        title: '操作时间',
        dataIndex: 'updated_at',
        width: 100,
        render: (text: any, record: any) => {
          return (
            <div style={{ whiteSpace: 'pre-wrap' }}>
              {moment(text).format('YYYY-MM-DD\nHH:mm:ss')}
            </div>
          );
        },
      },
      {
        title: '操作',
        key: 'op',
        fixed: 'right',
        render: (text: any, record: any, i: number) => (
          <span>
            <PermA perm="video_resource:set_visible" onClick={() => updateRecordStatus(record)}>
              {record.status == 0 ? '隐藏' : '展示'}
            </PermA>
            <Divider type="vertical" />
            <PermA
              perm="video_resource:sort"
              // disabled={filter.status != '' || filter.keyword != ''}
              onClick={() => changeOrder(record, getSeq(i))}
            >
              排序
            </PermA>
          </span>
        ),
        width: 140,
      },
    ];

    return values;
  };

  useEffect(() => {
    // getList({ current: 1, size: 10 });
    setMenuHook(dispatch, props);
  }, []);

  useEffect(() => {
    getList({ current: 1, size: defaultSize });
  }, [f]);

  const addRecord = () => {
    // addRef.current.showModal()
    setDrawer({
      key: Date.now(),
      visible: true,
      record: null,
    });
  };

  const handleAddArticleOk = () => {
    getList({ current: 1, size: defaultSize });
  };

  const handleKey = (e: { which: number }) => {
    if (e.which === 13) {
      setFilter({
        ...filter,
        keyword: search.keyword,
      });
    }
  };

  const editConfig = () => {
    api.getVideoResourceConfig({}).then((res: any) => {
      setVideoConfigModal({
        visible: true,
        record: res.data,
        key: Date.now(),
      });
    });
  };

  const showPaperConfig = () => {
    let value = false;
    const valueChange = (v: any) => {
      value = v;
      modal.update({
        content: (
          <>
            <span>展示早晚报模块</span>&nbsp;&nbsp;&nbsp;
            <Switch checked={value} onChange={valueChange}></Switch>
          </>
        ),
      });
    };

    var modal = Modal.confirm({
      title: '早晚报配置',
      content: (
        <>
          <span>展示早晚报模块</span>&nbsp;&nbsp;&nbsp;
          <Switch checked={value} onChange={valueChange}></Switch>
        </>
      ),
      onOk: (destroy: Function) => {
        const { permissions } = store.getState().session;
        const disabled = permissions.indexOf('web_feature:audio_zwb_switch') === -1;
        if (disabled) {
          message.error('没有权限');
          destroy();
          return;
        }

        run(
          api.updateAudioZwbSwitch,
          {
            feature: 'audio_zwb_switch',
            status: value,
          },
          true
        ).then(() => {
          message.success('操作成功');
        });
        destroy();
      },
    });

    api
      .getAudioZwbSwitch()
      .then((data: any) => {
        valueChange(data.data?.switch);
      })
      .catch((e) => {});
  };

  const publish = () => {
    if (records.length < 20) {
      message.error('请至少选择20条数据');
      return;
    }
    const ids = records
      .slice(0, 20)
      .map((v: any) => v.id)
      .join(',');

    Modal.confirm({
      title: '确定发布前20条稿件吗？',
      onOk: () => {
        run(api.rankingPublish, { data_ids: ids, type: filter.type }, true).then(() => {
          message.success('操作成功');

          setFilter({
            ...filter,
            status: 0,
          });
        });
      },
    });
  };

  const onSubmitEnd = (v: any) => {
    if (filter.type != v) {
      setFilter({
        ...filter,
        type: v,
      });
    } else {
      getList({ current: current, size: defaultSize });
    }

    setDrawer({ ...drawer, visible: false });
  };

  const addArticle = () => {
    addRef.current.showModal();
  };

  const addColumn = () => {
    run(api.getAudioPlazaColumn, {}, true).then((res: any) => {
      setAddColumnState({
        visible: true,
        key: Date.now(),
        column_list: res.data?.column_list,
      });
    });
  };

  const editRedpacket = () => {
    run(api.getAudioPlazaRedpacket, {}, true).then((res: any) => {
      setRedpacketState({
        visible: true,
        key: Date.now(),
        record: res.data?.redpacket_info,
      });
    });
  };

  const showBroadcastDrawer = () => {
    run(api.getBroadcast, {}, true).then((res: any) => {
      setBroadcastDrawerState({
        visible: true,
        key: Date.now(),
        record: res.data?.recommend,
      });
    });
  };

  return (
    <>
      <Row className="layout-infobar">
        <Col span={12}>
          <PermButton
            perm="video_resource:get_config"
            style={{ marginRight: 8 }}
            onClick={editConfig}
          >
            视频设置
          </PermButton>
        </Col>
        <Col span={12} className="layout-breadcrumb">
          {getCrumb(props.breadCrumb)}
        </Col>
      </Row>
      <div className="component-content">
        <Row style={{ marginBottom: 16 }}>
          <Col span={12}>
            <Select
              value={filter.status}
              style={{ width: 150, marginRight: 8 }}
              onChange={(v) => setFilter({ ...filter, status: v })}
            >
              <Select.Option value="">全部状态</Select.Option>
              <Select.Option value={0}>展示</Select.Option>
              <Select.Option value={1}>隐藏</Select.Option>
            </Select>
          </Col>

          <Col span={12} style={{ textAlign: 'right' }}>
            <Input
              style={{ width: 160, marginRight: 8 }}
              onKeyPress={handleKey}
              value={search.keyword}
              placeholder="请输入视频标题"
              onChange={(e) =>
                setSearch({
                  keyword: e.target.value,
                })
              }
            />
            <Button onClick={() => handleKey({ which: 13 })}>
              <Icon type="search" />
              搜索
            </Button>
          </Col>
        </Row>
        <Table
          func="getVideoResourceList"
          index="list"
          filter={f}
          columns={getColumns()}
          rowKey="id"
          pagination={true}
          tableProps={{
            scroll: {
              x: 2000,
            },
          }}
        />

        <VideoResourceConfigModal
          {...videoConfigModal}
          onCancel={() => setVideoConfigModal({ ...videoConfigModal, visible: false })}
          onOk={() => {
            setVideoConfigModal({ ...videoConfigModal, visible: false });
            getList({ current: 1 });
          }}
        ></VideoResourceConfigModal>

        <Modal
          visible={logs.visible}
          title="操作日志"
          key={logs.key}
          cancelText={null}
          onCancel={() => setLogs({ visible: false })}
          onOk={() => setLogs({ visible: false })}
        >
          <div>
            <p
              style={{
                whiteSpace: 'nowrap',
                overflow: 'hidden',
                textOverflow: 'ellipsis',
              }}
            >
              {logs.title}
            </p>
            <Timeline>
              {logs?.logs?.map((v: any, i: number) => [
                <Timeline.Item className="timeline-dot-big" data-show={v.date} key={`time${i}`}>
                  &nbsp;
                </Timeline.Item>,
                v.actions.map((action: any, index: number) => (
                  <Timeline.Item
                    className="timeline-dot"
                    data-show={action.time}
                    key={`time${i}-action${index}`}
                  >
                    {action.user}&emsp;&emsp;{action.action}&emsp;&emsp;
                  </Timeline.Item>
                )),
              ])}
            </Timeline>
          </div>
        </Modal>

        <Modal
          visible={videoPreview.visible}
          destroyOnClose
          onCancel={() => setVideoPreview({ ...videoPreview, visible: false })}
          onOk={() => setVideoPreview({ ...videoPreview, visible: false })}
          closable={false}
        >
          <video
            style={{
              width: '100%',
              height: 'auto',
            }}
            src={videoPreview.url}
            controls
          ></video>
        </Modal>
      </div>
    </>
  );
}
