import {
  <PERSON><PERSON>,
  Col,
  DatePicker,
  Form,
  Icon,
  Input,
  InputNumber,
  Modal,
  Popconfirm,
  Radio,
  Select,
  Spin,
  Tooltip,
  message,
} from 'antd';
import React, { forwardRef, useEffect, useImperativeHandle, useState } from 'react';

import { opApi } from '@app/api';
import { A, Drawer, ImageUploader } from '@app/components/common';
import { useDispatch } from 'react-redux';
import { setConfig } from '@app/action/config';
import SearchAndInput from '@components/common/newNewsSearchAndInput';

const AddCreatorGuide = (props: any, ref: any) => {
  const dispatch = useDispatch();

  const { getFieldDecorator, getFieldsValue, setFieldsValue, getFieldValue } = props.form;
  useImperativeHandle(
    ref,
    () => ({
      ...props.form,
    }),
    []
  );

  const formLayout = {
    labelCol: { span: 4 },
    wrapperCol: { span: 18 },
  };

  const handleSubmit = () => {
    // e.preventDefault();
    props.form.validateFieldsAndScroll((err: any, values: any) => {
      if (!err) {
        dispatch(setConfig({ mLoading: true }));
        console.log(values);
        const params = {
          article_ids: values.channelArticles.map((item: any) => item.id).join(','),
        };
        opApi
          .createCreativeCenterGuide(params)
          .then((res: any) => {
            message.success('新增成功');
            dispatch(setConfig({ mLoading: false }));
            props.onEnd && props.onEnd();
          })
          .catch(() => {
            // message.error('添加失败');
            dispatch(setConfig({ mLoading: false }));
          });
      } else {
        message.error('请检查表单内容');
        dispatch(setConfig({ mLoading: false }));
      }
    });
  };

  const columns: any = [{
    width: 100,
    title: 'ID',
    key: 'id',
    dataIndex: 'id',
  },
  {
    title: '标题',
    key: 'list_title',
    dataIndex: 'list_title',
  },
  {
    width: 90,
    title: '类型',
    key: 'doc_type',
    dataIndex: 'doc_type',
    render: (text: any) => ['小视频', '', '短图文', '长文章'][text - 10],
  },
  ];

  return (
    <Drawer
      title={'添加创作者指南'}
      visible={props.visible}
      skey={props.key}
      onClose={props.onClose}
      maskClosable={false}
      width={"70%"}
      onOk={handleSubmit}
    >
      <Form {...formLayout}>
        <Form.Item label="关联稿件">
          {getFieldDecorator('channelArticles', {
            rules: [
              {
                required: true,
                message: '请关联内容',
                type: 'array',
              },
              {
                max: 50,
                message: '最多关联50条内容',
                type: 'array',
              },
              // {
              //   min: 1,
              //   message: `为保证客户端显示效果，关联内容数不能少于${1}条！`,
              //   type: 'array',
              // },
            ],
          })(
            <SearchAndInput
              max={50}
              func="listArticleRecommendSearch"
              columns={columns}
              placeholder="输入ID或标题查找，仅支持添加小视频、短图文、长文章"
              body={{ doc_types: '10,12,13' }}
              order={true}
            // addOnTop={true}
            // categoryTip="内容"
            // onAddItem={this.onAddItem}
            />
          )}
        </Form.Item>
      </Form>
    </Drawer>
  );
};

export default Form.create<any>({ name: 'AddCreatorGuide' })(forwardRef<any, any>(AddCreatorGuide));
