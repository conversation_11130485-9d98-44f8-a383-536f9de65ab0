import NewNewsSearchAndInput from '@app/components/common/newNewsSearchAndInput';
import {
  Button,
  Checkbox,
  Form,
  Icon,
  InputNumber,
  Modal,
  Radio,
  Spin,
  Tooltip,
  message,
} from 'antd';
import React, { forwardRef, useImperativeHandle, useState } from 'react';

import { opApi } from '@app/api';
import { FileUploader } from '@app/components/common';

const AddAudioArticleModal = (props: any, ref: any) => {
  const [visible, setVisible] = useState(false);
  const [loading, setLoading] = useState(false);
  const { getFieldDecorator, getFieldValue } = props.form;
  useImperativeHandle(
    ref,
    () => ({
      ...props.form,
      showModal,
    }),
    []
  );

  const showModal = () => {
    setVisible(true);
  };

  const column = [
    {
      title: 'ID',
      dataIndex: 'id',
      width: 80,
    },
    {
      title: '新闻频道',
      key: 'type',
      dataIndex: 'channel_name',
      width: 90,
    },
    {
      title: '新闻标题',
      key: 'list_title',
      dataIndex: 'list_title',
    },
  ];

  const formLayout = {
    labelCol: { span: 4 },
    wrapperCol: { span: 18 },
  };

  const handleSubmit = (e: any) => {
    e.preventDefault();
    props.form.validateFieldsAndScroll((err: any, values: any) => {
      if (!err) {
        setLoading(true);
        const params: any = {
          file_type: values.file_type,
        };
        if (values.file_type == '0') {
          params.position = values.position;
          params.ref_ids = values.channelArticles[0].id;
        } else {
          params.file_url = values.file_url;
        }
        opApi
          .addAudioPlazaArticle(params)
          .then((res: any) => {
            message.success('添加成功');
            setLoading(false);

            setVisible(false);
            props.onOk && props.onOk();
          })
          .catch(() => {
            // message.error('添加失败');
            setLoading(false);
          });
      } else {
        message.error('请检查表单内容');
        setLoading(false);
      }
    });
  };

  return (
    <Modal
      width={800}
      visible={visible}
      title="添加稿件"
      key={``}
      onCancel={() => {
        setVisible(false);
      }}
      onOk={handleSubmit}
      maskClosable={false}
      destroyOnClose={true}
    >
      <Spin spinning={false}>
        <Form {...formLayout} onSubmit={handleSubmit}>
          <Form.Item label="添加方式">
            {getFieldDecorator('file_type', {
              initialValue: '0',
            })(
              <Radio.Group>
                <Radio value="0">单篇添加</Radio>
                {/* <Checkbox value="2">服务频道</Checkbox> */}
                <Radio value="1">批量添加</Radio>
              </Radio.Group>
            )}
          </Form.Item>

          {getFieldValue('file_type') == '0' && (
            <>
              <Form.Item
                label={
                  <span style={{ position: 'relative' }}>
                    <Tooltip title={`仅支持关联新闻稿、视频稿`}>
                      <Icon
                        type="question-circle"
                        style={{ position: 'absolute', left: -30, top: 0 }}
                      />
                    </Tooltip>
                    关联稿件
                  </span>
                }
              >
                {getFieldDecorator('channelArticles', {
                  initialValue: null,
                  preserve: false,
                  rules: [
                    {
                      required: true,
                      message: '请关联新闻',
                      type: 'array',
                    },
                    {
                      max: 1,
                      message: '仅能添加1条稿件',
                      type: 'array',
                    },
                  ],
                })(
                  <NewNewsSearchAndInput
                    max={1}
                    func="listArticleRecommendSearch"
                    columns={column}
                    placeholder="输入ID或标题关联稿件"
                    body={{ doc_types: '2,9' }}
                    order={false}
                    addOnTop={true}
                  />
                )}
              </Form.Item>

              <Form.Item label="输入位置">
                {getFieldDecorator('position', {
                  // initialValue: 0,
                  rules: [
                    {
                      required: true,
                      message: '请填写位置',
                    },
                    {
                      min: 1,
                      message: '不能小于1',
                      type: 'number',
                    },
                    {
                      max: props.maxPosition,
                      message: `不能大于${props.maxPosition}`,
                      type: 'number',
                    },
                  ],
                })(
                  <InputNumber
                    max={props.maxPosition}
                    style={{ width: 200 }}
                    precision={0}
                    placeholder="请输入位置序号"
                    min={1}
                  />
                )}
              </Form.Item>
            </>
          )}

          {getFieldValue('file_type') == '1' && (
            <Form.Item
              label={
                <span style={{ position: 'relative' }}>
                  <Tooltip title={`表格第一列为稿件id即可`}>
                    <Icon
                      type="question-circle"
                      style={{ position: 'absolute', left: -30, top: 0 }}
                    />
                  </Tooltip>
                  上传表格
                </span>
              }
              extra="支持扩展名：.xls,.xlsx格式文件"
            >
              {getFieldDecorator('file_url', {
                // initialValue: ,
                rules: [
                  {
                    required: true,
                    message: '请上传文件',
                  },
                ],
              })(<FileUploader accept=".xls,.xlsx" />)}
            </Form.Item>
          )}
        </Form>
      </Spin>
    </Modal>
  );
};

export default Form.create<any>({ name: 'AddAudioArticleModal' })(
  forwardRef<any, any>(AddAudioArticleModal)
);
