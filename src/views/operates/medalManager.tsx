import React, { useState, useEffect, useRef, useCallback, useMemo } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { Table, A, Drawer } from '@components/common';
import Form from '@components/business/medalForm';
import { getCrumb, setMenuHook } from '@app/utils/utils';
import {
  Row,
  Col,
  Input,
  Button,
  message,
  Icon,
  Modal,
  Dropdown,
  Menu,
  Select,
  Form as AForm,
  Divider,
} from 'antd';
import { PermMenuItem, PermButton, PermA } from '@components/permItems';
import { getTableList } from '@app/action/tableList';
import { opApi as api } from '@app/api';
import useXHR from '@utils/useXhr';
import moment from 'moment';

export default function ServiceKeywords(props: any) {
  const [form, setForm] = useState(
    () =>
      ({
        visible: false,
        key: Date.now(),
      } as any)
  );
  const [grant, setGrant] = useState(
    () =>
      ({
        visible: false,
        key: Date.now() + 1,
        list: [],
        id: '',
        type: 0,
        account_ids: '',
      } as any)
  );
  const [status, setStatus] = useState('');
  const formRef = useRef<any>();
  const dispatch = useDispatch();
  const { loading, run } = useXHR();

  const { current, size } = useSelector((s: any) => s.tableList);

  const getList = useCallback(
    (overlap: any = {}) => {
      const body: any = { current, size };
      if (status !== '') {
        body.status = status;
      }
      dispatch(getTableList('getMedalList', 'achievement_medal_list', { ...body, ...overlap }));
    },
    [status, size, current]
  );

  const deleteRecord = (record: any) => {
    Modal.confirm({
      title: `确认删除？`,
      onOk: () => {
        run(api.deleteMedal, { id: record.id }, true).then(() => {
          message.success('操作成功');
          getList({ current: 1 });
        });
      },
    });
  };

  const handleChangeStatus = (record: any) => {
    run(api.updateMedalStatus, { id: record.id, status: record.status ? 0 : 1 }, true).then(() => {
      message.success('操作成功');
      getList();
    });
  };

  const editRecord = (record: any = {}) => {
    setForm({
      visible: true,
      key: Date.now(),
      ...record,
    });
  };

  const getSeq = (i: number) => {
    return (current - 1) * size + i + 1;
  };

  const columns = [
    {
      title: '序号',
      key: 'seq',
      render: (a: any, b: any, c: number) => getSeq(c),
      width: 70,
    },
    {
      title: '勋章名称',
      dataIndex: 'achievement_name',
    },
    {
      title: '勋章id',
      dataIndex: 'id',
      width: 100,
    },
    {
      title: '勋章',
      dataIndex: 'medal_pic_url',
      render: (text: any) => <img src={text} className="list-pic" alt="" />,
    },
    {
      title: '状态',
      dataIndex: 'status',
      render: (text: boolean) => (text ? '上线' : '下线'),
    },
    {
      title: '操作',
      key: 'op',
      render: (record: any) => (
        <>
          <PermA perm="achievement_medal:update" onClick={() => editRecord(record)}>
            编辑
          </PermA>
          <Divider type="vertical" />
          <PermA perm="achievement_medal:on_off" onClick={() => handleChangeStatus(record)}>
            {record.status ? '下线' : '上线'}
          </PermA>
          <Divider type="vertical" />
          <PermA perm="achievement_medal:delete" onClick={() => deleteRecord(record)}>
            删除
          </PermA>
        </>
      ),
      width: 180,
    },
  ];

  useEffect(() => {
    getList({ current: 1 });
    setMenuHook(dispatch, props);
  }, [status]);

  function submitEnd() {
    getList();
    setForm((s: any) => ({ ...s, visible: false }));
  }

  function grantMedal() {
    run(api.getMedalList, { current: 1, size: 10000, status: 1 }, true).then((res: any) => {
      console.log(res);
      setGrant({
        visible: true,
        key: Date.now(),
        list: res.data.achievement_medal_list.records,
        id: '',
        type: 0,
        account_ids: '',
      });
    });
  }

  function doGrant() {
    const { id, account_ids, type } = grant;
    if (!id || !account_ids) {
      message.error('请完整填写表单');
      return;
    }
    run(api.grantMedal, { id, account_ids, type }).then(() => {
      message.success('操作成功');
      setGrant({ ...grant, visible: false });
    });
  }

  return (
    <>
      <Row className="layout-infobar">
        <Col span={12}>
          <PermButton
            perm="achievement_medal:create"
            style={{ marginRight: 8 }}
            onClick={() => setForm({ visible: true, key: Date.now() })}
          >
            <Icon type="plus-circle" /> 新建勋章
          </PermButton>
          <PermButton perm="achievement_medal:grant" onClick={grantMedal}>
            勋章授予
          </PermButton>
        </Col>
        <Col span={12} className="layout-breadcrumb">
          {getCrumb(props.breadCrumb)}
        </Col>
      </Row>
      <div className="component-content">
        <Row style={{ marginBottom: 16 }}>
          <Col span={12}>
            <Select value={status} onChange={(v: any) => setStatus(v)} style={{ width: 120 }}>
              <Select.Option value="">全部勋章</Select.Option>
              <Select.Option value="1">上线勋章</Select.Option>
              <Select.Option value="0">下线勋章</Select.Option>
            </Select>
          </Col>
        </Row>
        <Table
          func="getMedalList"
          index="achievement_medal_list"
          filter={status ? { status } : {}}
          columns={columns}
          rowKey="id"
          pagination={true}
        />
        <Drawer
          visible={form.visible}
          title={form.id ? '编辑勋章' : '新建勋章'}
          skey={form.key}
          onClose={() => setForm((s: any) => ({ ...s, visible: false }))}
          onOk={() => formRef.current.doSubmit()}
        >
          <Form
            wrappedComponentRef={(ref: any) => {
              formRef.current = ref;
            }}
            formContent={form}
            onEnd={submitEnd}
          />
        </Drawer>
        <Modal
          visible={grant.visible}
          key={grant.key}
          onOk={doGrant}
          onCancel={() => setGrant({ ...grant, visible: false })}
          title="勋章授予"
          confirmLoading={loading}
        >
          <AForm labelCol={{ span: 4 }} wrapperCol={{ span: 16 }}>
            <AForm.Item label="操作目标">
              <Select value={grant.type} onChange={(v: any) => setGrant({ ...grant, type: v })}>
                <Select.Option value={0}>授予勋章</Select.Option>
                <Select.Option value={1}>取消勋章</Select.Option>
              </Select>
            </AForm.Item>
            <AForm.Item label="选择勋章">
              <Select value={grant.id} onChange={(v: any) => setGrant({ ...grant, id: v })}>
                <Select.Option value="" disabled>
                  选择勋章
                </Select.Option>
                {grant.list.map((v: any) => (
                  <Select.Option value={v.id} key={v.id}>
                    {v.achievement_name}
                  </Select.Option>
                ))}
              </Select>
            </AForm.Item>
            <AForm.Item label="授予目标">
              <Input.TextArea
                value={grant.account_ids}
                onChange={(e: any) => setGrant({ ...grant, account_ids: e.target.value })}
                placeholder="请输入用户长ID，以英文逗号隔开"
                rows={4}
              />
            </AForm.Item>
          </AForm>
        </Modal>
      </div>
    </>
  );
}
