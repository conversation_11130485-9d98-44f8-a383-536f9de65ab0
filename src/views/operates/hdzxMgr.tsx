import React, { useState, useEffect, useCallback, useMemo, useRef } from 'react';
import { useStore, useDispatch, useSelector } from 'react-redux';
import { useHistory } from 'react-router-dom';
import { Table, A, OrderColumn, Drawer } from '@components/common';
import { CommonObject } from '@app/types';
import { getCrumb, setMenuHook } from '@app/utils/utils';
import {
  Row,
  Col,
  Input,
  Divider,
  Button,
  Select,
  Icon,
  Modal,
  message,
  InputNumber,
  Tooltip,
  Timeline,
} from 'antd';
import { getTableList } from '@app/action/tableList';
import { opApi as api, releaseListApi } from '@app/api';
import useXHR from '@utils/useXhr';
import { PermA, PermButton } from '@components/permItems';
import Radio from 'antd/es/radio';
import { setConfig } from '@app/action/config';
import moment from 'moment';
import AssistantForm from './assistantForm';
import ConfigGPTStyleDrawer from './configGPTStyleDrawer';
import '@components/business/styles/business.scss';
import CommentActivityConfigModal from './commentActivityConfigModal';
import AvatarFormDrawer from './avatarFormDrawer';
import ImagePreviewColumn from '@app/components/common/imagePreviewColumn';

const defaultSize = 10;
export default function AssistantManager(props: any) {
  const [filter, setFilter] = useState<any>({});

  const dispatch = useDispatch();
  const store = useStore();
  const history = useHistory();
  const {
    total,
    current,
    size,
    records = [],
    allData,
  } = useSelector((state: any) => state.tableList);
  const { loading, run } = useXHR();
  const formRef = useRef<any>(null);

  const [logs, setLogs] = useState<any>({
    visible: false,
    logs: [],
    title: '',
  });

  const [commentConfigModal, setCommentConfigModal] = useState({
    visible: false,
    key: null,
    record: null,
  });

  const [drawer, setDrawer] = useState({
    visible: false,
    key: 0,
    formContent: null,
  });

  const [styleDrawer, setStyleDrawer] = useState({
    visible: false,
    key: 0,
    record: null,
  });

  const getSeq = (i: number) => (current - 1) * size + i + 1;

  const f = useMemo(() => {
    const x: CommonObject = { ...filter };
    return x;
  }, [filter]);

  const getList = useCallback(
    (overlap: CommonObject = {}) => {
      const { current, size = defaultSize } = store.getState().tableList;
      dispatch(getTableList('getHudongAvatarList', 'list', { ...f, current, size, ...overlap }));
    },
    [f]
  );

  const editRecord = (record: any) => {
    run(api.getHudongAvatarDetail, { id: record.id }, true).then((r: any) => {
      const detail = r?.data?.recommend;
      setDrawer({
        visible: true,
        key: Date.now(),
        formContent: detail,
      });
    });
  };

  const deleteRecord = (record: any) => {
    Modal.confirm({
      title: `确定要删除吗？`,
      onOk: () => {
        run(api.delHudongAvatar, { id: record.id }, true).then(() => {
          message.success('操作成功');
          getList();
        });
      },
    });
  };

  const updateRecordStatus = (record: any) => {
    run(
      api.updateHudongAvatarStatus,
      { id: record.id, status: record.status == 1 ? 0 : 1 },
      true
    ).then(() => {
      message.success('操作成功');
      getList();
    });
  };

  const getOperateLog = (record: any) => {
    run(releaseListApi.getRecommendOperateLog, { id: record.id, type: 'HudongRecommend' }, true)
      .then((r: any) => {
        setLogs({
          visible: true,
          logs: r.data.logs,
          title: record.title,
          key: Date.now(),
        });
      })
      .catch();
  };

  const getColumns = () => {
    let values = [
      {
        title: '序号',
        key: 'seq',
        render: (text: any, record: any, i: number) => <span>{getSeq(i)}</span>,
        width: 70,
      },
      {
        title: '名称',
        dataIndex: 'title',
        width: 200,
      },
      {
        title: '头像',
        dataIndex: 'pic_url',
        width: 100,
        render: (text: any) => {
          return (
            <div style={{ height: 60, textAlign: 'center' }}>
              <ImagePreviewColumn text={text} imgs={[text]}></ImagePreviewColumn>
              {/* <img src={text?.[0]} className='list-pic' onMouseEnter={() => showImagePreviewModal({ images: record.pic_array })}></img> */}
            </div>
          );
        },
      },
      {
        title: '有效期',
        dataIndex: 'ref_ids',
        width: 200,
        render: (text: any, record: any) => {
          return (
            <span>
              {record.ref_ids}~{record.ref_ids2}
            </span>
          );
        },
      },
      {
        title: '显示状态',
        dataIndex: 'status',
        width: 90,
        render: (text: any, record: any) => {
          return <span>{text == 1 ? '启用' : '禁用'}</span>;
        },
      },
      {
        title: '操作人',
        dataIndex: 'updated_by',
        width: 120,
      },
      {
        title: '操作时间',
        dataIndex: 'updated_at',
        width: 100,
        render: (text: any, record: any) => {
          return (
            <A onClick={() => getOperateLog(record)} style={{ whiteSpace: 'normal' }}>
              <div>{moment(text).format('YYYY-MM-DD')}</div>
              <div>{moment(text).format('HH:mm:ss')}</div>
            </A>
          );
        },
      },
      {
        title: '操作',
        key: 'op',
        render: (text: any, record: any, i: number) => (
          <span>
            <PermA perm="hudongzx:update" onClick={() => editRecord(record)}>
              编辑
            </PermA>
            <Divider type="vertical" />
            <PermA perm="hudongzx:update_status" onClick={() => updateRecordStatus(record)}>
              {record.status == 1 ? '禁用' : '启用'}
            </PermA>
            <Divider type="vertical" />
            <PermA perm="hudongzx:delete" onClick={() => deleteRecord(record)}>
              删除
            </PermA>
          </span>
        ),
        width: 140,
      },
    ];

    return values;
  };

  useEffect(() => {
    // getList({ current: 1, size: 10 });
    setMenuHook(dispatch, props);
  }, []);

  useEffect(() => {
    getList({ current: 1, size: defaultSize });
  }, [f]);

  const addRecord = () => {
    // addRef.current.showModal()
    setDrawer({
      key: Date.now(),
      visible: true,
      record: null,
    });
  };

  const publish = () => {
    if (records.length < 20) {
      message.error('请至少选择20条数据');
      return;
    }
    const ids = records
      .slice(0, 20)
      .map((v: any) => v.id)
      .join(',');

    Modal.confirm({
      title: '确定发布前20条稿件吗？',
      onOk: () => {
        run(api.rankingPublish, { data_ids: ids, type: filter.type }, true).then(() => {
          message.success('操作成功');

          setFilter({
            ...filter,
            status: 0,
          });
        });
      },
    });
  };

  const onSubmitEnd = (v: any) => {
    if (filter.type != v) {
      setFilter({
        ...filter,
        type: v,
      });
    } else {
      getList({ current: current, size: defaultSize });
    }

    setDrawer({ ...drawer, visible: false });
  };

  const showConfigModal = () => {
    run(api.gptHudongConfig, {}, true)
      .then((res: any) => {
        const config = res?.data?.config;
        setCommentConfigModal({
          record: config,
          visible: true,
          key: Date.now(),
        });
      })
      .catch((e) => {});
  };

  return (
    <>
      <Row className="layout-infobar">
        <Col span={12}>
          <PermButton perm="hudongzx:create" style={{ marginRight: 8 }} onClick={addRecord}>
            <Icon type="plus-circle" /> 新建
          </PermButton>

          <PermButton
            perm="hudongzx:config_save"
            style={{ marginRight: 8 }}
            onClick={showConfigModal}
          >
            评论活动配置
          </PermButton>
        </Col>
        <Col span={12} className="layout-breadcrumb">
          {getCrumb(props.breadCrumb)}
        </Col>
      </Row>
      <div className="component-content">
        <Table
          func="getHudongAvatarList"
          index="list"
          filter={f}
          columns={getColumns()}
          rowKey="id"
          pagination={true}
        />

        <AvatarFormDrawer
          {...drawer}
          onClose={() => setDrawer({ ...drawer, visible: false })}
          onOk={() => {
            setDrawer({ ...drawer, visible: false });
            getList();
          }}
        ></AvatarFormDrawer>

        <CommentActivityConfigModal
          {...commentConfigModal}
          onCancel={() =>
            setCommentConfigModal({
              visible: false,
              record: null,
              key: null,
            })
          }
          onEnd={() => {
            setCommentConfigModal({
              visible: false,
              record: null,
              key: null,
            });
          }}
        ></CommentActivityConfigModal>

        <Modal
          visible={logs.visible}
          title="操作日志"
          key={logs.key}
          cancelText={null}
          onCancel={() => setLogs({ visible: false })}
          onOk={() => setLogs({ visible: false })}
        >
          <div>
            <Timeline>
              {logs?.logs?.map((v: any, i: number) => [
                <Timeline.Item className="timeline-dot-big" data-show={v.date} key={`time${i}`}>
                  &nbsp;
                </Timeline.Item>,
                v.actions.map((action: any, index: number) => (
                  <Timeline.Item
                    className="timeline-dot"
                    data-show={action.time}
                    key={`time${i}-action${index}`}
                  >
                    {action.user}&emsp;&emsp;{action.action}&emsp;&emsp;
                  </Timeline.Item>
                )),
              ])}
            </Timeline>
          </div>
        </Modal>
      </div>
    </>
  );
}
