import NewNewsSearchAndInput from '@app/components/common/newNewsSearchAndInput';
import {
  Button,
  Checkbox,
  Col,
  Form,
  Icon,
  Input,
  InputNumber,
  Modal,
  Radio,
  Row,
  Select,
  Spin,
  Tooltip,
  TreeSelect,
  message,
} from 'antd';
import React, { forwardRef, useEffect, useImperativeHandle, useState } from 'react';
import _ from 'lodash';
import { communityApi, opApi, systemApi } from '@app/api';
import { FileUploader } from '@app/components/common';
import { iMReportTypeMap } from '@app/utils/utils';

const VideoResourceConfigModal = (props: any, ref: any) => {
  const [loading, setLoading] = useState(false);
  const { getFieldDecorator, getFieldValue } = props.form;
  const [accountOptions, setAccountOptions] = useState([]);
  const [channelList, setChannelList] = useState([]);
  const [checkedKeys, setCheckedKeys] = useState<any[]>([]);
  const formLayout = {
    labelCol: { span: 4 },
    wrapperCol: { span: 18 },
  };

  const [radioValue, setRadioValue] = useState<any>(0);

  useEffect(() => {
    if (props.visible) {
      getChannelList();
      if (props.record?.channel_ids == '-1' || !props.record?.channel_ids) {
        setCheckedKeys([]);
        setRadioValue(0);
      } else {
        setRadioValue(1);
        setCheckedKeys(props.record?.channel_ids?.split(',') || []);
      }
    }
  }, [props.visible]);

  const getChannelList = () => {
    setLoading(true);
    systemApi
      .getAllChannelsTree()
      .then((r: any) => {
        setChannelList(r.data.channel_list);
        setLoading(false);
      })
      .catch(() => {
        setLoading(false);
      });
  };

  const handleSubmit = (e: any) => {
    if (radioValue == 1 && checkedKeys.length === 0) {
      message.error('请选择频道');
      return;
    }
    setLoading(true);
    const params: any = {
      channel_ids: radioValue == 0 ? '-1' : checkedKeys.join(','),
    };

    opApi
      .updateVideoResourceConfig(params)
      .then((res: any) => {
        message.success('操作成功');
        setLoading(false);
        props.onOk && props.onOk();
      })
      .catch(() => {
        setLoading(false);
      });
  };

  const renderTreeNode = (node: any) => {
    return node.children && node.children.length > 0 ? (
      <TreeSelect.TreeNode title={node.name} key={node.id} value={node.id}>
        {node.children.map((snode: any) => renderTreeNode(snode))}
      </TreeSelect.TreeNode>
    ) : (
      <TreeSelect.TreeNode title={node.name} key={node.id} value={node.id} />
    );
  };

  return (
    <Modal
      width={500}
      visible={props.visible}
      title={'视频设置'}
      key={props.key}
      onCancel={() => {
        props.onCancel && props.onCancel();
      }}
      onOk={handleSubmit}
      maskClosable={false}
      destroyOnClose={true}
    >
      {console.log(checkedKeys)}
      <Spin spinning={false}>
        <Row style={{ lineHeight: '32px' }}>
          <Col span={4}>频道设置:</Col>
          <Col span={20}>
            <Radio.Group value={radioValue} onChange={(e) => setRadioValue(e.target.value)}>
              <Radio value={0}>全部频道</Radio>
              <Radio value={1}>部分频道</Radio>
            </Radio.Group>
          </Col>
        </Row>
        {radioValue == 1 && (
          <Row style={{ lineHeight: '32px' }}>
            <Col span={4}></Col>
            <Col span={20}>
              <TreeSelect
                dropdownStyle={{ maxHeight: 400, overflow: 'auto' }}
                placeholder="请选择频道"
                treeDefaultExpandAll={false}
                allowClear
                treeCheckable
                style={{ width: '100%' }}
                multiple
                value={checkedKeys}
                treeNodeFilterProp="title"
                onChange={(value: any) => {
                  setCheckedKeys(value);
                }}
              >
                {channelList.map((node: any) => renderTreeNode(node))}
              </TreeSelect>
            </Col>
          </Row>
        )}
      </Spin>
    </Modal>
  );
};

export default Form.create<any>({ name: 'VideoResourceConfigModal' })(
  forwardRef<any, any>(VideoResourceConfigModal)
);
