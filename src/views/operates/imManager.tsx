import React, { useState, useEffect, useCallback, useMemo, useRef } from 'react';
import { useStore, useDispatch, useSelector } from 'react-redux';
import { useHistory } from 'react-router-dom';
import { Table, A, OrderColumn, Drawer } from '@components/common';
import { CommonObject } from '@app/types';
import { getCrumb, setMenuHook, UserDetail } from '@app/utils/utils';
import {
  Row,
  Col,
  Input,
  Divider,
  Button,
  Select,
  Icon,
  Modal,
  message,
  InputNumber,
  Tooltip,
  Timeline,
  Switch,
  DatePicker,
  Checkbox,
} from 'antd';
import { getTableList } from '@app/action/tableList';
import { opApi as api, communityApi, releaseListApi, userApi } from '@app/api';
import useXHR from '@utils/useXhr';
import { PermA, PermButton } from '@components/permItems';
import { setConfig } from '@app/action/config';
import moment from 'moment';
import AssistantForm from './assistantForm';
import '@components/business/styles/business.scss';
import AddAudioArticleModal from './addAudioArticleModal';
import AddAudioColumnModal from './addAudioColumnModal';
import AudioRedPacketModal from './audioRedPacketModal';
import AudioBroadcastDrawer from './audioBroadcastDrawer';
import _ from 'lodash';
import ImRecordDrawer from './imRecordDrawer';

const defaultSize = 10;
export default function ImManager(props: any) {
  const [accountOptions, setAccountOptions] = useState([]);

  const [filter, setFilter] = useState<any>({
    search_type: 1,
    keyword: '',
    only_show_whitelist: false,
  });

  const [search, setSearch] = useState({
    search_type: 1,
    keyword: '',
    // user_id: undefined,
  });
  const dispatch = useDispatch();
  const store = useStore();
  const history = useHistory();
  const {
    total,
    current,
    size,
    records = [],
    allData,
  } = useSelector((state: any) => state.tableList);
  const { loading, run } = useXHR();
  const formRef = useRef<any>(null);
  const addRef = useRef<any>(null);

  const [recordDrawer, setRecordDrawer] = useState({
    visible: false,
    key: 0,
    record: null,
  });

  const [userDetail, setUserDetail] = useState({
    key: null,
    visible: false,
    detail: null,
  });

  const getSeq = (i: number) => (current - 1) * size + i + 1;

  const f = useMemo(() => {
    const x: CommonObject = { ...filter };
    if (!x.keyword) {
      delete x.keyword;
      delete x.search_type;
    }

    if (x.start_time) {
      x.start_time = x.start_time.format('YYYY-MM-DD');
    }

    if (x.end_time) {
      x.end_time = x.end_time.format('YYYY-MM-DD');
    }

    return x;
  }, [filter]);

  const getList = (goToFirstPage = false, newFilter = f) => {
    let cur = goToFirstPage ? 1 : current;

    dispatch(getTableList('getIMList', 'list', { current: cur, size, ...newFilter }));
  };

  const editRecord = (record: any) => {
    run(api.gptQuestionDetail, { id: record.id }, true)
      .then((data: any) => {
        console.log('gptQuestionDetail', data);
        const {
          data: { detail, article_list },
        } = data;
        const body = {
          ...detail,
          articles: article_list,
        };

        setDrawer({
          visible: true,
          key: Date.now(),
          record: body,
        });
      })
      .catch((e: any) => {});
  };
  const deleteRecord = (record: any) => {
    Modal.confirm({
      title: `确定要删除吗？`,
      onOk: () => {
        run(api.deleteAudioPlazaArticle, { recommend_id: record.recommend_id }, true).then(() => {
          message.success('操作成功');
          getList();
        });
      },
    });
  };

  const updateRecordStatus = (record: any) => {
    run(
      api.updateGPTQuestionStatus,
      { id: record.id, status: record.status == 1 ? 0 : 1 },
      true
    ).then(() => {
      message.success('操作成功');
      getList();
    });
  };

  const listSort = (record: any, current: number, offset: number) => {
    let data: any = {
      recommend_id: record.recommend_id,
      offset: offset,
      current,
    };
    api.audioArticleSort(data).then((res: any) => {
      message.success('操作成功');
      getList();
    });
  };

  const changeOrder = (record: any, i: any) => {
    let position = i;
    const WAIT_TIME = 1000;
    const positionChange = (v: any) => {
      position = v;
    };

    Modal.confirm({
      title: (
        <p
          style={{
            whiteSpace: 'nowrap',
            overflow: 'hidden',
            textOverflow: 'ellipsis',
          }}
        >
          排序：{record.list_title}
        </p>
      ),
      icon: <Icon type="info-circle" />,
      content: (
        <div>
          <span>请输入位置：</span>
          <InputNumber min={1} defaultValue={position} onChange={positionChange} />
        </div>
      ),
      onOk: (closeFunc: Function) => {
        if (!position) {
          message.error('请填写位置');
          return;
        }

        dispatch(setConfig({ loading: true }));
        api
          .audioArticleMovePosition({ recommend_id: record.recommend_id, position: position })
          .then((res: any) => {
            message.success('操作成功');
            getList();
            dispatch(setConfig({ loading: false }));
            closeFunc();
          })
          .catch(() => {
            dispatch(setConfig({ loading: false }));
          });
      },
    });
  };

  const getOperateLog = (record: any) => {
    run(
      releaseListApi.getRecommendOperateLog,
      { id: record.recommend_id, type: 'AudioHomeRecommend' },
      true
    )
      .then((r: any) => {
        setLogs({
          visible: true,
          logs: r.data.logs,
          title: record.list_title,
          key: Date.now(),
        });
      })
      .catch();
  };

  const titleClick = (record: any) => {
    // setPreview({
    //   visible: true,
    //   skey: Date.now(),
    //   data: record
    // })
    if (!!record.url) {
      window.open(record.url, '_blank');
    }
  };

  const showRecord = (record: any) => {
    setRecordDrawer({
      visible: true,
      key: Date.now(),
      record: record,
      title: `${record.send_account_name}和${record.receive_account_name}的对话记录`,
    });
  };

  const getColumns = () => {
    let values = [
      {
        title: '发送人',
        dataIndex: 'send_account_name',
        width: 150,
        render: (text: any, record: any) => (
          <a onClick={() => showUserDetailModal(record.send_account_id, true)}>{text}</a>
        ),
      },
      {
        title: '接收人',
        dataIndex: 'receive_account_name',
        width: 150,
        render: (text: any, record: any) => (
          <a onClick={() => showUserDetailModal(record.receive_account_id, true)}>{text}</a>
        ),
      },
      {
        title: '私信内容',
        dataIndex: 'content',
      },
      {
        title: '发送时间',
        dataIndex: 'created_at',
        width: 160,
        render: (text: any, record: any) => moment(text).format('YYYY-MM-DD HH:mm:ss'),
      },
      {
        title: '操作',
        key: 'op',
        render: (text: any, record: any, i: number) => (
          <span>
            <PermA perm="im:list" onClick={() => showRecord(record)}>
              查看对话记录
            </PermA>
          </span>
        ),
        width: 140,
      },
    ];
    return values;
  };

  useEffect(() => {
    // getList({ current: 1, size: 10 });
    setMenuHook(dispatch, props);
  }, []);

  useEffect(() => {
    getList(true);
  }, [f]);

  const onChangeType = (e: any) => {
    setFilter((of: any) => {
      return {
        ...of,
        type: e.target.value,
        search_type: 1,
        keyword: '',
      };
    });
    setSearch({
      search_type: 1,
      keyword: '',
    });
  };

  const searchInputChange = (e: any) => {
    setSearch({
      ...search,
      search_type: e,
    });
  };

  const addRecord = () => {
    // addRef.current.showModal()
    setDrawer({
      key: Date.now(),
      visible: true,
      record: null,
    });
  };

  const handleAddArticleOk = () => {
    getList({ current: 1, size: defaultSize });
  };

  const handleKey = (e: { which: number }) => {
    if (e.which === 13) {
      setFilter({
        ...filter,
        keyword: search.search_type == 1 ? search.keyword : search.user_id,
        search_type: search.search_type,
      });
    }
  };

  const editStyle = () => {
    api.getGptStyleDetail({}).then((res: any) => {
      setStyleDrawer({
        visible: true,
        record: {
          ...(res?.data?.detail || {}),
          color: res?.data?.detail?.ref_ids?.split(','),
        },
        key: Date.now(),
      });
    });
  };

  const showPaperConfig = () => {
    let value = false;
    const valueChange = (v: any) => {
      value = v;
      modal.update({
        content: (
          <>
            <span>展示早晚报模块</span>&nbsp;&nbsp;&nbsp;
            <Switch checked={value} onChange={valueChange}></Switch>
          </>
        ),
      });
    };

    var modal = Modal.confirm({
      title: '早晚报配置',
      content: (
        <>
          <span>展示早晚报模块</span>&nbsp;&nbsp;&nbsp;
          <Switch checked={value} onChange={valueChange}></Switch>
        </>
      ),
      onOk: (destroy: Function) => {
        const { permissions } = store.getState().session;
        const disabled = permissions.indexOf('web_feature:audio_zwb_switch') === -1;
        if (disabled) {
          message.error('没有权限');
          destroy();
          return;
        }

        run(
          api.updateAudioZwbSwitch,
          {
            feature: 'audio_zwb_switch',
            status: value,
          },
          true
        ).then(() => {
          message.success('操作成功');
        });
        destroy();
      },
    });

    api
      .getAudioZwbSwitch()
      .then((data: any) => {
        valueChange(data.data?.switch);
      })
      .catch((e) => {});
  };

  const publish = () => {
    if (records.length < 20) {
      message.error('请至少选择20条数据');
      return;
    }
    const ids = records
      .slice(0, 20)
      .map((v: any) => v.id)
      .join(',');

    Modal.confirm({
      title: '确定发布前20条稿件吗？',
      onOk: () => {
        run(api.rankingPublish, { data_ids: ids, type: filter.type }, true).then(() => {
          message.success('操作成功');

          setFilter({
            ...filter,
            status: 0,
          });
        });
      },
    });
  };

  const onSubmitEnd = (v: any) => {
    if (filter.type != v) {
      setFilter({
        ...filter,
        type: v,
      });
    } else {
      getList({ current: current, size: defaultSize });
    }

    setDrawer({ ...drawer, visible: false });
  };

  const addArticle = () => {
    addRef.current.showModal();
  };

  const addColumn = () => {
    run(api.getAudioPlazaColumn, {}, true).then((res: any) => {
      setAddColumnState({
        visible: true,
        key: Date.now(),
        column_list: res.data?.column_list,
      });
    });
  };

  const editRedpacket = () => {
    run(api.getAudioPlazaRedpacket, {}, true).then((res: any) => {
      setRedpacketState({
        visible: true,
        key: Date.now(),
        record: res.data?.redpacket_info,
      });
    });
  };

  const showBroadcastDrawer = () => {
    run(api.getBroadcast, {}, true).then((res: any) => {
      setBroadcastDrawerState({
        visible: true,
        key: Date.now(),
        record: res.data?.recommend,
      });
    });
  };

  // 显示用户详情
  const showUserDetailModal = (id: any, visible: boolean) => {
    run(userApi.getUserDetail, { accountId: id }, true).then((r: any) => {
      setUserDetail({
        key: Date.now(),
        visible,
        detail: r.data.account,
      });
    });
  };

  // 时间选择
  const timeChange = (dates: any) => {
    if (dates.length === 0) {
      const { start_time, end_time, ...newFilter } = filter;
      setFilter({ ...newFilter });
    } else {
      setFilter({
        ...filter,
        start_time: dates[0],
        end_time: dates[1],
      });
    }
  };

  const handleAccountSearch = _.debounce((val: any) => {
    if (!val) {
      setAccountOptions([]);
      return;
    }
    // type: 1, current: 1, size: 50, biz_type: props.formContent?.biz_type,
    communityApi
      .recommendAccount_Search({ keyword: val })
      .then((res) => {
        setAccountOptions(res?.data?.list || []);
      })
      .catch(() => {});
  }, 500);

  return (
    <>
      <Row className="layout-infobar">
        <Col span={12}>
          <PermButton
            perm="im_complain:list"
            style={{ marginRight: 8 }}
            onClick={() => {
              history.push('/view/im-report-list');
            }}
          >
            私信举报
          </PermButton>
          <PermButton
            // perm="gpt_question:style_detail"
            perm="im_blacklist:list"
            style={{ marginRight: 8 }}
            onClick={() => {
              history.push('/view/im-black-list');
            }}
          >
            私信黑名单
          </PermButton>

          <PermButton
            // perm="gpt_question:style_detail"
            perm="im_whitelist:list"
            style={{ marginRight: 8 }}
            onClick={() => {
              history.push('/view/im-white-list');
            }}
          >
            私信白名单
          </PermButton>

          {/* <PermButton
            perm=""
            onClick={() => {
              showPaperConfig();
            }}
          >
            私信检测配置
          </PermButton> */}
        </Col>
        <Col span={12} className="layout-breadcrumb">
          {getCrumb(props.breadCrumb)}
        </Col>
      </Row>
      <div className="component-content">
        <Row style={{ marginBottom: 16 }}>
          <Col span={12}>
            <DatePicker.RangePicker
              value={[filter.start_time, filter.end_time]}
              style={{ width: 260, marginRight: 8 }}
              format="YYYY-MM-DD"
              onChange={timeChange}
            />

            <Checkbox
              value={filter.only_show_whitelist}
              onChange={(e) => {
                setFilter({
                  ...filter,
                  only_show_whitelist: e.target.checked,
                });
              }}
            >
              只看白名单私信
            </Checkbox>
          </Col>

          <Col span={12} style={{ textAlign: 'right' }}>
            <Select
              value={search.search_type}
              style={{ width: 150, marginRight: 8, marginLeft: 8 }}
              onChange={searchInputChange}
            >
              <Select.Option value={1}>私信内容</Select.Option>
              <Select.Option value={2}>发送人</Select.Option>
              <Select.Option value={3}>接收人</Select.Option>
            </Select>
            {search.search_type == 1 ? (
              <Input
                style={{ width: 160, marginRight: 8 }}
                onKeyPress={handleKey}
                value={search.keyword}
                placeholder="请输入搜索内容"
                onChange={(e) =>
                  setSearch({
                    ...search,
                    keyword: e.target.value,
                  })
                }
              />
            ) : (
              <Select
                style={{ width: 200, marginRight: 8 }}
                value={search.user_id}
                placeholder="输入用户昵称或用户ID"
                onSearch={handleAccountSearch}
                showSearch
                allowClear={true}
                filterOption={false}
                onChange={(val) => {
                  setSearch({
                    ...search,
                    user_id: val,
                  });
                }}
              >
                {accountOptions.map((d: any) => (
                  <Select.Option
                    style={{
                      whiteSpace: 'pre-wrap',
                    }}
                    key={d.id}
                    value={d.id}
                  >
                    {props.optionMap
                      ? props.optionMap(d)
                      : `${
                          ['潮客 - ', '潮鸣号 - ', '潮鸣号 - '][d.cert_type] + d.nick_name
                        } | 用户ID： ${d.chao_id}`}
                  </Select.Option>
                ))}
              </Select>
            )}
            <Button onClick={() => handleKey({ which: 13 })}>
              <Icon type="search" />
              搜索
            </Button>
          </Col>
        </Row>
        <Table
          func="getIMList"
          index="list"
          filter={f}
          columns={getColumns()}
          rowKey="id"
          pagination={true}
        />

        <ImRecordDrawer
          {...recordDrawer}
          onCancel={() => setRecordDrawer({ ...recordDrawer, visible: false })}
        ></ImRecordDrawer>
      </div>

      <Modal
        visible={userDetail.visible}
        key={userDetail.key}
        title="用户详情"
        width={800}
        onCancel={() => setUserDetail({ ...userDetail, visible: false })}
        onOk={() => setUserDetail({ ...userDetail, visible: false })}
      >
        {userDetail.visible && <UserDetail detail={userDetail.detail} />}
      </Modal>
    </>
  );
}
