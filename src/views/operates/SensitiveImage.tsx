import React, { useRef, useState } from 'react';
import './sensitiveImage.scss';

function SensitiveImage(props: any) {
  const { src, evidence, className } = props;
  const imgRef = useRef<any>(null);

  const [svgAttr, setSvgAttr] = useState({
    viewBox: '0 0 0 0',
    polyline: [],
    width: 0,
    height: 0,
  });
  const handleImageLoad = () => {
    console.log('handleImageLoad', imgRef.current.naturalWidth, imgRef.current.naturalHeight);

    const attr: any = {
      viewBox: '0 0 0 0',
      polyline: [],
      width: 0,
      height: 0,
    };

    const width = imgRef.current.offsetWidth;
    const height = imgRef.current.offsetHeight;
    attr.viewBox = `0 0 ${width} ${height}`;
    attr.width = width;
    attr.height = height;
    if (props.evidence) {
      let array = [];
      if (props.evidence?.texts) {
        array = props.evidence?.texts;
      } else if (props.evidence?.location) {
        array = [props.evidence?.location];
      }
      attr.polyline =
        array.map((item: any) => {
          let points = [];
          // 左上角
          points.push((item.leftOffsetInPixel / imgRef.current.naturalWidth) * width);
          points.push((item.topOffsetInPixel / imgRef.current.naturalHeight) * height);
          // 右上角
          points.push(
            ((item.leftOffsetInPixel + item.widthInPixel) / imgRef.current.naturalWidth) * width
          );
          points.push((item.topOffsetInPixel / imgRef.current.naturalHeight) * height);
          // 右下角
          points.push(
            ((item.leftOffsetInPixel + item.widthInPixel) / imgRef.current.naturalWidth) * width
          );
          points.push(
            ((item.topOffsetInPixel + item.heightInPixel) / imgRef.current.naturalHeight) * height
          );
          // 左下角
          points.push((item.leftOffsetInPixel / imgRef.current.naturalWidth) * width);
          points.push(
            ((item.topOffsetInPixel + item.heightInPixel) / imgRef.current.naturalHeight) * height
          );
          // 左上角
          points.push((item.leftOffsetInPixel / imgRef.current.naturalWidth) * width);
          points.push((item.topOffsetInPixel / imgRef.current.naturalHeight) * height);

          return {
            points: points.join(','),
          };
        }) || [];
    }

    setSvgAttr(attr);
  };
  return (
    <div className={`sensitive-image-wrapper ${className}`}>
      <img ref={imgRef} src={src} onLoad={handleImageLoad} />
      {!!evidence && (
        <svg
          viewBox={svgAttr.viewBox}
          width={svgAttr.width}
          height={svgAttr.height}
          className="img_mask"
          style={{ zIndex: 1, pointerEvents: 'none' }}
        >
          {svgAttr.polyline.map((item: any, index: number) => {
            return (
              <polyline
                key={index}
                points={item.points}
                strokeWidth="2"
                fill="none"
                stroke="#FF0000"
              ></polyline>
            );
          })}
        </svg>
      )}
    </div>
  );
}

export default SensitiveImage;
