import { opApi as api } from '@app/api';
import { ITableProps, IBaseProps, CommonObject } from '@app/types';
import { A, Table, BaseComponent, Drawer } from '@components/common';
import ColumnForm from '@components/business/ColumnForm';
import { connectTable as connect } from '@utils/connect';
import {
  Button,
  Col,
  Icon,
  message,
  Modal,
  Row,
  Menu,
  Dropdown,
  Input,
  Select,
  DatePicker,
  Tooltip,
} from 'antd';
import moment from 'moment';
import React from 'react';
import { withRouter } from 'react-router';
import { RangePickerValue } from 'antd/es/date-picker/interface';
import { TopicRecord, TopicAllData } from './operates';
import ReactClipboard from 'react-clipboardjs-copy';
import { clearTableList, setTableList } from '@app/action/tableList';
import { setTableCache } from '@app/action/tableCache';
type State = {
  filter: {
    begin: string;
    end: string;
    class_id: number;
    type: 0 | 1;
    search_type: 1 | 2;
    keyword: string;
  };
  cType: 1 | 2;
  cKeyword: string;
  onCount: number;
  form: {
    visible: boolean;
    key: number;
    record: any;
  };
  classList: CommonObject[];
};

type Props = IBaseProps<ITableProps<TopicRecord, TopicAllData>>;

class ColumnManager extends BaseComponent<ITableProps<TopicRecord, TopicAllData>, State> {
  constructor(props: Props) {
    super(props);
    this.state = {
      filter: {
        begin: '',
        end: '',
        class_id: 0,
        type: 0,
        search_type: 1,
        keyword: '',
      },
      cType: 1,
      cKeyword: '',
      onCount: 0,
      form: {
        visible: false,
        key: Date.now(),
        record: null,
      },
      classList: [],
    };
  }

  componentDidMount() {
    this.setMenu();
    this.getTopicClass();

    if (
      this.props.tableCache?.beforeRoute === this.props.match?.path &&
      this.props.tableCache.records.length > 0
    ) {
      this.props.dispatch(setTableList(this.props.tableCache));

      if (this.props.tableCache?.filters) {
        this.setState({
          filter: this.props.tableCache?.filters,
          cType: this.props.tableCache?.filters.search_type,
          cKeyword: this.props.tableCache?.filters.keyword,
        });
      }
    } else {
      this.getData({ current: 1, size: 10 });
    }
  }

  componentDidUpdate(prevProps: Props) {
    if (
      prevProps.tableList.timestamp !== this.props.tableList.timestamp &&
      Boolean(this.props.tableList.allData.list)
    ) {
      this.setState({
        onCount: this.props.tableList.allData.on_show_count,
      });
    }
  }

  getTopicClass = () => {
    this.setLoading(true);
    api
      .getColumnClassList({ type: 0 })
      .then((res: any) => {
        // TODO 不分页列表修改
        this.setState({
          classList: res.data.list,
        });
        this.setLoading(false);
      })
      .catch(() => {
        this.setLoading(false);
      });
  };

  getData = (overlap: CommonObject = {}, filters = this.getFilters()) => {
    this.dispatchTable('getColumnList', 'list', { ...filters, ...overlap });
  };

  getFilters = () => {
    const { current, size } = this.props.tableList;
    const { filter } = this.state;
    const filters: CommonObject = { current, size, ...filter };
    Object.keys(filter).map((k: string) => {
      if (k === 'type') {
        return;
      }
      if (!filters[k]) {
        delete filters[k];
      }
    });
    return filters;
  };

  getColumns = () => {
    const { current, size } = this.props.tableList;
    const { onCount } = this.state;
    const { begin, keyword } = this.state.filter;
    const disableSort = begin || keyword;
    const getSeq = (i: number) => (current - 1) * size + i + 1;
    const permMid = '0:';
    const getDropdown = (record: any) => {
      const menu = (
        <Menu>
          {this.requirePerm(`topic_label:0:update`)(
            <Menu.Item onClick={this.editRecord.bind(this, record)}>编辑</Menu.Item>
          )}
          {this.requirePerm(`topic_label:0:change_enable`)(
            <Menu.Item onClick={this.updateStatus.bind(this, record)}>
              {record.enabled ? '下线' : '上线'}
            </Menu.Item>
          )}
          {this.requirePerm(`topic_label:0:delete`)(
            <Menu.Item onClick={this.deleteRecord.bind(this, record)}>删除</Menu.Item>
          )}
          <Menu.Item>
            {record.url ? (
              <ReactClipboard
                action="copy"
                text={record.url}
                onSuccess={() => message.success('链接已复制')}
                onError={() => message.error('复制失败')}
              >
                <a>复制链接</a>
              </ReactClipboard>
            ) : (
              '复制链接'
            )}
          </Menu.Item>
        </Menu>
      );
      return (
        <Dropdown overlay={menu}>
          <a className="ant-dropdown-link">
            操作 <Icon type="down" />
          </a>
        </Dropdown>
      );
    };
    const column = [
      {
        title: '序号',
        key: 'seq',
        render: (text: any, record: any, i: number) => <span>{getSeq(i)}</span>,
        width: 70,
      },
      {
        title: '地方号ID',
        dataIndex: 'id',
      },
      {
        title: '地方号名称',
        key: 'title',
        dataIndex: 'name',
        // width: 200,
        render: (text: any, record: any) => (
          <A onClick={this.toArticleList.bind(this, record.channel_id, text)}>{text}</A>
        ),
      },
      {
        title: '分组数',
        dataIndex: 'group_content_num',
        align: 'center',
        width: 100,
        render: (text: string, record: any) => (
          <a onClick={this.toGroupList.bind(this, record)}>{text || 0}</a>
        ),
      },
      {
        title: '关注数量',
        key: 'focus_number',
        dataIndex: 'focus_number',
        width: 100,
      },
      {
        title: '状态',
        key: 'enabled',
        dataIndex: 'enabled',
        render: (text: boolean) => <span>{text ? '展示中' : '待展示'}</span>,
        width: 90,
      },
      {
        title: '创建人',
        key: 'created_user_name',
        dataIndex: 'created_user_name',
        width: 110,
      },
      {
        title: '创建时间',
        key: 'created_at',
        dataIndex: 'created_at',
        render: (text: number) => <span>{moment(text).format('YYYY-MM-DD HH:mm:ss')}</span>,
        width: 95,
      },
      {
        title: '操作',
        key: 'op',
        render: (text: any, record: any) => <span>{getDropdown(record)}</span>,
        width: 70,
      },
    ];
    return column;
  };

  editRecord = (record: any = null) => {
    this.setState({
      form: {
        visible: true,
        key: Date.now(),
        record,
      },
    });
  };

  updateStatus = (record: any) => {
    this.setLoading(true);
    api
      .updateColumnStatus({
        id: record.id,
        enable: record.enabled ? 0 : 1,
      })
      .then(() => {
        message.success('操作成功');
        this.getData();
        this.setLoading(false);
      })
      .catch(() => {
        this.setLoading(false);
      });
  };

  deleteRecord = (record: any) => {
    Modal.confirm({
      title: '确定删除吗？',
      onOk: () => {
        this.setLoading(true);
        api
          .deleteColumn({
            id: record.id,
          })
          .then(() => {
            message.success('操作成功');
            this.getData();
            this.setLoading(false);
          })
          .catch(() => {
            this.setLoading(false);
          });
      },
    });
  };

  exchangeOrder = (id: number, current: number, offset: number) => {
    this.setLoading(true);
    api
      .sortColumn({ id, current, offset })
      .then(() => {
        message.success('操作成功');
        this.getData();
        this.setLoading(false);
      })
      .catch(() => {
        this.setLoading(false);
      });
  };

  toGroupList = (record: any) => {
    this.props.dispatch(
      setTableCache({
        beforeRoute: this.props.match.path,
        ...this.props.tableList,
        filters: this.state.filter,
      })
    );

    this.props.history.push(
      `/view/columnGroupMgr/${record.id}/${encodeURIComponent(record.name)}/${record.channel_id}`
    );
  };

  toArticleList = (id: number, name: string) => {
    this.props.dispatch(
      setTableCache({
        beforeRoute: this.props.match.path,
        ...this.props.tableList,
        filters: this.state.filter,
      })
    );

    const menu = this.props.session.menus.find((v: any) => v.url == '/releasePages');
    const child = menu?.children
      ?.find((v: any) => v.name == '地方号')
      ?.children?.find((v: any) => v.url.includes(id));
    let url;
    if (child) {
      if (child.children && child.children.length > 0) {
        url = `/view${child.children[0].url}`;
      } else {
        url = `/view${child.url}`;
      }
    }
    if (url) {
      this.props.history.push(url);
    } else {
      message.info('无法找到关联频道，请退出重试或重新关联频道');
    }
  };

  handleRangePickerChange = (dates: RangePickerValue) => {
    if (dates.length === 0) {
      this.setState(
        {
          filter: { ...this.state.filter, begin: '', end: '' },
        },
        () => this.getData({ current: 1 })
      );
    } else {
      this.setState(
        {
          filter: {
            ...this.state.filter,
            begin: dates[0] ? dates[0].format('YYYY-MM-DD') : '',
            end: dates[1] ? dates[1].format('YYYY-MM-DD') : '',
          },
        },
        () => this.getData({ current: 1 })
      );
    }
  };

  handleCTypeChange = (value: 1 | 2) => {
    this.setState({
      cType: value,
    });
  };

  handleKeywordChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    this.setState({
      cKeyword: e.target.value,
    });
  };

  doSearch = () => {
    this.setState(
      {
        filter: {
          ...this.state.filter,
          search_type: this.state.cType,
          keyword: this.state.cKeyword,
        },
      },
      () => this.getData({ current: 1 })
    );
  };

  closeDrawer = () => {
    this.setState({
      form: { ...this.state.form, visible: false },
    });
  };

  submitEnd = () => {
    this.closeDrawer();
    this.getData();
  };

  render() {
    const { form, filter, cType, cKeyword, onCount, classList } = this.state;
    return (
      <>
        <Row className="layout-infobar">
          <Col span={12}>
            {this.requirePerm('topic_label:0:create')(
              <Button onClick={() => this.editRecord()}>
                <Icon type="plus-circle-o" />
                添加地方号
              </Button>
            )}
            {this.requirePerm('topic_class:0:list')(
              <Button
                onClick={() => {
                  this.props.dispatch(clearTableList());
                  this.props.history.push('/view/columnClassifyManager');
                }}
                style={{
                  marginLeft: 10,
                }}
              >
                地方号分类
              </Button>
            )}
          </Col>
          <Col span={12} className="layout-breadcrumb">
            {this.getCrumb()}
          </Col>
        </Row>
        <div className="component-content">
          <Row style={{ marginBottom: 16 }}>
            <Col span={12}>
              <DatePicker.RangePicker
                format="YYYY-MM-DD"
                onChange={this.handleRangePickerChange}
                value={
                  filter.begin
                    ? [moment(this.state.filter.begin), moment(this.state.filter.end)]
                    : []
                }
              />
            </Col>
            <Col span={12} style={{ textAlign: 'right' }}>
              <Select
                value={cType}
                onChange={this.handleCTypeChange}
                style={{ marginRight: 8, width: 120 }}
              >
                <Select.Option value={1}>地方号名称</Select.Option>
                <Select.Option value={2}>创建人</Select.Option>
              </Select>
              <Input
                value={cKeyword}
                onChange={this.handleKeywordChange}
                style={{ width: 180, marginRight: 8 }}
                onKeyPress={this.handleKey}
                placeholder="请输入搜索内容"
              />
              <Button onClick={this.doSearch}>
                <Icon type="search" />
                搜索
              </Button>
            </Col>
          </Row>
          <Table
            func="getColumnList"
            index="list"
            columns={this.getColumns()}
            pagination={true}
            rowKey="id"
            filter={this.getFilters()}
          />
          <ColumnForm
            {...form}
            classList={this.state.classList}
            onClose={this.closeDrawer}
            onEnd={this.submitEnd}
          />
        </div>
      </>
    );
  }
}

export default withRouter(connect<TopicRecord, TopicAllData>()(ColumnManager));
