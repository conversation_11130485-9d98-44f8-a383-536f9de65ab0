import { setConfig } from '@action/config';
import { getTableList } from '@app/action/tableList';
import { opApi as api, userApi, opApi } from '@app/api';
import { CommonObject } from '@app/types';
import EntryForm from '@components/business/redPacketEntryForm';
import Form from '@components/business/redPacketInfoForm';
import { A, Drawer, Table, OrderColumn, BaseComponent, NavButton } from '@components/common';
import connect from '@utils/connectTable';
import {getCrumb, requirePerm, setLoading,  UserDetail} from '@utils/utils.tsx';
import { Button, Col, DatePicker, Icon, Input, message, Modal, Row, Select, Divider, Table as ATable } from 'antd';
import moment from 'moment';
import React from 'react';
import { withRouter } from 'react-router';

@(withRouter as any)
@connect
class RedPacketUserTotal extends BaseComponent<any, any> {
  constructor(props: any) {
    super(props);
    this.state = {
      filters: {
        nick_name: '',
      },
      sKey: 'nick_name',
      skeyword: '',
      detail: {},
      money: {
        accountFinance: {},
        finance_data_list: {
          current: 1,
          total: 0,
          records: [],
        },
      },
      visible: false,
      key: Date.now(),
      loading: false,
      eloading: false,
    };
  }

  componentDidMount() {
    this.getData({ current: 1, size: 10 });
    this.props.dispatch(
      setConfig({
        selectKeys: this.props.selectKeys,
        openKeys: this.props.openKeys,
      })
    );
  }

  getData = (overlap: CommonObject = {}) => {
    const { current, size } = this.props.tableList;
    const filters = this.getFilter();
    this.props.dispatch(
      getTableList('getUserMoneyList', 'money_detail_list', { current, size, ...filters, ...overlap })
    );
  };

  getFilter = () => {
    const filters: CommonObject = {};
    Object.keys(this.state.filters).forEach((v: any) => {
      if (this.state.filters[v]) {
        filters[v] = this.state.filters[v];
      }
    });
    return filters;
  };

  getColumns = () => {
    const { current, size, total } = this.props.tableList;
    const getSeq = (i: number) => (current - 1) * size + i + 1;

    return [
      {
        title: '序号',
        key: 'seq',
        width: 70,
        render: (text: any, record: any, index: number) => getSeq(index),
      },
      {
        title: '用户长ID',
        dataIndex: 'id',
      },
      {
        title: '用户昵称',
        dataIndex: 'nick_name',
      },
      {
        title: '手机号',
        dataIndex: 'phone_number',
        width: 110,
      },
      {
        title: '支付宝账号',
        dataIndex: 'alipay_user_no',
      },
      {
        title: '累计现金奖励',
        dataIndex: 'receive_money',
        width: 100,
        render:(text: number) => text ? text.toFixed(2) : '0.00',
      },
      {
        title: '已提现',
        dataIndex: 'cash_out_money',
        width: 80,
        render:(text: number) => text ? text.toFixed(2) : '0.00',
      },
      {
        title: '已失效',
        dataIndex: 'invalid_money',
        width: 80,
        render:(text: number) => text ? text.toFixed(2) : '0.00',
      },
      {
        title: '账户余额',
        dataIndex: 'total_money',
        width: 80,
        render:(text: number) => text ? text.toFixed(2) : '0.00',
      },
      {
        title: '操作',
        key: 'op',
        render: (record: any) => (
          <React.Fragment>
            {requirePerm(this, 'redpacket_account:detail')(<A onClick={this.showAccountDetail.bind(this, record)}>用户详情</A>)}
            <Divider type="vertical" />
            <A onClick={this.showAccountMoney.bind(this, record)}>金额详情</A>
          </React.Fragment>
        ),
        width: 160,
      },
    ];
  };

  showAccountDetail = (record: any) => {
    setLoading(this, true);
    userApi
      .getUserDetail({ accountId: record.id })
      .then((r: any) => {
        this.setState({
          visible: 'user',
          key: Date.now(),
          detail: r.data.account,
        });
        setLoading(this, false);
      })
      .catch(() => setLoading(this, false));
  };

  showAccountMoney = (record: any) => {
    setLoading(this, true);
    opApi
      .getUserMoneyDetail({
        account_id: record.id,
        current: 1,
        size: 5,
        export_flag: false,
      })
      .then((res: any) => {
        this.setState({
          visible: 'money',
          key: Date.now(),
          money: {
            ...res.data,
            accountId: record.id,
            userName: record.nick_name,
          },
        });
        setLoading(this, false);
      })
      .catch(() => setLoading(this, false));
  };

  accountPageChange = (page: number) => {
    this.setState({ loading: true });
    opApi
      .getUserMoneyDetail({
        account_id: this.state.money.accountId,
        current: page,
        size: 5,
        export_flag: false,
      })
      .then((res: any) => {
        this.setState({
          visible: 'money',
          money: {
            ...this.state.money,
            ...res.data,
          },
          loading: false,
        });
      })
      .catch(() => this.setState({ loading: false }));
  };

  exportUser = () => {
    this.setState({ eloading: true });
    opApi
      .downloadUserMoneyDetail({
        account_id: this.state.money.accountId,
        export_flag: true,
      })
      .then((res: any) => {
        this.setState({
          eloading: false,
        });
        const a = document.createElement('a');
        a.href = window.URL.createObjectURL(res.data);
        a.download = `${this.state.money.userName}_${moment().format('YYYY-MM-DD_HH:mm:ss')}.xlsx`;
        a.click();
      })
      .catch(() => this.setState({ eloading: false }));
  };

  handleRangeChange = (a: any, b: any) => {
    this.setState(
      {
        filters: { ...this.state.filters, begin: b[0], end: b[1] },
      },
      () => this.getData({ current: 1 })
    );
  };

  sKeyChange = (v: any) => {
    this.setState({
      sKey: v,
    });
  };

  sKeywordChange = (e: any) => {
    this.setState({
      sKeyword: e.target.value,
    });
  };

  doSearch = () => {
    const { begin, end } = this.state.filters;
    this.setState(
      {
        filters: {
          begin,
          end,
          [this.state.sKey]: this.state.sKeyword || '',
        },
      },
      () => this.getData({ current: 1 })
    );
  };

  typeChange = (e: any) => {
    this.setState(
      {
        type: e.target.value,
      },
      () => this.getData({ current: 1 })
    );
  };

  render() {
    const toFixed = (value: number) => {
      return value ? value.toFixed(2) : '0.00';
    }
    const { money, sKey, sKeyword } = this.state;
    const detailColumn = [
      {
        title: '时间',
        dataIndex: 'created_at',
        render: (text: number) => moment(text).format('YYYY-MM-DD HH:mm'),
        width: 150,
      },
      {
        title: '金额',
        dataIndex: 'money',
        render: (text: any, record: any) => `${['+', '-', '-'][record.type]} ${text ? text.toFixed(2) : '0.00'}`,
      },
      {
        title: '红包场景',
        dataIndex: 'redpacket_name',
      },
      {
        title: '动作',
        dataIndex: 'type',
        render: (text: number) => ['领取红包', '提现', '失效'][text],
      },
    ];
    return (
      <React.Fragment>
        <Row className="layout-infobar">
          <Col span={12}>
            <NavButton style={{ marginRight: 8, verticalAlign: 'top' }} url="/view/redPacketMgr">
              <Icon type="left-circle" />
              返回
            </NavButton>
            <Select value={sKey} onChange={this.sKeyChange} style={{ width: 160, marginRight: 8 }}>
              <Select.Option value="nick_name">搜索用户昵称</Select.Option>
              <Select.Option value="account_id">搜索用户长ID</Select.Option>
              <Select.Option value="phone_number">搜索用户手机号</Select.Option>
            </Select>
            <Input
              value={sKeyword}
              onChange={this.sKeywordChange}
              style={{ width: 200, marginRight: 8 }}
              placeholder="请输入关键词"
              onKeyPress={this.handleKey}
            />
            <Button type="primary" style={{ verticalAlign: 'top' }} onClick={this.doSearch}>
              <Icon type="search" />
              搜索
            </Button>
          </Col>
          <Col span={12} className="layout-breadcrumb">
            {getCrumb(this.props.breadCrumb)}
          </Col>
        </Row>
        <div className="component-content">
          <Table
            func="getUserMoneyList"
            index="money_detail_list"
            columns={this.getColumns()}
            filter={this.getFilter()}
            rowKey="id"
            pagination={true}
          />
          <Modal
            visible={this.state.visible === 'user'}
            key={this.state.key}
            title="用户详情"
            width={800}
            onCancel={() => this.setState({ visible: false })}
            onOk={() => this.setState({ visible: false })}
          >
            {/*{getUserDetail(this.state.detail)}*/}
            <UserDetail detail={this.state.detail}/>
          </Modal>
          <Modal
            visible={this.state.visible === 'money'}
            key={this.state.key + 1}
            title="金额详情"
            width={800}
            onCancel={() => this.setState({ visible: false })}
            onOk={this.exportUser}
            confirmLoading={this.state.eloading}
            okText="导出记录"
            cancelText="关闭"
          >
            <div style={{ marginBottom: 8 }}>
              累计现金奖励：{toFixed(money.accountFinance.receive_money)}&emsp;&emsp;已提现：
              {toFixed(money.accountFinance.cash_out_money)}&emsp;&emsp;已失效：{toFixed(money.accountFinance.invalid_money)}
              &emsp;&emsp;账户余额：{toFixed(money.accountFinance.total_money)}
            </div>
            <h3>金额记录</h3>
            <Divider type="horizontal" style={{ margin: '0 0 8px 0' }} />
            <ATable
              dataSource={money.finance_data_list.records}
              columns={detailColumn}
              loading={this.state.loading}
              pagination={{
                showQuickJumper: true,
                pageSize: 5,
                total: money.finance_data_list.total,
                showTotal: (total) => `共${total}条`,
                onChange: this.accountPageChange,
                size: 'small',
              }}
            />
          </Modal>
        </div>
      </React.Fragment>
    );
  }
}

export default RedPacketUserTotal;
