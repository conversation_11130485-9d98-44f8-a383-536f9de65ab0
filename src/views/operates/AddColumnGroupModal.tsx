import React from 'react';
import { Form, message, Modal, Input, Select } from 'antd';
import connect from '@utils/connectSession';
import { communityApi as api, opApi } from '@app/api';

@connect
@(Form.create({ name: 'AddColumnGroupModal' }) as any)
export default class AddColumnGroupModal extends React.Component<any, any> {
  state = {
    loading: false,
  };

  handleOkClick = () => {
    const {
      form: { validateFields },
      onEnd,
      record,
    } = this.props;
    validateFields((err: any, values: any) => {
      if (!err) {
        this.setState({ loading: true });
        const params = { ...values, topic_label_id: this.props.columnId };
        let request = api.addColumnGroup;
        if (record) {
          request = api.editColumnGroup;
          params.id = record.id;
        }
        request(params)
          .then(() => {
            this.setState({ loading: false });
            message.success(record ? '修改成功' : '添加成功');
            onEnd(false);
          })
          .catch(() => {
            this.setState({ loading: false });
          });
      }
    });
  };

  componentDidUpdate(prevProps: Readonly<any>, prevState: Readonly<any>, snapshot?: any): void {
    if (this.props.visible && !prevProps.visible) {
      this.getAssociatedChannel();
    }
  }

  getAssociatedChannel = () => {
    opApi.getChannelList({ type: 3, parent_id: this.props.topChannelId }).then((res: any) => {
      this.setState({ appNavList: res.data.list });
    });
  };

  render() {
    const {
      record,
      visible,
      onCancel,
      form: { getFieldDecorator },
    } = this.props;
    const dataSrc = record || {};
    const { name = '', channel_id } = dataSrc;
    const formLayout = {
      labelCol: { span: 5 },
      wrapperCol: { span: 16 },
    };
    return (
      <Modal
        visible={visible}
        key="AddColumnGroupModal"
        title={`${record ? '编辑' : '添加'}地方号分组`}
        confirmLoading={this.state.loading}
        width="500px"
        maskClosable={false}
        onCancel={onCancel}
        onOk={this.handleOkClick}
        destroyOnClose
      >
        <Form {...formLayout}>
          <Form.Item label="分组名称">
            {getFieldDecorator('name', {
              initialValue: name,
              rules: [
                {
                  required: true,
                  whitespace: true,
                  message: '请输入分组名称',
                },
              ],
            })(<Input maxLength={20} placeholder="请输入分组名称，最多20个字" />)}
          </Form.Item>

          <Form.Item label="关联频道">
            {getFieldDecorator('channel_id', {
              initialValue: channel_id,
              rules: [
                {
                  required: true,
                  message: '请选择需要关联的频道',
                },
              ],
            })(
              <Select allowClear={true} placeholder="请选择需要关联的频道">
                {this.state.appNavList?.map((v: any) => (
                  <Select.Option key={v.channel_id} value={`${v.channel_id}`}>
                    {v.channel_name}
                  </Select.Option>
                ))}
              </Select>
            )}
          </Form.Item>
        </Form>
      </Modal>
    );
  }
}
