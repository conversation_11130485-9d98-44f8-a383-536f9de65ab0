import { getTableList } from '@action/tableList';
import { opApi as api } from '@app/api';
import { CommonObject } from '@app/types';
import { A, Table, OrderColumn, Drawer, PreviewMCN } from '@components/common';
import connect from '@utils/connectTable';
import { getCrumb, requirePerm, setLoading, setMenu, searchToObject } from '@utils/utils';
import Form from '@app/components/business/longvideoUserForm';

import { Button, Col, Divider, Icon, Input, message, DatePicker, Row, Select, Modal } from 'antd';
import moment from 'moment';
import React from 'react';
import { withRouter } from 'react-router';
import { RangePickerValue } from 'antd/es/date-picker/interface';

@(withRouter as any)
@connect
class longVideoUser extends React.Component<any, any> {
  formRef: any;
  constructor(props: any) {
    super(props);
    this.state = {
      duration_id: props.match.params.id,
      cType: 1,
      cKeyword: '',
      filter: {
        search_type: 1,
        search_content: '',
      },
      listSelectedKeys: [],
      visible: false,
    };
  }

  componentDidMount() {
    this.getData({ current: 1, size: 10 });
    setMenu(this);
  }

  componentDidUpdate(prevProps: any) {
    if (this.props.tableList.timestamp !== prevProps.tableList.timestamp) {
      this.setState({ listSelectedKeys: [] });
    }
  }

  getData = (overlap: CommonObject = {}, filter = this.getFilter()) => {
    this.props.dispatch(
      getTableList('getLongVideoUserList', 'account_list', { ...filter, ...overlap })
    );
  };
  // 列表请求参数
  getFilter = () => {
    const { current, size } = this.props.tableList;
    const { duration_id } = this.state;
    const { filter } = this.state;
    const filters: CommonObject = { current, size, id: duration_id, ...filter };
    Object.keys(filter).forEach((k: string) => {
      if (!filters[k]) {
        delete filters[k];
      }
    });
    return filters;
  };

  getColumns = () => {
    const { current, size, total } = this.props.tableList;
    const { channel_id } = this.state;
    const getSeq = (i: number) => (current - 1) * size + i + 1;
    return [
      {
        title: '用户昵称',
        key: 'nick_name',
        dataIndex: 'nick_name',
        width: 100,
      },
      {
        title: '绑定手机号',
        key: 'phone',
        dataIndex: 'phone',
        width: 100,
      },
      {
        title: '用户长ID',
        key: 'account_id',
        dataIndex: 'account_id',
        width: 100,
      },
      {
        title: '更新时间',
        key: 'created_at',
        dataIndex: 'created_at',
        render: (text: number) => <span>{moment(text).format('YYYY-MM-DD HH:mm:ss')}</span>,
        width: 95,
      },
      {
        title: '操作',
        key: 'op',
        dataIndex: 'account_id',
        render: (text: any) => (
          <span>
            {requirePerm(
              this,
              `long_video_duration:delete_account`
            )(<A onClick={this.deleteRecord.bind(this, text)}>删除</A>)}
          </span>
        ),
        width: 100,
      },
    ];
  };

  // 新建权限分组
  addNews = () => {
    this.setState({
      visible: true,
    });
  };
  // 查看详细
  toDetail = () => {};
  // 删除权限分组
  deleteRecord = (id: number) => {
    Modal.confirm({
      title: '是否确定要删除该用户',
      onOk: () => {
        setLoading(this, true);
        api
          .deleteLongVideoUser({ account_id: id, duration_id: this.state.duration_id })
          .then(() => {
            message.success('操作成功');
            setLoading(this, false);
            this.getData();
          })
          .catch(() => setLoading(this, false));
      },
    });
  };
  // 关闭弹窗
  closeDrawer = () => {
    this.setState({
      visible: false,
    });
  };
  // 提交成功
  submitEnd = () => {
    this.closeDrawer();
    this.getData();
  };
  back = () => {
    this.props.history.go(-1);
  };
  submitForm = () => {
    this['formRef'].doSubmit();
  };
  setRef = (ref: 'formRef', instance: any) => {
    this[ref] = instance;
  };
  handleCTypeChange = (value: 1 | 2 | 3) => {
    this.setState({
      cType: value,
    });
  };
  handleKeywordChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    this.setState({
      cKeyword: e.target.value,
    });
  };
  doSearch = () => {
    this.setState(
      {
        filter: {
          search_type: this.state.cType,
          search_content: this.state.cKeyword,
        },
      },
      () => this.getData({ current: 1 })
    );
  };
  handleKey = (e: any) => {
    if (e.which === 13) {
      this.doSearch();
    }
  };
  render() {
    const { duration_id, cType, cKeyword } = this.state;
    return (
      <React.Fragment>
        <Row className="layout-infobar">
          <Col span={16}>
            <Button onClick={this.back} style={{ marginRight: 8 }}>
              <Icon type="left-circle-o" />
              返回
            </Button>

            {requirePerm(
              this,
              `long_video_duration:save_account`
            )(
              <Button onClick={this.addNews} style={{ marginRight: 8 }}>
                添加用户
              </Button>
            )}
          </Col>
          <Col span={8} className="layout-breadcrumb">
            {getCrumb(this.props.breadCrumb)}
          </Col>
        </Row>

        <div className="component-content">
          <Row style={{ marginBottom: 16 }}>
            <Col span={12}></Col>
            <Col span={12} style={{ textAlign: 'right' }}>
              <Select
                value={cType}
                onChange={this.handleCTypeChange}
                style={{ marginRight: 8, width: 120 }}
              >
                <Select.Option value={1}>昵称</Select.Option>
                <Select.Option value={2}>手机号</Select.Option>
                <Select.Option value={3}>用户长ID</Select.Option>
              </Select>
              <Input
                value={cKeyword}
                onChange={this.handleKeywordChange}
                style={{ width: 180, marginRight: 8 }}
                placeholder="请输入搜索内容"
                onKeyPress={this.handleKey}
              />
              <Button onClick={this.doSearch}>
                <Icon type="search" />
                搜索
              </Button>
            </Col>
          </Row>
          <Table
            func="getLongVideoUserList"
            index="account_list"
            filter={this.getFilter()}
            columns={this.getColumns()}
            rowKey="id"
            pagination={true}
          />
        </div>
        <Drawer
          visible={this.state.visible}
          skey={this.state.key}
          title="添加用户"
          onClose={this.closeDrawer}
          onOk={this.submitForm.bind('form')}
        >
          <Form
            id={duration_id}
            wrappedComponentRef={this.setRef.bind(this, 'formRef')}
            onEnd={this.submitEnd}
          />
        </Drawer>
      </React.Fragment>
    );
  }
}

export default longVideoUser;
