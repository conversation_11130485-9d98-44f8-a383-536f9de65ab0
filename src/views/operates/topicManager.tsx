import { opApi as api } from '@app/api';
import { ITableProps, IBaseProps, CommonObject, IAllProps } from '@app/types';
import { A, Table, BaseComponent, Drawer } from '@components/common';
import UGCForm from '@components/business/ugcTopicForm';
// import TopicForm from '@components/business/topicForm';
import { connectTable as connect } from '@utils/connect';
import {
  Button,
  Col,
  Icon,
  message,
  Modal,
  Row,
  Menu,
  Dropdown,
  Input,
  Select,
  DatePicker,
  Tooltip,
} from 'antd';
import moment from 'moment';
import React from 'react';
import ReactClipboard from 'react-clipboardjs-copy';
import { withRouter } from 'react-router';
import { RangePickerValue } from 'antd/es/date-picker/interface';
import { TopicRecord, TopicAllData } from './operates';
import { setTableCache } from '@action/tableCache';
import { setTableList } from '@action/tableList';

type State = {
  filter: {
    begin: string;
    end: string;
    class_id: number;
    search_type: 1 | 2;
    keyword: string;
    type: string;
    from_source: string;
    enabled: string;
  };
  cType: 1 | 2;
  cKeyword: string;
  onCount: number;
  form: {
    visible: boolean;
    key: number;
    name: string;
    logo_url: string;
    class_ids: number[];
    show_style: 0 | 1 | 2;
    background_url: string;
    id?: number;
    description?: string;
    rank_status?: 1 | 0;
    rank_desc?: string;
    rank_logo?: string;
    type: number;
    show_type: 1 | 2 | 3;
    enable_recent_hot: boolean;
    hot_sort_by: number;
    created_user_name: any;
    account_id: any;
  };
  classList: CommonObject[];
};

type Props = IBaseProps<IAllProps<TopicRecord, TopicAllData>>;

class TopicManager extends BaseComponent<Props, State> {
  constructor(props: Props) {
    super(props);
    this.state = {
      filter: {
        begin: '',
        end: '',
        class_id: 0,
        search_type: 1,
        keyword: '',
        type: '',
        from_source: '',
        enabled: '',
      },
      cType: 1,
      cKeyword: '',
      onCount: 0,
      form: {
        visible: false,
        key: Date.now(),
        name: '',
        logo_url: '',
        class_ids: [],
        show_style: 0,
        background_url: '',
        type: 2,
        show_type: 1,
        enable_recent_hot: false,
        hot_sort_by: 2,
        created_user_name: undefined,
        account_id: undefined,
      },
      classList: [],
    };
  }

  componentDidMount() {
    this.setMenu();
    this.getTopicClass();
    if (
      this.props.tableCache?.beforeRoute === this.props.match?.path &&
      this.props.tableCache.records.length > 0
    ) {
      this.props.dispatch(setTableList(this.props.tableCache));

      if (this.props.tableCache?.filters) {
        this.setState({
          filter: this.props.tableCache?.filters,
          cType: this.props.tableCache?.filters?.search_type,
          cKeyword: decodeURIComponent(this.props.tableCache?.filters?.keyword ?? ''),
        });
      }
    } else {
      this.getData({ current: 1, size: 10 });
    }
  }

  componentDidUpdate(prevProps: Props) {
    if (
      prevProps.tableList.timestamp !== this.props.tableList.timestamp &&
      Boolean(this.props.tableList.allData.list)
    ) {
      this.setState({
        onCount: this.props.tableList.allData.on_show_count,
      });
    }
  }

  getTopicClass = () => {
    this.setLoading(true);
    api
      .getTopicClassList()
      .then((res: any) => {
        // TODO 不分页列表修改
        this.setState({
          classList: res.data.list,
        });
        this.setLoading(false);
      })
      .catch(() => {
        this.setLoading(false);
      });
  };

  getData = (overlap: CommonObject = {}, filters = this.getFilters()) => {
    this.dispatchTable('getTopicList', 'list', { ...filters, ...overlap });
  };

  getFilters = () => {
    const { current, size } = this.props.tableList;
    const { filter } = this.state;
    const filters: CommonObject = { current, size, ...filter };
    Object.keys(filter).forEach((k: string) => {
      if (!filters[k]) {
        delete filters[k];
      }
    });
    return filters;
  };

  copySuccess = () => {
    message.success('链接已复制');
  };

  copyFail = () => {
    message.error('复制失败');
  };

  getColumns = () => {
    const { current, size } = this.props.tableList;
    const { onCount } = this.state;
    const { begin, keyword, class_id, type, from_source } = this.state.filter;
    const disableSort = begin || keyword || class_id || type || from_source;
    const getSeq = (i: number) => (current - 1) * size + i + 1;
    const getDropdown = (record: any) => {
      const menu = (
        <Menu>
          {this.requirePerm(`topic_label:update`)(
            <Menu.Item onClick={this.editRecord.bind(this, record)}>编辑</Menu.Item>
          )}
          {this.requirePerm(`topic_label:change_enable`)(
            <Menu.Item onClick={this.updateStatus.bind(this, record)}>
              {record.enabled ? '下线' : '上线'}
            </Menu.Item>
          )}
          {this.requirePerm(`topic_label:delete`)(
            <Menu.Item onClick={this.deleteRecord.bind(this, record)}>删除</Menu.Item>
          )}
          <Menu.Item disabled={record.url === ''}>
            {record.url === '' ? (
              '复制话题链接'
            ) : (
              <ReactClipboard
                action="copy"
                text={record.url}
                onSuccess={this.copySuccess}
                onError={this.copyFail}
              >
                <a>复制话题链接</a>
              </ReactClipboard>
            )}
          </Menu.Item>
        </Menu>
      );
      return (
        <Dropdown overlay={menu}>
          <a className="ant-dropdown-link">
            操作 <Icon type="down" />
          </a>
        </Dropdown>
      );
    };
    const column1 = [
      // {
      //   title: '排序',
      //   key: 'order',
      //   render: (text: any, record: any, i: number) => (
      //     <span>
      //       {this.requirePerm(`topic_label:order`)(
      //         <A
      //           disabled={getSeq(i) === 1 || !record.enabled || disableSort}
      //           className="sort-up"
      //           onClick={this.exchangeOrder.bind(this, record.id, getSeq(i), -1)}
      //         >
      //           <Icon type="up-circle" theme="filled" />
      //         </A>
      //       )}{' '}
      //       {this.requirePerm(`topic_label:order`)(
      //         <A
      //           disabled={getSeq(i) >= onCount || !record.enabled || disableSort}
      //           className="sort-down"
      //           onClick={this.exchangeOrder.bind(this, record.id, getSeq(i), 1)}
      //         >
      //           <Icon type="down-circle" theme="filled" />
      //         </A>
      //       )}
      //     </span>
      //   ),
      //   width: 70,
      // },
      {
        title: '序号',
        key: 'seq',
        render: (text: any, record: any, i: number) => <span>{getSeq(i)}</span>,
        width: 70,
      },
      {
        title: '话题名称',
        key: 'title',
        dataIndex: 'name',
        render: (text: any, record: any) => (
          <A onClick={this.toArticleList.bind(this, record.id, text, record.type)}>{text}</A>
        ),
      },
      // {
      //   title: '话题类型',
      //   key: 'type',
      //   dataIndex: 'type',
      //   render: (text: any, record: any) => <span>{record.type === 1 ? '视频话题' : '普通话题'}</span>,
      //   width: 100,
      // },
    ];
    const column2 = [
      {
        title: (
          <span>
            内容数
            <Tooltip
              title="话题下已过审的全部内容数（包括沉底）；前台话题页不显示沉底内容"
              overlayStyle={{ maxWidth: 294 }}
            >
              <Icon type="question-circle-o" />
            </Tooltip>
          </span>
        ),
        key: 'article_count',
        dataIndex: 'article_count',
        width: 110,
      },
      {
        title: '沉底内容数',
        dataIndex: 'article_down_count',
        width: 110,
      },
      // {
      //   title: '分类',
      //   key: 'class_names',
      //   dataIndex: 'class_names',
      //   width: 150,
      //   render: (text: any) => <span>{text ? text.join('，') : ''}</span>,
      // },
    ];
    const column3 = [
      {
        title: '状态',
        key: 'enabled',
        dataIndex: 'enabled',
        render: (text: boolean) => <span>{text ? '展示中' : '待展示'}</span>,
        width: 110,
      },
      {
        title: '创建人',
        key: 'created_user_name',
        dataIndex: 'created_user_name',
        width: 110,
      },
      {
        title: '创建时间',
        key: 'created_at',
        dataIndex: 'created_at',
        render: (text: number) => <span>{moment(text).format('YYYY-MM-DD HH:mm:ss')}</span>,
        width: 95,
      },
      {
        title: '操作',
        key: 'op',
        render: (text: any, record: any) => <span>{getDropdown(record)}</span>,
        width: 70,
      },
    ];
    return [...column1, ...column2, ...column3];
  };

  editRecord = (record: any = {}) => {
    this.setState({
      form: {
        visible: true,
        key: Date.now(),
        name: record.name || '',
        logo_url: record.logo_url || '',
        class_ids: record.class_ids
          ? record.class_ids.split(',').map((v: any) => v.toString())
          : [],
        show_style: record.show_style || 0,
        id: record.id || '',
        description: record.description || '',
        background_url: record.background_url || '',
        rank_desc: record.rank_desc || '',
        rank_status: record.rank_status || 0,
        rank_logo: record.rank_logo || '',
        type: record.type || 2,
        show_type: record.show_type || 1,
        enable_recent_hot: record.enable_recent_hot,
        hot_sort_by: record.hot_sort_by,
        created_user_name: record.created_user_name,
        account_id: record.account_id,
      },
    });
  };

  updateStatus = (record: any) => {
    this.setLoading(true);
    api
      .updateTopicStatus({
        id: record.id,
        enable: record.enabled ? 0 : 1,
      })
      .then(() => {
        message.success('操作成功');
        this.getData();
        this.setLoading(false);
      })
      .catch(() => {
        this.setLoading(false);
      });
  };

  deleteRecord = (record: any) => {
    Modal.confirm({
      title: '确定删除吗？',
      onOk: () => {
        this.setLoading(true);
        api
          .deleteTopic({
            id: record.id,
          })
          .then(() => {
            message.success('操作成功');
            this.getData();
            this.setLoading(false);
          })
          .catch(() => {
            this.setLoading(false);
          });
      },
    });
  };

  exchangeOrder = (id: number, current: number, offset: number) => {
    this.setLoading(true);
    api
      .sortTopic({ id, current, offset })
      .then(() => {
        message.success('操作成功');
        this.getData();
        this.setLoading(false);
      })
      .catch(() => {
        this.setLoading(false);
      });
  };

  toArticleList = (id: number, name: string, type: number) => {
    this.props.dispatch(
      setTableCache({
        beforeRoute: this.props.match.path,
        ...this.props.tableList,
        filters: this.state.filter,
      })
    );
    this.props.history.push(`/view/ugcTopicArticle/${id}/${encodeURIComponent(name)}/${type}`);
  };

  handleRangePickerChange = (dates: RangePickerValue) => {
    if (dates.length === 0) {
      this.setState(
        {
          filter: { ...this.state.filter, begin: '', end: '' },
        },
        () => this.getData({ current: 1 })
      );
    } else {
      this.setState(
        {
          filter: {
            ...this.state.filter,
            begin: dates[0] ? dates[0].format('YYYY-MM-DD') : '',
            end: dates[1] ? dates[1].format('YYYY-MM-DD') : '',
          },
        },
        () => this.getData({ current: 1 })
      );
    }
  };

  handleClassIdChange = (value: number) => {
    this.setState(
      {
        filter: { ...this.state.filter, class_id: value },
      },
      () => this.getData({ current: 1 })
    );
  };

  handleTypeChange = (value: string) => {
    this.setState(
      {
        filter: { ...this.state.filter, type: value },
      },
      () => this.getData({ current: 1 })
    );
  };

  handleSourceChange = (value: string) => {
    this.setState(
      {
        filter: { ...this.state.filter, from_source: value },
      },
      () => this.getData({ current: 1 })
    );
  };

  handleEnableChange = (value: string) => {
    this.setState(
      {
        filter: { ...this.state.filter, enabled: value },
      },
      () => this.getData({ current: 1 })
    );
  };

  handleCTypeChange = (value: 1 | 2) => {
    this.setState({
      cType: value,
    });
  };

  handleKeywordChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    this.setState({
      cKeyword: e.target.value,
    });
  };

  doSearch = () => {
    this.setState(
      {
        filter: {
          ...this.state.filter,
          search_type: this.state.cType,
          keyword: this.state.cKeyword,
        },
      },
      () => this.getData({ current: 1 })
    );
  };

  closeDrawer = () => {
    this.setState({
      form: { ...this.state.form, visible: false },
    });
  };

  submitEnd = () => {
    this.closeDrawer();
    this.getData();
  };

  toAudit = () => {
    this.props.history.push('/view/auditTopic');
  };

  render() {
    const { form, filter, cType, cKeyword, onCount, classList } = this.state;
    return (
      <>
        <Row className="layout-infobar">
          <Col span={12}>
            {this.requirePerm('topic_label:create')(
              <Button onClick={() => this.editRecord()} style={{ marginRight: 8 }}>
                <Icon type="plus-circle-o" />
                添加话题
              </Button>
            )}
            {this.requirePerm('ugc_topic_audit:pass')(
              <Button onClick={this.toAudit} style={{ marginRight: 8 }}>
                <Icon type="edit" />
                审核话题
              </Button>
            )}
            <Button
              onClick={() => {
                this.props.history.push('/view/ugcTopicRecommend');
              }}
            >
              管理推荐话题
            </Button>
          </Col>
          <Col span={12} className="layout-breadcrumb">
            {this.getCrumb()}
          </Col>
        </Row>
        <div className="component-content">
          <Row style={{ marginBottom: 16 }}>
            <Col span={16}>
              <DatePicker.RangePicker
                format="YYYY-MM-DD"
                onChange={this.handleRangePickerChange}
                value={
                  filter.begin
                    ? [moment(this.state.filter.begin), moment(this.state.filter.end)]
                    : []
                }
              />

              {/* <Select
                value={filter.class_id}
                onChange={this.handleClassIdChange}
                style={{ marginLeft: 8, width: 140 }}
              >
                <Select.Option value={0}>全部分类</Select.Option>
                {classList.map((v: any, i: number) => (
                  <Select.Option value={v.id} key={i}>
                    {v.name}
                  </Select.Option>
                ))}
              </Select> */}
              {/* <Select
                value={filter.type}
                onChange={this.handleTypeChange}
                style={{ marginLeft: 8, width: 140 }}
              >
                <Select.Option value="">全部类型</Select.Option>
                <Select.Option value="2">普通话题</Select.Option>
                <Select.Option value="1">视频话题</Select.Option>
              </Select> */}
              <Select
                value={filter.from_source}
                onChange={this.handleSourceChange}
                style={{ marginLeft: 8, width: 140 }}
              >
                <Select.Option value="">话题来源</Select.Option>
                <Select.Option value="0">后台创建</Select.Option>
                <Select.Option value="1">用户创建</Select.Option>
              </Select>
              <Select
                value={filter.enabled}
                onChange={this.handleEnableChange}
                style={{ marginLeft: 8, width: 140 }}
              >
                <Select.Option value="">话题状态</Select.Option>
                <Select.Option value="1">展示中</Select.Option>
                <Select.Option value="0">待展示</Select.Option>
              </Select>
            </Col>
            <Col span={8} style={{ textAlign: 'right' }}>
              <Select
                value={cType}
                onChange={this.handleCTypeChange}
                style={{ marginRight: 8, width: 120 }}
              >
                <Select.Option value={1}>话题名称</Select.Option>
                <Select.Option value={2}>创建人</Select.Option>
              </Select>
              <Input
                value={cKeyword}
                onChange={this.handleKeywordChange}
                style={{ width: 180, marginRight: 8 }}
                onKeyPress={this.handleKey}
                placeholder="请输入搜索内容"
              />
              <Button onClick={this.doSearch}>
                <Icon type="search" />
                搜索
              </Button>
            </Col>
          </Row>
          <Table
            func="getTopicList"
            index="list"
            columns={this.getColumns()}
            pagination={true}
            rowKey="id"
            filter={this.getFilters()}
          />
          <Drawer
            visible={form.visible}
            skey={form.key}
            title={`${form.id ? '编辑' : '添加'}UGC话题`}
            onClose={this.closeDrawer}
            onOk={this.handleSubmitForm.bind(this, 'ugcform')}
          >
            <UGCForm
              formContent={form}
              wrappedComponentRef={this.setFormRef.bind(this, 'ugcform')}
              classList={this.state.classList}
              onEnd={this.submitEnd}
            />
          </Drawer>
          {/* <Drawer
            visible={filter.type === 0 && form.visible}
            skey={form.key + 1}
            title={`${form.id ? '编辑' : '添加'}稿件话题`}
            onClose={this.closeDrawer}
            onOk={this.handleSubmitForm.bind(this, 'topicform')}
          >
            <TopicForm
              formContent={form}
              wrappedComponentRef={this.setFormRef.bind(this, 'topicform')}
              classList={this.state.classList}
              onEnd={this.submitEnd}
            />
          </Drawer> */}
        </div>
      </>
    );
  }
}

export default withRouter(connect<TopicRecord, TopicAllData>()(TopicManager));
