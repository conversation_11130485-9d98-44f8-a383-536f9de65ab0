import NewNewsSearchAndInput from '@app/components/common/newNewsSearchAndInput';
import { Button, Form, Icon, Input, InputNumber, Modal, Radio, Spin, Tooltip, message } from 'antd';
import React, { forwardRef, useImperativeHandle, useState } from 'react';

import { opApi } from '@app/api';
import ReactWEditor from 'wangeditor-for-react/lib/core';
import '@components/business/styles/business.scss';
import { setMLoading } from '@app/utils/utils';
import { useDispatch } from 'react-redux';
import { setConfig } from '@app/action/config';
import { ImageUploader } from '@app/components/common';
import '@components/business/styles/business.scss'
import Text from 'antd/lib/typography/Text';

const AnswerFormItem = forwardRef((props: any, ref: any) => {
  const [answer, setAnswer] = useState(props.value.answer_type == 1 ? props.value.answer : '');
  const [articls, setArticles] = useState(props.value.articles || []);
  const category = props.category ?? 0;

  const handleTypeChange = (e: any) => {
    const aType = e.target.value;
    let v: any = {
      answer_type: aType,
    };

    if (aType == 1) {
      v.answer = answer;
    } else if (aType == 2) {
      v.answer = articls?.map((e: any) => e.uuid || e.id)?.join(',') || '';
    } else {
      v.answer = '';
    }

    triggerChange(v);
  };

  const handleArticleChange = (e: any) => {
    const list = e || [];
    setArticles(list);
    triggerChange({ answer: list?.map((e: any) => e.uuid || e.id)?.join(',') || '' });
  };

  const triggerChange = (changedValue: any) => {
    const { onChange, value } = props;
    if (onChange) {
      onChange({
        ...value,
        ...changedValue,
      });
    }
  };

  const column = [
    {
      title: 'ID',
      dataIndex: 'id',
      width: 80,
    },
    {
      title: '新闻频道',
      key: 'type',
      dataIndex: 'channel_name',
      width: 90,
    },
    {
      title: '新闻标题',
      key: 'list_title',
      dataIndex: 'list_title',
      render: (text: any, record: any) => {
        return text || '-';
      },
    },
  ];

  return (
    <div>
      <Radio.Group defaultValue={props.value.answer_type} onChange={handleTypeChange}>
        <Radio value={1}>文本</Radio>
        <Radio value={2}>
          关联稿件&nbsp;
          <Tooltip title={'可关联天目蓝云稿件和UGC内容'}>
            <Icon type="question-circle" />
          </Tooltip>
        </Radio>
        <Radio value={3}>由大模型返回</Radio>
        {/* <Radio value={4}>互动组件</Radio> */}
        {category !== 3 && (
          <>
            {/* <Radio value={5}>热点新闻</Radio> */}
            <Radio value={6}>近期活动</Radio>
            {/* <Radio value={7}>浙里办服务</Radio>
            <Radio value={8}>潮小帮智能体</Radio>
            <Radio value={9}>潮小拍智能体</Radio>
            <Radio value={10}>公文智搜智能体</Radio>
            <Radio value={11}>AI标题</Radio>
            <Radio value={12}>AI封面图</Radio>
            <Radio value={13}>AI配文</Radio> */}
          </>
        )}
      </Radio.Group>
      {props.value.answer_type == 1 && (
        <ReactWEditor
          className={`ugc_topic_form_weditor gpt_weditor`}
          defaultValue={answer}
          placeholder="答案最多输入1000字"
          onChange={(e) => {
            setAnswer(e);
            triggerChange({ answer: e });
          }}
          instanceHook={{
            // 使用方法是，通常 key 代表的钩子是一个对象，可以利用方法来绑定。方法的形参第一位是当前实例的 editor，后面依次是 key 分割代表的对象。
            'config.menus': function (editor, config: any, menus) {
              config.height = 100;
              config.menus = ['link'];
              config.showFullScreen = false;
              config.linkCheck = function (text: string, link: string) {
                if (text === link) {
                  message.error('请输入链接文字');
                  return;
                }
                return true;
              };
            },
          }}
        />
      )}
      {props.value.answer_type == 2 && (
        <NewNewsSearchAndInput
          max={10}
          func="listArticleRecommendSearch"
          columns={column}
          placeholder="输入ID或标题关联稿件"
          body={{ doc_types: '2,3,4,5,8,9,10,11,12,13' }}
          order={true}
          addOnTop={true}
          onChange={handleArticleChange}
          value={articls}
        />
      )}

      {props.value.answer_type == 5 && (
        <Text type="warning">近24小时热榜的前3条内容。</Text>
      )}

      {props.value.answer_type == 6 && (
        <Text type="warning">活动中心进行中的前3条活动。</Text>
      )}

      {props.value.answer_type == 7 && (
        <Text type="warning">推荐浙里办服务的前4个。</Text>
      )}
    </div>
  );
});

const AssistantForm = (props: any, ref: any) => {
  const dispatch = useDispatch();
  const [content, setContent] = useState({
    type: 2,
    question_type: 1,
    question: '',
    answer_type: 1,
    answer: '',
    articles: [],
    pic_url: '',
    url: '',
    ref_tip: '',
    ...props.formContent,
  });

  const { getFieldDecorator, getFieldValue } = props.form;
  useImperativeHandle(
    ref,
    () => ({
      ...props.form,
      doSubmit,
    }),
    []
  );

  const doSubmit = () => {
    props.form.validateFieldsAndScroll((err: any, values: any) => {
      if (!err) {
        const body = {
          type: values.type,
          question: values.question,
          question_keyword: values.question_keyword,
          ...values.answerObj,
          ...values.questionObj,
          question_type: 1
        };

        delete body.articles;
        if (!!props.formContent) {
          body.id = props.formContent.id;
        }

        if (!!props.category) {
          body.category = props.category;
        }

        if (!!props.channel_id) {
          body.channel_id = props.channel_id;
        }

        if (body.answer_type == 3) {
          body.answer = '';
        }

        if (body.question_type == 1 && body.type == 1) {
          body.pic_url = values.pic_url;
        }

        if (body.question_type == 1 && body.answer_type == 4) {
          body.answer = '';
          body.url = values.url
          body.height = values.height
          body.ref_id = values.ref_id || ''
          body.ref_id1 = values.ref_id1 || ''
          body.ref_id2 = values.ref_id2 || ''
          body.ref_tip = values.ref_tip || ''
        }

        dispatch(setConfig({ mLoading: true }));
        const api = (!!props.formContent) ? opApi.questionUpdate : opApi.questionCreate;
        api(body)
          .then(() => {
            dispatch(setConfig({ mLoading: false }));
            message.success('操作成功');
            props.onEnd(values.type);
          })
          .catch(() => {
            dispatch(setConfig({ mLoading: false }));
          });
      } else {
        message.error('请检查表单内容');
      }
    });
  };

  const column = [
    {
      title: 'ID',
      dataIndex: 'id',
      width: 80,
    },
    {
      title: '新闻频道',
      key: 'type',
      dataIndex: 'channel_name',
      width: 90,
    },
    {
      title: '新闻标题',
      key: 'list_title',
      dataIndex: 'list_title',
    },
  ];

  const formLayout = {
    labelCol: { span: 4 },
    wrapperCol: { span: 18 },
  };


  const questionTriggerChange = (changedValue: any) => {
    const { onChange, value } = props;
    if (onChange) {
      onChange({
        ...value,
        ...changedValue,
      });
    }
  };

  const questionKeyWordTriggerChange = (changedValue: any) => {
    const { onChange, value } = props;
    if (onChange) {
      onChange({
        ...value,
        ...changedValue,
      });
    }
  };


  const urlValidator = (rule: any, value: any, callback: any) => {
    const regex = /^https?:\/\//;
    if (value === '') {
      callback('请填写链接');
      return;
    }
    if (!regex.test(value)) {
      callback('请正确填写链接格式');
      return;
    }

    callback();
  };

  return (
    <Form {...formLayout}>
      <Form.Item label="应用场景">
        {getFieldDecorator('type', {
          initialValue: content.type,
          rules: [
            {
              required: true,
              message: '请选择应用场景',
            },
          ],
        })(
          <Radio.Group>
            <Radio value={0}>
              功能引导&nbsp;
              <Tooltip title={'显示在欢迎语中引导用户使用'}>
                <Icon type="question-circle" />
              </Tooltip>
            </Radio>
            <Radio value={1}>
              快捷输入&nbsp;
              <Tooltip title={'显示输入框上方'}>
                <Icon type="question-circle" />
              </Tooltip>
            </Radio>
            <Radio value={2}>问答</Radio>
          </Radio.Group>
        )}
      </Form.Item>

      <Form.Item label="问题">
        {getFieldDecorator('question', {
          initialValue: content.question || undefined,
          rules: [
            {
              required: true,
              message: '请输入问题',
            }, {
              max: 20,
              message: '最多20字',
            }
          ],
        })(
          <Input
            placeholder={'最多输入20字'}
          ></Input>
        )}
      </Form.Item>

      <Form.Item label="问题关键词">
        {getFieldDecorator('question_keyword', {
          initialValue: content.question_keyword || undefined,
          rules: [
            {
              required: true,
              message: '请输入问题关键词',
            }, {
              max: 40,
              message: '最多40字',
            }
          ],
        })(
          <Input
            placeholder={'多个词用英文逗号隔开，最多输入40字'}
          ></Input>
        )}
      </Form.Item>

      {(content.question_type == 1 && getFieldValue('type') == 1) && (
        <Form.Item
          label="图片"
          extra="支持扩展名：.jpg .jpeg .png .gif,比例不限制，建议高度84px，宽度300px"
        >
          {getFieldDecorator('pic_url', {
            initialValue: content.pic_url,
            preserve: true,
          })(<ImageUploader isCutting={true} accept={['image/png', 'image/jpeg', 'image/jpg', 'image/gif']} />)}
        </Form.Item>
      )}

      {content.question_type == 1 && (
        <Form.Item label="答案" style={{ display: 'flex', alignItems: 'flex-start' }}>
          {getFieldDecorator('answerObj', {
            initialValue: {
              answer_type: content.answer_type,
              answer: content.answer,
              articles: content.articles,
            },
            rules: [
              {
                required: true,
                // message: '请选择问题类型',
                validator: (rule: any, value: any, callback: any) => {
                  if (value.answer_type == 1) {
                    if (!value?.answer) {
                      callback('请输入答案');
                      return;
                    }

                    // 创建一个新的DOMParser实例
                    const parser = new DOMParser();
                    // 使用DOMParser解析字符串
                    const xmlDoc = parser.parseFromString(value.answer, 'text/html');
                    const text: any = xmlDoc?.documentElement?.textContent;
                    if (text?.length > 1000) {
                      callback('答案不能超过1000字');
                      return;
                    }
                    if (text?.trim()?.length <= 0) {
                      callback('请输入答案');
                      return;
                    }
                  }
                  if (value.answer_type == 2) {
                    if (!value?.answer) {
                      callback('请关联稿件');
                      return;
                    }
                  }
                  callback();
                },
              },
            ],
          })(
            <AnswerFormItem
              category={props.category}
              onChange={(e: any) => {
                setContent({
                  ...content,
                  ...e,
                });
              }}
            ></AnswerFormItem>
          )}
        </Form.Item>
      )
      }

      {
        (content.question_type == 1 && content.answer_type == 4) && (
          <>
            <Form.Item label="组件链接">
              {getFieldDecorator('url', {
                initialValue: content.url || undefined,
                rules: [
                  {
                    required: true,
                    message: '请输入组件链接',
                  },
                  {
                    validator: urlValidator,
                  },
                ],
              })(
                <Input
                  placeholder="请输入活动H5链接"
                // max={10000}
                />
              )}
            </Form.Item>

            <Form.Item label="组件高度">
              {getFieldDecorator('height', {
                initialValue: content.height || undefined,
                rules: [
                  {
                    required: true,
                    message: '请输入组件高度',
                  },
                ],
              })(
                <InputNumber
                  style={{ width: '100%' }}
                  placeholder="请输入高度（单位为dp）"
                  min={0}
                  precision={0}
                // max={10000}
                />
              )}
            </Form.Item>

            <Form.Item label="引导语">
              {getFieldDecorator('ref_tip', {
                initialValue: content.ref_tip || undefined,
                rules: [
                  {
                    max: 20,
                    message: '最多20字',
                  },
                ],
              })(
                <Input
                  placeholder="请输入引导语，最多20字"
                // max={10000}
                />
              )}
            </Form.Item>

            <Form.Item label="相关问题">
              {getFieldDecorator('ref_id', {
                initialValue: content.ref_id || undefined,
                rules: [
                  {
                    max: 20,
                    message: '最多20字',
                  },
                ],
              })(
                <Input
                  placeholder="请输入相关问题，最多20字"
                // max={10000}
                />
              )}
            </Form.Item>
            <Form.Item label=" " colon={false}>
              {getFieldDecorator('ref_id1', {
                initialValue: content.ref_id1 || undefined,
                rules: [
                  {
                    max: 20,
                    message: '最多20字',
                  },
                ],
              })(
                <Input
                  placeholder="请输入相关问题，最多20字"
                // max={10000}
                />
              )}
            </Form.Item>
            <Form.Item label=" " colon={false}>
              {getFieldDecorator('ref_id2', {
                initialValue: content.ref_id2 || undefined,
                rules: [
                  {
                    max: 20,
                    message: '最多20字',
                  },
                ],
              })(
                <Input
                  placeholder="请输入相关问题，最多20字"
                // max={10000}
                />
              )}
            </Form.Item>
          </>
        )
      }
    </Form >
  );
};

export default Form.create<any>({ name: 'ContentForm' })(forwardRef<any, any>(AssistantForm));
