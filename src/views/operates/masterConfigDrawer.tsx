import {
  Form,
  Icon,
  Input,
  Radio,
  Select,
  Switch,
  Tooltip,
  message,
} from 'antd';
import React, { forwardRef, useEffect, useImperativeHandle, useState } from 'react';
import _, { set } from 'lodash';

import { communityApi, opApi as api } from '@app/api';
import { Drawer, FileUploader, ImageUploader } from '@app/components/common';
import { useDispatch } from 'react-redux';
import { setConfig } from '@app/action/config';



// 数字圈主功能配置
const MasterConfigDrawer = (props: any, ref: any) => {
  const dispatch = useDispatch();
  const channel_id = props.channel_id ?? "";
  const [selectedUser, setSelectedUser] = useState([]);

  const { getFieldDecorator, getFieldValue, setFieldsValue } = props.form;
  useImperativeHandle(
    ref,
    () => ({
      ...props.form,
    }),
    []
  );

  const formLayout = {
    labelCol: { span: 4 },
    wrapperCol: { span: 18 },
  };

  const [detailData, setDetailData] = useState({
    id: 0,
    status: 0,
    model_code: "",
    float_win_image_url: "",
    gpt_image_url: "",
    name_image_url: "",
    common_image_url: "",
    image_com_url: "",
    name: "",
    short_desc: "",
    account_id: ""
  });

  useEffect(() => {
    if (props.visible) {
      gptQuestionConfigDetail('');
    }
  }, [props.visible]);


  const gptQuestionConfigDetail = _.debounce((val: any) => {
    api.gptQuestionConfigDetail({ channel_id: channel_id })
      .then((res: any) => {
        const detail = res.data.detail;
        if (!!detail) {
          setDetailData(detail);
          setFieldsValue({ ...detail });
          if (!!detail.nick_name) {
            setSelectedUser([{ id: detail.account_id, nick_name: detail.nick_name, chao_id: detail.chao_id, nick_ncert_typeame: detail.cert_type }]);
            handleAccountSearch(detail.chao_id);
          }
        }
      })
      .catch(() => { });
  }, 500);

  const handleSubmit = () => {
    // e.preventDefault();
    props.form.validateFieldsAndScroll((err: any, values: any) => {
      if (!err) {
        dispatch(setConfig({ mLoading: true }));
        let parmas: any = { channel_id: channel_id }
        if (values.status) {
          parmas.status = 1
        } else {
          parmas.status = 0
        }

        parmas.model_code = values.model_code
        parmas.name = values.name
        parmas.float_win_image_url = values.float_win_image_url
        parmas.gpt_image_url = values.gpt_image_url
        parmas.name_image_url = values.name_image_url
        parmas.common_image_url = values.common_image_url
        parmas.image_com_url = values.image_com_url
        parmas.short_desc = values.short_desc

        if (values.accountIds.length > 0) {
          parmas.account_id = values.accountIds.join(',')
        } else {
          dispatch(setConfig({ mLoading: false }));
          message.error('请检查表单内容');
          return
        }

        console.log('v +++++++3333333 ' + JSON.stringify(parmas));
        api.gptQuestionConfigSave(parmas)
          .then((res: any) => {
            message.success('保存成功');
            dispatch(setConfig({ mLoading: false }));
            props.onEnd && props.onEnd();
          })
          .catch((e) => {
            message.error('添加失败');
            console.error(e);
            dispatch(setConfig({ mLoading: false }));
          });
      } else {
        message.error('请检查表单内容');
        dispatch(setConfig({ mLoading: false }));
      }
    });
  };

  const [optionData, setOptionData] = useState([]);

  const handleAccountSearch = _.debounce((val: any) => {
    if (!val) {
      setOptionData([]);
      return;
    }
    // type: 1, current: 1, size: 50, biz_type: props.formContent?.biz_type,
    communityApi
      .recommendAccount_Search({ keyword: val })
      .then((res) => {
        setOptionData(res?.data?.list || []);
      })
      .catch(() => { });
  }, 500);

  return (
    <Drawer
      title={
        <>
          <span>数字圈主功能配置</span>
        </>
      }
      visible={props.visible}
      skey={props.skey}
      onClose={props.onClose}
      maskClosable={false}
      // width={500}
      onOk={handleSubmit}
      okPerm='gpt_question:dp_config_save'
      okText="保存"
    >
      <Form {...formLayout}>
        <Form.Item label="功能开启">
          {getFieldDecorator('status', {
            initialValue: (detailData.status === 1),
            valuePropName: 'checked',
            rules: [
              {
                required: true,
              },
            ],
          })(<Switch></Switch>)}
        </Form.Item>
        <Form.Item label="大模型编码">
          {getFieldDecorator('model_code', {
            initialValue: detailData.model_code || '',
            rules: [
              {
                required: true,

                message: '请输入大模型编码',
              }
            ],
          })(<Input placeholder="请输入对应的大模型编码，如有疑问可联系技术部门" />)}

        </Form.Item>

        <Form.Item label="功能入口图标" extra="支持jpg,jpeg,png,gif图片格式，比例为1:1">
          {getFieldDecorator("float_win_image_url", {
            initialValue: detailData.float_win_image_url || '',
            rules: [
              {
                required: true,
                message: '请上传功能入口图',
              },
            ],
          })(<ImageUploader ratio={1 / 1} />)}
        </Form.Item>

        <Form.Item label="对话页头像" extra="支持jpg,jpeg,png,gif图片格式，比例为1:1">
          {getFieldDecorator("gpt_image_url", {
            initialValue: detailData.gpt_image_url || '',
            rules: [
              {
                required: true,
                message: '请上传对话页头像',
              },
            ],
          })(<ImageUploader ratio={1 / 1} />)}
        </Form.Item>

        <Form.Item label="对话页标题图" extra={
          <span style={{ position: 'relative' }}> 支持jpg,jpeg,png图片格式，比例未限制, 建议高度60px

            <Tooltip
              title={<img src={`/assets/gbt_name_image_url.png`} width={200} height={97} />}
            >
              <Icon
                type="question-circle"
                style={{ position: 'absolute', right: -20, top: 2 }}
              />
            </Tooltip>
          </span>
        }>
          {getFieldDecorator("name_image_url", {
            initialValue: detailData.name_image_url || '',
            rules: [
              {
                required: true,
                message: '请上传对话页标题图',
              },
            ],
          })(<ImageUploader accept={['image/jpeg', 'image/png', 'image/jpg']} />)}


        </Form.Item>

        <Form.Item label="全局通用头像" extra="支持jpg,jpeg,png图片格式，比例为1:1">
          {getFieldDecorator("common_image_url", {
            initialValue: detailData.common_image_url || '',
            rules: [
              {
                required: true,
                message: '请上传全局通用头像',
              },
            ],
          })(<ImageUploader ratio={1 / 1} accept={['image/jpeg', 'image/png', 'image/jpg']} />)}
        </Form.Item>



        <Form.Item label="圈主名称">
          {getFieldDecorator('name', {
            initialValue: detailData.name || '',
            rules: [
              {
                required: true,

                message: '请输入圈主名称',
              }, {
                max: 6,
                message: '最多6字',
              }
            ],
          })(<Input placeholder="最多6字" />)}

        </Form.Item>

        <Form.Item label="一句话介绍">
          {getFieldDecorator('short_desc', {
            initialValue: detailData.short_desc || '',
            rules: [
              {
                required: false,

                message: '请输入一句话介绍',
              }, {
                max: 12,
                message: '最多12字',
              }
            ],
          })(<Input placeholder="最多12字" maxLength={20} />)}

        </Form.Item>


        <Form.Item label="关联账号" help={optionData.length === 0 ? '输入昵称或用户ID查找' : ''}>
          {getFieldDecorator('accountIds', {
            initialValue: detailData.account_id ? [detailData.account_id] : undefined,
            rules: [{ required: true, message: '请选择至少一个账号' }],
          })(
            <Select
              mode="multiple"
              allowClear
              placeholder="输入昵称或用户ID查找"
              onSearch={(v) => handleAccountSearch(v)}
              showSearch={true}
              filterOption={false}
              optionLabelProp="label"
              onChange={(val, options: any) => {
                if (options?.length <= 1) {
                  setSelectedUser(options.map((d: any) => d.props.obj));
                } else {
                  message.error('最多关联一个账号')
                  setTimeout(() => {
                    setFieldsValue({ accountIds: selectedUser.map((d: any) => d.id) });
                  }, 250);
                }
              }}
            >
              {optionData.map((d: any) => (
                <Select.Option
                  key={d.id}
                  label={d.nick_name}
                  value={String(d.id)} // 将 d.id 显式转换为字符串
                  obj={d}
                >
                  {['潮客 - ', '潮鸣号 - ', '潮鸣号 - '][d.cert_type] + d.nick_name} | 用户ID：{d.chao_id}

                </Select.Option>
              ))}
            </Select>

          )}

          <span
            style={{
              display: 'flex',
              position: 'absolute',
              width: 60,
              top: '-12px',
              right: '-60px',
            }}
          >

            <Tooltip
              title={"登录该账号，可以用数字圈主形象手动评论、点赞，对外不展示该账号信息"}
              placement="topRight"
            >
              <Icon
                type="question-circle"

                style={{
                  height: '40px',
                  lineHeight: '40px',
                  width: '40px',
                  textAlign: 'center',
                  cursor: 'pointer',
                  position: 'absolute', left: 10, top: 2
                }}

              />
            </Tooltip>



          </span>
        </Form.Item>
      </Form>
    </Drawer>
  );
};

export default Form.create<any>({ name: 'MasterConfigDrawer' })(
  forwardRef<any, any>(MasterConfigDrawer)
);
