import React, { useState, useEffect, useCallback, useMemo, useRef } from 'react';
import { useStore, useDispatch, useSelector } from 'react-redux';
import { useHistory } from 'react-router-dom';
import { Table, A, OrderColumn, Drawer } from '@components/common';
import { CommonObject } from '@app/types';
import { getCrumb, setMenuHook, UserDetail } from '@app/utils/utils';
import {
  Row,
  Col,
  Input,
  Divider,
  Button,
  Select,
  Icon,
  Modal,
  message,
  InputNumber,
  Tooltip,
  Timeline,
  Switch,
} from 'antd';
import { getTableList } from '@app/action/tableList';
import { opApi as api, communityApi, opApi, releaseListApi, userApi } from '@app/api';
import useXHR from '@utils/useXhr';
import { PermA, PermButton } from '@components/permItems';
import { setConfig } from '@app/action/config';
import moment from 'moment';
import '@components/business/styles/business.scss';
import _ from 'lodash';
import AddIMBlackUserModal from './addIMBlackUserModal';

const defaultSize = 10;
export default function ImBlackListManager(props: any) {
  const [accountOptions, setAccountOptions] = useState([]);

  const [filter, setFilter] = useState<any>({});

  const dispatch = useDispatch();
  const store = useStore();
  const history = useHistory();
  const {
    total,
    current,
    size,
    records = [],
    allData,
  } = useSelector((state: any) => state.tableList);
  const { loading, run } = useXHR();
  const addRef = useRef<any>(null);
  const [drawer, setDrawer] = useState({
    visible: false,
    key: 0,
    record: null,
  });

  const [userDetail, setUserDetail] = useState({
    key: null,
    visible: false,
    detail: null,
  });

  const [logs, setLogs] = useState<any>({
    visible: false,
    logs: [],
    title: '',
  });

  const [addIMBlackUserModal, setAddIMBlackUserModal] = useState<any>({
    visible: false,
    key: '',
  });

  const getSeq = (i: number) => (current - 1) * size + i + 1;

  const getList = (goToFirstPage = false, newFilter = filter) => {
    let cur = goToFirstPage ? 1 : current;

    dispatch(getTableList('getIMBlackList', 'list', { current: cur, size, ...newFilter }));
  };

  const getColumns = () => {
    let values = [
      {
        title: '账号昵称',
        dataIndex: 'account_name',
        width: 150,
        render: (text: any, record: any) => (
          <A onClick={() => showUserDetailModal(record.account_id, true)}>{text}</A>
        ),
      },
      {
        title: '用户ID',
        dataIndex: 'chao_id',
        width: 150,
      },
      {
        title: '状态',
        dataIndex: 'status',
        width: 150,
        render: (text: any, record: any) => (text == 0 ? '正常' : '拉黑'),
      },
      {
        title: '操作时间',
        dataIndex: 'updated_at',
        width: 160,
        render: (text: any, record: any) => moment(text).format('YYYY-MM-DD HH:mm:ss'),
      },
      {
        title: '操作',
        key: 'op',
        render: (text: any, record: any, i: number) => (
          <span>
            {record.status == 1 && (
              <PermA perm="im_blacklist:cancel" onClick={() => cancelBlackUser(record)}>
                取消拉黑
              </PermA>
            )}
            &nbsp;
            <PermA perm="" onClick={() => showOperationRecord(record)}>
              查看记录
            </PermA>
          </span>
        ),
        width: 140,
      },
    ];
    return values;
  };

  const cancelBlackUser = (record: any) => {
    Modal.confirm({
      title: '确定取消拉黑吗？',
      onOk: () => {
        run(
          api.cancelBlackUser,
          {
            id: record.id,
          },
          true
        ).then(() => {
          message.success('取消拉黑成功');
          getList();
        });
      },
    });
  };

  // 显示用户详情
  const showUserDetailModal = (id: any, visible: boolean) => {
    run(userApi.getUserDetail, { accountId: id }, true).then((r: any) => {
      setUserDetail({
        key: Date.now(),
        visible,
        detail: r.data.account,
      });
    });
  };

  const showOperationRecord = (record: any) => {
    run(opApi.getOperateLog, { type: 170, target_id: record.id }, true)
      .then((res: any) => {
        setLogs({
          visible: true,
          logs: res?.data?.admin_log_list || [],
          title: record.account_name,
        });
      })
      .catch(() => {});
  };

  useEffect(() => {
    // getList({ current: 1, size: 10 });
    setMenuHook(dispatch, props);
    getList(true);
  }, []);

  const addBlackUser = () => {
    setAddIMBlackUserModal({
      visible: true,
      key: Date.now(),
    });
  };

  return (
    <>
      <Row className="layout-infobar">
        <Col span={12}>
          <Button onClick={() => history.goBack()}>
            <Icon type="left-circle" />
            返回
          </Button>

          <PermButton
            // perm="gpt_question:style_detail"
            perm="im_blacklist:create"
            style={{ marginLeft: 8 }}
            onClick={addBlackUser}
          >
            添加黑名单
          </PermButton>
        </Col>
        <Col span={12} className="layout-breadcrumb">
          {getCrumb(props.breadCrumb)}
        </Col>
      </Row>
      <div className="component-content">
        <Table
          func="getIMBlackList"
          index="list"
          filter={filter}
          columns={getColumns()}
          rowKey="id"
          pagination={true}
        />
        <AddIMBlackUserModal
          {...addIMBlackUserModal}
          onCancel={() =>
            setAddIMBlackUserModal({
              visible: false,
              key: '',
            })
          }
          onOk={() => {
            setAddIMBlackUserModal({
              visible: false,
              key: '',
            });
            getList();
          }}
        ></AddIMBlackUserModal>
      </div>

      <Modal
        visible={logs.visible}
        title="操作日志"
        key={logs.key}
        cancelText={null}
        onCancel={() => setLogs({ ...logs, visible: false })}
        onOk={() => setLogs({ ...logs, visible: false })}
      >
        <div>
          <Timeline>
            {logs.logs?.map((v: any, i: number) => [
              <Timeline.Item className="timeline-dot-big" data-show={v.date} key={`time${i}`}>
                &nbsp;
              </Timeline.Item>,
              v.log_list?.map((action: any, index: number) => (
                <Timeline.Item
                  className="timeline-dot"
                  data-show={moment(action.created_at).format('HH:mm:ss')}
                  key={`time${i}-action${index}`}
                >
                  {action.admin_name}&emsp;{action.remark}
                  {/* {action.user}&emsp;&emsp;{action.action}&emsp;&emsp; */}
                </Timeline.Item>
              )),
            ])}
          </Timeline>
        </div>
      </Modal>

      <Modal
        visible={userDetail.visible}
        key={userDetail.key}
        title="用户详情"
        width={800}
        onCancel={() => setUserDetail({ ...userDetail, visible: false })}
        onOk={() => setUserDetail({ ...userDetail, visible: false })}
      >
        {userDetail.visible && <UserDetail detail={userDetail.detail} />}
      </Modal>
    </>
  );
}
