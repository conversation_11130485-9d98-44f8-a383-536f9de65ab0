import NewNewsSearchAndInput from '@app/components/common/newNewsSearchAndInput';
import { Button, Form, Icon, InputNumber, Modal, Spin, Tooltip, message } from 'antd';
import React, { forwardRef, useImperativeHandle, useState } from 'react';

import { opApi } from '@app/api';
import { SearchAndInput } from '@app/components/common';
import { useStore } from 'react-redux';

const AddAudioColumnModal = (props: any, ref: any) => {
  const [loading, setLoading] = useState(false);
  const { getFieldDecorator } = props.form;
  const store = useStore();
  const { permissions } = store.getState().session;

  const column = [
    {
      title: '序号',
      dataIndex: 'seq',
      width: 80,
      render: (text: any, record: any, i: number) => <span>{i + 1}</span>,
    },
    {
      title: '地方号',
      key: 'name',
      dataIndex: 'name',
    },
  ];

  const formLayout = {
    labelCol: { span: 4 },
    wrapperCol: { span: 18 },
  };

  const handleSubmit = (e: any) => {
    e.preventDefault();
    props.form.validateFieldsAndScroll((err: any, values: any) => {
      if (!err) {
        setLoading(true);
        const parmas = {
          ref_ids: values.column_list.join(','),
        };
        opApi
          .addAudioPlazaColumn(parmas)
          .then((res: any) => {
            message.success('添加成功');
            setLoading(false);
            props.onOk && props.onOk();
          })
          .catch(() => {
            // message.error('添加失败');
            setLoading(false);
          });
      } else {
        message.error('请检查表单内容');
        setLoading(false);
      }
    });
  };

  return (
    <Modal
      width={500}
      visible={props.visible}
      title="精选地方号"
      key={props.key}
      onCancel={props.onCancel}
      onOk={handleSubmit}
      maskClosable={false}
      destroyOnClose={true}
      confirmLoading={loading}
      okButtonProps={{ disabled: !permissions.includes('audio_home:top_column_save') }}
      bodyStyle={{
        height: 500,
        maxHeight: 500,
        overflow: 'auto',
      }}
    >
      <Spin spinning={loading}>
        <Form {...formLayout} onSubmit={handleSubmit}>
          <Form.Item label="关联地方号" required>
            {getFieldDecorator('column_list', {
              initialValue: props.column_list?.map((v: any) => {
                return v.uuid || v.id
              }),
              rules: [
                {
                  max: 50,
                  message: '最多添加50个地方号',
                  type: 'array',
                },
              ],
            })(
              <SearchAndInput
                max={50}
                func="searchUGCTopic"
                columns={column}
                placeholder="输入名称搜索地方号"
                initialValues={{ list: props.column_list || [] }}
                body={{ type: 0 }}
                order={true}
                addOnTop={false}
                searchKey="name"
                funcIndex="list"
                apiWithPagination={true}
                selectOptionDisplay={(record: any) => `#${record.name}#`}
              />
            )}
          </Form.Item>
        </Form>
      </Spin>
    </Modal>
  );
};

export default Form.create<any>({ name: 'AddAudioColumnModal' })(
  forwardRef<any, any>(AddAudioColumnModal)
);
