import NewNewsSearchAndInput from '@app/components/common/newNewsSearchAndInput';
import {
  Button,
  Checkbox,
  Form,
  Icon,
  InputNumber,
  Modal,
  Radio,
  Spin,
  Table,
  Tabs,
  Tooltip,
  message,
} from 'antd';
import React, { forwardRef, useState } from 'react';
import { PhotoSlider } from 'react-photo-view';

const AiFeedbackModal = (props: any, ref: any) => {
  const [currentTab, setCurrentTab] = useState('1');
  const [imagePreview, setImagePreview] = useState({
    visible: false,
    imgs: [],
    index: 0,
  });
  let output: any = {};
  let input = {};
  if (props?.detail?.output) {
    try {
      output = JSON.parse(props?.detail.output);
      if (output.special_search == 103 && output.gov_articles?.length > 0) {
        output = {
          gov_articles: output.gov_articles,
        };
      } else if (
        (output.special_search == 100 || output.special_search == 101) &&
        output.docs?.length > 0
      ) {
        output = {
          articles: output.docs,
        };
      } else if (output.articles?.length > 0 && !output.content) {
        output = {
          articles: output.articles?.map((item: any) => {
            return {
              ...item,
              pic_url: item.list_pics?.[0],
            };
          }),
        };
      } else if (props?.detail?.type >= 100) {
        output = {
          content: output.content,
        };
      }
    } catch (e) {
      output = {
        content: props?.detail.output,
      };
    }
  }

  if (props?.detail?.input) {
    try {
      input = JSON.parse(props?.detail.input);
      if (!input || typeof input != 'object') {
        input = {
          description: props?.detail.input,
        };
      }
    } catch (e) {
      input = {
        description: props?.detail.input,
      };
    }
  }
  const list = Object.keys(input)?.map((key) => {
    return {
      title: {
        keyword: props.type == 1 ? '关键词' : '提问',
        description: props.type == 1 ? '关键词' : '提问',
        question: props.type == 1 ? '关键词' : '提问',
        content: '正文',
        images: '图片',
        style: props.detail.type == 2 ? '图片风格' : '文案风格',
        number: '字数要求',
        size_type: '尺寸要求',
      }[key],
      content: input[key],
      isImage: key == 'images',
    };
  });

  const columns = [
    { title: '要求', dataIndex: 'title', width: 100 },
    {
      title: '详情',
      dataIndex: 'content',
      render: (text, record) => {
        if (record.isImage && typeof text == 'string') {
          const imgs = text.split(',');
          return (
            <div>
              {imgs?.map((item, index) => (
                <img
                  key={item}
                  style={{
                    width: '60px',
                    height: '60px',
                    objectFit: 'cover',
                    marginRight: '10px',
                    marginBottom: '10px',
                    cursor: 'pointer',
                  }}
                  onClick={() => {
                    setImagePreview({
                      visible: true,
                      imgs: imgs,
                      index: index,
                    });
                  }}
                  src={item}
                ></img>
              ))}
            </div>
          );
        } else {
          return text;
        }
      },
    },
  ];

  const resultColumns = [
    { title: '序号', dataIndex: 'seq', width: 80, render: (text, record, index) => index + 1 },
    { title: '标题', dataIndex: 'content', render: (text, record) => record },
  ];

  const articleColumns = [
    { title: '稿件ID', dataIndex: 'id', width: 80 },
    { title: '稿件标题', dataIndex: 'list_title' },
    {
      title: '封面图',
      dataIndex: 'pic_url',
      width: 80,
      render: (text, record) =>
        text && (
          <img src={text} style={{ width: '60px', height: '60px', objectFit: 'cover' }}></img>
        ),
    },
  ];

  const govArticleColumns = [
    { title: '标题', dataIndex: 'list_title' },
    { title: '摘要', dataIndex: 'summary' },
    {
      title: '链接',
      dataIndex: 'source',
      width: 80,
      render: (text: any, record: any) => {
        return (
          <a href={record.url || record.content_source} target="_blank">
            点击查看
          </a>
        );
      },
    },
  ];

  return (
    <Modal
      width={500}
      visible={props.visible}
      title={props.type == 1 ? '智能创作详情' : '智能助手详情'}
      key={props.key}
      onCancel={props.onCancel}
      maskClosable={false}
      destroyOnClose={true}
      footer={null}
    >
      <div style={{ maxHeight: 600, overflow: 'auto' }}>
        <Tabs defaultActiveKey="1" onChange={(key) => setCurrentTab(key)}>
          <Tabs.TabPane tab="输入信息" key="1">
            <Table dataSource={list} columns={columns} rowKey={'title'} pagination={false}></Table>
          </Tabs.TabPane>
          <Tabs.TabPane tab="输出信息" key="2">
            {output?.content && <div style={{ whiteSpace: 'pre-wrap' }}>{output.content}</div>}
            {output?.list?.length > 0 && (
              <Table
                dataSource={output?.list}
                columns={resultColumns}
                rowKey={(record, index) => record}
                pagination={false}
              ></Table>
            )}
            {output?.articles?.length > 0 && (
              <Table
                dataSource={output?.articles}
                columns={articleColumns}
                rowKey={(record, index) => record.id}
                pagination={false}
              ></Table>
            )}
            {output?.gov_articles?.length > 0 && (
              <Table
                dataSource={output?.gov_articles}
                columns={govArticleColumns}
                rowKey={(record, index) => record.list_title}
                pagination={false}
              ></Table>
            )}

            {output?.imageUrl && (
              <div>
                <img style={{ width: '100%', height: 'auto' }} src={output?.imageUrl}></img>
              </div>
            )}
          </Tabs.TabPane>
        </Tabs>
      </div>

      <PhotoSlider
        maskOpacity={0.5}
        images={imagePreview.imgs?.map((v: any) => ({ src: v, key: v })) || []}
        visible={imagePreview.visible}
        onClose={() =>
          setImagePreview({
            ...imagePreview,
            visible: false,
          })
        }
        index={imagePreview.index}
        onIndexChange={(index) =>
          setImagePreview({
            ...imagePreview,
            index,
          })
        }
      />
    </Modal>
  );
};

export default Form.create<any>({ name: 'AiFeedbackModal' })(forwardRef<any, any>(AiFeedbackModal));
