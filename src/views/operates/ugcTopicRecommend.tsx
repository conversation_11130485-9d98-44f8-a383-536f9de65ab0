import { opApi as api, releaseListApi } from '@app/api';
import { ITableProps, IBaseProps, CommonObject } from '@app/types';
import { A, Table, BaseComponent, Drawer } from '@components/common';
import Form from '@components/business/ugcTopicRecommendForm';
import { connectTable as connect } from '@utils/connect';
import {
  Button,
  Col,
  Icon,
  message,
  Modal,
  Row,
  Divider,
  Tooltip,
  InputNumber,
  Form as AForm,
} from 'antd';
import moment from 'moment';
import React from 'react';
import { withRouter } from 'react-router';
import { RecommendTopicRecord, RecommendTopicAllData } from './operates';

type State = {
  visible: boolean;
  key: number;
  onCount: number;
  positionId: number;
  displayPos: number | undefined;
  visibleSort: any;
  nameSort: string;
};

type Props = IBaseProps<ITableProps<RecommendTopicRecord, RecommendTopicAllData>>;

class UGCTopicRecommendMgr extends BaseComponent<
  ITableProps<RecommendTopicRecord, RecommendTopicAllData>,
  State
> {
  constructor(props: Props) {
    super(props);
    this.state = {
      visible: false,
      key: Date.now(),
      onCount: 0,
      positionId: 1,
      displayPos: 1,
      visibleSort: false,
      nameSort: '',
    };
  }

  componentDidMount() {
    this.setMenu();
    this.getData({ current: 1, size: 10 });
  }

  componentDidUpdate(prevProps: Props) {
    if (
      prevProps.tableList.timestamp !== this.props.tableList.timestamp &&
      Boolean(this.props.tableList.allData.recommend_list)
    ) {
      this.setState({
        onCount: this.props.tableList.allData.on_show_count,
        positionId: this.props.tableList.allData.min_count_id,
        displayPos: this.props.tableList.allData.min_count,
      });
    }
  }

  getData = (overlap: CommonObject = {}) => {
    const { current, size } = this.props.tableList;
    this.dispatchTable('getRecommendTopicList', 'recommend_list', { current, size, ...overlap });
  };

  getColumns = () => {
    const { current, size } = this.props.tableList;
    const { onCount } = this.state;
    const getSeq = (i: number) => (current - 1) * size + i + 1;
    return [
      {
        title: '排序',
        key: 'order',
        render: (text: any, record: any, i: number) => (
          <span>
            {this.requirePerm('type_recommend:5:update_sort')(
              <A
                disabled={getSeq(i) === 1 || !record.enabled}
                className="sort-up"
                onClick={this.exchangeOrder.bind(this, record.id, 0)}
              >
                <Icon type="up-circle" theme="filled" />
              </A>
            )}{' '}
            {this.requirePerm('type_recommend:5:update_sort')(
              <A
                disabled={getSeq(i) >= onCount || !record.enabled}
                className="sort-down"
                onClick={this.exchangeOrder.bind(this, record.id, 1)}
              >
                <Icon type="down-circle" theme="filled" />
              </A>
            )}
          </span>
        ),
        width: 70,
      },
      {
        title: '序号',
        key: 'seq',
        render: (text: any, record: any, i: number) => <span>{getSeq(i)}</span>,
        width: 70,
      },
      {
        title: '话题名称',
        key: 'name',
        dataIndex: 'name',
      },
      // {
      //   title: '话题类型',
      //   key: 'type',
      //   dataIndex: 'type',
      //   render: (text: any, record: any) => (
      //     <span>{record.type === 1 ? '视频话题' : '普通话题'}</span>
      //   ),
      //   width: 100,
      // },
      {
        title: (
          <span>
            内容数量
            <Tooltip
              title="话题下已过审的全部内容数（包括沉底）；前台话题页不显示沉底内容"
              overlayStyle={{ maxWidth: 426 }}
            >
              <Icon type="question-circle-o" />
            </Tooltip>
          </span>
        ),
        key: 'article_count',
        dataIndex: 'article_count',
        width: 110,
      },
      // {
      //   title: '所属分类',
      //   key: 'classes',
      //   dataIndex: 'class_names',
      //   render: (text: any) => <span>{text ? text.join('，') : ''}</span>,
      //   width: 170,
      // },
      {
        title: '创建时间',
        key: 'created_at',
        dataIndex: 'created_at',
        render: (text: any) => <span>{moment(text).format('YYYY-MM-DD HH:mm:ss')}</span>,
        width: 180,
      },
      {
        title: '操作',
        key: 'op',
        render: (text: any, record: any, i: number) => (
          <span>
            {this.requirePerm('type_recommend:5:update_status')(
              <A onClick={this.updateStatus.bind(this, record)}>
                {record.enabled ? '下架' : '上架'}
              </A>
            )}
            <Divider type="vertical" />
            {this.requirePerm(`type_recommend:5:update_sort${record.enabled ? '' : 'XXXX'}`)(
              <A onClick={this.exchangeInsertSort.bind(this, record, getSeq(i))}>排序</A>
            )}
            <Divider type="vertical" />
            {this.requirePerm('type_recommend:5:delete')(
              <A onClick={this.deleteRecord.bind(this, record)}>删除</A>
            )}
          </span>
        ),
        width: 160,
      },
    ];
  };

  exchangeOrder = (id: number, sortFlag: number) => {
    this.setLoading(true);
    api
      .sortRecommendTopic({ id, sort_flag: sortFlag })
      .then(() => {
        message.success('操作成功');
        this.getData();
        this.setLoading(false);
      })
      .catch(() => this.setLoading(false));
  };

  exchangeInsertSort = (record: any, newSort: number | undefined) => {
    console.log(record);
    const posChange = (value: number | undefined) => {
      newSort = value;
    };
    Modal.confirm({
      title: `#${record.name}#`,
      content: (
        <div>
          <AForm>
            <AForm.Item
            // label={`#${record.name}#`}
            // extra="客户端只呈现上架状态且视频数大于等于所填数字的话题。"
            >
              <InputNumber
                placeholder="请输入修改序号"
                min={1}
                onChange={posChange}
                defaultValue={newSort}
                style={{ width: 200 }}
                precision={0}
              />
              <br />
              {/* <span style={{ color: 'red' }}>为保证客户端呈现，只能大于等于0</span> */}
            </AForm.Item>
          </AForm>
        </div>
      ),
      onOk: (closeFunc: Function) => {
        if (!newSort) {
          message.error('请填写序号');
          return;
        }
        api
          .sortPositionRecommendTopic({
            id: record.id,
            position: newSort,
          })
          .then(() => {
            message.success('操作成功');
            this.getData();
            closeFunc();
          });
      },
    });

    // this.setLoading(true);
    // api
    //   .sortPositionRecommendTopic({ id, position: record })
    //   .then(() => {
    //     message.success('操作成功');
    //     this.getData();
    //     this.setLoading(false);
    //   })
    //   .catch(() => this.setLoading(false));
  };

  updateStatus = (record: any) => {
    if (this.state.onCount >= 50 && !record.enabled) {
      message.error('推荐位数量已达上限50个，请先下架再上架');
      return;
    }
    if (record.article_count < this.state.displayPos && !record.enabled) {
      message.error(`该话题下的内容数量小于${this.state.displayPos}条，不建议上架`);
      return;
    }
    this.setLoading(true);
    api
      .updateRecommendTopicStatus({ id: record.id, status: record.enabled ? 0 : 1 })
      .then(() => {
        message.success('操作成功');
        this.getData();
        this.setLoading(false);
      })
      .catch(() => this.setLoading(false));
  };

  deleteRecord = (record: any) => {
    Modal.confirm({
      title: '确定删除吗？',
      onOk: () => {
        this.setLoading(true);
        api
          .deleteRecommendTopic({ id: record.id })
          .then(() => {
            message.success('操作成功');
            this.getData();
            this.setLoading(false);
          })
          .catch(() => this.setLoading(false));
      },
    });
  };

  closeDrawer = () => {
    this.setState({
      visible: false,
    });
  };

  submitEnd = () => {
    this.closeDrawer();
    this.getData();
  };

  createRecord = () => {
    this.setState({
      visible: true,
      key: Date.now(),
    });
  };

  showPosChangeModal = () => {
    let pos: any = this.state.displayPos || 4;
    const posChange = (value: number | undefined) => {
      pos = value;
    };
    Modal.confirm({
      title: '设置',
      content: (
        <div>
          <AForm>
            <AForm.Item
              label="上架话题的最小内容数"
              extra="客户端只呈现上架状态且内容数大于等于所填数字的话题。"
            >
              <InputNumber
                placeholder="请输入修改序号"
                min={4}
                onChange={posChange}
                defaultValue={pos}
                style={{ width: 200 }}
              />
              <br />
              <span style={{ color: 'red' }}>为保证客户端呈现，只能大于等于4</span>
            </AForm.Item>
          </AForm>
        </div>
      ),
      onOk: (closeFunc: Function) => {
        if (!pos) {
          message.error('请填写序号');
          return;
        }
        releaseListApi
          .updateRecommendPosition({
            id: this.state.positionId,
            position: pos,
          })
          .then(() => {
            message.success('操作成功');
            this.getData();
            closeFunc();
          });
      },
    });
  };

  render() {
    const text = (
      <span>
        <p>1.上架状态的话题，最多为50个。</p>
        <p>2.推荐话题呈现在客户端嘉友圈的话题页面中。</p>
      </span>
    );
    return (
      <>
        <Row className="layout-infobar">
          <Col span={12}>
            <Button
              style={{ marginRight: 8 }}
              onClick={() => {
                this.props.history.goBack();
              }}
            >
              <Icon type="left-circle" />
              返回
            </Button>
            {this.requirePerm('type_recommend:5:create')(
              <Button onClick={this.createRecord} style={{ marginRight: 8 }}>
                <Icon type="plus-circle-o" />
                添加推荐话题
              </Button>
            )}
            {this.requirePerm('type_recommend:14:update_position')(
              <Button onClick={this.showPosChangeModal} style={{ marginRight: 8 }}>
                <Icon type="form" />
                设置内容数限制
              </Button>
            )}
            <Tooltip placement="right" title={text}>
              <Icon type="question-circle-o" />
            </Tooltip>
          </Col>
          <Col span={12} className="layout-breadcrumb">
            {this.getCrumb()}
          </Col>
        </Row>
        <div className="component-content">
          <Table
            func="getRecommendTopicList"
            index="recommend_list"
            pagination={true}
            filter={{}}
            columns={this.getColumns()}
            rowKey="id"
          />
          <Drawer
            visible={this.state.visible}
            skey={this.state.key}
            title="添加推荐话题"
            onClose={this.closeDrawer}
            onOk={this.handleSubmitForm.bind(this, 'form')}
          >
            <Form wrappedComponentRef={this.setFormRef.bind(this, 'form')} onEnd={this.submitEnd} />
          </Drawer>
        </div>
      </>
    );
  }
}

export default withRouter(
  connect<RecommendTopicRecord, RecommendTopicAllData>()(UGCTopicRecommendMgr)
);
