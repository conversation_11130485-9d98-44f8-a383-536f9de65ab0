import NewNewsSearchAndInput from '@app/components/common/newNewsSearchAndInput';
import {
  Button,
  Col,
  Form,
  Icon,
  Input,
  InputNumber,
  Modal,
  Row,
  Select,
  Spin,
  Tooltip,
  message,
} from 'antd';
import React, { forwardRef, useEffect, useImperativeHandle, useState } from 'react';

import { opApi } from '@app/api';
import { NewTable } from '@app/components/common';
import useTable from '@app/utils/useTable';
import { PermA } from '@app/components/permItems';

const LongVideoSearchUserDialog = (props: any, ref: any) => {
  const { getFieldDecorator } = props.form;
  const [search, setSearch] = useState({
    search_type: '1',
    search_content: '',
  });
  const [filter, setFilter] = useState({
    // id: props.id,
  });

  const handleKey = (e: any) => {
    if (e.which === 13) {
      // handleSubmit();
      const params: any = {
        ...filter,
        search_type: search.search_type,
        search_content: search.search_content,
      };

      if (!params.search_content) {
        delete params.search_content;
        delete params.search_type;
      }

      setFilter(params);
    }
  };

  const columns = [
    {
      title: '作者昵称',
      dataIndex: 'nick_name',
      width: 80,
    },
    {
      title: '手机号',
      dataIndex: 'phone',
      width: 80,
    },
    {
      title: '用户长ID',
      dataIndex: 'account_id',
      width: 80,
    },
    {
      title: '所在权限组',
      dataIndex: 'duration_name',
      width: 80,
      render: (text: any, record: any) => {
        return (
          <a onClick={() => props.onCancel && props.onCancel(record.duration_id)}>
            {record.duration_name}
          </a>
        );
      },
    },
    {
      title: '时长（秒）',
      dataIndex: 'duration',
      width: 80,
    },
    {
      title: '操作',
      key: 'type',
      dataIndex: 'op',
      width: 90,
      render: (text: any, record: any) => {
        return <PermA perm="long_video_duration:delete_account" onClick={() => handleDelete(record)}>删除</PermA>;
      },
    },
  ];

  const handleDelete = (record: any) => {
    Modal.confirm({
      title: '是否确定要删除该用户',
      onOk: () => {
        // setLoading(this, true);
        opApi
          .deleteLongVideoUser({ account_id: record.account_id, duration_id: record.duration_id })
          .then(() => {
            message.success('操作成功');
            // setLoading(this, false);
            getTableList();
          })
          .catch(() => {});
      },
    });
  };

  const formLayout = {
    labelCol: { span: 4 },
    wrapperCol: { span: 18 },
  };

  const getFilter = () => {
    return filter;
  };

  const { tableList, loading, setLoading, getTableList } = useTable({
    api: opApi.getDurationUserList,
    filter: getFilter,
    index: 'account_list',
  });

  useEffect(() => {
    if (props.visible) {
      getTableList();
    }
  }, [props.visible, filter]);

  const handleSubmit = (e: any) => {
    e.preventDefault();
    props.form.validateFieldsAndScroll((err: any, values: any) => {
      if (!err) {
        setLoading(true);
        const parmas = {
          type: props.type,
          sort_index: values.sort_index,
          article_id: values.channelArticles[0].id,
        };
        opApi
          .addRankingArticle(parmas)
          .then((res: any) => {
            message.success('添加成功');
            setLoading(false);

            setVisible(false);
            props.onOk && props.onOk();
          })
          .catch(() => {
            // message.error('添加失败');
            setLoading(false);
          });
      } else {
        message.error('请检查表单内容');
        setLoading(false);
      }
    });
  };

  return (
    <Modal
      width={800}
      visible={props.visible}
      title="查找权限用户"
      key={`${props.key}`}
      onCancel={() => {
        props.onCancel && props.onCancel();
      }}
      // onOk={}
      maskClosable={false}
      destroyOnClose={true}
      footer={null}
    >
      <Spin spinning={false}>
        <Row style={{ marginBottom: 10 }}>
          <Col span={24}>
            <Select
              value={search.search_type}
              style={{ width: 130, marginRight: 8, marginLeft: 8 }}
              onChange={(v: any) => setSearch({ ...search, search_type: v })}
            >
              <Select.Option value="1">昵称</Select.Option>
              <Select.Option value="2">手机号</Select.Option>
              <Select.Option value="3">用户长ID</Select.Option>
            </Select>
            <Input
              value={search.search_content}
              style={{ marginRight: 8, width: 200 }}
              onChange={(e: any) => setSearch({ ...search, search_content: e.target.value })}
              onKeyPress={handleKey}
              placeholder="输入搜索内容"
            />
            <Button onClick={() => handleKey({ which: 13 })}>
              <Icon type="search" /> 搜索
            </Button>
          </Col>
        </Row>

        <NewTable
          columns={columns}
          rowKey="id"
          pagination={true}
          getTableList={getTableList}
          loading={loading}
          tableList={tableList}
        ></NewTable>
      </Spin>
    </Modal>
  );
};

export default Form.create<any>({ name: 'AddArticleModal' })(
  forwardRef<any, any>(LongVideoSearchUserDialog)
);
