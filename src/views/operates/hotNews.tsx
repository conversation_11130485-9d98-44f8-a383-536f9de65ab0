import { opApi as api, releaseListApi } from '@app/api';
import { A, Table, BaseComponent, PreviewMCN, OrderColumn } from '@components/common';
import connect from '@utils/connectAll';
import {
  Button,
  Col,
  Icon,
  Row,
  Input,
  Select,
  DatePicker,
  Modal,
  InputNumber,
  message,
} from 'antd';
import moment from 'moment';
import React from 'react';
import { withRouter } from 'react-router';
import { getCrumb, requirePerm, setLoading, resolveNewsType, setMenu } from '@utils/utils';
import { getTableList } from '@app/action/tableList';

import '../news/index.scss';

@(withRouter as any)
@connect
export default class HotNewsList extends React.Component<any, any> {
  componentDidMount() {
    this.getData({ current: 1, size: 10 });
    setMenu(this);
  }

  getData = (overlap = {}) => {
    const { current, size } = this.props.tableList;
    this.props.dispatch(
      getTableList('getHotNewsList', 'recommend_articles', { type: 1, current, size, ...overlap })
    );
  };

  toMlf = (id: number, type: 'mlf_edit_url' | 'mlf_detail_url') => {
    releaseListApi
      .toMlf(type, { id })
      .then((r: any) => {
        window.open(r.data.url);
      })
      .catch();
  };

  getColumns = () => {
    const { current, size } = this.props.tableList;
    const getSeq = (i: number) => (current - 1) * size + i + 1;
    return [
      {
        title: '序号',
        key: 'seq',
        render: (text: any, record: any, i: number) => <span>{getSeq(i)}</span>,
        width: 70,
      },
      {
        title: 'ID',
        dataIndex: 'article_id',
        width: 100,
      },
      {
        title: '天目蓝云ID',
        key: 'metadata_id',
        dataIndex: 'metadata_id',
        width: 100,
      },
      {
        title: '新闻标题',
        key: 'list_title',
        dataIndex: 'list_title',
        render: (text: any, record: any) => (
          <A
            className="list-title"
            onClick={() => this.toMlf(record.article_id, 'mlf_detail_url')}
            style={!record.recommend_visible ? { color: '#ccc' } : {}}
          >
            {text}
          </A>
        ),
      },
      {
        title: '阅读用户数',
        dataIndex: 'fake_count',
        width: 100,
      },
      {
        title: '频道',
        dataIndex: 'channel_name',
        width: 80,
      },
      {
        title: '类型',
        dataIndex: 'doc_type',
        render: (text: any) => resolveNewsType(text),
        width: 70,
      },
      {
        title: '发稿人',
        dataIndex: 'creator',
        width: 110,
      },
      {
        title: '发布时间',
        key: 'published_at',
        dataIndex: 'published_at',
        render: (text: any, record: any) => (
          <span>{moment(text).format('YYYY-MM-DD HH:mm:ss')}</span>
        ),
        width: 95,
      },
      {
        title: '操作',
        key: 'op',
        render: (text: any, record: any) =>
          requirePerm(
            this,
            'recommend_article:set_visible'
          )(
            <A onClick={this.setVisible.bind(this, record)}>
              {record.recommend_visible ? '隐藏' : '取消隐藏'}
            </A>
          ),
        width: 90,
      },
    ];
  };

  setVisible = (record: any) => {
    setLoading(this, true);
    api
      .setHotNewsVisible({
        recommend_id: record.id,
        visible: !record.recommend_visible,
      })
      .then(() => {
        setLoading(this, false);
        this.getData();
      })
      .catch(() => setLoading(this, false));
  };

  render() {
    return (
      <>
        <Row className="layout-infobar">
          <Col span={12} />
          <Col span={12} className="layout-breadcrumb">
            {getCrumb(this.props.breadCrumb)}
          </Col>
        </Row>
        <div className="component-content">
          <Table
            func="getHotNewsList"
            index="recommend_articles"
            columns={this.getColumns()}
            rowKey="id"
            filter={{ type: 1 }}
            pagination={true}
          />
        </div>
      </>
    );
  }
}
