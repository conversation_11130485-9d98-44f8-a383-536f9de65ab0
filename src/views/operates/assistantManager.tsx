import React, { useState, useEffect, useCallback, useMemo, useRef } from 'react';
import { useStore, useDispatch, useSelector } from 'react-redux';
import { useHistory, useParams, useRouteMatch } from 'react-router-dom';
import { Table, A, OrderColumn, Drawer } from '@components/common';
import { CommonObject } from '@app/types';
import { getCrumb, searchToObject, setMenuHook } from '@app/utils/utils';
import {
  Row,
  Col,
  Input,
  Divider,
  Button,
  Select,
  Icon,
  Modal,
  message,
  InputNumber,
  Tooltip,
} from 'antd';
import { getTableList } from '@app/action/tableList';
import { opApi as api } from '@app/api';
import useXHR from '@utils/useXhr';
import { PermA, PermButton } from '@components/permItems';
import Radio from 'antd/es/radio';
import { setConfig } from '@app/action/config';
import moment from 'moment';
import AssistantForm from './assistantForm';
import ConfigGPTStyleDrawer from './configGPTStyleDrawer';
import '@components/business/styles/business.scss';
import ConfigSaloonbStyleDrawer from '@app/views/reporter/configSaloonStyleDrawer';
import GuideBannerModal from '@app/components/common/guideBannerModal';
import MasterBubbleDrawer from '@app/views/operates/masterBubbleDrawer';
import MasterConfigDrawer from '@app/views/operates/masterConfigDrawer';

const defaultSize = 10;
export default function AssistantManager(props: any) {
  const category = props.category ?? 0;
  //圈子信息
  const channel_id = searchToObject().id ?? '';
  const name = searchToObject().name ?? '';

  const [filter, setFilter] = useState<any>({
    type: 0,
    status: 2,
    search_type: 1,
    keyword: '',
    category: category,
    channel_id: channel_id,
  });

  const [search, setSearch] = useState({
    search_type: 1,
    keyword: '',
  });
  const dispatch = useDispatch();
  const store = useStore();
  const history = useHistory();
  const {
    total,
    current,
    size,
    records = [],
    allData,
  } = useSelector((state: any) => state.tableList);
  const { loading, run } = useXHR();
  const formRef = useRef<any>(null);

  const [drawer, setDrawer] = useState({
    visible: false,
    key: 0,
    record: null,
  });

  const [styleDrawer, setStyleDrawer] = useState({
    visible: false,
    key: 0,
    record: null,
  });

  const [functionDrawer, setFunctionDrawer] = useState({
    visible: false,
    key: 1,
    record: null,
  });

  const [guideBannerDrawer, setGuideBannerDrawer] = useState({
    visible: false,
    key: 2,
    record: null,
  });

  const [masterBubbleDrawer, setMasterBubbleDrawer] = useState({
    visible: false,
    key: 3,
    record: null,
  });

  const [masterConfigDrawer, setMasterConfigDrawer] = useState({
    visible: false,
    key: 4,
    record: null,
  });

  const getSeq = (i: number) => (current - 1) * size + i + 1;

  const f = useMemo(() => {
    const x: CommonObject = { ...filter };
    if (!x.keyword) {
      delete x.keyword;
      delete x.search_type;
    }
    if (x.status == 2) {
      delete x.status;
    }

    return x;
  }, [filter]);

  const getList = useCallback(
    (overlap: CommonObject = {}) => {
      const { current, size = defaultSize } = store.getState().tableList;
      dispatch(
        getTableList('getGPTQuestionList', 'gpt_question_list', { ...f, current, size, ...overlap })
      );
    },
    [f]
  );

  const editRecord = (record: any) => {
    run(api.gptQuestionDetail, { id: record.id, category: category, channel_id: channel_id }, true)
      .then((data: any) => {
        console.log('gptQuestionDetail', data);
        const {
          data: { detail, article_list },
        } = data;
        const body = {
          ...detail,
          articles: article_list,
        };

        setDrawer({
          visible: true,
          key: Date.now(),
          record: body,
        });
      })
      .catch((e: any) => {});
  };
  const deleteRecord = (record: any) => {
    Modal.confirm({
      title: `确定要删除吗？`,
      onOk: () => {
        run(api.questionDelete, { id: record.id }, true).then(() => {
          message.success('操作成功');
          getList();
        });
      },
    });
  };

  const updateRecordStatus = (record: any) => {
    run(
      api.updateGPTQuestionStatus,
      { id: record.id, status: record.status == 1 ? 0 : 1 },
      true
    ).then(() => {
      message.success('操作成功');
      getList();
    });
  };

  const listSort = (record: any, i: number) => {
    let data: any = {
      id: record.id,
      sort_flag: i,
    };
    api.questionSort(data).then((res: any) => {
      message.success('操作成功');
      getList({ current: current, size: defaultSize });
    });
  };

  const changeOrder = (record: any, i: any) => {
    let position = i;
    const WAIT_TIME = 1000;
    const positionChange = (v: any) => {
      position = v;
    };

    Modal.confirm({
      title: <p>排序：《{record.article_title}》</p>,
      icon: <Icon type="info-circle" />,
      content: (
        <div>
          <span>请输入位置：</span>
          <InputNumber min={1} max={total + 1} defaultValue={position} onChange={positionChange} />
        </div>
      ),
      onOk: (closeFunc: Function) => {
        if (!position) {
          message.error('请填写位置');
          return;
        }

        dispatch(setConfig({ loading: true }));
        api
          .rankingSort({ id: record.id, sort_flag: 2, position: position })
          .then((res: any) => {
            message.success('操作成功');
            getList({ current: 1, size: defaultSize });
            dispatch(setConfig({ loading: false }));
            closeFunc();
          })
          .catch(() => {
            dispatch(setConfig({ loading: false }));
          });
      },
    });
  };

  const titleClick = (record: any) => {
    // setPreview({
    //   visible: true,
    //   skey: Date.now(),
    //   data: record
    // })
    if (!!record.url) {
      window.open(record.url, '_blank');
    }
  };

  const getColumns = () => {
    let values = [
      {
        title: '序号',
        key: 'seq',
        render: (text: any, record: any, i: number) => <span>{getSeq(i)}</span>,
        width: 70,
      },
      {
        title: '问题',
        dataIndex: 'question',
        width: 300,
      },
      {
        title: '答案',
        dataIndex: 'answer',
        // width: 90,
        render: (text: any, record: any, i: number) => {
          let content = '';

          if (record.question_type == 2) {
            content = '近24小时热榜的前3条内容';
          } else if (record.question_type == 3) {
            content = '活动中心进行中的前3条活动';
          } else {
            content = [
              '',
              '',
              '关联稿件',
              '由大模型返回',
              '互动组件',
              '近24小时热榜的前3条内容',
              '活动中心进行中的前3条活动',
              '推荐浙里办服务的前4个',
              '潮小帮智能体',
              '潮小拍智能体',
              '公文智搜智能体',
              'AI标题',
              'AI封面图',
              'AI配文',
            ][record.answer_type];
            if (!content) {
              // // 创建一个新的DOMParser实例
              // const parser = new DOMParser();

              // // 使用DOMParser解析字符串
              // const xmlDoc = parser.parseFromString(text, "text/xml");

              return (
                <Tooltip
                  overlayClassName="gpt-tooltip"
                  title={<div dangerouslySetInnerHTML={{ __html: text }}></div>}
                >
                  <div className="line-max-2" dangerouslySetInnerHTML={{ __html: text }}></div>
                </Tooltip>
              );
            }
          }

          return <span>{content}</span>;
        },
      },
      // {
      //   title: '稿件标题',
      //   key: 'article_title',
      //   dataIndex: 'article_title',
      //   render: (text: any, record: any) => (
      //     // onClick={this.toMlf.bind(this, record.id, 'mlf_detail_url')}
      //     <a onClick={() => titleClick(record)} className="list-title">
      //       {text}
      //     </a>
      //   ),
      // },
      {
        title: '状态',
        dataIndex: 'status',
        width: 90,
        render: (text: any, record: any) => {
          return <span>{text == 1 ? '启用' : '停用'}</span>;
        },
      },
      {
        title: '操作人',
        dataIndex: 'updated_by',
        width: 120,
      },
      {
        title: '操作时间',
        dataIndex: 'updated_at',
        width: 100,
        render: (text: any, record: any) => {
          return (
            <div style={{ whiteSpace: 'normal' }}>
              <div>{moment(text).format('YYYY-MM-DD')}</div>
              <div>{moment(text).format('HH:mm:ss')}</div>
            </div>
          );
        },
      },
      {
        title: '操作',
        key: 'op',
        render: (text: any, record: any, i: number) => (
          <span>
            <PermA
              perm={
                category === 0
                  ? 'gpt_question:update_status'
                  : `gpt_question:${category}:update_status`
              }
              onClick={() => updateRecordStatus(record)}
            >
              {record.status == 1 ? '停用' : '启用'}
            </PermA>
            <Divider type="vertical" />
            <PermA
              perm={category === 0 ? 'gpt_question:update' : `gpt_question:${category}:update`}
              onClick={() => editRecord(record)}
            >
              编辑
            </PermA>
            <Divider type="vertical" />
            <PermA
              perm={category === 0 ? 'gpt_question:delete' : `gpt_question:${category}:delete`}
              onClick={() => deleteRecord(record)}
            >
              删除
            </PermA>
          </span>
        ),
        width: 140,
      },
    ];

    if (!filter.keyword) {
      values.splice(0, 0, {
        title: '排序',
        key: 'sort',
        render: (text: any, record: any, i: number) => {
          const pos = getSeq(i);
          // if (pos < 4) {
          return (
            <OrderColumn
              perm={category === 0 ? 'gpt_question:sort' : `gpt_question:${category}:sort`}
              start={1}
              pos={pos}
              end={allData.on_show_count}
              disableUp={record.status != 1}
              disableDown={record.status != 1}
              onUp={() => listSort(record, 0)}
              onDown={() => listSort(record, 1)}
            />
          );
          // } else {
          //   return null
          // }
        },
        width: 70,
      });
    }
    return values;
  };

  useEffect(() => {
    // getList({ current: 1, size: 10 });
    setMenuHook(dispatch, props);
  }, []);

  useEffect(() => {
    getList({ current: 1, size: defaultSize });
  }, [f]);

  const onChangeType = (e: any) => {
    setFilter((of: any) => {
      return {
        ...of,
        type: e.target.value,
        search_type: 1,
        keyword: '',
      };
    });
    setSearch({
      search_type: 1,
      keyword: '',
    });

    // let { pathname: path, search } = history.location
    // const index = search.lastIndexOf('&type')
    // if (index >= 0) {
    //   search = search.substring(0, index)
    // }
    // path = `${path}${search}&type=${e.target.value}`
    // history.replace(path)
    // setFilter({
    //   ...filter,
    //   type: e.target.value
    // })
  };

  const searchInputChange = (e: any) => {
    setSearch({
      ...search,
      search_type: e,
    });
  };

  const addRecord = () => {
    // addRef.current.showModal()
    setDrawer({
      key: Date.now(),
      visible: true,
      record: null,
    });
  };

  const handleAddArticleOk = () => {
    getList({ current: 1, size: defaultSize });
  };

  const handleKey = (e: { which: number }) => {
    if (e.which === 13) {
      setFilter({
        ...filter,
        keyword: search.keyword,
        search_type: search.search_type,
      });
    }
  };

  const editStyle = () => {
    api.getGptStyleDetail({ category: category, channel_id: channel_id }).then((res: any) => {
      setStyleDrawer({
        visible: true,
        record: {
          ...(res?.data?.detail || {}),
          color: res?.data?.detail?.ref_ids?.split(','),
        },
        key: Date.now(),
      });
    });
  };

  const functionEntry = () => {
    setFunctionDrawer({
      visible: true,
      record: null,
      key: Date.now(),
    });
  };

  const guideBanner = () => {
    setGuideBannerDrawer({
      visible: true,
      record: null,
      key: Date.now(),
    });
  };

  const masterConfig = () => {
    setMasterConfigDrawer({
      visible: true,
      record: null,
      key: Date.now(),
    });
  };

  const masterBubble = () => {
    setMasterBubbleDrawer({
      visible: true,
      record: null,
      key: Date.now(),
    });
  };

  const addWelcome = () => {
    let msg = '';
    let bannerPicUrl = '';
    let bannerLinkUrl = '';
    const valueChange = (v: any) => {
      // msg = v.replace(/\r\n/g, '\n').slice(0, 40)
      msg = v?.trim();
      modal.update({
        content: (
          <>
            <Input
              value={msg}
              placeholder="最多输入40字"
              maxLength={40}
              onChange={(e) => valueChange(e.target.value)}
            />
            <div style={{ textAlign: 'right', marginTop: '8px' }}>{msg?.length}/40</div>
          </>
        ),
      });
    };

    var modal = Modal.confirm({
      title: '欢迎语',
      content: (
        <>
          <Input
            placeholder="最多输入40字"
            maxLength={40}
            onChange={(e) => valueChange(e.target.value)}
          />
          <div style={{ textAlign: 'right', marginTop: '8px' }}>0/40</div>
        </>
      ),
      onOk: (destroy: Function) => {
        const { permissions } = store.getState().session;
        const disabled =
          permissions.indexOf(
            category === 0 ? 'gpt_question:welcome_save' : `gpt_question:${category}:welcome_save`
          ) === -1;
        if (disabled) {
          message.error('没有权限');
          destroy();
          return;
        }
        if (msg?.length <= 0) {
          message.error('请输入欢迎语');
          return;
        }

        if (!!msg) {
          run(
            api.gptQuestionWelcomeSave,
            {
              welcome: msg,
              category: category,
              channel_id: channel_id,
              banner_pic_url: bannerPicUrl,
              banner_link_url: bannerLinkUrl,
            },
            true
          ).then(() => {
            message.success('操作成功');
          });
          destroy();
        } else {
        }
      },
    });

    api
      .gptQuestionWelcomeQuery({ category: category, channel_id: channel_id })
      .then((data: any) => {
        const {
          data: { welcome = '', banner_pic_url = '', banner_link_url = '' },
        } = data;
        bannerPicUrl = banner_pic_url || '';
        bannerLinkUrl = banner_link_url || '';
        valueChange(welcome);
        console.log(welcome.length);
      })
      .catch((e) => {});
  };

  const publish = () => {
    if (records.length < 20) {
      message.error('请至少选择20条数据');
      return;
    }
    const ids = records
      .slice(0, 20)
      .map((v: any) => v.id)
      .join(',');

    Modal.confirm({
      title: '确定发布前20条稿件吗？',
      onOk: () => {
        run(
          api.rankingPublish,
          { data_ids: ids, type: filter.type, category: category, channel_id: channel_id },
          true
        ).then(() => {
          message.success('操作成功');

          setFilter({
            ...filter,
            status: 0,
          });
        });
      },
    });
  };

  const onSubmitEnd = (v: any) => {
    if (filter.type != v) {
      setFilter({
        ...filter,
        type: v,
      });
    } else {
      getList({ current: current, size: defaultSize });
    }

    setDrawer({ ...drawer, visible: false });
  };

  return (
    <>
      <Row className="layout-infobar">
        <Col span={12}>
          {category === 2 && (
            <Button style={{ marginRight: 8 }} onClick={() => history.goBack()}>
              <Icon type="left-circle" />
              返回
            </Button>
          )}

          <PermButton
            perm={category === 0 ? 'gpt_question:create' : `gpt_question:${category}:create`}
            style={{ marginRight: 8 }}
            onClick={addRecord}
          >
            <Icon type="plus-circle" /> 新建问答
          </PermButton>

          <PermButton perm="" style={{ marginRight: 8 }} onClick={addWelcome}>
            欢迎语
          </PermButton>

          {/* {category === 0 && (
            <PermButton
              style={{ marginRight: 8 }}
              // perm="gpt_question:style_detail"
              perm=""
              onClick={editStyle}
            >
              设置样式
            </PermButton>
          )} */}

          {category === 1 && (
            <PermButton
              style={{ marginRight: 8 }}
              // perm="gpt_question:1:style_detail"
              perm=""
              onClick={functionEntry}
            >
              功能入口
            </PermButton>
          )}

          {category === 3 && (
            <PermButton
              style={{ marginRight: 8 }}
              // perm="gpt_question:1:style_detail"
              perm=""
              onClick={guideBanner}
            >
              引导banner
            </PermButton>
          )}

          {category === 2 && (
            <PermButton
              style={{ marginRight: 8 }}
              // perm="gpt_question:1:style_detail"
              perm=""
              onClick={masterConfig}
            >
              功能配置
            </PermButton>
          )}

          {category === 2 && (
            <PermButton
              style={{ marginRight: 8 }}
              // perm='gpt_question:dp_bubble_save'
              perm=""
              onClick={masterBubble}
            >
              提醒气泡
            </PermButton>
          )}
        </Col>
        <Col span={12} className="layout-breadcrumb">
          {category === 2 && getCrumb([...props.breadCrumb, name])}

          {category !== 2 && getCrumb(props.breadCrumb)}
        </Col>
      </Row>
      <div className="component-content">
        <Row style={{ marginBottom: 16 }}>
          <Col span={12}>
            <Radio.Group
              style={{ marginRight: 8 }}
              value={filter.type}
              buttonStyle="solid"
              onChange={onChangeType}
            >
              <Radio.Button value={0}>功能引导</Radio.Button>
              <Radio.Button value={1}>快捷输入</Radio.Button>
              <Radio.Button value={2}>问答</Radio.Button>
            </Radio.Group>

            <span>状态</span>
            <Select
              value={filter.status}
              style={{ width: 150, marginRight: 8, marginLeft: 8 }}
              onChange={(e) => setFilter({ ...filter, status: e })}
            >
              <Select.Option value={2}>请选择</Select.Option>
              <Select.Option value={1}>启用</Select.Option>
              <Select.Option value={0}>停用</Select.Option>
            </Select>
          </Col>

          <Col span={12}>
            <div className="title-right-col">
              <Tooltip title="搜索答案时，仅支持搜索文本类型答案">
                <Icon type="question-circle" />
              </Tooltip>
              <Select
                value={search.search_type}
                style={{ width: 150, marginRight: 8, marginLeft: 8 }}
                onChange={searchInputChange}
              >
                <Select.Option value={1}>问题</Select.Option>
                <Select.Option value={2}>答案</Select.Option>
              </Select>
              <Input
                style={{ width: 160, marginRight: 8 }}
                onKeyPress={handleKey}
                value={search.keyword}
                placeholder="请输入搜索内容"
                onChange={(e) =>
                  setSearch({
                    ...search,
                    keyword: e.target.value,
                  })
                }
              />
              <Button onClick={() => handleKey({ which: 13 })}>
                <Icon type="search" />
                搜索
              </Button>
            </div>
          </Col>
        </Row>
        <Table
          func="getGPTQuestionList"
          index="gpt_question_list"
          filter={f}
          columns={getColumns()}
          rowKey="id"
          pagination={true}
        />

        <Drawer
          visible={drawer.visible}
          skey={drawer.key}
          title={`${!!drawer.record?.id ? '编辑' : '新建'}问答`}
          onClose={() => setDrawer({ ...drawer, visible: false })}
          onOk={() => formRef.current.doSubmit()}
        >
          <AssistantForm
            onEnd={onSubmitEnd}
            category={category}
            channel_id={channel_id}
            formContent={drawer.record}
            // eslint-disable-next-line no-return-assign
            wrappedComponentRef={(instance: any) => (formRef.current = instance)}
          />
        </Drawer>

        <ConfigGPTStyleDrawer
          {...styleDrawer}
          onClose={() => setStyleDrawer({ ...styleDrawer, visible: false })}
          onEnd={() => setStyleDrawer({ ...styleDrawer, visible: false })}
        ></ConfigGPTStyleDrawer>

        <ConfigSaloonbStyleDrawer
          category={category}
          {...functionDrawer}
          onClose={() => setFunctionDrawer({ ...functionDrawer, visible: false })}
          onEnd={() => setFunctionDrawer({ ...functionDrawer, visible: false })}
        ></ConfigSaloonbStyleDrawer>

        <GuideBannerModal
          category={category}
          {...guideBannerDrawer}
          onCancel={() => setGuideBannerDrawer({ ...guideBannerDrawer, visible: false })}
          onEnd={() => setGuideBannerDrawer({ ...guideBannerDrawer, visible: false })}
        ></GuideBannerModal>

        <MasterBubbleDrawer
          category={category}
          channel_id={channel_id}
          {...masterBubbleDrawer}
          onClose={() => setMasterBubbleDrawer({ ...masterBubbleDrawer, visible: false })}
          onEnd={() => setMasterBubbleDrawer({ ...masterBubbleDrawer, visible: false })}
        ></MasterBubbleDrawer>

        <MasterConfigDrawer
          category={category}
          channel_id={channel_id}
          {...masterConfigDrawer}
          onClose={() => setMasterConfigDrawer({ ...masterConfigDrawer, visible: false })}
          onEnd={() => setMasterConfigDrawer({ ...masterConfigDrawer, visible: false })}
        ></MasterConfigDrawer>
      </div>
    </>
  );
}
