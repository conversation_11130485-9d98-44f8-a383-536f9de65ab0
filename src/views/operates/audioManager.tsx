import React, { useState, useEffect, useCallback, useMemo, useRef } from 'react';
import { useStore, useDispatch, useSelector } from 'react-redux';
import { useHistory } from 'react-router-dom';
import { Table, A, OrderColumn, Drawer } from '@components/common';
import { CommonObject } from '@app/types';
import { getCrumb, setMenuHook } from '@app/utils/utils';
import {
  Row,
  Col,
  Input,
  Divider,
  Button,
  Select,
  Icon,
  Modal,
  message,
  InputNumber,
  Tooltip,
  Timeline,
  Switch,
} from 'antd';
import { getTableList } from '@app/action/tableList';
import { opApi as api, releaseListApi } from '@app/api';
import useXHR from '@utils/useXhr';
import { PermA, PermButton } from '@components/permItems';
import Radio from 'antd/es/radio';
import { setConfig } from '@app/action/config';
import moment from 'moment';
import AssistantForm from './assistantForm';
import ConfigGPTStyleDrawer from './configGPTStyleDrawer';
import '@components/business/styles/business.scss';
import AddArticleModal from './addArticleModal';
import AddAudioArticleModal from './addAudioArticleModal';
import AddAudioColumnModal from './addAudioColumnModal';
import AudioRedPacketModal from './audioRedPacketModal';
import { render } from 'react-dom';
import AudioBroadcastDrawer from './audioBroadcastDrawer';

const defaultSize = 10;
export default function AudioManager(props: any) {
  const [filter, setFilter] = useState<any>({
    // type: 0,
    // status: 2,
    search_type: 1,
    keyword: '',
  });

  const [search, setSearch] = useState({
    search_type: 1,
    keyword: '',
  });
  const dispatch = useDispatch();
  const store = useStore();
  const history = useHistory();
  const {
    total,
    current,
    size,
    records = [],
    allData,
  } = useSelector((state: any) => state.tableList);
  const { loading, run } = useXHR();
  const formRef = useRef<any>(null);
  const addRef = useRef<any>(null);
  const [drawer, setDrawer] = useState({
    visible: false,
    key: 0,
    record: null,
  });

  const [broadcastDrawerState, setBroadcastDrawerState] = useState({
    visible: false,
    key: 0,
    record: null,
  });

  const [logs, setLogs] = useState<any>({
    visible: false,
    logs: [],
    title: '',
  });

  const [addColumnState, setAddColumnState] = useState({
    visible: false,
    key: 0,
    column_list: null,
  });

  const [redpacketState, setRedpacketState] = useState({
    visible: false,
    key: 0,
    record: null,
  });

  const [styleDrawer, setStyleDrawer] = useState({
    visible: false,
    key: 0,
    record: null,
  });

  const getSeq = (i: number) => (current - 1) * size + i + 1;

  const f = useMemo(() => {
    const x: CommonObject = { ...filter };
    if (!x.keyword) {
      delete x.keyword;
      delete x.search_type;
    }
    return x;
  }, [filter]);

  const getList = useCallback(
    (overlap: CommonObject = {}) => {
      const { current, size = defaultSize } = store.getState().tableList;
      dispatch(
        getTableList('getAudioPlazaArticleList', 'audio_article_list', {
          ...f,
          current,
          size,
          ...overlap,
        })
      );
    },
    [f]
  );

  const editRecord = (record: any) => {
    run(api.gptQuestionDetail, { id: record.id }, true)
      .then((data: any) => {
        console.log('gptQuestionDetail', data);
        const {
          data: { detail, article_list },
        } = data;
        const body = {
          ...detail,
          articles: article_list,
        };

        setDrawer({
          visible: true,
          key: Date.now(),
          record: body,
        });
      })
      .catch((e: any) => {});
  };
  const deleteRecord = (record: any) => {
    Modal.confirm({
      title: `确定要删除吗？`,
      onOk: () => {
        run(api.deleteAudioPlazaArticle, { recommend_id: record.recommend_id }, true).then(() => {
          message.success('操作成功');
          getList();
        });
      },
    });
  };

  const updateRecordStatus = (record: any) => {
    run(
      api.updateGPTQuestionStatus,
      { id: record.id, status: record.status == 1 ? 0 : 1 },
      true
    ).then(() => {
      message.success('操作成功');
      getList();
    });
  };

  const listSort = (record: any, current: number, offset: number) => {
    let data: any = {
      recommend_id: record.recommend_id,
      offset: offset,
      current,
    };
    api.audioArticleSort(data).then((res: any) => {
      message.success('操作成功');
      getList();
    });
  };

  const changeOrder = (record: any, i: any) => {
    let position = i;
    const WAIT_TIME = 1000;
    const positionChange = (v: any) => {
      position = v;
    };

    Modal.confirm({
      title: (
        <p
          style={{
            whiteSpace: 'nowrap',
            overflow: 'hidden',
            textOverflow: 'ellipsis',
          }}
        >
          排序：{record.list_title}
        </p>
      ),
      icon: <Icon type="info-circle" />,
      content: (
        <div>
          <span>请输入位置：</span>
          <InputNumber min={1} defaultValue={position} onChange={positionChange} />
        </div>
      ),
      onOk: (closeFunc: Function) => {
        if (!position) {
          message.error('请填写位置');
          return;
        }

        dispatch(setConfig({ loading: true }));
        api
          .audioArticleMovePosition({ recommend_id: record.recommend_id, position: position })
          .then((res: any) => {
            message.success('操作成功');
            getList();
            dispatch(setConfig({ loading: false }));
            closeFunc();
          })
          .catch(() => {
            dispatch(setConfig({ loading: false }));
          });
      },
    });
  };

  const getOperateLog = (record: any) => {
    run(
      releaseListApi.getRecommendOperateLog,
      { id: record.recommend_id, type: 'AudioHomeRecommend' },
      true
    )
      .then((r: any) => {
        setLogs({
          visible: true,
          logs: r.data.logs,
          title: record.list_title,
          key: Date.now(),
        });
      })
      .catch();
  };

  const titleClick = (record: any) => {
    // setPreview({
    //   visible: true,
    //   skey: Date.now(),
    //   data: record
    // })
    if (!!record.url) {
      window.open(record.url, '_blank');
    }
  };

  const getColumns = () => {
    let values = [
      {
        title: '序号',
        key: 'seq',
        render: (text: any, record: any, i: number) => {
          return <span>{getSeq(i)}</span>;
        },
        width: 70,
      },
      {
        title: 'ID',
        dataIndex: 'id',
        width: 80,
      },
      {
        title: '频道',
        dataIndex: 'channel_name',
        width: 120,
      },
      {
        title: '稿件标题',
        key: 'list_title',
        dataIndex: 'list_title',
        render: (text: any, record: any) => (
          // onClick={this.toMlf.bind(this, record.id, 'mlf_detail_url')}
          <Tooltip title={text}>
            <a
              onClick={() => titleClick(record)}
              style={{
                display: '-webkit-box',
                textOverflow: 'ellipsis',
                WebkitLineClamp: 3,
                WebkitBoxOrient: 'vertical',
                overflow: 'hidden',
              }}
            >
              {text}
            </a>
          </Tooltip>
        ),
      },
      {
        title: '浏览量',
        dataIndex: 'fake_count',
        width: 120,
      },
      {
        title: '签发时间',
        dataIndex: 'published_at',
        width: 100,
        render: (text: any, record: any) => {
          return (
            <div style={{ whiteSpace: 'normal' }}>
              <div>{moment(text).format('YYYY-MM-DD')}</div>
              <div>{moment(text).format('HH:mm:ss')}</div>
            </div>
          );
        },
      },
      {
        title: '操作人',
        dataIndex: 'updated_by',
        width: 120,
      },
      {
        title: <Tooltip title="点击时间查看操作日志">操作时间</Tooltip>,
        dataIndex: 'updated_at',
        width: 100,
        render: (text: any, record: any) => {
          return (
            <a style={{ whiteSpace: 'normal' }} onClick={() => getOperateLog(record)}>
              <span>{moment(text).format('YYYY-MM-DD')}</span>
              <br></br>
              <span>{moment(text).format('HH:mm:ss')}</span>
            </a>
          );
        },
      },
      {
        title: '操作',
        key: 'op',
        render: (text: any, record: any, i: number) => (
          <span>
            <PermA perm="audio_home:sort" onClick={() => changeOrder(record, getSeq(i))}>
              排序
            </PermA>
            <Divider type="vertical" />
            <PermA perm="audio_home:delete" onClick={() => deleteRecord(record)}>
              移除
            </PermA>
          </span>
        ),
        width: 140,
      },
    ];

    if (!filter.keyword) {
      values.splice(0, 0, {
        title: '排序',
        key: 'sort',
        render: (text: any, record: any, i: number) => {
          const pos = getSeq(i);
          // if (pos < 4) {
          return (
            <OrderColumn
              perm="audio_home:sort"
              start={1}
              pos={pos}
              end={total}
              // disableUp={record.status != 1}
              // disableDown={record.status != 1}
              onUp={() => listSort(record, pos, 1)}
              onDown={() => listSort(record, pos, -1)}
            />
          );
          // } else {
          //   return null
          // }
        },
        width: 70,
      });
    }
    return values;
  };

  useEffect(() => {
    // getList({ current: 1, size: 10 });
    setMenuHook(dispatch, props);
  }, []);

  useEffect(() => {
    getList({ current: 1, size: defaultSize });
  }, [f]);

  const onChangeType = (e: any) => {
    setFilter((of: any) => {
      return {
        ...of,
        type: e.target.value,
        search_type: 1,
        keyword: '',
      };
    });
    setSearch({
      search_type: 1,
      keyword: '',
    });

    // let { pathname: path, search } = history.location
    // const index = search.lastIndexOf('&type')
    // if (index >= 0) {
    //   search = search.substring(0, index)
    // }
    // path = `${path}${search}&type=${e.target.value}`
    // history.replace(path)
    // setFilter({
    //   ...filter,
    //   type: e.target.value
    // })
  };

  const searchInputChange = (e: any) => {
    setSearch({
      ...search,
      search_type: e,
    });
  };

  const addRecord = () => {
    // addRef.current.showModal()
    setDrawer({
      key: Date.now(),
      visible: true,
      record: null,
    });
  };

  const handleAddArticleOk = () => {
    getList({ current: 1, size: defaultSize });
  };

  const handleKey = (e: { which: number }) => {
    if (e.which === 13) {
      setFilter({
        ...filter,
        keyword: search.keyword,
        search_type: search.search_type,
      });
    }
  };

  const editStyle = () => {
    api.getGptStyleDetail({}).then((res: any) => {
      setStyleDrawer({
        visible: true,
        record: {
          ...(res?.data?.detail || {}),
          color: res?.data?.detail?.ref_ids?.split(','),
        },
        key: Date.now(),
      });
    });
  };

  const showPaperConfig = () => {
    let value = false;
    const valueChange = (v: any) => {
      value = v;
      modal.update({
        content: (
          <>
            <span>展示早晚报模块</span>&nbsp;&nbsp;&nbsp;
            <Switch checked={value} onChange={valueChange}></Switch>
          </>
        ),
      });
    };

    var modal = Modal.confirm({
      title: '早晚报配置',
      content: (
        <>
          <span>展示早晚报模块</span>&nbsp;&nbsp;&nbsp;
          <Switch checked={value} onChange={valueChange}></Switch>
        </>
      ),
      onOk: (destroy: Function) => {
        const { permissions } = store.getState().session;
        const disabled = permissions.indexOf('web_feature:audio_zwb_switch') === -1;
        if (disabled) {
          message.error('没有权限');
          destroy();
          return;
        }

        run(
          api.updateAudioZwbSwitch,
          {
            feature: 'audio_zwb_switch',
            status: value,
          },
          true
        ).then(() => {
          message.success('操作成功');
        });
        destroy();
      },
    });

    api
      .getAudioZwbSwitch()
      .then((data: any) => {
        valueChange(data.data?.switch);
      })
      .catch((e) => {});
  };

  const publish = () => {
    if (records.length < 20) {
      message.error('请至少选择20条数据');
      return;
    }
    const ids = records
      .slice(0, 20)
      .map((v: any) => v.id)
      .join(',');

    Modal.confirm({
      title: '确定发布前20条稿件吗？',
      onOk: () => {
        run(api.rankingPublish, { data_ids: ids, type: filter.type }, true).then(() => {
          message.success('操作成功');

          setFilter({
            ...filter,
            status: 0,
          });
        });
      },
    });
  };

  const onSubmitEnd = (v: any) => {
    if (filter.type != v) {
      setFilter({
        ...filter,
        type: v,
      });
    } else {
      getList({ current: current, size: defaultSize });
    }

    setDrawer({ ...drawer, visible: false });
  };

  const addArticle = () => {
    addRef.current.showModal();
  };

  const addColumn = () => {
    run(api.getAudioPlazaColumn, {}, true).then((res: any) => {
      setAddColumnState({
        visible: true,
        key: Date.now(),
        column_list: res.data?.column_list,
      });
    });
  };

  const editRedpacket = () => {
    run(api.getAudioPlazaRedpacket, {}, true).then((res: any) => {
      setRedpacketState({
        visible: true,
        key: Date.now(),
        record: res.data?.redpacket_info,
      });
    });
  };

  const showBroadcastDrawer = () => {
    run(api.getBroadcast, {}, true).then((res: any) => {
      setBroadcastDrawerState({
        visible: true,
        key: Date.now(),
        record: res.data?.recommend,
      });
    });
  };

  return (
    <>
      <Row className="layout-infobar">
        <Col span={12}>
          <PermButton perm="" style={{ marginRight: 8 }} onClick={addColumn}>
            精选地方号
          </PermButton>

          <PermButton
            // perm="gpt_question:style_detail"
            perm=""
            style={{ marginRight: 8 }}
            onClick={editRedpacket}
          >
            红包活动
          </PermButton>

          <PermButton
            // perm="gpt_question:style_detail"
            perm=""
            style={{ marginRight: 8 }}
            onClick={showBroadcastDrawer}
          >
            听广播
          </PermButton>

          <PermButton
            perm=""
            onClick={() => {
              showPaperConfig();
            }}
          >
            早晚报
          </PermButton>
        </Col>
        <Col span={12} className="layout-breadcrumb">
          {getCrumb(props.breadCrumb)}
        </Col>
      </Row>
      <div className="component-content">
        <Row style={{ marginBottom: 16 }}>
          <Col span={12}>
            <strong>大家都在听：</strong>
            <PermButton perm="audio_home:create" style={{ marginRight: 8 }} onClick={addArticle}>
              <Icon type="plus-circle" /> 添加稿件
            </PermButton>
          </Col>

          <Col span={12} style={{ textAlign: 'right' }}>
            <Select
              value={search.search_type}
              style={{ width: 150, marginRight: 8, marginLeft: 8 }}
              onChange={searchInputChange}
            >
              <Select.Option value={1}>稿件ID</Select.Option>
              <Select.Option value={2}>稿件标题</Select.Option>
            </Select>
            <Input
              style={{ width: 160, marginRight: 8 }}
              onKeyPress={handleKey}
              value={search.keyword}
              placeholder="请输入搜索内容"
              onChange={(e) =>
                setSearch({
                  ...search,
                  keyword: e.target.value,
                })
              }
            />
            <Button onClick={() => handleKey({ which: 13 })}>
              <Icon type="search" />
              搜索
            </Button>
          </Col>
        </Row>
        <Table
          func="getAudioPlazaArticleList"
          index="audio_article_list"
          filter={f}
          columns={getColumns()}
          rowKey="recommend_id"
          pagination={true}
        />

        <Drawer
          visible={drawer.visible}
          skey={drawer.key}
          title={`${!!drawer.record?.id ? '编辑' : '新建'}问答`}
          onClose={() => setDrawer({ ...drawer, visible: false })}
          onOk={() => formRef.current.doSubmit()}
        >
          <AssistantForm
            onEnd={onSubmitEnd}
            formContent={drawer.record}
            // eslint-disable-next-line no-return-assign
            wrappedComponentRef={(instance: any) => (formRef.current = instance)}
          />
        </Drawer>

        <AddAudioColumnModal
          {...addColumnState}
          onCancel={() => setAddColumnState({ ...addColumnState, visible: false })}
          onOk={() => setAddColumnState({ ...addColumnState, visible: false })}
        ></AddAudioColumnModal>

        <AudioRedPacketModal
          {...redpacketState}
          onCancel={() => setRedpacketState({ ...redpacketState, visible: false })}
          onOk={() => setRedpacketState({ ...redpacketState, visible: false })}
        ></AudioRedPacketModal>

        <AddAudioArticleModal
          maxPosition={total + 1}
          wrappedComponentRef={addRef}
          onOk={() => {
            getList();
          }}
        ></AddAudioArticleModal>

        <AudioBroadcastDrawer
          {...broadcastDrawerState}
          onCancel={() => setBroadcastDrawerState({ ...broadcastDrawerState, visible: false })}
          onOk={() => setBroadcastDrawerState({ ...broadcastDrawerState, visible: false })}
        ></AudioBroadcastDrawer>

        <Modal
          visible={logs.visible}
          title="操作日志"
          key={logs.key}
          cancelText={null}
          onCancel={() => setLogs({ visible: false })}
          onOk={() => setLogs({ visible: false })}
        >
          <div>
            <p
              style={{
                whiteSpace: 'nowrap',
                overflow: 'hidden',
                textOverflow: 'ellipsis',
              }}
            >
              {logs.title}
            </p>
            <Timeline>
              {logs?.logs?.map((v: any, i: number) => [
                <Timeline.Item className="timeline-dot-big" data-show={v.date} key={`time${i}`}>
                  &nbsp;
                </Timeline.Item>,
                v.actions.map((action: any, index: number) => (
                  <Timeline.Item
                    className="timeline-dot"
                    data-show={action.time}
                    key={`time${i}-action${index}`}
                  >
                    {action.user}&emsp;&emsp;{action.action}&emsp;&emsp;
                  </Timeline.Item>
                )),
              ])}
            </Timeline>
          </div>
        </Modal>
      </div>
    </>
  );
}
