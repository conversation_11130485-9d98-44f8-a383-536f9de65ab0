import { opApi, communityApi } from '@app/api';
import { ImageUploader, Drawer } from '@app/components/common';
import { Checkbox, DatePicker, Form, Input, message, Modal, Select, Spin } from 'antd';
import moment from 'moment';
import React, { useEffect, useState } from 'react';
import _, { set } from 'lodash';

export const AvatarFormDrawer = (props: any) => {
  const {
    form: { getFieldDecorator, setFieldsValue },
  } = props;
  const [loading, setLoading] = useState(false);
  const [optionData, setOptionData] = useState([]);
  const [selectedUsers, setSelectedUsers] = useState([]);

  const handleOkClick = () => {
    props.form.validateFieldsAndScroll((err: any, values: any) => {
      if (!err) {
        const { title, pic_url, ref_ids } = values;
        const body: any = { title, pic_url };
        body.ref_ids = ref_ids[0].format('YYYY-MM-DD');
        body.ref_ids2 = ref_ids[1].format('YYYY-MM-DD');
        body.ref_extensions = JSON.stringify(
          selectedUsers.map((item: any) => {
            return {
              id: item.id,
              nick_name: item.nick_name,
              chao_id: item.chao_id,
            };
          })
        );

        let request = opApi.saveHudongAvatar
        if (props.formContent) {
          body.id = props.formContent.id;
          request = opApi.editHudongAvatar
        }

        setLoading(true);
        request(body)
          .then(() => {
            message.success('操作成功');
            setLoading(false);
            props.onOk();
          })
          .catch(() => {
            setLoading(false);
          });
      } else {
        message.error('请检查表单内容');
      }
    });
  };

  const formLayout = {
    labelCol: { span: 4 },
    wrapperCol: { span: 18 },
  };

  useEffect(() => {
    if (props.visible) {
      const initUsers = props.formContent?.ref_extensions && JSON.parse(props.formContent?.ref_extensions)
      setOptionData(initUsers || [])
      setSelectedUsers(initUsers || [])
    }
  }, [props.visible]);

  const handleAccountSearch = _.debounce((val: any) => {
    if (!val) {
      setOptionData([]);
      return;
    }
    // type: 1, current: 1, size: 50, biz_type: props.formContent?.biz_type,
    communityApi
      .recommendAccount_Search({ keyword: val })
      .then((res) => {
        setOptionData(res?.data?.list || []);
      })
      .catch(() => {});
  }, 500);
  const initUsers = props.formContent?.ref_extensions && JSON.parse(props.formContent?.ref_extensions)
  const initUserIds = initUsers?.map((item: any) => {
    return item.id
  })
  return (
    <Drawer
      title={!!props.formContent ? '编辑' : '添加'}
      visible={props.visible}
      skey={props.skey}
      onClose={props.onClose}
      onOk={handleOkClick}
      okText="确定"
    >
      <Spin tip="正在加载..." spinning={loading}>
        <Form {...formLayout}>
          <Form.Item label="名称">
            {getFieldDecorator('title', {
              initialValue: props.formContent?.title,
              rules: [
                {
                  required: true,
                  whitespace: true,
                  message: '请输入名称',
                },
              ],
            })(<Input placeholder="最多输入10字" maxLength={10}></Input>)}
          </Form.Item>

          <Form.Item label="头像挂件" extra="支持.jpg .jpeg .png等格式，比例为 1:1">
            {getFieldDecorator('pic_url', {
              initialValue: props.formContent?.pic_url,
              rules: [
                {
                  required: true,
                  message: '请选择头像挂件',
                },
              ],
            })(<ImageUploader ratio={1}></ImageUploader>)}
          </Form.Item>

          <Form.Item label="有效期">
            {getFieldDecorator('ref_ids', {
              initialValue: !!props.formContent
                ? [moment(props.formContent.ref_ids), moment(props.formContent.ref_ids2)]
                : [],
              rules: [
                {
                  required: true,
                  message: '请选择有效期',
                },
              ],
            })(<DatePicker.RangePicker format="YYYY-MM-DD" />)}
          </Form.Item>
            {/* {console.log('props.formContent.ref_extensionsprops.formContent.ref_extensions', typeof props.formContent?.ref_extensions)} */}
          <Form.Item label="选择用户">
            {getFieldDecorator('ref_extensions', {
              initialValue: initUserIds || [],
              rules: [{ required: true, message: '请选择用户' }],
            })(
              <Select
                mode="multiple"
                style={{ width: 400, marginRight: 8 }}
                placeholder="输入用户昵称或用户ID"
                onSearch={(v) => handleAccountSearch(v)}
                showSearch
                filterOption={false}
                allowClear
                optionLabelProp="label"
                onChange={(val, options: any) => {
                  if (options?.length <= 50) {
                    setSelectedUsers(options.map((d: any) => d.props.obj));
                  } else {
                    message.error('最多选择50个用户')
                    setTimeout(() => {
                      setFieldsValue({ ref_extensions: selectedUsers.map((d: any) => d.id) });  
                    }, 250);
                  }
                }}
              >
                {optionData.map((d: any) => (
                  <Select.Option key={d.id} value={d.id} label={d.nick_name} obj={d}>
                    {d.nick_name} | 用户ID：{d.chao_id}
                  </Select.Option>
                ))}
              </Select>
            )}
          </Form.Item>
        </Form>
      </Spin>
    </Drawer>
  );
};

export default Form.create<any>({ name: 'AvatarFormDrawer' })(AvatarFormDrawer);
