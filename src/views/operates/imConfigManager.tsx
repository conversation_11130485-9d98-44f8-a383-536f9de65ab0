import React, { useState, useEffect, useCallback, useMemo, useRef } from 'react';
import { useStore, useDispatch, useSelector } from 'react-redux';
import { useHistory } from 'react-router-dom';
import { Table, A, OrderColumn, Drawer } from '@components/common';
import { CommonObject } from '@app/types';
import { getCrumb, setMenuHook } from '@app/utils/utils';
import {
  Row,
  Col,
  Input,
  Divider,
  Button,
  Select,
  Icon,
  Modal,
  message,
  InputNumber,
  Tooltip,
  Timeline,
  Switch,
} from 'antd';
import { getTableList } from '@app/action/tableList';
import { opApi as api, communityApi, releaseListApi } from '@app/api';
import useXHR from '@utils/useXhr';
import { PermA, PermButton } from '@components/permItems';
import { setConfig } from '@app/action/config';
import moment from 'moment';
import '@components/business/styles/business.scss';
import _ from 'lodash';

const defaultSize = 10;
export default function ImConfigManager(props: any) {
  const [accountOptions, setAccountOptions] = useState([]);

  const [filter, setFilter] = useState<any>({});

  const dispatch = useDispatch();
  const store = useStore();
  const history = useHistory();
  const {
    total,
    current,
    size,
    records = [],
    allData,
  } = useSelector((state: any) => state.tableList);
  const { loading, run } = useXHR();
  const addRef = useRef<any>(null);
  const [drawer, setDrawer] = useState({
    visible: false,
    key: 0,
    record: null,
  });

  const [logs, setLogs] = useState<any>({
    visible: false,
    logs: [],
    title: '',
  });

  const getSeq = (i: number) => (current - 1) * size + i + 1;

  const getList = (goToFirstPage = false, newFilter = filter) => {
    let cur = goToFirstPage ? 1 : current;

    dispatch(getTableList('getIMWhiteList', 'list', { current: cur, size, ...newFilter }));
  };

  const editRecord = (record: any) => {
    run(api.gptQuestionDetail, { id: record.id }, true)
      .then((data: any) => {
        console.log('gptQuestionDetail', data);
        const {
          data: { detail, article_list },
        } = data;
        const body = {
          ...detail,
          articles: article_list,
        };

        setDrawer({
          visible: true,
          key: Date.now(),
          record: body,
        });
      })
      .catch((e: any) => {});
  };
  const deleteRecord = (record: any) => {
    Modal.confirm({
      title: `确定要删除吗？`,
      onOk: () => {
        run(api.deleteAudioPlazaArticle, { recommend_id: record.recommend_id }, true).then(() => {
          message.success('操作成功');
          getList();
        });
      },
    });
  };

  const updateRecordStatus = (record: any) => {
    run(
      api.updateGPTQuestionStatus,
      { id: record.id, status: record.status == 1 ? 0 : 1 },
      true
    ).then(() => {
      message.success('操作成功');
      getList();
    });
  };

  const listSort = (record: any, current: number, offset: number) => {
    let data: any = {
      recommend_id: record.recommend_id,
      offset: offset,
      current,
    };
    api.audioArticleSort(data).then((res: any) => {
      message.success('操作成功');
      getList();
    });
  };

  const changeOrder = (record: any, i: any) => {
    let position = i;
    const WAIT_TIME = 1000;
    const positionChange = (v: any) => {
      position = v;
    };

    Modal.confirm({
      title: (
        <p
          style={{
            whiteSpace: 'nowrap',
            overflow: 'hidden',
            textOverflow: 'ellipsis',
          }}
        >
          排序：{record.list_title}
        </p>
      ),
      icon: <Icon type="info-circle" />,
      content: (
        <div>
          <span>请输入位置：</span>
          <InputNumber min={1} defaultValue={position} onChange={positionChange} />
        </div>
      ),
      onOk: (closeFunc: Function) => {
        if (!position) {
          message.error('请填写位置');
          return;
        }

        dispatch(setConfig({ loading: true }));
        api
          .audioArticleMovePosition({ recommend_id: record.recommend_id, position: position })
          .then((res: any) => {
            message.success('操作成功');
            getList();
            dispatch(setConfig({ loading: false }));
            closeFunc();
          })
          .catch(() => {
            dispatch(setConfig({ loading: false }));
          });
      },
    });
  };

  const getOperateLog = (record: any) => {
    run(
      releaseListApi.getRecommendOperateLog,
      { id: record.recommend_id, type: 'AudioHomeRecommend' },
      true
    )
      .then((r: any) => {
        setLogs({
          visible: true,
          logs: r.data.logs,
          title: record.list_title,
          key: Date.now(),
        });
      })
      .catch();
  };

  const titleClick = (record: any) => {
    // setPreview({
    //   visible: true,
    //   skey: Date.now(),
    //   data: record
    // })
    if (!!record.url) {
      window.open(record.url, '_blank');
    }
  };

  const getColumns = () => {
    let values = [
      {
        title: '账号昵称',
        dataIndex: 'account_name',
        width: 150,
      },
      {
        title: '用户ID',
        dataIndex: 'chao_id',
        width: 150,
      },
      {
        title: '操作人',
        dataIndex: 'updated_by',
        width: 150,
      },
      {
        title: '操作时间',
        dataIndex: 'updated_at',
        width: 160,
        render: (text: any, record: any) => moment(text).format('YYYY-MM-DD HH:mm:ss'),
      },
      {
        title: '操作',
        key: 'op',
        render: (text: any, record: any, i: number) => (
          <span>
            <PermA perm="im_whitelist:delete" onClick={() => deleteRecord(record)}>
              删除
            </PermA>
          </span>
        ),
        width: 140,
      },
    ];
    return values;
  };

  useEffect(() => {
    // getList({ current: 1, size: 10 });
    setMenuHook(dispatch, props);
    getList(true);
  }, []);

  const addRecord = () => {
    // addRef.current.showModal()
    setDrawer({
      key: Date.now(),
      visible: true,
      record: null,
    });
  };

  const handleAddArticleOk = () => {
    getList({ current: 1, size: defaultSize });
  };

  const handleKey = (e: { which: number }) => {
    if (e.which === 13) {
      setFilter({
        ...filter,
        keyword: search.search_type == 1 ? search.keyword : search.user_id,
        search_type: search.search_type,
      });
    }
  };

  const editStyle = () => {
    api.getGptStyleDetail({}).then((res: any) => {
      setStyleDrawer({
        visible: true,
        record: {
          ...(res?.data?.detail || {}),
          color: res?.data?.detail?.ref_ids?.split(','),
        },
        key: Date.now(),
      });
    });
  };

  const showPaperConfig = () => {
    let value = false;
    const valueChange = (v: any) => {
      value = v;
      modal.update({
        content: (
          <>
            <span>展示早晚报模块</span>&nbsp;&nbsp;&nbsp;
            <Switch checked={value} onChange={valueChange}></Switch>
          </>
        ),
      });
    };

    var modal = Modal.confirm({
      title: '早晚报配置',
      content: (
        <>
          <span>展示早晚报模块</span>&nbsp;&nbsp;&nbsp;
          <Switch checked={value} onChange={valueChange}></Switch>
        </>
      ),
      onOk: (destroy: Function) => {
        const { permissions } = store.getState().session;
        const disabled = permissions.indexOf('web_feature:audio_zwb_switch') === -1;
        if (disabled) {
          message.error('没有权限');
          destroy();
          return;
        }

        run(
          api.updateAudioZwbSwitch,
          {
            feature: 'audio_zwb_switch',
            status: value,
          },
          true
        ).then(() => {
          message.success('操作成功');
        });
        destroy();
      },
    });

    api
      .getAudioZwbSwitch()
      .then((data: any) => {
        valueChange(data.data?.switch);
      })
      .catch((e) => {});
  };

  const publish = () => {
    if (records.length < 20) {
      message.error('请至少选择20条数据');
      return;
    }
    const ids = records
      .slice(0, 20)
      .map((v: any) => v.id)
      .join(',');

    Modal.confirm({
      title: '确定发布前20条稿件吗？',
      onOk: () => {
        run(api.rankingPublish, { data_ids: ids, type: filter.type }, true).then(() => {
          message.success('操作成功');

          setFilter({
            ...filter,
            status: 0,
          });
        });
      },
    });
  };

  const onSubmitEnd = (v: any) => {
    if (filter.type != v) {
      setFilter({
        ...filter,
        type: v,
      });
    } else {
      getList({ current: current, size: defaultSize });
    }

    setDrawer({ ...drawer, visible: false });
  };

  const addArticle = () => {
    addRef.current.showModal();
  };

  const addColumn = () => {
    run(api.getAudioPlazaColumn, {}, true).then((res: any) => {
      setAddColumnState({
        visible: true,
        key: Date.now(),
        column_list: res.data?.column_list,
      });
    });
  };

  const editRedpacket = () => {
    run(api.getAudioPlazaRedpacket, {}, true).then((res: any) => {
      setRedpacketState({
        visible: true,
        key: Date.now(),
        record: res.data?.redpacket_info,
      });
    });
  };

  const showBroadcastDrawer = () => {
    run(api.getBroadcast, {}, true).then((res: any) => {
      setBroadcastDrawerState({
        visible: true,
        key: Date.now(),
        record: res.data?.recommend,
      });
    });
  };

  // 时间选择
  const timeChange = (dates: any) => {
    if (dates.length === 0) {
      const { start_time, end_time, ...newFilter } = filter;
      setFilter({ ...newFilter });
    } else {
      setFilter({
        ...filter,
        start_time: dates[0],
        end_time: dates[1],
      });
    }
  };

  const handleAccountSearch = _.debounce((val: any) => {
    if (!val) {
      setAccountOptions([]);
      return;
    }
    // type: 1, current: 1, size: 50, biz_type: props.formContent?.biz_type,
    communityApi
      .recommendAccount_Search({ keyword: val })
      .then((res) => {
        setAccountOptions(res?.data?.list || []);
      })
      .catch(() => {});
  }, 500);

  return (
    <>
      <Row className="layout-infobar">
        <Col span={12}>
          <Button onClick={() => history.goBack()}>
            <Icon type="left-circle" />
            返回
          </Button>

          <PermButton
            perm="im_whitelist:create"
            style={{ marginLeft: 8 }}
            onClick={editRedpacket}
          >
            添加放行规则
          </PermButton>
        </Col>
        <Col span={12} className="layout-breadcrumb">
          {getCrumb(props.breadCrumb)}
        </Col>
      </Row>
      <div className="component-content">
        <Table
          func="getIMWhiteList"
          index="list"
          filter={filter}
          columns={getColumns()}
          rowKey="id"
          pagination={true}
        />
      </div>
    </>
  );
}
