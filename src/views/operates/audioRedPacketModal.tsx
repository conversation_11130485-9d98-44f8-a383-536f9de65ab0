import NewNewsSearchAndInput from '@app/components/common/newNewsSearchAndInput';
import {
  Button,
  Form,
  Icon,
  Input,
  InputNumber,
  Modal,
  Spin,
  Switch,
  Tooltip,
  message,
} from 'antd';
import React, { forwardRef, useState } from 'react';

import { opApi } from '@app/api';
import { Drawer } from '@app/components/common';

const AudioRedPacketModal = (props: any, ref: any) => {
  const [loading, setLoading] = useState(false);
  const { getFieldDecorator } = props.form;

  const formLayout = {
    labelCol: { span: 5 },
    wrapperCol: { span: 19 },
  };

  const handleSubmit = () => {
    props.form.validateFieldsAndScroll((err: any, values: any) => {
      if (!err) {
        setLoading(true);
        const parmas = {
          ...values,
          redpacket_rule: values.redpacket_rule || '',
          redpacket_info_list: JSON.stringify(
            values.redpacket_info_list?.map((v: any, index: number) => {
              return {
                ...v,
                redpacket_id: `hongbao_${index + 1}`,
              };
            })
          ),
        };
        opApi
          .addAudioPlazaRedpacket(parmas)
          .then((res: any) => {
            message.success('添加成功');
            setLoading(false);
            props.onOk && props.onOk();
          })
          .catch(() => {
            // message.error('添加失败');
            setLoading(false);
          });
      } else {
        message.error('请检查表单内容');
        setLoading(false);
      }
    });
  };
  const redpacket_info_list =
    props.record?.redpacket_info_list?.length > 0 && JSON.parse(props.record?.redpacket_info_list);
  return (
    <Drawer
      // width={600}
      visible={props.visible}
      title="红包活动"
      skey={props.key}
      onClose={props.onCancel}
      onOk={handleSubmit}
      maskClosable={false}
      // destroyOnClose={true}
      // confirmLoading={loading}
      okPerm={'audio_home:redpacket_save'}
    >
      <Spin spinning={loading}>
        <Form {...formLayout} onSubmit={handleSubmit}>
          <Form.Item label="红包开关">
            {getFieldDecorator('redpacket_switch', {
              valuePropName: 'checked',
              initialValue: props.record?.redpacket_switch,
            })(<Switch checkedChildren="开" unCheckedChildren="关"></Switch>)}
          </Form.Item>
          {/* <Form.Item label="活动规则" extra="最多1000字">
            {getFieldDecorator('redpacket_rule', {
              initialValue: props.record?.redpacket_rule || '',
            })(
              <Input.TextArea
                rows={4}
                placeholder="请输入活动规则"
                maxLength={1000}
              ></Input.TextArea>
            )}
          </Form.Item>
 */}
          {(redpacket_info_list || [1, 2, 3]).map((v: any, index: number) => {
            return (
              <div key={index}>
                <h3>红包{index + 1}</h3>
                <Form.Item label="红包链接">
                  {getFieldDecorator(`redpacket_info_list[${index}].redpacket_url`, {
                    initialValue: redpacket_info_list?.[index]?.redpacket_url,
                    rules: [
                      {
                        required: true,
                        message: '请输入云点红包链接',
                        whitespace: true,
                      },
                      {
                        pattern: /^https?:\/\//,
                        message: '请输入正确的链接格式',
                      },
                    ],
                  })(
                    <Input
                      style={{
                        width: '400px',
                      }}
                      placeholder="请输入云点红包链接"
                    ></Input>
                  )}
                </Form.Item>

                <Form.Item label="听音频时长">
                  {getFieldDecorator(`redpacket_info_list[${index}].single_redpacket_time`, {
                    initialValue: redpacket_info_list?.[index]?.single_redpacket_time,
                    rules: [
                      {
                        required: true,
                        message: '请输入听音频时长',
                      },
                    ],
                  })(
                    <InputNumber
                      style={{
                        width: '400px',
                        marginRight: 10,
                      }}
                      placeholder="请输入听音频时长"
                      precision={0}
                    ></InputNumber>
                  )}
                  <span>分钟</span>
                </Form.Item>
              </div>
            );
          })}
        </Form>
      </Spin>
    </Drawer>
  );
};

export default Form.create<any>({ name: 'AudioRedPacketModal' })(
  forwardRef<any, any>(AudioRedPacketModal)
);
