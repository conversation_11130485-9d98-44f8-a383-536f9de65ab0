import React, { useState } from 'react';
import { BaseComponent, Table, Drawer, A, CKEditor } from '@components/common';
import { setConfig } from '@app/action/config';
import { ITableProps, IBaseProps, CommonObject } from '@app/types';
import CommentPresuppositionForm from '@components/business/CommentPresuppositionForm';
import CommentTipsForm from '@components/business/CommentTipsForm';
import {
  Button,
  Col,
  Icon,
  Form,
  Input,
  Select,
  DatePicker,
  Menu,
  Dropdown,
  AutoComplete,
  Collapse,
  message,
  Modal,
  Row,
  Divider,
  Radio,
  Tooltip,
} from 'antd';
import { requirePerm } from '@utils/utils';
import { withRouter } from 'react-router';
import { connectTable as connect } from '@utils/connect';
import { RadioChangeEvent } from 'antd/es/radio';
import moment from 'moment';
import { userApi as api } from '@app/api';
import { cloneDeep } from 'lodash';
import { ProposalRecord, ProposalAllData } from './operates';
import CommonAddUserModal from '@app/components/common/commonAddUserModal';

const InputGroup = Input.Group;
const { Panel } = Collapse;
const { confirm } = Modal;

enum TABSTATE {
  COMMENTPRESET = 1,
  COMMENTTIPS,
  BLACKLIST,
}

type State = {
  type: TABSTATE;
  commentForm: {
    show: boolean;
    key: number;
    id?: number;
    title: string;
    data: any;
  };
  commentTipsForm: {
    show: boolean;
    key: number;
    id?: number;
    title: string;
    data: any;
  };
  blacklistDialog: {
    visible: boolean;
    key: any;
  };
  searchPreset: {
    begin: any;
    end: any;
    state: number | string;
    type: string;
    plan_name: string;
    content: string;
    channel_article_id: string | number;
    channel_article_title: string;
    text: string;
  };
  searchTips: {
    begin: any;
    end: any;
    state: number | string;
    type: string;
    plan_name: string;
    content: string;
    channel_article_id: string | number;
    channel_article_title: string;
    text: string;
  };
};

type Props = IBaseProps<ITableProps<ProposalRecord, ProposalAllData>>;

class CommentOperate extends BaseComponent<ITableProps<ProposalRecord, ProposalAllData>, State> {
  constructor(props: Props) {
    console.log(props);
    super(props);
    this.state = {
      type: TABSTATE.COMMENTTIPS,
      // 评论form窗口
      commentForm: {
        show: false,
        key: Date.now(),
        id: 0,
        title: '评论预设',
        data: {},
      },
      commentTipsForm: {
        show: false,
        key: Date.now(),
        id: 0,
        title: '评论提示',
        data: {},
      },
      searchPreset: {
        begin: '',
        end: '',
        state: '',
        type: 'plan_name',
        plan_name: '',
        content: '',
        channel_article_id: '',
        channel_article_title: '',
        text: '',
      },
      searchTips: {
        begin: '',
        end: '',
        state: '',
        type: 'plan_name',
        plan_name: '',
        content: '',
        channel_article_id: '',
        channel_article_title: '',
        text: '',
      },
      blacklistDialog: {
        visible: false,
        key: 'blacklistDialog',
      },
    };
  }

  componentDidMount() {
    this.setMenu();
    this.getData1();
  }

  handleTypeChange = (e: RadioChangeEvent) => {
    if (e.target.value === this.state.type) {
      return;
    }
    this.setState(
      {
        type: e.target.value,
      },
      () => {
        // 切换
        if (e.target.value === TABSTATE.COMMENTPRESET) {
          this.getData();
        } else if (e.target.value === TABSTATE.COMMENTTIPS) {
          this.getData1();
        } else if (e.target.value === TABSTATE.BLACKLIST) {
          this.getData2();
        }
      }
    );
  };

  getData = (filters = this.getFilters()) => {
    this.dispatchTable('getListCommentPreset', 'preset_list', {
      current: 1,
      size: 10,
      ...filters,
    });
  };

  getFilters = () => {
    const data: any = cloneDeep(this.state.searchPreset);
    if (data.text) {
      data[data.type] = data.text;
    }
    delete data.type;
    delete data.text;
    data.begin = data.begin ? data.begin : '';
    data.end = data.end ? data.end : '';
    return data;
  };

  getData1 = (filters = this.getFilters2()) => {
    this.dispatchTable('getListCommentTips', 'tips_list', {
      current: 1,
      size: 10,
      ...filters,
    });
  };

  getData2 = () => {
    this.dispatchTable('getListCommentTips', 'tips_list', {
      current: 1,
      size: 10,
    });
  };

  getFilters2 = () => {
    const data: any = cloneDeep(this.state.searchTips);
    if (data.text) {
      data[data.type] = data.text;
    }
    delete data.type;
    delete data.text;
    data.begin = data.begin ? data.begin : '';
    data.end = data.end ? data.end : '';
    return data;
  };

  // 评论 打开新建设置
  openDrawer = () => {
    this.setState({
      commentForm: {
        ...this.state.commentForm,
        show: true,
      },
    });
  };

  // 提示 打开新建设置
  openTipsDrawer = () => {
    this.setState({
      commentTipsForm: {
        ...this.state.commentTipsForm,
        show: true,
      },
    });
  };

  addBlacklist = () => {
    this.setState({
      blacklistDialog: {
        key: Date.now(),
        visible: true,
      },
    });
  };

  // 评论-关闭
  closeDrawer = () => {
    this.setState({
      commentForm: { ...this.state.commentForm, show: false, data: {} },
    });
  };

  // 提示关闭
  closeTipsDrawer = () => {
    this.setState({
      commentTipsForm: { ...this.state.commentTipsForm, show: false, data: {} },
    });
  };

  // 评论提交
  commentSubmitEnd = () => {
    this.closeDrawer();
    this.getData();
  };

  // 提示-提交
  commentTipsSubmitEnd = () => {
    this.closeTipsDrawer();
    this.getData1();
  };

  filters = (Obj: any) => {
    const data = cloneDeep(Obj);
    Object.keys(data).forEach((x) => {
      if (!data[x]) {
        delete data[x];
      }
    });
    return data;
  };

  commentPickerChange = (dates: any) => {
    const { searchPreset } = this.state;
    if (dates.length === 0) {
      this.setState({
        searchPreset: { ...searchPreset, begin: false, end: false },
      });
    } else {
      this.setState({
        searchPreset: {
          ...searchPreset,
          begin: dates[0].format('YYYY-MM-DD'),
          end: dates[1].format('YYYY-MM-DD'),
        },
      });
    }
  };

  commentStateSelectChange = (e: any) => {
    const { searchPreset } = this.state;
    console.log(e);
    this.setState({
      searchPreset: { ...searchPreset, state: e },
    });
  };

  commentTypeSelectChange = (e: any) => {
    const { searchPreset } = this.state;
    this.setState({
      searchPreset: { ...searchPreset, type: e },
    });
    console.log(searchPreset);
  };

  commentTextSelectChange = (e: any) => {
    const { searchPreset } = this.state;
    this.setState({
      searchPreset: { ...searchPreset, text: e.target.value },
    });
  };

  tipsPickerChange = (dates: any) => {
    if (dates.length === 0) {
      this.setState({
        searchTips: { ...this.state.searchTips, begin: false, end: false },
      });
    } else {
      this.setState({
        searchTips: {
          ...this.state.searchTips,
          begin: dates[0].format('YYYY-MM-DD'),
          end: dates[1].format('YYYY-MM-DD'),
        },
      });
    }
  };

  tipsStateSelectChange = (e: any) => {
    const { searchTips } = this.state;
    console.log(e);
    this.setState({
      searchTips: { ...searchTips, state: e },
    });
  };

  tipsTypeSelectChange = (e: any) => {
    const { searchTips } = this.state;
    this.setState({
      searchTips: { ...searchTips, type: e },
    });
  };

  tipsTextSelectChange = (e: any) => {
    const { searchTips } = this.state;
    this.setState({
      searchTips: { ...searchTips, text: e.target.value },
    });
  };

  getColumns = () => {
    const that = this;
    const getDropDown = (record: any) => {
      // 查看
      const seeComment = () => {
        const seeData = record;
        seeData.disabled = true;
        this.setState({
          commentForm: {
            ...this.state.commentForm,
            data: seeData,
            show: true,
          },
        });
      };
      // 编辑
      const editComment = () => {
        const seeData = record;
        seeData.disabled = false;
        this.setState({
          commentForm: {
            ...this.state.commentForm,
            data: seeData,
            show: true,
          },
        });
      };
      // 删除
      const deleteComment = () => {
        confirm({
          title: '确认删除吗？',
          content: `删除方案：${record.plan_name}`,
          onOk() {
            api.deleteCommentPreset({ id: record.id }).then((res) => {
              message.success('删除成功');
              that.getData();
            });
          },
          onCancel() {},
        });
      };
      // 上下架
      const enableComment = () => {
        api.updateTypeCommentPreset({ id: record.id, state: record.state ? 0 : 1 }).then((res) => {
          if (record.state) {
            message.success('下架成功');
          } else {
            message.success('上架成功');
          }
          that.getData();
        });
      };

      const menu = (
        <Menu>
          <Menu.Item onClick={seeComment}>查看</Menu.Item>
          {this.requirePerm(`comment_preset:update${record.state ? 'xx' : ''}`)(
            <Menu.Item onClick={editComment}>编辑</Menu.Item>
          )}
          {this.requirePerm('comment_preset:delete')(
            <Menu.Item onClick={deleteComment}>删除</Menu.Item>
          )}
          {this.requirePerm('comment_preset:update_displayed')(
            <Menu.Item onClick={enableComment}>{record.state ? '下线' : '启用'}</Menu.Item>
          )}
        </Menu>
      );
      return (
        <Dropdown overlay={menu}>
          <a className="ant-dropdown-link">
            操作 <Icon type="down" />
          </a>
        </Dropdown>
      );
    };

    const columns = [
      {
        title: '序号',
        key: 'id',
        render: (text: any, record: any, i: number) => <span>{i + 1}</span>,
        width: 50,
      },
      {
        title: '评论预设方案名',
        key: 'plan_name',
        dataIndex: 'plan_name',
        width: 120,
      },
      {
        title: '评论预设',
        key: 'content',
        dataIndex: 'content',
        render: (ls: any) => {
          try {
            const t = JSON.parse(ls);
            if (t.length > 0) {
              return (
                <Collapse>
                  <Panel header={`${t[0]}`} key="1">
                    {t.map((temp: any, i: number) => {
                      return (
                        <p key={Math.round(Math.random() * 1000)}>
                          {i + 1}. {`${temp}`}
                        </p>
                      );
                    })}
                  </Panel>
                </Collapse>
              );
            }
            return <span>{ls}</span>;
          } catch (error) {
            return <span>{ls}</span>;
          }
        },
        width: 200,
      },
      {
        title: '状态',
        key: 'state',
        dataIndex: 'state',
        render: (text: any) => <span>{text ? '启用' : '未启用'}</span>,
        width: 100,
      },
      {
        title: '创建时间',
        key: 'created_at',
        dataIndex: 'created_at',
        render: (text: any) => <span>{moment(text).format('YYYY-MM-DD HH:mm:ss')}</span>,
        width: 120,
      },
      {
        title: '创建人',
        key: 'creator',
        dataIndex: 'creator',
        width: 90,
      },

      {
        title: '操作',
        key: 'op',
        render: (text: any, record: any) => getDropDown(record),
        width: 70,
      },
    ];
    return columns;
  };

  getColumns2 = () => {
    const that = this;
    const getDropDown = (record: any) => {
      // 查看
      const seeTips = () => {
        const seeData = record;
        seeData.disabled = true;
        this.setState({
          commentTipsForm: {
            ...this.state.commentTipsForm,
            data: seeData,
            show: true,
          },
        });
      };
      // 编辑
      const editTips = () => {
        const seeData = record;
        seeData.disabled = false;
        this.setState({
          commentTipsForm: {
            ...this.state.commentTipsForm,
            data: seeData,
            show: true,
          },
        });
      };

      const deleteTips = () => {
        confirm({
          title: '确认删除吗？',
          content: `删除提示语：${record.plan_name}`,
          onOk() {
            api.deleteCommentTips({ id: record.id }).then((res) => {
              message.success('删除成功');
              that.getData1();
            });
          },
          onCancel() {},
        });
      };

      // 上下架
      const enableTips = () => {
        api.updateTypeCommentTips({ id: record.id, state: record.state ? 0 : 1 }).then((res) => {
          if (record.state) {
            message.success('下架成功');
          } else {
            message.success('上架成功');
          }
          that.getData1();
        });
      };

      const menu = (
        <Menu>
          <Menu.Item onClick={seeTips}>查看</Menu.Item>

          {this.requirePerm(`comment_tips:update${record.state ? 'xx' : ''}`)(
            <Menu.Item onClick={editTips}>编辑</Menu.Item>
          )}
          {this.requirePerm(`comment_tips:delete${record.types ? 'wy' : ''}`)(
            <Menu.Item onClick={deleteTips}>删除</Menu.Item>
          )}
          {this.requirePerm('comment_tips:update_displayed')(
            <Menu.Item onClick={enableTips}>{record.state ? '下线' : '启用'}</Menu.Item>
          )}
        </Menu>
      );
      return (
        <Dropdown overlay={menu}>
          <a className="ant-dropdown-link">
            操作 <Icon type="down" />
          </a>
        </Dropdown>
      );
    };

    const columns2 = [
      {
        title: '序号',
        key: 'id',
        render: (text: any, record: any, i: number) => <span>{i + 1}</span>,
        width: 50,
      },
      {
        title: '提示语方案名',
        key: 'plan_name',
        dataIndex: 'plan_name',
        width: 120,
      },
      {
        title: '提示语',
        key: 'content',
        dataIndex: 'content',
        width: 200,
      },
      {
        title: '状态',
        key: 'state',
        dataIndex: 'state',
        render: (text: any) => <span>{text ? '启用' : '未启用'}</span>,
        width: 100,
      },
      {
        title: '创建时间',
        key: 'created_at',
        dataIndex: 'created_at',
        render: (text: any) => <span>{moment(text).format('YYYY-MM-DD HH:mm:ss')}</span>,
        width: 120,
      },
      {
        title: '创建人',
        key: 'creator',
        dataIndex: 'creator',
        width: 90,
      },

      {
        title: '操作',
        key: 'op',
        render: (text: any, record: any) => getDropDown(record),
        width: 70,
      },
    ];
    return columns2;
  };

  removeBlacklist = (record: any) => {
    const that = this;
    confirm({
      title: '确认删除吗？',
      onOk() {
        api.deleteCommentPreset({ id: record.id }).then((res) => {
          message.success('删除成功');
          that.getData2();
        });
      },
      onCancel() {},
    });
  };

  getColumns3 = () => {
    const { current, size, total } = this.props.tableList;
    const getSeq = (i: number) => (current - 1) * size + i + 1;
    const columns3 = [
      {
        title: '序号',
        key: 'id',
        render: (text: any, record: any, i: number) => <span>{getSeq(i)}</span>,
        width: 50,
      },
      {
        title: '用户昵称',
        key: 'creator',
        dataIndex: 'creator',
        width: 90,
      },
      {
        title: '用户ID',
        key: 'creator',
        dataIndex: 'creator',
        width: 90,
      },
      {
        title: '用户长ID',
        key: 'creator',
        dataIndex: 'creator',
        width: 90,
      },
      {
        title: '添加人',
        key: 'creator',
        dataIndex: 'creator',
        width: 90,
      },
      {
        title: '添加时间',
        key: 'creator',
        dataIndex: 'creator',
        width: 90,
      },
      {
        title: '操作',
        key: 'op',
        dataIndex: 'op',
        width: 90,
        render: (text: any, record: any) => <a onClick={() => this.removeBlacklist(record)}>删除</a>,
      },
    ];

    return columns3;
  };

  // eslint-disable-next-line consistent-return
  TabContentDom = (tab: number) => {
    const { searchPreset, searchTips } = this.state;
    const TabDom1 = (
      <>
        <Form labelCol={{ span: 0 }} wrapperCol={{ span: 18 }} key="1">
          <Row justify="start">
            <Col span={6}>
              <Form.Item labelCol={{ span: 0 }} wrapperCol={{ span: 24 }}>
                <DatePicker.RangePicker
                  format="YYYY-MM-DD"
                  onChange={this.commentPickerChange}
                  value={
                    searchPreset.begin ? [moment(searchPreset.begin), moment(searchPreset.end)] : []
                  }
                />
              </Form.Item>
            </Col>
            <Col span={4}>
              <Form.Item>
                <Select
                  defaultValue={searchPreset.state}
                  style={{ width: '100px' }}
                  onChange={this.commentStateSelectChange}
                >
                  <Select.Option value="">全部</Select.Option>
                  <Select.Option value="1">启用</Select.Option>
                  <Select.Option value="0">未启用</Select.Option>
                </Select>
              </Form.Item>
            </Col>
            <Col span={10} offset={2}>
              <Form.Item labelCol={{ span: 0 }}>
                <InputGroup compact>
                  <Select
                    style={{ width: '30%' }}
                    defaultValue={searchPreset.type}
                    onChange={this.commentTypeSelectChange}
                  >
                    <Select.Option value="plan_name">方案名</Select.Option>
                    <Select.Option value="content">评论内容</Select.Option>
                    <Select.Option value="channel_article_id">稿件ID</Select.Option>
                    <Select.Option value="channel_article_title">稿件标题</Select.Option>
                  </Select>
                  <Input
                    value={searchPreset.text}
                    onChange={this.commentTextSelectChange}
                    style={{ width: '70%' }}
                    placeholder=""
                  />
                </InputGroup>
              </Form.Item>
            </Col>
            <Col span={2}>
              <Form.Item>
                <Button type="primary" onClick={() => this.getData()}>
                  查找
                </Button>
              </Form.Item>
            </Col>
          </Row>
        </Form>
        <Table
          key="table1"
          func="getListCommentPreset"
          index="preset_list"
          pagination={true}
          filter={this.getFilters()}
          rowKey="id"
          columns={this.getColumns()}
        />
      </>
    );

    const TabDom2 = (
      <>
        <Form labelCol={{ span: 0 }} wrapperCol={{ span: 18 }}>
          <Row justify="start">
            <Col span={6}>
              <Form.Item labelCol={{ span: 0 }} wrapperCol={{ span: 24 }}>
                <DatePicker.RangePicker
                  format="YYYY-MM-DD"
                  onChange={this.tipsPickerChange}
                  value={searchTips.begin ? [moment(searchTips.begin), moment(searchTips.end)] : []}
                />
              </Form.Item>
            </Col>
            <Col span={4}>
              <Form.Item labelCol={{ span: 0 }}>
                <Select
                  defaultValue={searchTips.state}
                  style={{ width: '100px' }}
                  onChange={this.tipsStateSelectChange}
                >
                  <Select.Option value="">全部</Select.Option>
                  <Select.Option value="1">启用</Select.Option>
                  <Select.Option value="0">未启用</Select.Option>
                </Select>
              </Form.Item>
            </Col>
            <Col span={10} offset={2}>
              <Form.Item labelCol={{ span: 0 }}>
                <InputGroup compact>
                  <Select
                    style={{ width: '30%' }}
                    defaultValue={searchTips.type}
                    onChange={this.tipsTypeSelectChange}
                  >
                    <Select.Option value="plan_name">方案名</Select.Option>
                    <Select.Option value="content">评论内容</Select.Option>
                    <Select.Option value="channel_article_id">稿件ID</Select.Option>
                    <Select.Option value="channel_article_title">稿件标题</Select.Option>
                  </Select>
                  <Input
                    value={searchTips.text}
                    onChange={this.tipsTextSelectChange}
                    style={{ width: '70%' }}
                    placeholder=""
                  />
                </InputGroup>
              </Form.Item>
            </Col>
            <Col span={2}>
              <Form.Item>
                <Button type="primary" onClick={() => this.getData1()}>
                  查找
                </Button>
              </Form.Item>
            </Col>
          </Row>
        </Form>
        <Table
          key="table2"
          func="getListCommentTips"
          index="tips_list"
          pagination={true}
          filter={this.getFilters2()}
          rowKey="id"
          columns={this.getColumns2()}
        />
      </>
    );

    if (tab === TABSTATE.COMMENTPRESET) {
      return TabDom1;
    }
    if (tab === TABSTATE.COMMENTTIPS) {
      return TabDom2;
    }

    if (tab === TABSTATE.BLACKLIST) {
      return (
        <>
          <Table
            key="table3"
            func="getListCommentTips"
            index="tips_list"
            pagination={true}
            filter={{}}
            rowKey="id"
            columns={this.getColumns3()}
          />
        </>
      );
    }
  };

  render() {
    const { type, commentForm, commentTipsForm } = this.state;
    return (
      <>
        <Row className="layout-infobar">
          <Col span={12}>
            {/* <Radio.Group
              value={type}
              style={{ marginRight: 8 }}
              onChange={this.handleTypeChange}
              buttonStyle="solid"
            >
              <Radio.Button value={TABSTATE.COMMENTPRESET}>评论预设</Radio.Button>
              <Radio.Button value={TABSTATE.COMMENTTIPS}>评论提示语</Radio.Button>
            </Radio.Group> */}
            {type === TABSTATE.COMMENTPRESET &&
              this.requirePerm('comment_preset:create')(
                <Button onClick={this.openDrawer}>
                  <Icon type="plus-circle" />
                  新建设置 {/* 评论预设 */}
                </Button>
              )}
            {type === TABSTATE.COMMENTTIPS &&
              this.requirePerm('comment_tips:create')(
                <Button onClick={this.openTipsDrawer}>
                  <Icon type="plus-circle" />
                  新建设置 {/* 评论提示语 */}
                </Button>
              )}
            {type === TABSTATE.BLACKLIST &&
              this.requirePerm('')(
                <>
                  <Button style={{ marginRight: 8 }} onClick={this.addBlacklist}>
                    添加用户
                  </Button>
                  <Tooltip title="关闭指定用户发布的所有ugc稿件下的评论区">
                    <Icon type="question-circle" />
                  </Tooltip>
                </>
              )}
          </Col>
          <Col span={12} className="layout-breadcrumb">
            {this.getCrumb()}
          </Col>
        </Row>
        <div className="component-content news-pages">{this.TabContentDom(type)}</div>
        <Drawer
          visible={commentForm.show}
          skey={commentForm.key}
          title={commentForm.title}
          onClose={this.closeDrawer}
          onOk={this.handleSubmitForm.bind(this, 'commentForm')}
        >
          <CommentPresuppositionForm
            formContent={commentForm.data}
            wrappedComponentRef={this.setFormRef.bind(this, 'commentForm')}
            onEnd={this.commentSubmitEnd}
            onClose={this.closeDrawer}
          />
        </Drawer>

        <Drawer
          visible={commentTipsForm.show}
          skey={commentTipsForm.key}
          title={commentTipsForm.title}
          onClose={this.closeTipsDrawer}
          onOk={this.handleSubmitForm.bind(this, 'commentTipsForm')}
        >
          <CommentTipsForm
            formContent={commentTipsForm.data}
            wrappedComponentRef={this.setFormRef.bind(this, 'commentTipsForm')}
            onEnd={this.commentTipsSubmitEnd}
            onClose={this.closeTipsDrawer}
          />
        </Drawer>

        <CommonAddUserModal
          visible={this.state.blacklistDialog.visible}
          key={this.state.blacklistDialog.key}
          addApi={api.addBlacklistRecord}
          onCancel={() => {
            this.setState({
              blacklistDialog: {
                visible: false,
                key: '',
              },
            });
          }}
          onOk={() => {
            this.setState({
              blacklistDialog: {
                visible: false,
                key: '',
              },
            });
            this.getData2();
          }}
        ></CommonAddUserModal>
      </>
    );
  }
}

export default withRouter(connect<ProposalRecord, ProposalAllData>()(CommentOperate));
