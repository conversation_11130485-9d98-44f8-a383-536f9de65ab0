import { getTableList } from '@action/tableList';
import {communityApi, userApi as api} from '@app/api';
import { CommonObject } from '@app/types';
import { A, Table } from '@components/common';
import connect from '@utils/connectTable';
import { getCrumb, requirePerm, setLoading, setMenu } from '@utils/utils.tsx';
import {Button, Col, Icon, Input, message, Modal, Row, Select} from 'antd';
import moment from 'moment';
import React from 'react';
import { withRouter } from 'react-router';
import _ from "lodash";

@(withRouter as any)
@connect
class PrivilegeGroupUser extends React.Component<any, any> {
  constructor(props: any) {
    super(props);
    this.state = {
      id: props.match.params.id,
      name: decodeURIComponent(props.match.params.name),
      optionData: [],
    };
  }

  componentDidMount() {
    this.getData({ current: 1, size: 10 });
    setMenu(this);
  }

  getData = (overlap: CommonObject = {}) => {
    const { current, size } = this.props.tableList;
    const body = { current, size, privilege_id: this.state.id, ...overlap };
    this.props.dispatch(getTableList('getPGUserList', 'privilege_user_list', body));
  };

  getColumns = () => {
    return [
      {
        title: '用户昵称',
        key: 'nick_name',
        dataIndex: 'nick_name',
      },
      {
        title: '添加时间',
        key: 'created_at',
        dataIndex: 'created_at',
        render: (text: any) => <span>{moment(text).format('YYYY-MM-DD HH:mm:ss')}</span>,
      },
      {
        title: '操作',
        key: 'op',
        render: (text: any, record: any) =>
          requirePerm(this, 'privilege_user:deleted')(<A onClick={() => this.deleteRecord(record)}>删除</A>),
        width: 70,
      },
    ];
  };

  deleteRecord = (record: any) => {
    Modal.confirm({
      title: <p>确定从分组中删除用户“{record.nick_name}”吗？</p>,
      onOk: () => {
        setLoading(this, true);
        api
          .deletePGUser({ id: record.id })
          .then(() => {
            message.success('操作成功');
            this.getData();
            setLoading(this, false);
          })
          .catch(() => setLoading(this, false));
      },
    });
  };
  addRecord = () => {
    const {optionData} = this.state
    let account_id = ''
    const chooseSelect = (e: any) => {
      let onSelect = JSON.parse(e)
      account_id = onSelect.id
    }
    const handleSearch = _.debounce((val: any) => {
      let data = { keyword: val.toString()}
      if (!val)
        return
      communityApi.recommendAccount_Search(data).then((res: any) => {
        let records = [...res.data.list]
        this.setState({
          optionData:records
        },()=>{
          selectModal.update({ content: selectContent(records) })
        })
      })
    }, 500)
    const selectContent = (list) => {
      return <Select style={{ width: '80%' }}
              placeholder="请输入用户昵称或用户ID"
              onSelect={(e) => chooseSelect(e)}
              onSearch={(e) => handleSearch(e)}
              defaultActiveFirstOption={false} showSearch>
        {list.map((d: any) => <Select.Option key={d.id} value={JSON.stringify(d)}>{`${d.nick_name} |用户ID：${d.chao_id}`}</Select.Option>)}
      </Select>
    }
    let selectModal = Modal.confirm({
      title: '添加用户',
      width: 650,
      content: selectContent(optionData),
      onOk: (closeFunc: any) => {
        if (account_id === '') {
          message.error('请输入用户昵称');
          return;
        }
        setLoading(this, true);
        api
          .addPGUser({ account_id, privilege_id: this.state.id })
          .then(() => {
            message.success('操作成功');
            this.getData();
            setLoading(this, false);
            closeFunc();
          })
          .catch(() => setLoading(this, false));
      },
    });
    selectModal
  };

  render() {
    return (
      <React.Fragment>
        <Row className="layout-infobar">
          <Col span={12}>
            <Button onClick={() => this.props.history.push('/view/privilegeGroup')}>
              <Icon type="left-circle" /> 返回特权组列表
            </Button>
            {requirePerm(
              this,
              'privilege_user:create'
            )(
              <Button onClick={this.addRecord} style={{ marginLeft: 8 }}>
                <Icon type="plus-circle" /> 添加用户
              </Button>
            )}
          </Col>
          <Col span={12} className="layout-breadcrumb">
            {getCrumb([...this.props.breadCrumb, this.state.name])}
          </Col>
        </Row>
        <div className="component-content">
          <Table
            func="getPGUserList"
            index="privilege_user_list"
            rowKey="id"
            filter={{ privilege_id: this.state.id }}
            columns={this.getColumns()}
            pagination={true}
          />
        </div>
      </React.Fragment>
    );
  }
}

export default PrivilegeGroupUser;
