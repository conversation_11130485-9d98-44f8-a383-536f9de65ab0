import React, {
  useState,
  useEffect,
  useCallback,
  useMemo,
  useRef,
  useImperativeHandle,
  forwardRef,
} from 'react';
import { useStore, useDispatch, useSelector } from 'react-redux';
import { useHistory } from 'react-router-dom';
import { Table, OrderColumn, PreviewMCN } from '@components/common';
import { CommonObject } from '@app/types';
import {
  getCrumb,
  objectToQueryString,
  searchToObject,
  setMenuHook,
  UserDetail,
} from '@app/utils/utils';
import {
  Row,
  Col,
  Divider,
  Icon,
  Modal,
  message,
  InputNumber,
  Tooltip,
  Popconfirm,
  Button,
  Select,
  Input,
  Tabs,
} from 'antd';
import { getTableList } from '@app/action/tableList';
import { userApi as api } from '@app/api';
import useXHR from '@utils/useXhr';
import { PermA, PermButton } from '@components/permItems';
import Radio from 'antd/es/radio';
import { setConfig } from '@app/action/config';
import moment from 'moment';
import { CardEditor } from '@components/common';
import AddCooperationAccountDrawer from './addCooperationAccountDrawer';
import ImagePreviewColumn from '@app/components/common/imagePreviewColumn';
import AddCooperationCaseDrawer from './addCooperationCaseDrawer';

export default function BusinessCooperation(props: any) {
  const [type, setType] = useState(parseInt(searchToObject().type ?? 0));
  const dispatch = useDispatch();
  const history = useHistory();
  const [editing, setEditing] = useState(false);
  const commonProblemRef = useRef(null);
  const authorRecRef = useRef(null);
  const activityCaseRef = useRef(null);
  useEffect(() => {
    // getList({ current: 1, size: 10 });
    setMenuHook(dispatch, props);
  }, []);

  const onChangeType = (v: any, name: any) => {
    let { pathname: path, search } = history.location;
    const obj = searchToObject();
    obj[name] = v;
    path = `${path}?${objectToQueryString(obj)}`;
    history.replace(path);
    setType(v);
  };

  return (
    <>
      <Row className="layout-infobar">
        <Col span={12}>
          <Radio.Group
            defaultValue={type}
            buttonStyle="solid"
            onChange={(e) => {
              onChangeType(e.target.value, 'type');
              setEditing(false);
            }}
          >
            <Radio.Button value={0}>创作者推荐</Radio.Button>
            <Radio.Button value={1}>活动案例</Radio.Button>
            <Radio.Button value={2}>常见问题</Radio.Button>
          </Radio.Group>

          {type == 0 && (
            <PermButton
              perm="cooperation_account:create"
              style={{ marginLeft: 8 }}
              onClick={() => {
                authorRecRef.current?.addAccount();
              }}
            >
              添加账号
            </PermButton>
          )}
          {type == 1 && (
            <PermButton
              perm="cooperation_case:create"
              style={{ marginLeft: 8 }}
              onClick={() => {
                activityCaseRef.current?.addCase();
              }}
            >
              添加案例
            </PermButton>
          )}
          {type == 2 &&
            (editing ? (
              <>
                <Button
                  type="primary"
                  style={{ marginLeft: 8 }}
                  onClick={() => {
                    commonProblemRef.current?.save();
                    setEditing(false);
                  }}
                >
                  保存
                </Button>
                <Button style={{ marginLeft: 8 }} onClick={() => setEditing(false)}>
                  取消
                </Button>
              </>
            ) : (
              <PermButton
                perm="cooperation_question:edit"
                style={{ marginLeft: 8 }}
                onClick={() => {
                  setEditing(true);
                }}
              >
                编辑常见问题
              </PermButton>
            ))}
        </Col>
        <Col span={12} className="layout-breadcrumb">
          {getCrumb(props.breadCrumb)}
        </Col>
      </Row>
      <div className="component-content">
        {type == 0 && <AuthorRecommendation ref={authorRecRef}></AuthorRecommendation>}
        {type == 1 && <ActivityCase ref={activityCaseRef}></ActivityCase>}
        {type == 2 && <CommonProblem editing={editing} ref={commonProblemRef}></CommonProblem>}
      </div>
    </>
  );
}

const AuthorRecommendation = forwardRef((props: any, ref: any) => {
  const dispatch = useDispatch();
  const store = useStore();
  const { loading, run } = useXHR();

  const [filter, setFilter] = useState<any>({
    type: '',
  });
  const {
    total,
    current,
    size,
    records = [],
    allData,
  } = useSelector((state: any) => state.tableList);
  const getSeq = (i: number) => (current - 1) * size + i + 1;
  const [search, setSearch] = useState({
    search_type: 1,
    keyword: '',
  });

  const [drawer, setDrawer] = useState<any>({
    visible: false,
    formContent: null,
    key: 'drawer',
  });

  const [userDetailModal, setUserDetailModal] = useState<any>({
    visible: false,
    detail: null,
    key: 'userDetailModal',
  });

  useImperativeHandle(ref, () => ({
    addAccount: () => {
      setDrawer({
        visible: true,
        formContent: null,
        key: Math.random(),
      });
    },
  }));

  const f = useMemo(() => {
    const x: CommonObject = { ...filter };
    if (!filter.keyword) {
      delete x.keyword;
      delete x.search_type;
    }
    return x;
  }, [filter]);

  const getList = (goFirstPage: boolean = false, overlap: CommonObject = {}) => {
    const { current, size = 10 } = store.getState().tableList;
    dispatch(
      getTableList('getCooperationAccountList', 'list', {
        ...f,
        current: goFirstPage ? 1 : current,
        size,
        ...overlap,
      })
    );
  };

  const handleKey = (e: { which: number }) => {
    if (e.which === 13) {
      setFilter({
        ...filter,
        keyword: search.keyword,
        search_type: search.search_type,
      });
    }
  };

  useEffect(() => {
    getList(true);
  }, [f]);

  const listSort = (record: any, sort_flag: number, position: number = 0) => {
    let data: any = {
      id: record.id,
      sort_flag,
    };
    if (sort_flag == 2) {
      data.position = position;
    }
    api.updateCooperationAccountOrder(data).then((res: any) => {
      message.success('操作成功');
      getList();
    });
  };

  const changeOrder = (record: any, i: any) => {
    let position = i;
    const WAIT_TIME = 1000;
    const positionChange = (v: any) => {
      position = v;
    };

    Modal.confirm({
      title: <p>调整排序</p>,
      icon: <Icon type="info-circle" />,
      content: (
        <div>
          <span>排序值：</span>
          <InputNumber min={1} max={total} defaultValue={position} onChange={positionChange} />
        </div>
      ),
      onOk: (closeFunc: Function) => {
        if (!position) {
          message.error('请填写排序值');
          return;
        }

        dispatch(setConfig({ loading: true }));
        let data: any = {
          id: record.id,
          sort_flag: 2,
          position,
        };

        api
          .updateCooperationAccountOrder(data)
          .then((res: any) => {
            message.success('操作成功');
            getList();
            dispatch(setConfig({ loading: false }));
            closeFunc();
          })
          .catch(() => {
            dispatch(setConfig({ loading: false }));
          });
      },
    });
  };

  const deleteRecord = (record: any) => {
    Modal.confirm({
      title: `确认删除吗？`,
      onOk: () => {
        run(api.deleteCooperationAccount, { id: record.id }, true).then(() => {
          message.success('操作成功');
          getList();
        });
      },
    });
  };

  const editRecord = (record: any) => {
    setDrawer({
      visible: true,
      formContent: record,
      key: Math.random(),
    });
  };

  // 显示用户详情
  const showUserDetailModal = (record: any) => {
    run(api.getUserDetail, { accountId: record.account_id }).then((r: any) => {
      setUserDetailModal({
        key: Date.now(),
        visible: true,
        detail: r.data.account,
      });
    });
  };

  const columns = [
    {
      title: '排序',
      key: 'sort',
      render: (text: any, record: any, i: number) => (
        <OrderColumn
          perm="cooperation_account:order"
          start={1}
          pos={getSeq(i)}
          end={total}
          disable={!!filter.keyword || filter.type != ''}
          onUp={() => listSort(record, 0)}
          onDown={() => listSort(record, 1)}
        />
      ),
      width: 70,
    },
    {
      title: '序号',
      key: 'seq',
      render: (text: any, record: any, i: number) => <span>{getSeq(i)}</span>,
      width: 70,
    },
    {
      title: '账号名称',
      dataIndex: 'account_name',
      width: 90,
      render: (text: any, record: any) => (
        <a onClick={() => showUserDetailModal(record, true)}>{text}</a>
      ),
    },
    {
      title: '一句话介绍',
      dataIndex: 'desc',
      width: 90,
    },
    {
      title: '类型',
      dataIndex: 'type',
      width: 90,
      render: (text: any, record: any) =>
        ['', '文化', '文旅', '亲子', '美食', '汽车', '航拍', '主持', '生活', '财经', '科普', '律师', '海外'][text],
    },
    {
      title: '操作人',
      dataIndex: 'updated_by',
      width: 90,
    },
    {
      title: '操作时间',
      dataIndex: 'updated_at',
      width: 90,
      render: (text: any, record: any) => moment(text).format('YYYY-MM-DD HH:mm:ss'),
    },
    {
      title: '操作',
      key: 'op',
      render: (text: any, record: any, i: number) => (
        <span>
          <PermA perm="cooperation_account:edit" onClick={() => editRecord(record)}>
            编辑
          </PermA>
          <Divider type="vertical" />
          <PermA perm="cooperation_account:order" onClick={() => changeOrder(record, getSeq(i))}>
            排序
          </PermA>
          <Divider type="vertical" />
          <PermA perm="cooperation_account:delete" onClick={() => deleteRecord(record)}>
            删除
          </PermA>
        </span>
      ),
      width: 110,
    },
  ];
  return (
    <div>
      <Row style={{ marginBottom: 16 }}>
        <Col span={12}>
          <Select
            value={filter.type}
            style={{ width: 150, marginRight: 8, marginLeft: 8 }}
            onChange={(v) => setFilter({ ...filter, type: v })}
          >
            <Select.Option value="">账号类型</Select.Option>
            <Select.Option value={1}>文化</Select.Option>
            <Select.Option value={2}>文旅</Select.Option>
            <Select.Option value={3}>亲子</Select.Option>
            <Select.Option value={4}>美食</Select.Option>
            <Select.Option value={5}>汽车</Select.Option>
            <Select.Option value={6}>航拍</Select.Option>
            <Select.Option value={7}>主持</Select.Option>
            <Select.Option value={8}>生活</Select.Option>
            <Select.Option value={9}>财经</Select.Option>
            <Select.Option value={10}>科普</Select.Option>
            <Select.Option value={11}>律师</Select.Option>
            <Select.Option value={12}>海外</Select.Option>
          </Select>
        </Col>

        <Col span={12} style={{ textAlign: 'right' }}>
          <Select
            value={search.search_type}
            style={{ width: 150, marginRight: 8, marginLeft: 8 }}
            onChange={(v) => setSearch({ ...search, search_type: v })}
          >
            <Select.Option value={1}>昵称</Select.Option>
            <Select.Option value={2}>用户长ID</Select.Option>
            <Select.Option value={3}>用户ID</Select.Option>
          </Select>
          <Input
            style={{ width: 160, marginRight: 8 }}
            onKeyPress={handleKey}
            value={search.keyword}
            placeholder="请输入搜索内容"
            onChange={(e) =>
              setSearch({
                ...search,
                keyword: e.target.value,
              })
            }
          />
          <Button onClick={() => handleKey({ which: 13 })}>
            <Icon type="search" />
            搜索
          </Button>
        </Col>
      </Row>
      <Table
        func={'getCooperationAccountList'}
        index="list"
        filter={f}
        columns={columns}
        rowKey="id"
        pagination={true}
      />
      <AddCooperationAccountDrawer
        {...drawer}
        onEnd={() => {
          setDrawer({ ...drawer, visible: false });
          getList();
        }}
        onClose={() => setDrawer({ ...drawer, visible: false })}
      ></AddCooperationAccountDrawer>

      <Modal
        visible={userDetailModal.visible}
        key={userDetailModal.key}
        title="用户详情"
        width={800}
        onCancel={() => setUserDetailModal({ ...userDetailModal, visible: false })}
        onOk={() => setUserDetailModal({ ...userDetailModal, visible: false })}
      >
        {/*{user.visible && getUserDetail(user.detail)}*/}
        {userDetailModal.visible && <UserDetail detail={userDetailModal.detail} />}
      </Modal>
    </div>
  );
});

const ActivityCase = forwardRef((props: any, ref: any) => {
  const dispatch = useDispatch();
  const store = useStore();
  const { run } = useXHR();
  const [filter, setFilter] = useState<any>({});
  const {
    total,
    current,
    size,
    records = [],
    allData,
  } = useSelector((state: any) => state.tableList);
  const getSeq = (i: number) => (current - 1) * size + i + 1;
  const [drawer, setDrawer] = useState<any>({
    visible: false,
    formContent: null,
    key: 'drawer',
  });

  const f = useMemo(() => {
    const x: CommonObject = { ...filter };
    return x;
  }, [filter]);

  useImperativeHandle(ref, () => ({
    addCase: () => {
      setDrawer({
        visible: true,
        formContent: null,
        key: Math.random(),
      });
    },
  }));

  const getList = (goFirstPage: boolean = false, overlap: CommonObject = {}) => {
    const { current, size = 10 } = store.getState().tableList;
    dispatch(
      getTableList('getCooperationCaseList', 'list', {
        ...f,
        current: goFirstPage ? 1 : current,
        size,
        ...overlap,
      })
    );
  };
  const listSort = (record: any, sort_flag: number, position: number = 0) => {
    let data: any = {
      id: record.id,
      sort_flag,
    };
    if (sort_flag == 2) {
      data.position = position;
    }
    api.updateCooperationCaseOrder(data).then((res: any) => {
      message.success('操作成功');
      getList();
    });
  };

  useEffect(() => {
    getList(true);
  }, [f]);

  const deleteRecord = (record: any) => {
    Modal.confirm({
      title: `确认删除吗？`,
      onOk: () => {
        run(api.deleteCooperationCase, { id: record.id }, true).then(() => {
          message.success('操作成功');
          getList();
        });
      },
    });
  };

  const editRecord = (record: any) => {
    setDrawer({
      visible: true,
      formContent: record,
      key: Math.random(),
    });
  };

  const columns = [
    {
      title: '排序',
      key: 'sort',
      render: (text: any, record: any, i: number) => (
        <OrderColumn
          perm="cooperation_case:order"
          start={1}
          pos={getSeq(i)}
          end={total}
          onUp={() => listSort(record, 0)}
          onDown={() => listSort(record, 1)}
        />
      ),
      width: 70,
    },
    {
      title: '序号',
      key: 'seq',
      render: (text: any, record: any, i: number) => <span>{getSeq(i)}</span>,
      width: 70,
    },
    {
      title: '配图',
      key: 'pic',
      dataIndex: 'pic',
      width: 150,
      align: 'center',
      render: (text: any, record: any) => (
        <div style={{ height: 60, textAlign: 'center' }}>
          <ImagePreviewColumn text={text} imgs={[text]}></ImagePreviewColumn>
        </div>
      ),
    },
    {
      title: '标题',
      dataIndex: 'title',
    },
    {
      title: '链接',
      dataIndex: 'link',
      width: 180,
      render: (text: any, record: any) => (
        <a href={text} target="_blank">
          {text}
        </a>
      ),
    },
    {
      title: '操作人',
      dataIndex: 'updated_by',
      width: 100,
    },
    {
      title: '操作时间',
      dataIndex: 'updated_at',
      width: 160,
      render: (text: any, record: any) => moment(text).format('YYYY-MM-DD HH:mm:ss'),
    },
    {
      title: '操作',
      key: 'op',
      render: (text: any, record: any, i: number) => (
        <span>
          <PermA perm="cooperation_case:edit" onClick={() => editRecord(record)}>
            编辑
          </PermA>
          <Divider type="vertical" />
          <PermA perm="cooperation_case:delete" onClick={() => deleteRecord(record)}>
            删除
          </PermA>
        </span>
      ),
      width: 110,
    },
  ];
  return (
    <div>
      <Table
        func={'getCooperationCaseList'}
        index="list"
        filter={f}
        columns={columns}
        rowKey="id"
        pagination={true}
      />
      <AddCooperationCaseDrawer
        {...drawer}
        onEnd={() => {
          setDrawer({ ...drawer, visible: false });
          getList();
        }}
        onClose={() => setDrawer({ ...drawer, visible: false })}
      ></AddCooperationCaseDrawer>
    </div>
  );
});

const CommonProblem = React.forwardRef((props: any, ref: any) => {
  const [clientContent, setClientContent] = useState('');
  const [masterContent, setMasterContent] = useState('');
  const [activeKey, setActiveKey] = useState('1');
  const ce = useRef(null);
  const { run } = useXHR();

  useImperativeHandle(ref, () => ({
    save: () => {
      run(
        api.updateCooperationCommonProblem,
        {
          type: activeKey == '1' ? 0 : 1,
          content: ce.current?.getData() || '',
        },
        true
      ).then(() => {
        message.success('操作成功');
        getContent();
      });
    },
  }));

  useEffect(() => {
    getContent();
  }, []);

  const getContent = () => {
    api
      .getCooperationCommonProblem({
        type: 0,
      })
      .then((res: any) => {
        setClientContent(res.data?.detail?.content || '');
      });

    api
      .getCooperationCommonProblem({
        type: 1,
      })
      .then((res: any) => {
        setMasterContent(res.data?.detail?.content || '');
      });
  };

  return (
    <div>
      {!props.editing && (
        <Tabs activeKey={activeKey} onChange={(key) => setActiveKey(key)}>
          <Tabs.TabPane tab="我是客户" key="1"></Tabs.TabPane>

          <Tabs.TabPane tab="我是达人" key="2"></Tabs.TabPane>
        </Tabs>
      )}

      <CardEditor
        ref={ce}
        title={props.editing ? (activeKey == '1' ? '我是客户' : '我是达人') : ''}
        value={activeKey == '1' ? clientContent : masterContent}
        editing={props.editing}
      />
    </div>
  );
});
