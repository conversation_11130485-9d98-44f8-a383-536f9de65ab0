import React, { useState, useEffect, useCallback } from 'react';

import { useSelector, useDispatch } from 'react-redux';
import { Row, Col, Divider, Modal, message, Form, InputNumber } from 'antd';
import { getCrumb, setMenuHook } from '@app/utils/utils';
import { Table } from '@app/components/common';
import { getTableList } from '@app/action/tableList';
import { PermA, PermButton } from '@app/components/permItems';

import useXHR from '@utils/useXhr';
import { userApi } from '@app/api';

export default function CreditLevel(props: any) {
  const [form, setForm] = useState(
    () =>
      ({
        visible: false,
        key: Date.now(),
        level: 0,
        score: 0,
        minScore: 0,
        maxScore: 0,
      } as any)
  );

  const dispatch = useDispatch();

  const { run, loading } = useXHR();

  const records = useSelector((state: any) => state.tableList.records);

  const getList = () => {
    dispatch(getTableList('getCreditLevelList', 'score_level_list', {}));
  };

  const editRecord = useCallback(
    (record: any = {}, index: number = records.length - 1) => {
      const maxLevel = records.length > 0 ? records[records.length - 1].level + 1 : 1;
      // let minScore = records.length > 0 ? records[records.length - 1].score + 1 : 0;
      let minScore = 0;
      let maxScore = 9999999999;
      if (records?.length > 0) {
        minScore = index > 0 ? records[index - 1].score + 1 : 0;
        minScore = !record.id ? records[index].score + 1 : minScore;
        maxScore = index < records.length - 1 ? records[index + 1].score - 1 : 9999999999;
      }
      setForm({
        visible: true,
        key: Date.now(),
        level: record.id ? record.level : maxLevel,
        score: record.id ? record.score : minScore,
        minScore,
        maxScore,
        id: record.id || '',
      });
    },
    [records]
  );

  const deleteRecord = (record: any) => {
    Modal.confirm({
      title: `确定删除积分等级${record.level}吗？`,
      onOk: () => {
        run(userApi.deleteCreditLevel, { id: record.id }, true).then(() => {
          message.success('操作成功');
          getList();
        });
      },
    });
  };

  const submit = () => {
    const { score, level, id, minScore, maxScore } = form;
    const body: any = { score, level };
    if (id) {
      body.id = id;
    }
    if (score <= maxScore && score >= minScore) {
      run(id ? userApi.updateCreditLevel : userApi.createCreditLevel, body).then(() => {
        message.success('操作成功');
        getList();
        setForm((f: any) => ({ ...f, visible: false }));
      });
    } else {
      message.error('请确认填写积分是否正确');
    }
  };

  const columns = [
    {
      title: '等级序号',
      dataIndex: 'level',
    },
    {
      title: '积分值',
      dataIndex: 'score',
    },
    {
      title: '操作',
      key: 'op',
      width: 90,
      render: (text: any, record: any, i: number) => (
        <span>
          <PermA perm="" onClick={() => editRecord(record, i)}>
            编辑
          </PermA>
          <Divider type="vertical" />
          <PermA perm="" onClick={() => deleteRecord(record)}>
            删除
          </PermA>
        </span>
      ),
    },
  ];

  useEffect(() => {
    getList();
    setMenuHook(dispatch, props);
  }, []);

  return (
    <>
      <Row className="layout-infobar">
        <Col span={12}>
          <PermButton perm="" onClick={() => editRecord()}>
            新建等级
          </PermButton>
        </Col>
        <Col span={12} className="layout-breadcrumb">
          {getCrumb(props.breadCrumb)}
        </Col>
      </Row>
      <div className="component-content">
        <Table
          columns={columns}
          func="getCreditLevelList"
          index="score_level_list"
          filter={{}}
          pagination={false}
          rowKey="id"
        />
        <Modal
          visible={form.visible}
          title={form.id ? '编辑等级' : '新建等级'}
          onCancel={() => setForm({ ...form, visible: false })}
          confirmLoading={loading}
          onOk={submit}
        >
          <Form labelCol={{ span: 4 }} wrapperCol={{ span: 18 }}>
            <Form.Item label="等级">{form.level}</Form.Item>
            <Form.Item label="积分">
              <InputNumber
                min={form.minScore}
                max={form.maxScore}
                step={1}
                value={form.score}
                onChange={(e: any) => setForm({ ...form, score: parseInt(e || '0', 10) })}
              />
            </Form.Item>
          </Form>
        </Modal>
      </div>
    </>
  );
}
