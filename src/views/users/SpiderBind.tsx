import React, { useImperativeHandle, useState, forwardRef, useRef, useEffect, } from 'react';
import {
  Row,
  Col,
  Button,
  Select,
  Input,
  Icon,
  Drawer,
  Form,
  Radio,
  Checkbox,
  Table as ATable,
  Modal, Spin, Divider, Dropdown, Menu
} from 'antd';
import moment from "moment";
import A from '@components/common/a';
import { UserDetail, getCrumb, requirePerm, requirePerm4Function } from '@utils/utils';
import { Table } from '@components/common';
import { withRouter } from 'react-router';
import { connectTable as connect } from '@utils/connect';
import { useDispatch, useSelector, useStore } from "react-redux";
import { getTableList } from "@action/tableList";
import { setConfig } from "@action/config";
import { communityApi, userApi } from "@app/api";
import { PermA, PermButton } from '@app/components/permItems';
import PullDataModal from './pullDataModal';
import uuid from 'uuid';

const { Option } = Select;

const platform = [['微信公众号', 'wx'], ['视频号', 'shipin'], ['头条号', 'toutiao'], ['抖音', 'douyin'], ['微博', 'wb'], ['快手', 'kuaishou'], ['小红书', 'xiaohongshu'], ['哔哩哔哩', 'bilibili'], ['YouTube', 'youtube']];
const platformMap = {
  'wx': '微信公众号',
  'shipin': '视频号',
  'toutiao': '头条号',
  'douyin': '抖音',
  'wb': '微博',
  'kuaishou': '快手',
  'xiaohongshu': '小红书',
  'bilibili': '哔哩哔哩',
  'youtube': 'YouTube'
}
const type = [['用户昵称'], ['用户长ID'], ['抓取账号名'], ['用户ID']];
enum doc_type {
  '视频' = 10,
  '长文章' = 13,
  '短图文' = 12
}

const contentType = [['视频', 10], ['长文章', 13], ['短图文', 12]];


interface DialogRef {
  open: (...param: any) => void;
}

// 下载
const handleDownload = (url: string) => {
  const a = document.createElement('a');
  a.setAttribute('href', url);
  a.target = '_self';
  a.href = url;
  document.body.appendChild(a);
  a.click();
  document.body.removeChild(a);
};

const Td = (text, item, key) => {
  let index = -1
  item.error_filed.forEach((item, i) => {
    if (item === key) {
      index = i
    }
  })
  return <div style={{ color: index > -1 ? 'red' : '' }}>{index > -1 ? item.error_message[index] : text}</div>
}

const ImportColumns = [
  {
    title: '序号',
    dataIndex: 'index',
    key: 'index',
    render(text) {
      return <div>{text + 1}</div>
    }
  },
  {
    title: '用户ID',
    dataIndex: 'chao_id',
    render: (text, item) => Td(text, item, 'chao_id')
  },
  {
    title: '账号类型',
    dataIndex: 'cert_type',
    key: 'cert_type',
    render(text) {
      return <div>{['潮客', '潮鸣号', '潮鸣号'][text]}</div>
    }
  },
  {
    title: '抓取平台',
    dataIndex: 'platform_type',
    key: 'platform_type',
    render: (text, item) => Td(platformMap[text], item, 'platform_type')
  },
  {
    title: '抓取内容类型',
    dataIndex: 'doc_type',
    key: 'doc_type',
    render: (text, item) => Td(text, item, 'doc_type')
  },
  {
    title: '抓取账号',
    dataIndex: 'platform_name',
    key: 'platform_name',
    render: (text, item) => Td(text, item, 'platform_name')
  },
  {
    title: '抓取账号id',
    dataIndex: 'platform_id',
    key: 'platform_id ',
    render: (text, item) => Td(text, item, 'platform_id')
  },
];
const ImportResultColumns = [
  {
    title: '序号',
    dataIndex: 'index',
    key: 'index',
    render(text) {
      return <div>{text + 1}</div>
    }
  },
  {
    title: '用户ID',
    dataIndex: 'chao_id',
    render: (text, item) => Td(text, item, 'chao_id')
  },
  {
    title: '抓取平台',
    dataIndex: 'platform_type',
    key: 'platform_type',
    render: (text, item) => (<div>{platformMap[text]}</div>)
  },
  // {
  //   title: '抓取内容类型',
  //   dataIndex: 'doc_type',
  //   key: 'doc_type',
  //   render: (text, item) => Td(text, item, 'doc_type')
  // },
  {
    title: '抓取账号',
    dataIndex: 'platform_name',
    key: 'platform_name',
    // render: (text, item) => Td(text, item, 'platform_name')
  },
  {
    title: '抓取账号id',
    dataIndex: 'platform_id',
    key: 'platform_id ',
    // render: (text, item) => Td(text, item, 'platform_id')
  },
  {
    title: '处理结果',
    dataIndex: 'error_filed',
    key: 'error_filed ',
    render: (text, item) => {
      console.log(text)
      return (<div style={{ color: !text?.length ? '' : 'red' }}>{!text?.length ? '成功' : '失败'}</div>)
    }
  },
  {
    title: '原因',
    dataIndex: 'error_message',
    key: 'error_message ',
  },
];
const ImportDialog = forwardRef((props: any, ref) => {
  const { onUpdate } = props;
  const [visible, setVisible] = useState(false);
  const [result, setResult] = useState<any>({
    data: [],
    uploadData: [],
    err: 0,
    importResult: null
  })
  const inputRef = useRef(null);
  useImperativeHandle(
    ref,
    () => {
      return {
        open() {
          setResult({
            data: [],
            uploadData: [],
            err: 0,
          })
          setVisible(true);
        },
      };
    },
    []
  );

  const uploadRef = useRef<any>(null)
  const handleUplaod = async () => {
    const file = uploadRef.current?.files[0]
    const result = await userApi.importCheckSpiderBind({ file })
    let err = 0;
    const uploadData = result.data.check_list.flatMap(item => {
      if (!item.skip) {
        return [{
          ...item,
          doc_type: item.doc_type.split('+').map(key => {
            return doc_type[key]
          }).toString()
        }]
      } else {
        return []
      }
    })
    const data = result.data.check_list.map((item, index) => {
      if (item.skip) {
        err++
      }
      return {
        ...item,
        index
      }
    })
    setResult({ data, err, uploadData, importResult: null })
  }
  return (
    <Modal
      title="批量导入抓取账号"
      visible={visible}
      width="900px"
      okButtonProps={{ disabled: !result.data.length || !result.uploadData.length }}
      onOk={async () => {
        if (!!result.importResult) {
          setVisible(false);
        } else {
          const parameters = result.uploadData.map(item => {
            const { account_id,
              nick_name,
              platform_type,
              doc_type,
              platform_name,
              chao_id,
              platform_id } = item;
            return {
              account_id,
              nick_name,
              platform_type,
              doc_type,
              platform_name,
              platform_id,
              chao_id
            }
          })
          const response: any = await userApi.importSubmitSpiderBind(parameters)

          const list = response.data.submit_list.map((item, index) => {
            return {
              ...item,
              index
            }
          })

          const importResult = {
            ...response.data,
            submit_list: list
          }

          setResult({
            ...result, importResult: importResult
          })
          onUpdate()
        }
      }}
      onCancel={() => {
        setVisible(false);
      }}
      maskClosable={false}
    >
      {result.importResult ? (
        <div>
          <div>
            导入{result.importResult?.success_count + result.importResult?.failed_count}条数据,成功{result.importResult?.success_count}条，<span style={{ color: 'red' }}>失败{result.importResult?.failed_count}条</span>，具体如下
          </div>
          <ATable columns={ImportResultColumns} dataSource={result.importResult.submit_list} />
        </div>
      ) : (!result.data.length ? (
        <div>
          <div style={{ width: '100%', display: 'flex', justifyContent: 'center' }}>
            <input style={{ display: 'none' }} type="file" ref={uploadRef} accept={'.xls,.xlsx'} onChange={handleUplaod} />
            <Button type="primary" icon="upload" size="large" onClick={() => {
              uploadRef.current?.click()
            }}>
              上传文件
            </Button>
          </div>
          <div style={{ marginTop: '20px', textAlign: 'center' }}>
            提示：单次最多可导入1000条数据，
            <A
              onClick={() => {
                handleDownload('https://app-stc.zjol.com.cn/static/custom/批量导入抓取账号模板.xlsx');
              }}
            >
              点击下载模板
            </A>
            ，请按格式上传excel文件
          </div>
        </div>
      ) : (
        <div>
          {result.err ? <div>
            解析出{result.data.length}条数据（{result.err}条格式有误），请核对是否需要重新修改上传。点击确定后，只导入格式正确的数据
          </div> : ''}
          <ATable key={Date.now()} columns={ImportColumns} dataSource={result.data} />
        </div>
      ))}
    </Modal>
  );
});

const FormDrawer = forwardRef((props, ref) => {
  const { getFieldDecorator, validateFields, getFieldsValue, resetFields, setFieldsValue } = props.form;
  const { initialValue, onUpdateAccountId } = props
  const [searchData, setSearchData] = useState<any>({
    fetching: false,
    data: [],
  })
  useEffect(() => {
    if (initialValue.account_id) {
      setSearchData({
        ...searchData,
        data: [
          {
            account_id: initialValue.account_id,
            nick_name: initialValue.nick_name,
            id: initialValue.account_id,
            chao_id: initialValue.chao_id,
            cert_type: initialValue.cert_type,
          }
        ]
      })
    }
  }, [initialValue.account_id])
  let timer = null;

  useImperativeHandle(ref, () => {
    return {
      validateFields,
      getFieldsValue,
      resetFields() {
        resetFields();
        setSearchData({
          data: [],
          fetching: false
        })
      }
    }
  }, [])

  const fetchUser = (nick_name) => {
    clearInterval(timer)
    if (!searchData.fetching) {
      setSearchData({
        ...searchData,
        fetching: true
      })
    }
    timer = setInterval(() => {
      if (nick_name) {
        communityApi.recommendAccount_Search({
          keyword: nick_name,
          size: 10,
          current: 1
        }).then((res) => {
          setSearchData({
            data: res.data.list,
            fetching: false
          })
        })
      } else {
        setSearchData({
          data: [],
          fetching: false
        })
      }
      clearInterval(timer)
    }, 1000)
  }

  const handleNickNameChange = (e) => {
    console.log(e)
    const data = searchData.data.find(item => {
      return item.id === e
    })
    setFieldsValue({
      chao_id: data.chao_id,
      nick_name: data.nick_name,
    })
    onUpdateAccountId(data.id)
  }

  return <Form style={{ width: '100%' }} labelCol={{ span: 5 }} wrapperCol={{ span: 18 }} layout="horizontal">
    <Form.Item style={{ display: "none" }}>
      {getFieldDecorator('chao_id', {
        initialValue: initialValue.chao_id || '',
      })(
        <Input />
      )}
    </Form.Item>
    <Form.Item style={{ display: "none" }}>
      {getFieldDecorator('nick_name', {
        initialValue: initialValue.nick_name || '',
      })(
        <Input />
      )}
    </Form.Item>

    <Form.Item label="用户">
      {getFieldDecorator('account_id', {
        initialValue: initialValue.account_id || undefined,
        rules: [{ required: true, message: '请输入要绑定抓取账号的用户昵称', }],
      })(
        <Select
          disabled={initialValue.account_id}
          showSearch
          defaultActiveFirstOption={false}
          showArrow={false}
          filterOption={false}
          placeholder="请输入要绑定抓取账号的用户昵称或用户ID"
          notFoundContent={searchData.fetching ? <Spin size="small" /> : null}
          onSearch={fetchUser}
          style={{ width: '100%' }}
          onChange={handleNickNameChange}
        >
          {searchData.data.map(d => (
            <Option key={d.id} value={d.id}>
              {['潮客 - ', '潮鸣号 - ', '潮鸣号 - '][d.cert_type] + d.nick_name} | 用户ID：{d.chao_id}</Option>
          ))}
        </Select>
      )}
    </Form.Item>
    <Form.Item label="抓取平台">
      {getFieldDecorator('platform_type', {
        initialValue: initialValue.platform_type,
        rules: [{ required: true, message: '请选择抓取平台' }],
      })(
        <Radio.Group disabled={initialValue.platform_type} style={{ lineHeight: 2.5 }}>
          {platform.map((item) => {
            return <Radio key={item[0]} value={item[1]}>{item[0]}</Radio>;
          })}
        </Radio.Group>
      )}
    </Form.Item>
    <Form.Item label="抓取内容类型">
      {getFieldDecorator('doc_type', {
        initialValue: initialValue.doc_type,
        rules: [{ required: true, message: '请选择抓取内容类型' }],
      })(
        <Checkbox.Group>
          {contentType.map((item) => {
            return <Checkbox key={item[1]} value={item[1]}>{item[0]}</Checkbox>;
          })}
        </Checkbox.Group>
      )}
    </Form.Item>
    <Form.Item label="抓取账号名">
      {getFieldDecorator('platform_name', {
        initialValue: initialValue.platform_name,
        rules: [{ required: true, message: '请输入抓取账号名' }],
      })(<Input placeholder="请输入抓取平台的账号名" maxLength={50} />)}
    </Form.Item>
    <Form.Item label="抓取账号id">
      {getFieldDecorator('platform_id', {
        initialValue: initialValue.platform_id,
        rules: [],
      })(<Input placeholder="请输入抓取平台的账号id，如有，请填写" maxLength={50} />)}
    </Form.Item>
  </Form>
})


const WithForm = Form.create({ name: 'account' })(FormDrawer);

const WithFormDrawer = forwardRef<DialogRef, any>((props, ref) => {
  const [visible, setVisible] = useState(false);
  const [title, setTitle] = useState('')
  const [id, setId] = useState('')
  const defaultValue = {
    nick_name: '',
    platform_type: '',
    doc_type: [],
    platform_name: '',
    platform_id: '',
  }
  const [initialValue, setInitialValue] = useState(defaultValue)
  const account_id = useRef('')
  const { onUpdate } = props;
  const formRef = useRef(null)
  useImperativeHandle(
    ref,
    () => {
      return {
        open(isEdit = false, value) {
          account_id.current = ''
          setVisible(true);
          formRef.current?.resetFields()
          setTitle('添加')
          setInitialValue({
            ...defaultValue
          })
          setId('')
          if (isEdit) {
            setTitle('编辑')
            account_id.current = value.account_id
            const data = {
              ...value, doc_type: value.doc_type.split(',').map((item: string) => {
                return Number.parseInt(item)
              })
            }
            setId(data.id)
            setInitialValue({
              ...data
            })
          }
        },

      };
    },
    []
  );
  const handleConfirm = () => {
    formRef.current?.validateFields(async (err) => {
      if (!err) {
        const value = formRef.current?.getFieldsValue()
        await userApi[id ? 'updateSpiderBind' : 'createSpiderBind']({
          id,
          ...value,
          platform_name: value.platform_name.trim(),
          platform_id: value.platform_id.trim(),
          account_id: account_id.current,
          doc_type: value.doc_type.toString()
        })
        setVisible(false)
        onUpdate()
      }
    })
  }
  return (
    <Drawer
      title={`${title}抓取账号`}
      width={720}
      onClose={() => {
        setVisible(false);
      }}
      visible={visible}
      bodyStyle={{ paddingBottom: 80 }}
      maskClosable={false}
    >
      <WithForm initialValue={initialValue} wrappedComponentRef={formRef} onUpdateAccountId={(id) => { account_id.current = id }} />
      <div
        style={{
          position: 'absolute',
          right: 0,
          bottom: 0,
          width: '100%',
          borderTop: '1px solid #e9e9e9',
          padding: '10px 16px',
          background: '#fff',
          textAlign: 'right',
        }}
      >
        <Button
          onClick={() => {
            setVisible(false);
          }}
          style={{ marginRight: 8 }}
        >
          取消
        </Button>
        <Button
          onClick={handleConfirm}
          type="primary"
        >
          确定
        </Button>
      </div>
    </Drawer>
  );
});


const SpiderBind: React.FC<{
  breadCrumb: string[];
  tableList: {
    current: number;
    size: number;
  };
  selectKeys: string;
  openKeys: string;
}> = ({ breadCrumb, tableList, selectKeys, openKeys }) => {
  const { current, size } = useSelector((state: any) => state.tableList)
  const dispatch = useDispatch();

  const store = useStore();
  const { session } = useStore().getState();
  const [searchFilter, setSearchFilter] = useState({
    search_type: 1,
    keyword: '',
  })

  const [pullDataModel, setPullDataModel] = useState<any>({
    visible: false,
    skey: Date.now(),
    formContent: null,
  })

  const [userDetailModal, setUserDetailModal] = useState<any>({
    visible: false,
    key: uuid(),
    detail: null,
  })

  const [filter, setFilter] = useState({
    current: 1,
    size: 10,
    platform_type: '',
  });
  const getData = () => {
    // console.log('+++++++++++', current, size)
    dispatch(getTableList('getSpiderBindList', 'list', { ...filter, ...searchFilter, current, size }));
  };

  // 初始化
  useEffect(() => {
    dispatch(setConfig({ selectKeys, openKeys }));
    getData();
  }, [filter]);

  const getFilters = () => {
    console.log({ ...filter, ...searchFilter })
    return { ...filter, ...searchFilter, current, size };
  }

  const deleteConfirm = (id: any) => {
    Modal.confirm({
      title: '确定删除该抓取账号？',
      content: '已抓取的内容不会被删除，只是不再抓取新内容',
      okText: '确认',
      cancelText: '取消',
      onOk: async () => {
        await userApi.deleteSpiderBind({ id })
        getData();
      }
    })
  }

  const pull = (item: any) => {
    setPullDataModel({
      visible: true,
      skey: Date.now(),
      formContent: item,
    })
  }

  const showUserDetailModal = (record: any) => {
    dispatch(setConfig({ loading: true }))
    userApi
      .getUserDetail({ accountId: record.account_id })
      .then((r: any) => {
        dispatch(setConfig({ loading: false }))
        setUserDetailModal({
          key: uuid(),
          visible: true,
          detail: r.data.account,
        })
      })
      .catch(() => dispatch(setConfig({ loading: false })));
  };

  const getColumns = () => {
    const { current, size } = tableList;
    const getSeq = (i: number) => (current - 1) * size + i + 1;
    return [
      {
        title: '序号',
        key: 'seq',
        render: (text: any, record: any, i: number) => <span>{getSeq(i)}</span>,
        width: 45,
      },
      {
        title: '用户ID',
        key: "chao_id",
        dataIndex: "chao_id",
      },
      {
        title: '用户昵称',
        width: 90,
        key: 'nick_name',
        dataIndex: 'nick_name',
        render: (text: any, record: any) => <a onClick={() => showUserDetailModal(record)}>{text}</a>
      },
      {
        title: '用户类型',
        key: 'cert_type',
        dataIndex: 'cert_type',
        width: 90,
        render: (text: any, record: any, i: number) => <span>{['潮客', '潮鸣号', '潮鸣号'][text]}</span>,
      },
      {
        title: '抓取平台',
        width: 90,
        key: 'platform_type',
        dataIndex: 'platform_type',
        render: (text: any, record: any, i: number) => {
          const data = platform.find(item => item[1] === text);
          return <span>{data ? data[0] : ''}</span>
        }
      },
      {
        title: '抓取内容类型',
        width: 120,
        key: 'doc_type',
        dataIndex: 'doc_type',
        render(text: any) {
          return <div>{(text && text?.split) ? text?.split(',').map(item => {
            return doc_type[item]
          }).toString() : ''}</div>
        }
      },
      {
        title: '抓取账号名',
        key: 'platform_name',
        dataIndex: 'platform_name',
      },
      {
        title: '抓取账号id',
        width: 120,
        key: 'platform_id',
        dataIndex: 'platform_id',
      },
      {
        title: '最后操作人',
        key: 'updated_by',
        dataIndex: 'updated_by',
      },
      {
        title: '添加时间',
        key: 'created_at',
        dataIndex: 'created_at',
        render(text: string) {
          return <div>{moment(text).format("YYYY-MM-DD HH:mm:ss")}</div>
        }
      },
      {
        title: '操作',
        key: 'op',
        dataIndex: 'op',
        render: (text: any, item: any) => (
          <Dropdown overlay={<Menu>
            {requirePerm4Function(session, 'spider_bind:update')(
              <Menu.Item onClick={() => drawerRef.current?.open(true, item)}>编辑</Menu.Item>
            )}
            {requirePerm4Function(session, 'spider_bind:delete')(
              <Menu.Item onClick={() => deleteConfirm(item.id)}>删除</Menu.Item>
            )}
            {requirePerm4Function(session, 'spider_bind:manual_spider')(
              <Menu.Item onClick={() => pull(item)}>拉取数据</Menu.Item>
            )}
          </Menu>}>
            <a className="ant-dropdown-link">
              操作 <Icon type="down" />
            </a>
          </Dropdown>
        ),
      },
    ];
  };

  const drawerRef = useRef<DialogRef>(null);
  const importRef = useRef<DialogRef>(null);
  return (
    <>
      <WithFormDrawer ref={drawerRef} onUpdate={getData} />
      <ImportDialog ref={importRef} onUpdate={getData} />
      <Row className="layout-infobar">
        <Col span={12}>
          <PermButton
            perm="spider_bind:create"
            onClick={() => {
              drawerRef.current?.open();
            }}
            style={{ marginRight: 8 }}
          >
            添加抓取账号
          </PermButton>
          <PermButton
            perm='spider_bind:import'
            onClick={() => {
              importRef.current?.open();
            }}
          >
            批量导入抓取账号
          </PermButton>
        </Col>
        <Col span={12} className="layout-breadcrumb">
          {getCrumb(breadCrumb)}
        </Col>
      </Row>
      <div className="component-content">
        <Row style={{ marginBottom: 16 }}>
          <Col span={12}>
            <Select value={filter.platform_type} style={{ marginRight: 8, width: 120 }} onChange={(platform_type) => {
              setFilter({
                ...filter,
                platform_type
              })
            }}>
              <Option value="">抓取平台</Option>
              {platform.map((item) => (
                <Option key={item[0]} value={item[1]}>{item[0]}</Option>
              ))}
            </Select>
          </Col>
          <Col span={12} style={{ textAlign: 'right' }}>
            <Select value={searchFilter.search_type} style={{ marginLeft: 8, width: 120 }} onChange={(search_type) => {
              setSearchFilter({
                ...searchFilter,
                search_type
              })
            }}>
              {type.map((item, index) => (
                <Option key={item[0]} value={index + 1}>{item[0]}</Option>
              ))}
            </Select>
            <Input
              value={searchFilter.keyword}
              style={{ marginLeft: 8, width: 120 }}
              onChange={(e: any) => setSearchFilter({ ...searchFilter, keyword: e.target.value })}
              placeholder="输入搜索内容"
            />
            <Button style={{ marginLeft: 8 }} onClick={getData}>
              <Icon type="search" /> 搜索
            </Button>
          </Col>
        </Row>
        <Table
          func="getSpiderBindList"
          index="list"
          rowKey="id"
          filter={getFilters()}
          columns={getColumns()}
          pagination={true}
        />
        <PullDataModal
          {...pullDataModel}
          onCancel={() => setPullDataModel({ visible: false })}
          onOk={() => {
            setPullDataModel({ visible: false })
            // getTableList()
            // props.onReload && props.onReload()
          }}
        ></PullDataModal>

        {/* 用户详情弹窗 */}
        <Modal
          visible={userDetailModal.visible}
          key={userDetailModal.key}
          title="用户详情"
          width={800}
          onCancel={() => setUserDetailModal({ visible: false })}
          onOk={() => setUserDetailModal({ visible: false })}
        >
          {/*{user.visible && getUserDetail(user.detail)}*/}
          {userDetailModal.visible && <UserDetail detail={userDetailModal.detail} />}
        </Modal>

      </div>
    </>
  );
};

export default withRouter(connect()(SpiderBind));
