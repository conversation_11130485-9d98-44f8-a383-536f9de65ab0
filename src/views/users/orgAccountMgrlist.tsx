import {
  Col,
  Divider,
  Input,
  Modal,
  Row,
  message,
  InputNumber,
  Button,
  Icon,
  Tooltip,
  Select,
} from 'antd';
import {
  getCrumb,
} from '@app/utils/utils';
import { useHistory, useParams } from 'react-router';
import { useSelector, useDispatch, useStore } from 'react-redux';
import { getTableList } from '@app/action/tableList';
import { Table, OrderColumn } from '@components/common';
import React, { useEffect, useState } from 'react';
import { userApi } from '@app/api';
import { PermA, PermButton } from '@app/components/permItems';
import { setConfig } from '@app/action/config';
import uuid from 'uuid';
import AddOrgClassAccount from './addOrgClassAccount';
import ClassAccountSortModal from './classAccountSortModal';

export default function OrgAccountMgrlist(props: any) {
  const history = useHistory();
  const dispatch = useDispatch();
  const { id, name } = useParams<any>();
  const { total, current, size, records = [] } = useSelector((state: any) => state.tableList);
  const { TextArea } = Input;
  const { session } = useStore().getState();
  const [filter, setFilter] = useState({
    class_id: id,
  });

  const [search, setSearch] = useState({
    search_type: 1,
    keyword: ''
  })

  const [accountModal, setAccountModal] = useState<any>({
    visible: false,
    class_id: id,
  })

  const [accountSort, setAccountSort] = useState<any>({
    visible: false,
    max: 0,
    pos: 1,
    formContent: null,
    fix: false
  })

  const getSeq = (i: number) => (current - 1) * size + i + 1;

  const columns: any = [
    ...(!filter.keyword ? [{
      title: '序号',
      dataIndex: 'seq',
      // width: 50,
      render: (a: any, record: any, index: number) => {
        const fixed_position = record.fixed_position || 0
        return <>
          <span style={{ marginRight: 8 }}>{getSeq(index)}</span>
          {fixed_position > 0 && (
            <PermA
              perm={`tmh_class_account:mgr`}
              // disabled={this.state.filter.recommend_enabled === '1'}
              onClick={() => { fixPos(record, getSeq(index)) }}
            >
              <Icon type="pushpin" theme="filled" />
            </PermA>
          )}
          {fixed_position == 0 && (
            <PermA
              perm={`tmh_class_account:mgr`}
              // disabled={this.state.filter.recommend_enabled === '1'}
              onClick={() => { fixPos(record, getSeq(index)) }}
            >
              <Icon type="pushpin" />
            </PermA>
          )}
        </>
      }
    }] : []),
    {
      title: '用户ID',
      dataIndex: 'chao_id',
      // width: 160
    },
    {
      title: '潮鸣号名称',
      dataIndex: 'nick_name'
    },
    {
      title: <div>近30天发布内容数
        <Tooltip title={<span>1、包括主动发布及订阅抓取的内容，仅统计审核通过且未删除的<br></br>2、数据统计会有一定延迟，5～10分钟</span>} placement="topLeft">
          <Icon type="question-circle" style={{ marginLeft: 5 }} />
        </Tooltip>
      </div>,
      dataIndex: 'article_count'
    },
    {
      title: '操作',
      key: 'op',
      render: (text: any, record: any, i: number) => (
        <span>
          <PermA perm="tmh_class_account:mgr" onClick={() => handelDel(record)}>
            移除
          </PermA>
          <Divider type="vertical" />
          <PermA perm="tmh_class_account:mgr" onClick={() => handelSort(record, getSeq(i))}>
            排序
          </PermA>
        </span>
      ),
      // width: 180,
    },
  ];

  // 排序
  const handelSort = (record: any, pos: any) => {
    setAccountSort({
      visible: true,
      max: total,
      pos: pos,
      fix: record.fixed_position > 0,
      fixPos: record.fixed_position,
      formContent: {
        class_id: id,
        account_id: record.account_id
      }
    })
  };

  const fixPos = (record: any, pos: any) => {
    dispatch(setConfig({ loading: true }));
    const params: any = { class_id: id, account_id: record.account_id }
    if (record.fixed_position == 0) {
      params.position = pos
    }
    userApi
      .orderOrgClassAccount(params)
      .then(() => {
        message.success('操作成功');
        dispatch(setConfig({ loading: false }));
        getData();
      })
      .catch(() => {
        dispatch(setConfig({ loading: false }));
      });
  }

  // 删除
  const handelDel = (val: any) => {
    Modal.confirm({
      title: '确定将该账号从当前分类移除？',
      onOk: () => {
        dispatch(setConfig({ loading: true }));
        userApi
          .deleteOrgClassAccount({ class_id: id, account_id: val.account_id })
          .then(() => {
            message.success('操作成功');
            dispatch(setConfig({ loading: false }));
            getData();
          })
          .catch(() => {
            dispatch(setConfig({ loading: false }));
          });
      },
    });
  };


  // 获取table
  const getData = (goToFirstPage = false, newFilter = filter) => {
    let cur = goToFirstPage ? 1 : current;
    dispatch(getTableList('getOrgClassAccountList', 'list', { current: cur, size, ...newFilter }));
  };
  // 添加
  const add = () => {
    setAccountModal({
      visible: true,
      class_id: id,
      class_name: name,
    })
  };

  useEffect(() => {
    const { selectKeys, openKeys } = props;
    dispatch(setConfig({ selectKeys, openKeys }));
  }, []);

  useEffect(() => {
    getData(true);
  }, [filter])

  const handleKey = (e: { which: number }) => {
    if (e.which === 13) {
      const newFilter: any = {
        ...filter,
        keyword: search.keyword || '',
        search_type: search.search_type
      };
      
      if (!search.keyword) {
        delete newFilter.keyword
        delete newFilter.search_type
      }

      setFilter(newFilter);
    }
  };

  return (
    <>
      <Row className="layout-infobar">
        <Col span={12}>
          <Button style={{ marginRight: 8 }} onClick={() => history.goBack()}>
            <Icon type="left-circle" /> 返回
          </Button>

          <PermButton
            perm="tmh_class_account:mgr"
            onClick={() => {
              add();
            }}
            style={{ marginLeft: 8 }}
          >
            <Icon type="plus-circle" /> 添加潮鸣号
          </PermButton>
        </Col>
        <Col span={12} className="layout-breadcrumb">
          {getCrumb(props.breadCrumb.concat(name))}
        </Col>
      </Row>
      <div className="component-content">
        <Row>
        <Col span={24} style={{ textAlign: 'right', marginBottom: 10 }}>
            <Select
              value={search.search_type}
              style={{ width: 150, marginRight: 8, marginLeft: 8 }}
              onChange={(e) => {
                setSearch({ ...search, search_type: e });
              }}
            >
              <Select.Option value={1}>潮鸣号名称</Select.Option>
              <Select.Option value={2}>用户长ID</Select.Option>
              <Select.Option value={3}>用户ID</Select.Option>
            </Select>

            <Input
              style={{ width: 160, marginRight: 8 }}
              onKeyPress={(e) => handleKey(e)}
              value={search.keyword}
              placeholder={'请输入搜索内容'}
              onChange={(e) => setSearch({ ...search, keyword: e.target.value })}
            />
            <Button onClick={() => handleKey({ which: 13 })}>
              <Icon type="search" />
              搜索
            </Button>
          </Col>
        </Row>
        <Table
          func="getOrgClassAccountList"
          index="list"
          pagination={true}
          rowKey="id"
          columns={columns}
          filter={filter}
        />
      </div>

      <AddOrgClassAccount
        {...accountModal}
        onCancel={() => setAccountModal({ visible: false })}
        onOk={() => {
          setAccountModal({ visible: false })
          getData()
        }}
      ></AddOrgClassAccount>

      <ClassAccountSortModal {...accountSort}
        onCancel={() => setAccountSort({ visible: false })}
        onOk={() => {
          setAccountSort({ visible: false })
          getData(false);
        }}
      >
      </ClassAccountSortModal>

    </>
  );
}
