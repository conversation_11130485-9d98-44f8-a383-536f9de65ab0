import SearchAndInput from '@components/common/newNewsSearchAndInput';
import {
  Button,
  Checkbox,
  Dropdown,
  Form,
  Icon,
  Input,
  InputNumber,
  Menu,
  Modal,
  Radio,
  Select,
  Spin,
  Tooltip,
  message,
} from 'antd';
import React, { forwardRef, useEffect, useImperativeHandle, useRef, useState } from 'react';

import { communityApi, userApi } from '@app/api';
import '@components/business/styles/business.scss';
import { useDispatch } from 'react-redux';
import { setConfig } from '@app/action/config';
import { A, Drawer, ImageUploader, VideoUploader } from '@app/components/common';
import ImgUploadList from '@app/components/common/imgUploadList';
import _, { set } from 'lodash';
import PermsCheckbox from '@app/components/common/PermsCheckbox';
import { render } from 'react-dom';
import { getImageRatio } from '@app/utils/utils';
import TMFormList, { FormListTitle, TMFormListRef } from '@app/components/common/TMFormList';

const AddCooperationAccountDrawer = (props: any, ref: any) => {
  const dispatch = useDispatch();
  const [accountOptions, setAccountOptions] = useState([]);
  const [platformList, setPlatformList] = useState([]);
  const platformListRef = useRef<TMFormListRef>(null);
  const { getFieldDecorator, getFieldValue, getFieldsValue, setFieldsValue } = props.form;
  useImperativeHandle(
    ref,
    () => ({
      ...props.form,
      doSubmit,
    }),
    []
  );

  useEffect(() => {
    setPlatformList(props.formContent?.platform_list || []);
    if (!!props.formContent && !!props.formContent.account_name) {
      handleAccountSearch(props.formContent.account_name);
    } else {
      handleAccountSearch('');
    }
  }, [props.formContent]);

  const doSubmit = () => {
    props.form.validateFieldsAndScroll((err: any, values: any) => {
      if (!err) {
        const body = {
          ...values,
        };

        if (values.platform_list?.length > 0) {
          const uniquePlatformList = new Set(values.platform_list.map((item: any) => item.name));
          if (uniquePlatformList.size !== values.platform_list.length) {
            message.error('请勿添加重复的推广平台');
            return;
          }
        }

        if (props.formContent?.id) {
          body.id = props.formContent?.id;
        }
        dispatch(setConfig({ mLoading: true }));
        userApi
          .editCooperationAccount(body)
          .then(() => {
            dispatch(setConfig({ mLoading: false }));
            message.success('操作成功');
            props.onEnd();
          })
          .catch(() => {
            dispatch(setConfig({ mLoading: false }));
          });
      } else {
        message.error('请检查表单内容');
      }
    });
  };

  const handleAccountSearch = _.debounce((val: any) => {
    if (!val?.trim()) {
      setAccountOptions([]);
      return;
    }

    communityApi
      .recommendAccount_Search({ keyword: val })
      .then(({ data }) => {
        const { list } = data as any;
        setAccountOptions(list || []);
      })
      .catch(() => {});
  }, 500);

  const editPic = async (url: string, record: object, index: number) => {
    let pic: string = url;
    let modal: any;
    let proportion = (3 / 4).toFixed(2);
    let consistentProportions = true;
    if (url) {
      const afterCalculation = await getImageRatio(url);
      if (Math.abs(afterCalculation - proportion) <= 0.05) {
        consistentProportions = false;
      } else {
        consistentProportions = true;
      }
    }

    const picChange = (u: string) => {
      pic = u;
      modal.update({
        content: (
          <>
            <ImageUploader value={pic} onChange={picChange} ratio={3 / 4} />
            <p> 比例3: 4</p>
          </>
        ),
      });
    };
    modal = Modal.confirm({
      title: '编辑图片',
      width: 500,
      content: (
        <>
          <ImageUploader value={pic} onChange={picChange} imgMaxWidth={400} ratio={3 / 4} />
          {consistentProportions && url ? (
            <span style={{ color: 'red' }}>
              该图片比例非{3}:{4}，图片将自动居中截取，建议重新上传。
            </span>
          ) : (
            <p>
              建议比例{3}:{4}
            </p>
          )}
        </>
      ),
      onOk: async (destroy: Function) => {
        if (!pic) {
          message.error('请上传图片');
          return;
        }

        const works_list = getFieldsValue().works_list;
        works_list[index].picture = pic;
        setFieldsValue({ works_list: works_list });
        destroy();
      },
    });
  };

  const formLayout = {
    labelCol: { span: 4 },
    wrapperCol: { span: 18 },
  };

  const columns: any = [
    {
      title: '标题',
      dataIndex: 'title',
    },
    {
      title: '类型',
      key: 'doc_type',
      dataIndex: 'doc_type',
      width: 90,
      render: (text: any, record: any) => ['小视频', '', '短图文', '长文章'][text - 10],
    },
    {
      title: '封面图',
      align: 'center',
      key: 'picture',
      dataIndex: 'picture',
      width: 120,
      render: (text: string, record: any, index: number) => (
        <div style={{ display: 'flex', justifyContent: 'space-around', alignItems: 'center' }}>
          {text && <img src={text} style={{ height: 40, maxWidth: 70 }} />}
          <A onClick={() => editPic(text, record, index)}>{text ? '编辑' : '上传'}</A>
        </div>
      ),
    },
  ];

  return (
    <Drawer
      title={!!props.formContent ? '编辑合作账号' : '添加合作账号'}
      visible={props.visible}
      skey={props.skey}
      onClose={props.onClose}
      onOk={doSubmit}
      okText="保存"
      // footer={((<><Button style={{ width: 100, marginRight: 8 }} onClick={props.onClose}>取消</Button>
      //   <Dropdown trigger={['hover']} overlay={menu} placement="topCenter">
      //     <PermButton style={{ width: 100 }} type="primary" perm='' onClick={props.onOk}>
      //       保存<Icon type="up" />
      //     </PermButton>
      //   </Dropdown>

      // </>))}
    >
      <Form {...formLayout}>
        <Form.Item label="账号">
          {getFieldDecorator('account_id', {
            initialValue: !!props.formContent ? props.formContent.account_id : undefined,
            rules: [
              {
                required: true,
                message: '请输入用户昵称',
              },
            ],
          })(
            <Select
              style={{ width: '100%' }}
              placeholder="请输入昵称或用户ID"
              onSearch={handleAccountSearch}
              showSearch
              disabled={!!props.formContent}
              filterOption={false}
              onChange={(value: any) => {
                setFieldsValue({ works_list: [] });
              }}
            >
              {accountOptions.map((item: any) => (
                <Select.Option key={item.id} value={item.id}>
                  {['潮客 - ', '潮鸣号 - ', '潮鸣号 - '][item.cert_type] + item.nick_name} |
                  用户ID： {item.chao_id}
                </Select.Option>
              ))}
            </Select>
          )}
        </Form.Item>

        {!props.formContent && (
          <Form.Item label="排序值">
            {getFieldDecorator('position', {
              initialValue: props.formContent?.position,
            })(
              <InputNumber
                style={{ width: '100%' }}
                placeholder="为空时，默认放在列表最前"
                precision={0}
                max={999}
              ></InputNumber>
            )}
          </Form.Item>
        )}
        <Form.Item label="一句话介绍">
          {getFieldDecorator('desc', {
            initialValue: props.formContent?.desc,
            // rules: [{}],
          })(<Input placeholder="最多20字；为空时，自动读取账号简介" maxLength={20}></Input>)}
        </Form.Item>

        <Form.Item label="类型">
          {getFieldDecorator('type', {
            initialValue: props.formContent?.type ?? 1,
            rules: [
              {
                required: true,
                message: '请选择类型',
              },
            ],
          })(
            <Radio.Group>
              <Radio value={1}>文化</Radio>
              <Radio value={2}>文旅</Radio>
              <Radio value={3}>亲子</Radio>
              <Radio value={4}>美食</Radio>
              <Radio value={5}>汽车</Radio>
              <Radio value={6}>航拍</Radio>
              <Radio value={7}>主持</Radio>
              <Radio value={8}>生活</Radio>
              <Radio value={9}>财经</Radio>
              <Radio value={10}>科普</Radio>
              <Radio value={11}>律师</Radio>
              <Radio value={12}>海外</Radio>
            </Radio.Group>
          )}
        </Form.Item>

        <Form.Item label="全网粉丝">
          {getFieldDecorator('fans_count', {
            initialValue: props.formContent?.fans_count,
            // rules: [{}],
          })(<Input placeholder="选填，最多10字" maxLength={10}></Input>)}
        </Form.Item>

        <Form.Item label="合作方向">
          {getFieldDecorator('scope', {
            initialValue: props.formContent?.scope,
            // rules: [{}],
          })(<Input placeholder="选填，最多20字" maxLength={20}></Input>)}
        </Form.Item>

        <Form.Item label="推广平台">
          <Button onClick={() => platformListRef.current?.addItem()}>添加推广平台</Button>
        </Form.Item>

        <TMFormList
          ref={platformListRef}
          dataList={platformList}
          form={props.form}
          fromItem={() => {
            return {
              name: '',
              url: '',
            };
          }}
          filed="platform_list"
        >
          {(item, index, length) => {
            return (
              <div key={`${item.name}-${index}`}>
                <FormListTitle
                  total={length}
                  i={index}
                  title="平台"
                  upMove={() => platformListRef.current?.upMove(index)}
                  downMove={() => platformListRef.current?.downMove(index)}
                  removeItem={() => platformListRef.current?.removeItem(index)}
                  allowClear={true}
                />
                <div style={{ display: 'flex', alignItems: 'center', paddingLeft: '105px' }}>
                  <Form.Item label=" " colon={false}>
                    {getFieldDecorator(`platform_list[${index}].name`, {
                      initialValue: item.name,
                      rules: [
                        {
                          // required: true,
                          validator: (rule: any, value: any, callback: any) => {
                            if (!value) {
                              return callback('请选择平台');
                            } else {
                              return callback();
                            }
                          },
                        },
                      ],
                    })(
                      <Select style={{ width: '150px' }}>
                        <Select.Option value="">请选择平台</Select.Option>
                        <Select.Option value="抖音">抖音</Select.Option>
                        <Select.Option value="微博">微博</Select.Option>
                        <Select.Option value="小红书">小红书</Select.Option>
                        <Select.Option value="微信公众号">微信公众号</Select.Option>
                        <Select.Option value="微信视频号">微信视频号</Select.Option>
                        <Select.Option value="快手">快手</Select.Option>
                        <Select.Option value="哔哩哔哩">哔哩哔哩</Select.Option>
                        <Select.Option value="Instagram">Instagram</Select.Option>
                        <Select.Option value="Twitter(X)">Twitter(X)</Select.Option>
                        <Select.Option value="YouTube">YouTube</Select.Option>
                        <Select.Option value="TikTok">TikTok</Select.Option>
                        <Select.Option value="Facebook">Facebook</Select.Option>
                      </Select>
                    )}
                  </Form.Item>
                  <Form.Item label=" " colon={false}>
                    {getFieldDecorator(`platform_list[${index}].url`, {
                      initialValue: item.url,
                      rules: [
                        {
                          // required: true,
                          validator: (rule: any, value: any, callback: any) => {
                            const regex = /^https?:\/\//;
                            if (!value) {
                              return callback();
                            } else if (!regex.test(value)) {
                              return callback('请正确填写链接格式');
                            } else {
                              return callback();
                            }
                          },
                        },
                      ],
                    })(
                      <Input
                        style={{ width: '345px', marginLeft: '-30px' }}
                        placeholder="请输入推广平台账号的主页链接，选填"
                      ></Input>
                    )}
                  </Form.Item>
                </div>
              </div>
            );
          }}
        </TMFormList>

        <Form.Item label="代表作">
          {getFieldDecorator('works_list', {
            initialValue: props.formContent?.works_list,
            rules: [
              {
                required: true,
                message: '为保证显示效果，请添加3个作品',
                type: 'array',
              },
              {
                max: 3,
                message: '最多可添加3个作品',
                type: 'array',
              },
              {
                min: 3,
                message: '为保证显示效果，请添加3个作品',
                type: 'array',
              },
              {
                validator: (rule: any, value: any, callback: any) => {
                  if (!value) {
                    return callback('');
                  } else if (value.filter((v: any) => !v.picture).length > 0) {
                    return callback('请上传封面图');
                  } else {
                    return callback();
                  }
                },
              },
            ],
          })(
            <SearchAndInput
              disabled={!getFieldValue('account_id')}
              max={3}
              func="getOrgUserArticleList"
              indexKey="article_list"
              columns={columns}
              apiWithPagination={true}
              placeholder="输入ID或标题查找，仅支持添加小视频、短图文、长文章"
              body={{ account_id: getFieldValue('account_id') }}
              order={true}
              selectMap={(article: any) => {
                return `${article.id} - 【${
                  ['小视频', '', '短图文', '长文章'][article.doc_type - 10]
                }】  ${article.list_title}`;
              }}
              map={(article: any) => {
                const list_pics = article.list_pics.split(',')[0] || null;
                return {
                  id: article.id,
                  picture: list_pics || null,
                  doc_type: article.doc_type,
                  title: article.list_title,
                };
              }}
            />
          )}
        </Form.Item>
      </Form>
    </Drawer>
  );
};

export default Form.create<any>({ name: 'AddCooperationAccountDrawer' })(
  forwardRef<any, any>(AddCooperationAccountDrawer)
);
