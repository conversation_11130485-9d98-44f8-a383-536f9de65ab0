import { userApi as api } from '@app/api';
import { ITableProps, IBaseProps, CommonObject } from '@app/types';
import { Table, BaseComponent, Drawer } from '@components/common';
import Form from '@components/business/virtualUserForm';
import { connectAll as connect } from '@utils/connect';
import { Button, Col, Icon, Row, Input, Select, Tooltip } from 'antd';
import moment from 'moment';
import React from 'react';
import { withRouter } from 'react-router';
import { VirtualUsersRecord, VirtualUsersAllData } from './users';

type State = {
  searchType: 'ref_code' | 'mobile' | 'nick_name';
  cType: 'ref_code' | 'mobile' | 'nick_name';
  keyword: string;
  cKeyword: string;
  form: {
    type: 0 | 1;
    visible: boolean;
    key: number;
    title: string;
  };
};

type Props = IBaseProps<ITableProps<VirtualUsersRecord, VirtualUsersAllData>>;

class VirtualAccount extends BaseComponent<
  ITableProps<VirtualUsersRecord, VirtualUsersAllData>,
  State
> {
  constructor(props: Props) {
    super(props);
    this.state = {
      searchType: 'mobile',
      keyword: '',
      cType: 'mobile',
      cKeyword: '',
      form: {
        visible: false,
        key: Date.now(),
        type: 1,
        title: '',
      },
    };
  }

  componentDidMount() {
    this.setMenu();
    this.getData({ current: 1, size: 10 });
  }

  getData = (overlap: CommonObject = {}, filter = this.getFilter()) => {
    this.dispatchTable('getVirtualUserList', 'account_list', { ...filter, ...overlap });
  };

  getFilter = () => {
    const { current, size } = this.props.tableList;
    const { searchType, keyword } = this.state;
    return {
      current,
      size,
      [searchType]: keyword,
      cert_type: 2,
      status: 2,
    };
  };

  getColumns = () => {
    const { current, size } = this.props.tableList;
    const getSeq = (i: number) => (current - 1) * size + i + 1;
    return [
      {
        title: '序号',
        key: 'seq',
        render: (text: any, record: any, i: number) => <span>{getSeq(i)}</span>,
        width: 70,
      },
      {
        title: '用户昵称',
        key: 'name',
        dataIndex: 'nick_name',
      },
      {
        title: '用户ID',
        key: 'chao_id',
        dataIndex: 'chao_id',
        width: 120
      },
      {
        title: '用户头像',
        key: 'image',
        dataIndex: 'image_url',
        render: (text: string) => <img src={text} className="list-pic" alt="" />,
        width: 120,
      },
      {
        title: '邀请人数',
        key: 'inv_num',
        dataIndex: 'invitation_number',
        width: 120,
      },
      {
        title: '虚拟号',
        key: 'mobile',
        dataIndex: 'phone_number',
        width: 110,
      },
      {
        title: '邀请码',
        key: 'works_count',
        dataIndex: 'ref_code',
        width: 180,
      },
      {
        title: '创建人',
        key: 'on_works_count',
        dataIndex: 'created_by',
        width: 130,
      },
      {
        title: '创建时间',
        key: 'fans_count',
        dataIndex: 'created_at',
        width: 180,
        render: (text: number) => <span>{moment(text).format('YYYY-MM-DD HH:mm:ss')}</span>,
      },
    ];
  };

  handleTypeChange = (value: 'ref_code' | 'mobile' | 'nick_name') => {
    this.setState({
      cType: value,
    });
  };

  handleKeywordChange = (e: any) => {
    this.setState({
      cKeyword: e.target.value,
    });
  };

  doSearch = () => {
    this.setState(
      {
        searchType: this.state.cType,
        keyword: this.state.cKeyword,
      },
      () => {
        this.getData({ current: 1 });
      }
    );
  };

  closeDrawer = () => {
    this.setState({
      form: {
        ...this.state.form,
        visible: false,
      },
    });
  };

  showForm = (type: 0 | 1) => {
    this.setState({
      form: {
        type,
        visible: true,
        key: Date.now(),
        title: type === 1 ? '创建潮鸣号' : '创建邀请码',
      },
    });
  };

  submitEnd = () => {
    this.getData();
    this.closeDrawer();
  };

  render() {
    const { cKeyword, cType, form } = this.state;
    return (
      <>
        <Row className="layout-infobar">
          <Col span={12} />
          <Col span={12} className="layout-breadcrumb">
            {this.getCrumb()}
          </Col>
        </Row>
        <div className="component-content">
          <Row style={{ marginBottom: 16 }}>
            <Col span={12}>
              {this.requirePerm('account:virtual_create')(
                <Button onClick={this.showForm.bind(this, 0)} style={{ marginRight: 8 }}>
                  <Icon type="plus-circle" /> 创建邀请码
                </Button>
              )}
            </Col>
            <Col span={12} style={{ textAlign: 'right' }}>
              <Select
                value={cType}
                onChange={this.handleTypeChange}
                style={{ marginRight: 8, width: 140 }}
              >
                <Select.Option value="mobile">搜索虚拟号</Select.Option>
                <Select.Option value="ref_code">搜索邀请码</Select.Option>
                <Select.Option value="nick_name">搜索昵称</Select.Option>
                <Select.Option value="chao_id">搜索用户ID</Select.Option>
              </Select>
              <Input
                value={cKeyword}
                onChange={this.handleKeywordChange}
                style={{ marginRight: 8, width: 160 }}
                placeholder="请输入搜索内容"
                onKeyPress={this.handleKey}
              />
              <Button onClick={this.doSearch}>
                <Icon type="search" /> 搜索
              </Button>
            </Col>
          </Row>
          <Table
            func="getVirtualUserList"
            index="account_list"
            filter={this.getFilter()}
            rowKey="id"
            columns={this.getColumns()}
            pagination={true}
          />
          <Drawer
            visible={form.visible}
            skey={form.key}
            title={form.title}
            onClose={this.closeDrawer}
            onOk={this.handleSubmitForm.bind(this, 'form')}
          >
            <Form
              type={form.type}
              wrappedComponentRef={this.setFormRef.bind(this, 'form')}
              onEnd={this.submitEnd}
            />
          </Drawer>
        </div>
      </>
    );
  }
}

export default withRouter(connect<VirtualUsersRecord, VirtualUsersAllData>()(VirtualAccount));
