import React, { useState, useEffect } from 'react';
import { useDispatch, useSelector, useStore } from 'react-redux';
import { useHistory } from 'react-router-dom';
import { Table, A, OrderColumn } from '@components/common';
import { getCrumb, setMenuHook } from '@app/utils/utils';
import { Row, Col, Input, Divider, Switch, message, Icon, Modal, Form, Button, DatePicker, Select, TimePicker } from 'antd';
import { getTableList } from '@app/action/tableList';
import { userApi as api, userApi } from '@app/api';
import useXHR from '@utils/useXhr';
import { PermA, PermButton } from '@app/components/permItems';
import { setConfig } from '@app/action/config';
import moment from 'moment';
import CkExponentConfigDrawer from './ckExponentConfigDrawer';

const format = (date: string, type = 'YYYY.MM.DD') => {
  if (!date) return date;
  return moment(date, 'YYYYMMDD').format(type);
};

const dateList = ['周一', '周二', '周三', '周四', '周五', '周六', '周日'];

export default function NewCkRankList(props: any) {
  const {
    total,
    allData: {
      publish_switch = 0,
      publish_time = '',
      pusblish_day = '周一',
    }
  } = useSelector((state: any) => state.tableList);

  const dispatch = useDispatch();
  const store = useStore();
  const history = useHistory();
  const { loading, run } = useXHR();
  const dayList: any = [...new Array(28)].map((el, i) => ({ label: `${i + 1}日`, value: String(i + 1) }));

  const [rank_dates, setRankDate] = useState([]);

  const [changeNameModal, setChangeNameModal] = useState<any>({
    visible: false,
    key: Date.now()
  });

  const [exponentConfigDrawer, setExponentConfigDrawer] = useState<any>({
    visible: false,
  })

  const [updateDateModal, setUpdateDateModal] = useState<any>({
    visible: false,
    beginDate: undefined,
  });

  const [publishModal, setPublishModal] = useState<any>({
    visible: false,
    publish_switch: true,
    publish_time: undefined,
    day: '周一'
  });

  const [manualPublishingModal, setManualPublishingModal] = useState<any>({
    visible: false,
    summary_start_date: '',
    summary_end_date: '',
    date: undefined,
  })

  const getList = () => {
    dispatch(getTableList('getCkRankList', 'page', {}));
  };

  const editRecord = (type: 'name' | 'rule', record: any) => {
    setChangeNameModal({
      visible: true,
      key: Date.now(),
      type,
      id: record.id,
      name: record.type,
    });
  };

  const handleSort = (record: any, sort_type: 1 | 2) => {
    run(api.updateCkRankSort, { id: record.id, sort_type }, true).then(() => {
      message.success('操作成功');
      getList();
    });
  };

  const updateStatus = (record: any) => {
    run(api.updateCkRankStatus, { id: record.id, status: record.status == 1 ? 2 : 1 }, true).then(() => {
      message.success('操作成功');
      getList();
    });
  };

  const columns = [
    {
      title: '排序',
      key: 'sort',
      render: (text: any, record: any, i: number) => (
        <OrderColumn
          perm="ranks_ck:sort"
          pos={i}
          start={0}
          end={total - 1}
          onUp={() => handleSort(record, 1)}
          onDown={() => handleSort(record, 2)}
        />
      ),
      width: 70,
    },
    {
      title: '序号',
      key: 'seq',
      render: (text: any, record: any, i: number) => i + 1,
      width: 70,
    },
    {
      title: '榜单名称',
      dataIndex: 'name',
      render: (text: any, record: any) => (
        <PermA
          perm="ranks_ck:result_data"
          onClick={() => history.push(`/view/singleCkRank/${record.id}/${record.rank_type}/${record.name}`)}
        >
          {text}
        </PermA>
      ),
    },
    {
      title: '前台展示榜单名称',
      dataIndex: 'type',
    },
    {
      title: '状态',
      dataIndex: 'status',
      render: (text: any) => (text == 1 ? '展示中' : '不展示'),
      width: 80,
    },
    {
      title: '操作',
      key: 'op',
      render: (record: any) => (
        <span>
          <PermA perm="ranks_ck:online" onClick={() => updateStatus(record)}>
            {record.status == 1 ? '下架' : '上架'}
          </PermA>
          <Divider type="vertical" />
          <PermA
            perm="ranks_ck:edit"
            onClick={() => editRecord('name', record)}
            style={{ marginLeft: 8 }}
          >
            编辑名称
          </PermA>
          <Divider type="vertical" />
          <PermA perm="ranks_ck:result_data" onClick={() => history.push(`/view/singleCkRank/${record.id}/${record.rank_type}/${record.name}`)}>
            榜单数据
          </PermA>
        </span>
      ),
      width: 220,
    },
  ];

  useEffect(() => {
    setMenuHook(dispatch, props)

    getList();
    handleGetMonths()
  }, []);

  const handleChangeName = () => {
    if (!changeNameModal.name?.trim()) {
      message.error('请填写名称');
      return
    }
    const body: any = { id: changeNameModal.id, type: changeNameModal.name.trim() };

    run(api.updateCkRank, body).then(() => {
      message.success('操作成功');
      setChangeNameModal({ visible: false })
      getList();
    });
  };

  // 更新榜单数据
  async function handleUpdate(year: string, cycleValue: any) {
    if (!year || !cycleValue) {
      message.error('请填写表单');
      return
    }
    userApi.updateCkRankData({
      year,
      weeknumber: cycleValue,
    }).then(() => {
      message.success('更新成功');
      setUpdateDateModal({ visible: false })
    }).catch(() => {

    })
  }

  const handleManualPublishing = async () => {
    run(api.ckManualPublish, {
      summary_start_date: manualPublishingModal.summary_start_date,
      summary_end_date: manualPublishingModal.summary_end_date,
      date: manualPublishingModal.date,
    }).then(() => {
      message.success('手动发榜成功');
      setManualPublishingModal({ visible: false })
    }).catch(() => {

    });
  };

  const handleGetMonths = async () => {
    try {
      const res = await userApi.getCkRankWeekList({})
      const list = res?.data?.list || [];
      setRankDate(list);
    } catch (error) {
    }
  };

  /* 弹窗开关 */
  async function handlePublishConfirm(): Promise<void> {
    if (publishModal.publish_switch) {
      if (!publishModal.publish_time) {
        message.error('请输入发榜时间');
        return;
      }
      if (!publishModal.day) {
        message.error('请选择发榜周期');
        return;
      }
    }

    userApi.changeCkRankPublishSwitch({ open: publishModal.publish_switch ? 1 : 0 })
      .then(() => {
        return publishModal.publish_switch ? userApi.changeCkRankPublishTime({
          publish_time: publishModal.publish_switch ? publishModal.publish_time.format("HH:mm:00") : '',
          day: publishModal.publish_switch ? publishModal.day : '',
        }) : Promise.resolve();
      }).then(() => {
        setPublishModal({
          visible: false
        })
        getList()
      }).catch((err) => { })
  }

  const handleChageRankDate = (value: string) => {
    const active = value.split('-');
    setManualPublishingModal({
      ...manualPublishingModal,
      summary_start_date: active[0],
      summary_end_date: active[1],
      date: value,
    });
  }

  return (
    <>
      <Row className="layout-infobar">
        <Col span={12}>
          <PermButton perm='ranks_ck:config' onClick={() => setExponentConfigDrawer({ visible: true })}>指数配置</PermButton>
          <PermButton perm='ranks_ck:publish' style={{ marginLeft: 8 }} onClick={() => setPublishModal({
            visible: true,
            publish_time: !!publish_time ? moment(publish_time, 'HH:mm') : undefined,
            day: pusblish_day,
            publish_switch: Boolean(publish_switch),
          })}>发榜设置</PermButton>
          <PermButton perm='ranks_ck:update_data' style={{ marginLeft: 8 }} onClick={() => setUpdateDateModal({ visible: true })}>更新榜单数据</PermButton>
          <PermButton perm='ranks_ck:blacklist' style={{ marginLeft: 8 }} onClick={() => history.push(`/view/ckRankBlackList`)}>黑名单管理</PermButton>
          <PermButton perm='ranks_ck:publish' style={{ marginLeft: 8 }} onClick={() => setManualPublishingModal({ visible: true })}>手动发榜</PermButton>
        </Col>
        <Col span={12} className="layout-breadcrumb">
          {getCrumb(props.breadCrumb)}
        </Col>
      </Row>
      <div className="component-content">
        <Table
          func="getCkRankList"
          index="page"
          filter={{}}
          columns={columns}
          rowKey="id"
          pagination={false}
        />
        <Modal
          visible={changeNameModal.visible}
          title={'编辑榜单'}
          onCancel={() => setChangeNameModal({ visible: false })}
          onOk={handleChangeName}
          confirmLoading={loading}
          key={changeNameModal.key}
        >
          <Form labelCol={{ span: 4 }} wrapperCol={{ span: 19 }}>
            <Form.Item label="榜单名称" required>
              <Input
                maxLength={15}
                value={changeNameModal.name}
                onChange={(e: any) => setChangeNameModal({ ...changeNameModal, name: e.target.value })}
                placeholder="请输入榜单名称"
              />
            </Form.Item>
          </Form>
        </Modal>

        <Modal
          title="更新榜单数据"
          okText="更新"
          visible={updateDateModal.visible}
          width={500}
          onCancel={() => setUpdateDateModal({ visible: false })}
          onOk={() => {
            handleUpdate(updateDateModal.year, updateDateModal.cycleValue);
          }}
        >
          <Row>
            <Col span={8}>年份：</Col>
            <Col span={16}>
              <Select
                value={updateDateModal.year}
                onChange={(e: any) => {
                  setUpdateDateModal({
                    ...updateDateModal,
                    year: e,
                    cycleValue: undefined,
                    dates: rank_dates.filter((el: any) => el.year === e)
                  })
                }}
                style={{ width: '100%' }}
                placeholder="选择要更新年份"
              >
                {[...new Set(rank_dates.map((el: any) => el.year))].map((v) => (<Select.Option value={v} key={v}>{v}</Select.Option>))}
              </Select>
            </Col>
          </Row>
          <Row style={{ marginTop: 20 }}>
            <Col span={8}>数据统计周期：</Col>
            <Col span={16}>
              <Select value={updateDateModal.cycleValue} onChange={(v) => setUpdateDateModal({
                ...updateDateModal, cycleValue: v
              })} style={{ width: '100%' }} placeholder="选择要更新的榜单周期">
                {updateDateModal.dates?.map((v: any) => (<Select.Option value={v.week_number} key={v.week_number}>{v.start_date}-{v.end_date}</Select.Option>))}
              </Select>
            </Col>
          </Row>
          <p style={{ color: 'red' }}>仅会更新当月数据来源为数仓的榜单内容</p>
        </Modal>

        <Modal
          width={850}
          visible={publishModal.visible}
          onOk={() => handlePublishConfirm()}
          onCancel={() => setPublishModal({ visible: false })}
          title="发榜设置"
        >
          <Row style={{ margin: '20px 0 0' }}>
            <Col span={3} style={{ textAlign: 'right' }}>
              定时发榜：
            </Col>
            <Col span={20} style={{ marginBottom: '20px' }}>
              <Switch checked={publishModal.publish_switch} onChange={(e) =>
                setPublishModal({ ...publishModal, publish_switch: e })} />
            </Col>
            <Col span={3} style={{ textAlign: 'right', lineHeight: '32px' }}>
              发榜时间：
            </Col>
            <Col span={20}>
              <span style={{ marginRight: 10 }}>每周</span>
              <Select style={{ width: '100px', marginRight: 10 }} disabled={!publishModal.publish_switch} value={publishModal.day} onChange={(e) => setPublishModal({ ...publishModal, day: e })}>
                {dateList.map((item: any, index: any) => {
                  return <Select.Option value={`${index + 1}`}>{item}</Select.Option>
                })}
              </Select>
              <TimePicker
                disabled={!publishModal.publish_switch}
                value={publishModal.publish_time}
                placeholder="请选择发榜时间"
                style={{ width: '200px' }}
                // showNow={false}
                format={'HH:mm:00'}
                onChange={(e: any) => setPublishModal({ ...publishModal, publish_time: e })}
              />
            </Col>
          </Row>
        </Modal>

        <Modal
          title="手动发榜"
          visible={manualPublishingModal.visible}
          width={500}
          onCancel={() => setManualPublishingModal({ visible: false })}
          onOk={handleManualPublishing}
        >
          <Row>
            <Col span={6} style={{ lineHeight: '30px' }}>选择发榜周期：</Col>
            <Col span={16}>
              <Select
                value={manualPublishingModal.date}
                onChange={(v) => handleChageRankDate(v)}
                style={{ width: '300px' }}
              >
                {rank_dates.map((v: any) => (
                  <Select.Option value={`${format(v.start_date, 'YYYYMMDD')}-${format(v.end_date, 'YYYYMMDD')}`} key={`${v.week_number}-${v.year}`}>
                    {format(v.start_date)}-{format(v.end_date)}
                  </Select.Option>))
                }
              </Select>
            </Col>
            <Col span={24} style={{ marginTop: '10px' }}>将对该周期下所有潮客榜进行发榜。已发榜周期的将更新数据。</Col>
          </Row>
        </Modal>

        <CkExponentConfigDrawer {...exponentConfigDrawer}
          onClose={() => setExponentConfigDrawer({ visible: false })}
          onOk={() => setExponentConfigDrawer({ visible: false })}>
        </CkExponentConfigDrawer>

      </div>
    </>
  );
}
