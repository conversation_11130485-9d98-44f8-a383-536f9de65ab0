import React, { useState, useEffect } from 'react';
import { useDispatch, useSelector, useStore } from 'react-redux';
import { useHistory } from 'react-router-dom';
import { Table, A, OrderColumn } from '@components/common';
import { getCrumb, objectToQueryString, searchToObject, setMenuHook } from '@app/utils/utils';
import {
  Row,
  Col,
  Input,
  Divider,
  Switch,
  message,
  Icon,
  Modal,
  Form,
  Button,
  DatePicker,
  Select,
  TimePicker,
  Radio,
} from 'antd';
import { getTableList } from '@app/action/tableList';
import { userApi as api, userApi } from '@app/api';
import useXHR from '@utils/useXhr';
import { PermA, PermButton } from '@app/components/permItems';
import { setConfig } from '@app/action/config';
import moment from 'moment';
import ExponentConfigDrawer from './exponentConfigDrawer';
import UpdateRankModal from './updateRankModal';

export default function NewCmhRankList(props: any) {
  const {
    total,
    allData: {
      publish_switch = 0,
      publish_time = '',
      week_publish_time = '',
      pusblish_day = '1',
      week_publish_day = '1',
    },
  } = useSelector((state: any) => state.tableList);

  const dispatch = useDispatch();
  const store = useStore();
  const history = useHistory();
  const { loading, run } = useXHR();
  const dayList: any = [...new Array(28)].map((el, i) => ({
    label: `${i + 1}日`,
    value: String(i + 1),
  }));

  const [filter, setFilter] = useState<any>({
    category: parseInt(searchToObject().category ?? 0),
  });

  const [rank_dates, setRankDate] = useState([]);
  const [rank_dates_week, setRankDateWeek] = useState([]);

  const [changeNameModal, setChangeNameModal] = useState<any>({
    visible: false,
    key: Date.now(),
  });

  const [exponentConfigDrawer, setExponentConfigDrawer] = useState<any>({
    visible: false,
  });

  const [updateDateModal, setUpdateDateModal] = useState<any>({
    visible: false,
    beginDate: undefined,
    key: Date.now(),
  });

  const [publishModal, setPublishModal] = useState<any>({
    visible: false,
    publish_switch: true,
    publish_time: undefined,
    day: '1',
    week_publish_day: '1',
    week_publish_time: undefined,
  });

  const [manualPublishingModal, setManualPublishingModal] = useState<any>({
    visible: false,
  });

  const getList = () => {
    dispatch(getTableList('getCmhRankList', 'page', { ...filter }));
  };

  const onChangeType = (v: any, name: any) => {
    let { pathname: path, search } = history.location
    const obj = searchToObject()
    obj[name] = v
    path = `${path}?${objectToQueryString(obj)}`
    history.replace(path)
    setFilter({
      ...filter,
      [name]: v
    })
  }

  const editRecord = (type: 'name' | 'rule', record: any) => {
    setChangeNameModal({
      visible: true,
      key: Date.now(),
      type,
      id: record.id,
      name: record.type,
    });
  };

  const handleSort = (record: any, sort_type: 1 | 2) => {
    run(api.updateCmhRankSort, { id: record.id, sort_type, category: filter.category }, true).then(
      () => {
        message.success('操作成功');
        getList();
      }
    );
  };

  const updateStatus = (record: any) => {
    run(api.updateCmhRankStatus, { id: record.id, status: record.status == 1 ? 2 : 1 }, true).then(
      () => {
        message.success('操作成功');
        getList();
      }
    );
  };

  const columns = [
    {
      title: '排序',
      key: 'sort',
      render: (text: any, record: any, i: number) => (
        <OrderColumn
          perm="ranks_cmh:sort"
          pos={i}
          start={0}
          end={total - 1}
          onUp={() => handleSort(record, 1)}
          onDown={() => handleSort(record, 2)}
        />
      ),
      width: 70,
    },
    {
      title: '序号',
      key: 'seq',
      render: (text: any, record: any, i: number) => i + 1,
      width: 70,
    },
    {
      title: '榜单名称',
      dataIndex: 'name',
      render: (text: any, record: any) => (
        <PermA
          perm="ranks_cmh:result_data"
          onClick={() =>
            history.push(
              `/view/singleCmhRank/${record.id}/${record.rank_type}/${
                filter.category
              }/${encodeURIComponent(record.name)}`
            )
          }
        >
          {text}
        </PermA>
      ),
    },
    {
      title: '前台展示榜单名称',
      dataIndex: 'type',
    },
    {
      title: '状态',
      dataIndex: 'status',
      render: (text: any) => (text == 1 ? '展示中' : '不展示'),
      width: 80,
    },
    {
      title: '操作',
      key: 'op',
      render: (record: any) => (
        <span>
          <PermA perm="ranks_cmh:online" onClick={() => updateStatus(record)}>
            {record.status == 1 ? '下架' : '上架'}
          </PermA>
          <Divider type="vertical" />
          <PermA
            perm="ranks_cmh:edit"
            onClick={() => editRecord('name', record)}
            style={{ marginLeft: 8 }}
          >
            编辑名称
          </PermA>
          <Divider type="vertical" />
          <PermA
            perm="ranks_cmh:result_data"
            onClick={() =>
              history.push(
                `/view/singleCmhRank/${record.id}/${record.rank_type}/${
                  filter.category
                }/${encodeURIComponent(record.name)}`
              )
            }
          >
            榜单数据
          </PermA>
        </span>
      ),
      width: 220,
    },
  ];

  useEffect(() => {
    setMenuHook(dispatch, props);

    handleGetMonths();
    handleGetMonthsWeek();
  }, []);

  useEffect(() => {
    getList();
  }, [filter]);

  const handleChangeName = () => {
    if (!changeNameModal.name?.trim()) {
      message.error('请填写名称');
      return;
    }
    const body: any = { id: changeNameModal.id, type: changeNameModal.name.trim() };

    run(api.updateCmhRank, body).then(() => {
      message.success('操作成功');
      setChangeNameModal({ visible: false });
      getList();
    });
  };

  // 更新榜单数据
  async function handleUpdate(begin_date?: string) {
    if (!begin_date) {
      message.error('请选择月份');
      return;
    }
    const day = moment(begin_date, 'YYYY-MM');
    userApi
      .updateCmhRankData({
        year: day.year(),
        month: day.month() + 1,
      })
      .then(() => {
        message.success('更新成功');
        setUpdateDateModal({ visible: false, key: Date.now() });
      })
      .catch(() => {});
  }

  const handleManualPublishing = async () => {
    const params: any = {};
    if (manualPublishingModal.rankType == 1) {
      if (!manualPublishingModal.summary_week) {
        message.error('请选择发榜周期');
        return;
      }
      params.summary_week = manualPublishingModal.summary_week;
      params.summary_year = manualPublishingModal.summary_year;
    } else {
      if (!manualPublishingModal.summary_month) {
        message.error('请选择发榜周期');
        return;
      }
      params.summary_month = manualPublishingModal.summary_month;
    }

    run(api.cmhManualPublish, params)
      .then(() => {
        message.success('手动发榜成功');
        setManualPublishingModal({ visible: false });
      })
      .catch(() => {});
  };

  const handleGetMonths = async () => {
    try {
      const res = await userApi.getCmhRankMonthList({});
      const list = res?.data?.list || [];
      setRankDate(list);
    } catch (error) {}
  };

  const handleGetMonthsWeek = async () => {
    try {
      const res = await userApi.getCmhRankWeekList({});
      const list = res?.data?.list || [];
      setRankDateWeek(list);
    } catch (error) {}
  };

  /* 弹窗开关 */
  async function handlePublishConfirm(): Promise<void> {
    if (publishModal.publish_switch) {
      if (!publishModal.publish_time || !publishModal.week_publish_time) {
        message.error('请输入发榜时间');
        return;
      }
      if (!publishModal.day || !publishModal.week_publish_day) {
        message.error('请选择发榜周期');
        return;
      }
    }

    userApi
      .changeCmhRankPublishSwitch({ open: publishModal.publish_switch ? 1 : 0 })
      .then(() => {
        return publishModal.publish_switch
          ? userApi.changeCmhRankPublishTime({
              publish_time: publishModal.publish_switch
                ? publishModal.publish_time.format('HH:mm:00')
                : '',
              week_publish_time: publishModal.publish_switch
                ? publishModal.week_publish_time.format('HH:mm:00')
                : '',
              day: publishModal.publish_switch ? publishModal.day : '',
              week_day: publishModal.publish_switch ? publishModal.week_publish_day : '',
            })
          : Promise.resolve();
      })
      .then(() => {
        setPublishModal({
          visible: false,
        });
        getList();
      })
      .catch((err) => {});
  }

  return (
    <>
      <Row className="layout-infobar">
        <Col span={12}>
          <PermButton perm="" onClick={() => history.push(`/view/cmhRankClassMgr`)}>
            参评主体管理
          </PermButton>
          <PermButton
            perm="ranks_cmh:config"
            style={{ marginLeft: 8 }}
            onClick={() => setExponentConfigDrawer({ visible: true })}
          >
            指数配置
          </PermButton>
          <PermButton
            perm="ranks_cmh:publish"
            style={{ marginLeft: 8 }}
            onClick={() =>
              setPublishModal({
                visible: true,
                publish_time: !!publish_time ? moment(publish_time, 'HH:mm') : undefined,
                week_publish_time: !!week_publish_time
                  ? moment(week_publish_time, 'HH:mm')
                  : undefined,
                day: pusblish_day,
                week_publish_day: week_publish_day,
                publish_switch: Boolean(publish_switch),
              })
            }
          >
            发榜设置
          </PermButton>
          <PermButton
            perm="ranks_cmh:update_data"
            style={{ marginLeft: 8 }}
            onClick={() =>
              setUpdateDateModal({ visible: true, key: Date.now(), dates: rank_dates_week })
            }
          >
            更新榜单数据
          </PermButton>
          <PermButton
            perm="ranks_cmh:publish"
            style={{ marginLeft: 8 }}
            onClick={() =>
              setManualPublishingModal({
                visible: true,
                rankType: 0,
              })
            }
          >
            手动发榜
          </PermButton>
        </Col>
        <Col span={12} className="layout-breadcrumb">
          {getCrumb(props.breadCrumb)}
        </Col>
      </Row>
      <div className="component-content">
        <Row style={{ marginBottom: 10 }}>
          <Radio.Group
            value={filter.category}
            buttonStyle="solid"
            onChange={(e) => onChangeType(e.target.value, 'category')}
          >
            <Radio.Button value={1}>周榜</Radio.Button>
            <Radio.Button value={0}>月榜</Radio.Button>
          </Radio.Group>
        </Row>
        <Table
          func="getCmhRankList"
          index="page"
          filter={filter}
          columns={columns}
          rowKey="id"
          pagination={false}
        />
        <Modal
          visible={changeNameModal.visible}
          title={'编辑榜单'}
          onCancel={() => setChangeNameModal({ visible: false })}
          onOk={handleChangeName}
          confirmLoading={loading}
          key={changeNameModal.key}
        >
          <Form labelCol={{ span: 4 }} wrapperCol={{ span: 19 }}>
            <Form.Item label="榜单名称" required>
              <Input
                maxLength={15}
                value={changeNameModal.name}
                onChange={(e: any) =>
                  setChangeNameModal({ ...changeNameModal, name: e.target.value })
                }
                placeholder="请输入榜单名称"
              />
            </Form.Item>
          </Form>
        </Modal>

        <UpdateRankModal
          {...updateDateModal}
          onCancel={() => setUpdateDateModal({ visible: false, key: Date.now() })}
          onEnd={() => {
            setUpdateDateModal({ visible: false, key: Date.now() });
          }}
        ></UpdateRankModal>
        {/* <Modal
          title="更新榜单数据"
          okText="更新"
          visible={updateDateModal.visible}
          width={300}
          onCancel={() => setUpdateDateModal({ visible: false })}
          onOk={() => {
            handleUpdate(updateDateModal.beginDate);
          }}
        >
          <DatePicker.MonthPicker
            placeholder="请选择月份"
            value={updateDateModal.beginDate ? moment(updateDateModal.beginDate) : undefined}
            style={{ width: '100%' }}
            allowClear
            disabledDate={(current: any) =>
              current && current.startOf('month') > moment().subtract(1, 'M').startOf('month')
            }
            onChange={(date) =>
              setUpdateDateModal({
                ...updateDateModal,
                beginDate: date?.format('YYYY-MM'),
              })
            }
          />
          <p style={{ color: 'red' }}>仅会更新当月数据来源为数仓的榜单内容</p>
        </Modal> */}

        <Modal
          width={850}
          visible={publishModal.visible}
          onOk={() => handlePublishConfirm()}
          onCancel={() => setPublishModal({ visible: false })}
          title="发榜设置"
        >
          <Row style={{ margin: '20px 0 0' }}>
            <Col span={3} style={{ textAlign: 'right' }}>
              定时发榜：
            </Col>
            <Col span={20} style={{ marginBottom: '20px' }}>
              <Switch
                checked={publishModal.publish_switch}
                onChange={(e) => setPublishModal({ ...publishModal, publish_switch: e })}
              />
            </Col>
            <Col span={3} style={{ textAlign: 'right', lineHeight: '32px' }}>
              发榜时间：
            </Col>
            <Col span={20}>
              <span style={{ marginRight: 10 }}>每月</span>
              <Select
                style={{ width: '100px', marginRight: 10 }}
                disabled={!publishModal.publish_switch}
                value={publishModal.day}
                onChange={(e) => setPublishModal({ ...publishModal, day: e })}
              >
                {dayList.map((item: any) => {
                  return (
                    <Select.Option key={item.value} value={item.value}>
                      {item.label}
                    </Select.Option>
                  );
                })}
              </Select>
              <TimePicker
                disabled={!publishModal.publish_switch}
                value={publishModal.publish_time}
                placeholder="请选择发榜时间"
                style={{ width: '200px' }}
                // showNow={false}
                format={'HH:mm:00'}
                onChange={(e: any) => setPublishModal({ ...publishModal, publish_time: e })}
              />
            </Col>
          </Row>
          <Row
            style={{
              marginTop: 10,
            }}
          >
            <Col span={3} style={{ textAlign: 'right', lineHeight: '32px' }}></Col>
            <Col span={20}>
              <span style={{ marginRight: 10 }}>每周</span>
              <Select
                style={{ width: '100px', marginRight: 10 }}
                disabled={!publishModal.publish_switch}
                value={publishModal.week_publish_day}
                onChange={(e) => setPublishModal({ ...publishModal, week_publish_day: e })}
              >
                {['周一', '周二', '周三', '周四', '周五', '周六', '周日'].map(
                  (item: any, index: number) => {
                    return <Select.Option value={`${index + 1}`}>{item}</Select.Option>;
                  }
                )}
              </Select>
              <TimePicker
                disabled={!publishModal.publish_switch}
                value={publishModal.week_publish_time}
                placeholder="请选择发榜时间"
                style={{ width: '200px' }}
                // showNow={false}
                format={'HH:mm:00'}
                onChange={(e: any) => setPublishModal({ ...publishModal, week_publish_time: e })}
              />
            </Col>{' '}
          </Row>
        </Modal>

        <Modal
          title="手动发榜"
          visible={manualPublishingModal.visible}
          width={500}
          onCancel={() => setManualPublishingModal({ visible: false })}
          onOk={handleManualPublishing}
        >
          <Radio.Group
            style={{ marginBottom: 10 }}
            value={manualPublishingModal.rankType}
            buttonStyle="solid"
            onChange={(e) =>
              setManualPublishingModal({ ...manualPublishingModal, rankType: e.target.value })
            }
          >
            <Radio.Button value={1}>周榜</Radio.Button>
            <Radio.Button value={0}>月榜</Radio.Button>
          </Radio.Group>
          <Row>
            <Col span={6} style={{ lineHeight: '30px' }}>
              选择发榜周期：
            </Col>
            <Col span={16}>
              {manualPublishingModal.rankType == 1 ? (
                <Select
                  value={
                    !!manualPublishingModal.summary_year
                      ? `${manualPublishingModal.summary_year}-${manualPublishingModal.summary_week}`
                      : undefined
                  }
                  onChange={(v) => {
                    const [year, week] = v.split('-');
                    setManualPublishingModal({
                      ...manualPublishingModal,
                      summary_year: year,
                      summary_week: week,
                    });
                  }}
                  style={{ marginRight: 10, width: '200px' }}
                >
                  {rank_dates_week.map((v: any) => {
                    return (
                      <Select.Option
                        value={`${v.summary_year}-${v.summary_week_number}`}
                        key={`${v.summary_year}-${v.summary_week_number}`}
                      >
                        {v.summary_start_date}-{v.summary_end_date}
                      </Select.Option>
                    );
                  })}
                </Select>
              ) : (
                <Select
                  value={manualPublishingModal.summary_month}
                  onChange={(v) =>
                    setManualPublishingModal({ ...manualPublishingModal, summary_month: v })
                  }
                  style={{ marginRight: 10, width: '100px' }}
                >
                  {rank_dates.map((v) => (
                    <Select.Option value={v} key={v}>
                      {moment(`${v}`, 'YYYYMM').format('YYYY.MM')}
                    </Select.Option>
                  ))}
                </Select>
              )}
            </Col>
            <Col span={24} style={{ marginTop: '10px' }}>
              将对该周期下所有潮鸣号榜进行发榜。已发榜周期的将更新数据。
            </Col>
          </Row>
        </Modal>

        <ExponentConfigDrawer
          {...exponentConfigDrawer}
          onClose={() => setExponentConfigDrawer({ visible: false })}
          onOk={() => setExponentConfigDrawer({ visible: false })}
        ></ExponentConfigDrawer>
      </div>
    </>
  );
}
