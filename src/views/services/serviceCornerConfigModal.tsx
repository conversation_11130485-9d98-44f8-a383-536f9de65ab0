import React, { forwardRef } from 'react';
import { Form, message, Modal, Radio, Select } from 'antd';
import { serviceApi } from '@app/api';

function ServiceCornerConfigModal(props: any, ref: any) {
  const { getFieldDecorator } = props.form;

  const formLayout = {
    labelCol: { span: 4 },
    wrapperCol: { span: 18 },
  };

  const handleSubmit = (e: any) => {
    e.preventDefault();
    props.form.validateFieldsAndScroll((err: any, values: any) => {
      if (!err) {
        const parmas = {
          id: props.record.id,
          ...values,
        };
        serviceApi
          .setServiceCorner(parmas)
          .then((res: any) => {
            message.success('添加成功');

            props.onOk && props.onOk();
          })
          .catch(() => {
            // message.error('添加失败');
          });
      } else {
        message.error('请检查表单内容');
      }
    });
  };

  return (
    <Modal title="角标设置" visible={props.visible} onOk={handleSubmit} onCancel={props.onCancel}>
      <Form {...formLayout}>
        <Form.Item label="角标">
          {getFieldDecorator('corner_type', {
            initialValue: props.record?.corner_type ?? 0,
            rules: [
              {
                required: true,
                message: '请选择角标类型',
              },
            ],
          })(
            <Radio.Group>
              <Radio value={1}>新</Radio>
              <Radio value={2}>热</Radio>
              <Radio value={0}>无</Radio>
            </Radio.Group>
          )}
        </Form.Item>
      </Form>
    </Modal>
  );
}

export default Form.create<any>({ name: 'ServiceCornerConfigModal' })(
  forwardRef<any, any>(ServiceCornerConfigModal)
);
