/* eslint-disable no-return-assign */
import { getTableList } from '@app/action/tableList';
import { serviceApi as api } from '@app/api';
import { CommonObject } from '@app/types';
import AppServiceForm from '@components/business/appServiceForm';
import { A, Drawer, Table } from '@components/common';
import connect from '@utils/connectTable';
import { getCrumb, requirePerm, setLoading, setMenu } from '@utils/utils';
import {
  Button,
  Col,
  Dropdown,
  Icon,
  Form,
  Input,
  Menu,
  message,
  Modal,
  Row,
  Select,
  Tag,
} from 'antd';
import React from 'react';
import { withRouter } from 'react-router';
import ServiceCornerConfigModal from './serviceCornerConfigModal';
import ServiceHeaderDrawer from './serviceHeaderDrawer';
import ServiceRecommendDrawer from './serviceRecommendDrawer';

@(withRouter as any)
@connect
class AppServiceList extends React.Component<any, any> {
  formRef: any;

  constructor(props: any) {
    super(props);
    this.state = {
      loading: false,
      form: {
        visible: false,
        key: Date.now(),
        mtitle: '',
      },
      onCount: 0,
      category: '',
      areaId: '',
      categoryList: [],
      keywordList: [],
      areaList: [],
      ModalAlert: {
        visible: false,
        mtitle: '',
        title: '',
      },
      CardName: '',
      serviceCornerConfigModal: {
        visible: false,
        record: null,
        key: '',
      },
      headerRecordModal: {
        visible: false,
        key: '',
        record: null,
      },
      serviceRecommendModal: {
        visible: false,
        key: '',
        record: null,
      },
    };
  }

  componentDidMount() {
    this.getCategoryList();
    this.getKeywordList();
    this.getAreaList();
    this.getData({ current: 1, size: 10 });
    setMenu(this);
  }

  componentDidUpdate(prevProps: any) {
    if (
      prevProps.tableList.timestamp !== this.props.tableList.timestamp &&
      this.props.tableList.allData.web_link_list
    ) {
      this.setState({
        onCount: this.props.tableList.allData.enabled_count,
      });
    }
  }

  getAreaList = () => {
    api.getServiceAreaList().then((res: any) => {
      const areaList = res.data.area_tree.map((v: any) => {
        return {
          title: v.name,
          value: v.id,
          key: v.id,
          // children: v.children.map((vv: any) => {
          //   return { title: vv.name, value: vv.id, key: vv.id };
          // }),
        };
      });
      this.setState({ areaList });
    });
  };

  getCategoryList = () => {
    api.getSimpleCategoryList().then((r: any) => {
      this.setState({
        categoryList: r.data.category_list,
      });
    });
  };

  getKeywordList = () => {
    api.getAllKeywordList().then((res: any) => {
      this.setState({
        keywordList: res.data.list.map((v: any) => {
          return {
            value: v.id,
            key: v.id,
            title: v.name,
            disabled: v.web_link_relevance_dtos.length === 0,
            children: v.web_link_relevance_dtos.map((vv: any) => {
              return { value: vv.id, key: vv.id, title: vv.name };
            }),
          };
        }),
      });
    });
  };

  getData = (overlap: CommonObject = {}, filter = this.getFilter()) => {
    this.props.dispatch(
      getTableList('getAppServiceList', 'web_link_list', { ...filter, ...overlap })
    );
  };

  getFilter = () => {
    const { current, size } = this.props.tableList;
    const { category, areaId } = this.state;
    const body: any = { current, size };
    if (category) {
      body.category_id = category;
    }
    if (areaId) {
      body.area_id = areaId;
    }
    return body;
  };

  getColumns = () => {
    const { current, size } = this.props.tableList;
    const getSeq = (i: number) => (current - 1) * size + i + 1;

    const getDropdown = (record: any) => {
      const menu = (
        <Menu>
          {requirePerm(
            this,
            ``
          )(<Menu.Item onClick={() => this.cornerConfig(record)}>角标设置</Menu.Item>)}
          {requirePerm(
            this,
            `web_link:${record.enabled ? 'global_off' : 'global_on'}`
          )(
            <Menu.Item onClick={() => this.changeEnabled(record)}>
              {record.enabled ? '停用' : '启用'}
            </Menu.Item>
          )}
          {requirePerm(
            this,
            'web_link:update'
          )(<Menu.Item onClick={() => this.editRecord(record)}>编辑</Menu.Item>)}
          {requirePerm(
            this,
            'web_link:global_delete'
          )(<Menu.Item onClick={() => this.deleteRecord(record)}>删除</Menu.Item>)}
        </Menu>
      );

      return (
        <Dropdown overlay={menu}>
          <a className="ant-dropdown-link">
            操作 <Icon type="down" />
          </a>
        </Dropdown>
      );
    };

    const orderColumns: any = [
      {
        title: '排序',
        key: 'order',
        render: (text: any, record: any, i: number) => (
          <span>
            {requirePerm(
              this,
              'web_link:global_order'
            )(
              <A
                disabled={getSeq(i) === 1 || !record.enabled}
                className="sort-up"
                onClick={() => this.order(record, getSeq(i), -1)}
              >
                <Icon type="up-circle" theme="filled" />
              </A>
            )}{' '}
            {requirePerm(
              this,
              'web_link:global_order'
            )(
              <A
                disabled={getSeq(i) === this.state.onCount || !record.enabled}
                className="sort-down"
                onClick={() => this.order(record, getSeq(i), 1)}
              >
                <Icon type="down-circle" theme="filled" />
              </A>
            )}
          </span>
        ),
        width: 80,
      },
    ];

    const columns: any = [
      {
        title: '序号',
        key: 'seq',
        render: (text: any, record: any, i: number) => <span>{getSeq(i)}</span>,
        width: 80,
      },
      {
        title: '图标',
        key: 'icon',
        dataIndex: 'pic_url',
        render: (text: any) => <img src={text} className="list-pic" alt="" />,
        width: 100,
      },
      {
        title: '应用名称',
        key: 'name',
        dataIndex: 'name',
        width: 150,
        render: (text: any, record: any) => (
          <span>
            {record.risk_level === 1 && <Tag color="#ff0000">险</Tag>}
            {text}
          </span>
        ),
      },
      {
        title: '所属分类',
        key: 'category_name',
        dataIndex: 'category_name',
        width: 150,
      },
      {
        title: '链接',
        key: 'link',
        dataIndex: 'service_url',
      },
      {
        title: '状态',
        key: 'status',
        dataIndex: 'enabled',
        render: (text: any) => <span>{text ? '已发布' : '未发布'}</span>,
        width: 80,
      },
      {
        title: '操作',
        key: 'op',
        render: (text: any, record: any) => getDropdown(record),
        width: 70,
      },
    ];

    if (this.state.category && this.state.areaId) {
      return orderColumns.concat(columns);
    }
    return columns;
  };

  order = (record: any, current: number, offset: number) => {
    setLoading(this, true);
    api
      .sortAppService({ current, offset, id: record.id, area_id: this.state.areaId })
      .then(() => {
        message.success('操作成功');
        this.getData();
        setLoading(this, false);
      })
      .catch(() => setLoading(this, false));
  };

  changeRecommend = (record: any) => {
    setLoading(this, true);
    api
      .updateAppServiceRecommend(record.recommended ? 'cancel_recommend' : 'do_recommend', {
        id: record.id,
      })
      .then(() => {
        message.success('操作成功');
        this.getData();
        setLoading(this, false);
      })
      .catch(() => setLoading(this, false));
  };

  changeEnabled = (record: any) => {
    setLoading(this, true);
    api
      .updateAppServiceStatus(record.enabled ? 'off' : 'on', { id: record.id })
      .then(() => {
        message.success('操作成功');
        this.getData();
        setLoading(this, false);
      })
      .catch(() => setLoading(this, false));
  };

  cornerConfig = (record: any) => {
    this.setState({
      serviceCornerConfigModal: {
        key: Date.now(),
        visible: true,
        record,
      },
    });
  };

  deleteRecord = (record: any) => {
    Modal.confirm({
      title: <p>确认删除全局服务“{record.name}”吗？</p>,
      onOk: () => {
        setLoading(this, true);
        api
          .deleteAppService({ id: record.id })
          .then(() => {
            message.success('操作成功');
            this.getData();
            setLoading(this, false);
          })
          .catch(() => setLoading(this, false));
      },
    });
  };

  editHeaderRecord = () => {
    api.getServiceHeader({}).then((res: any) => {
      this.setState({
        headerRecordModal: {
          visible: true,
          key: Date.now(),
          record: res.data,
        },
      });
    });
  };

  editServiceRecommend = () => {
    api.getServiceRecommend({}).then((res: any) => {
      this.setState({
        serviceRecommendModal: {
          visible: true,
          key: Date.now(),
          record: res.data,
        },
      });
    });
  };

  editRecord = (record: CommonObject = {}) => {
    if (!record.id) {
      this.setState({
        form: {
          visible: true,
          key: Date.now(),
          mtitle: '添加服务',
        },
      });
      return;
    }
    setLoading(this, true);
    api
      .getServiceDetail({ id: record.id })
      .then((r: any) => {
        this.setState({
          form: {
            visible: true,
            key: Date.now(),
            mtitle: '编辑服务',
            ...r.data.web_link,
            logo: r.data.web_link.pic_url,
            url: r.data.web_link.service_url,
            category_id: r.data.web_link.category_id.toString(),
            type: r.data.web_link.type.toString(),
            area_ref_ids: r.data.area_ref.map((v: any) => v.id),
            search_ref_ids: r.data.search_ref.map((v: any) => v.id),
          },
        });
        setLoading(this, false);
      })
      .catch(() => setLoading(this, false));
  };

  categoryChange = (v: any) => {
    this.setState(
      {
        category: v,
      },
      () => this.getData({ current: 1 })
    );
  };

  areaChange = (v: any) => {
    this.setState(
      {
        areaId: v,
      },
      () => this.getData({ current: 1 })
    );
  };

  submitEnd = () => {
    this.setState({
      form: {
        ...this.state.form,
        visible: false,
      },
      loading: false,
    });
    this.getData();
  };

  setLoading = (loading: boolean) => {
    this.setState({ loading });
  };

  closeDrawer = () => {
    this.setState({
      form: {
        ...this.state.form,
        visible: false,
      },
    });
  };

  cardConfigure = () => {
    const province = this.state.areaList.filter((x: any) => x.key === this.state.areaId);
    console.log(province);
    if (province.length > 0) {
      api.getCommonNameServiceDetail({ area_id: province[0].value }).then((res: any) => {
        this.setState({
          ModalAlert: {
            ...this.state.ModalAlert,
            visible: true,
            title: province[0].title,
          },
          CardName: (res.data && res.data.common_name) || '',
        });
      });
    } else {
      message.error('请选择省份');
    }
  };

  handleSubmit = () => {
    const { CardName, ModalAlert, areaId } = this.state;
    if (CardName === '') {
      message.error('请检查必填项');
      return;
    }

    this.setState({ loading: true });
    api
      .saveCommonNameService({ area_id: areaId, name: CardName })
      .then(() => {
        message.success('操作成功');
        this.getData();
        this.setState({
          loading: false,
          ModalAlert: {
            ...ModalAlert,
            visible: false,
          },
        });
      })
      .catch(() => this.setState({ loading: false }));
  };

  render() {
    const { form, loading, ModalAlert, CardName } = this.state;
    const formLayout = {
      labelCol: { span: 4 },
      wrapperCol: { span: 18 },
    };
    return (
      <>
        <Row className="layout-infobar">
          <Col span={16}>
            <Select
              value={this.state.category.toString()}
              onChange={this.categoryChange}
              style={{ width: 120, marginRight: 8 }}
            >
              <Select.Option value="">分类查询</Select.Option>
              {this.state.categoryList.map((v: any) => (
                <Select.Option key={v.id} value={v.id.toString()}>
                  {v.name}
                </Select.Option>
              ))}
            </Select>
            {requirePerm(
              this,
              'web_link:create'
            )(
              <Button onClick={() => this.editRecord()} style={{ marginRight: 8 }}>
                <Icon type="plus-circle" /> 创建服务
              </Button>
            )}
            {requirePerm(
              this,
              'web_link:head_query'
            )(
              <Button onClick={() => this.editHeaderRecord()} style={{ marginRight: 8 }}>
                <Icon type="plus-circle" /> 服务头部管理
              </Button>
            )}
            {requirePerm(
              this,
              'web_link:recommend_query'
            )(
              <Button onClick={() => this.editServiceRecommend()} style={{ marginRight: 8 }}>
                <Icon type="plus-circle" /> 2+1 推荐位
              </Button>
            )}
          </Col>
          <Col span={8} className="layout-breadcrumb">
            {getCrumb(this.props.breadCrumb)}
          </Col>
        </Row>
        <div className="component-content">
          <Table
            func="getAppServiceList"
            index="web_link_list"
            rowKey="id"
            filter={this.getFilter()}
            columns={this.getColumns()}
            pagination={true}
          />
          <Modal
            visible={ModalAlert.visible}
            key="sd"
            title={`页卡名称配置-${ModalAlert.title}`}
            confirmLoading={loading}
            width="300px"
            onCancel={() => this.setState({ ModalAlert: { ...ModalAlert, visible: false } })}
            onOk={this.handleSubmit}
          >
            <Form {...formLayout}>
              <Form.Item required={true} label="名称">
                <Input
                  value={CardName}
                  placeholder="请输入名称"
                  onChange={(e: any) => this.setState({ CardName: e.target.value })}
                />
              </Form.Item>
            </Form>
          </Modal>

          <Drawer
            visible={form.visible}
            skey={form.key}
            title={form.mtitle}
            onClose={this.closeDrawer}
            onOk={() => this.formRef.doSubmit()}
          >
            <AppServiceForm
              categoryList={this.state.categoryList}
              formContent={form}
              setLoading={this.setLoading}
              onEnd={this.submitEnd}
              wrappedComponentRef={(ref: any) => (this.formRef = ref)}
              keywordList={this.state.keywordList}
              areaList={this.state.areaList}
            />
          </Drawer>

          <ServiceCornerConfigModal
            {...this.state.serviceCornerConfigModal}
            onOk={() => {
              this.setState({
                serviceCornerConfigModal: {
                  ...this.state.serviceCornerConfigModal,
                  visible: false,
                },
              });

              this.getData();
            }}
            onCancel={() =>
              this.setState({
                serviceCornerConfigModal: {
                  ...this.state.serviceCornerConfigModal,
                  visible: false,
                },
              })
            }
          />

          <ServiceHeaderDrawer
            {...this.state.headerRecordModal}
            onOk={() => {
              this.setState({
                headerRecordModal: {
                  ...this.state.headerRecordModal,
                  visible: false,
                },
              });
            }}
            onClose={() => {
              this.setState({
                headerRecordModal: { ...this.state.headerRecordModal, visible: false },
              });
            }}
          />

          <ServiceRecommendDrawer
            {...this.state.serviceRecommendModal}
            onOk={() => {
              this.setState({
                serviceRecommendModal: {
                  ...this.state.serviceRecommendModal,
                  visible: false,
                },
              });
            }}
            onClose={() => {
              this.setState({
                serviceRecommendModal: { ...this.state.serviceRecommendModal, visible: false },
              });
            }}
          />
        </div>
      </>
    );
  }
}

export default AppServiceList;
