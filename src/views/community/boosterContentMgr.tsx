import { <PERSON><PERSON>, Col, Divider, Icon, Input, Modal, Row, Select, Tooltip, message, Radio } from "antd"
import { geBoostDetail, getCrumb, resolveNewsType, showIDDetailModal, UserDetail } from "@app/utils/utils"
import { useSelector, useDispatch } from 'react-redux';
import { getTableList } from '@app/action/tableList';
import { A, PreviewMCN, Table } from '@components/common';
import React, { useEffect, useState } from "react"
import AddCircleAuthorityDrawer from "./component/AddCircleAuthorityDrawer";
import { communityApi as api, sysApi, userApi } from "@app/api";
import { PermA } from "@app/components/permItems";
import { setConfig } from "@app/action/config";
import moment from "moment";
import showImagePreviewModal from "@components/common/imagePreviewModal";
import ImagePreviewColumn from "@app/components/common/imagePreviewColumn";

export default function BoosterContentMgr(props: any) {
  const dispatch = useDispatch();
  const { total, current, size, records = [] } = useSelector((state: any) => state.tableList)
  const [filter, setFilter] = useState({
    article_status: '',
    doc_type: '',
    recommend_status: '',
    search_type: 1,
    keyword: ''
  })
  const [searchState, setSearchState] = useState({
    search_type: 1,
    keyword: ''
  })
  const [circleList, setCircleList] = useState<any>([])
  const [authorityDrawerVisiable, setAuthorityDrawerVisiable] = useState(false)
  const [editRecord, setEditRecord] = useState(null)
  const [preview, setPreview] = useState({
    visible: false,
    skey: Date.now(),
    data: {}
  })
  const [boost, setBoost] = useState({
    visible: false,
    key: Date.now(),
    detail: {}
  })
  // 用户详情
  const [user, setUser] = useState({
    key: Date.now(),
    visible: false,
    detail: {},
  })
  const getSeq = (i: number) => (current - 1) * size + i + 1;
  const openPreview = (record: any) => {
    setPreview({
      visible: true,
      skey: Date.now(),
      data: record
    })
  };
  const closePreview = () => {
    setPreview({
      ...preview, visible: false
    })
  };

  const showUserDetailModal = (record: any, visible: boolean) => {
    userApi
      .getUserDetail({ accountId: record.account_id })
      .then((r: any) => {
        setUser({
          key: Date.now(),
          visible: true,
          detail: r.data.account,
        })
      })
  }
  const columns: any = [
    {
      title: '序号',
      key: 'orders',
      render: (a: any, b: any, index: number) => <span>{getSeq(index)}</span>,
      width: 50,
    },
    {
      title: 'ID',
      dataIndex: 'id',
      width: 80,
      render: (text: any, record: any) => (<a onClick={() => showIDDetailModal({
        id: record.id,
        uuid: record.uuid,
        metadata_id: record.metadata_id,
      })}>{text}</a>)
    },
    {
      title: '内容标题',
      dataIndex: 'list_title',
      render(text: string, record: any) {
        return <a
          style={{
            display: '-webkit-box',
            textOverflow: 'ellipsis',
            WebkitLineClamp: 2,
            WebkitBoxOrient: 'vertical',
            overflow: "hidden",
          }}
          onClick={() => openPreview(record)}>{text || '-'}</a>;
      },
    },
    {
      title: '封面图/配图',
      key: 'list_pics',
      dataIndex: 'list_pics',
      width: 150,
      align: 'center',
      render: (text: any, record: any) => (<div style={{ height: 60, textAlign: 'center' }}>
        <ImagePreviewColumn text={text} imgs={record.list_pics}></ImagePreviewColumn>
        {/* <img src={text} className='list-pic' onMouseEnter={() => showImagePreviewModal({ images: record.list_pics })}></img> */}
      </div>)
    },
    {
      title: '内容类型',
      dataIndex: 'doc_type',
      render: (doc_type: any) => resolveNewsType(doc_type, 1),
      width: 80,
    },
    {
      title: '作者',
      key: 'author',
      dataIndex: 'author',
      width: 110,
      render: (text: any, record: any) => (<a onClick={() => showUserDetailModal(record, true)}>{text}</a>)
    },
    {
      title: '内容状态',
      key: 'article_status',
      dataIndex: 'article_status',
      width: 80,
      render: (text: number, record: any) => {
        if (record.content_level && text === 4) {
          return ['通过', '沉底通过', '推荐'][record.content_level]
        } else {
          return ['', '沉底通过', '推荐', '待审核', '通过', '已驳回', '', '作者删除', '管理员删除', '作者注销'
            , '作者删除', '作者注销'][text]
        }
      },
    },
    {
      title: '助推状态',
      key: 'end_status',
      dataIndex: 'end_status',
      width: 90,
      render: (text) => <span>{text == 0 ? '进行中' : '已结束'}</span>,
    },
    {
      title: '开始时间',
      key: 'use_at',
      dataIndex: 'use_at',
      render: (text: number) => moment(text).format('YYYY-MM-DD HH:mm:ss'),
      width: 98,
    },
    {
      title: '操作',
      key: 'op',
      render: (text: any, record: any) => (
        <span>
          <PermA perm="ugc:encourage:card:list|ugc:encourage:card:article:list" onClick={() => openBooster(record)}>助推详情</PermA>
          {record.end_status == 0 && <Divider type="vertical" />}
          {record.end_status == 0 &&
            <PermA perm="ugc:encourage:card:stop" onClick={() => stopBooster(record)}>终止助推</PermA>
          }
        </span>
      ),
      width: 150,
    },
  ]
  // 助推详情
  const openBooster = (record) => {
    sysApi.getBoostDetail({ id: record.card_id }).then(res => {
      const { detail } = res.data
      setBoost({
        visible: true,
        key: Date.now(),
        detail: detail
      })
    })
  }
  const stopBooster = (record: any) => {
    Modal.confirm({
      title: '终止助推',
      width: 500,
      content: (
        <>
          <span>
            仅终止额外增加的推广，不影响内容审核状态及正常展示在各列表页。终止后无法撤销，请谨慎操作。
          </span>
          <div>
            <span>备注说明:</span>
            <span>
              <Input.TextArea
                placeholder="选填，仅用于后台查看，最多50字"
                rows={4}
                style={{ whiteSpace: "pre-wrap" }}
                maxLength={50}
                id={'booster'}
              />
            </span>
          </div>
        </>
      ),
      onOk: (destroy: Function) => {
        const dom = document.getElementById('booster')
        let str = dom.innerHTML.replace(/\s*/g, '');
        sysApi.stopCard({ id: record.card_id, reason: str }).then(res => {
          message.info('操作成功')
          getData()
          destroy()
        })
      },
    });
  }

  const changeFilter = (key: string, val: any, goToFirstPage = false) => {
    let initFilter = { ...filter }
    if (key === 'article_status') {
      if ([0, 1, 2].includes(val)) {
        initFilter.content_level = val
      } else {
        delete initFilter.content_level
      }
    }
    const newFilter = {
      ...initFilter,
      [key]: val
    }
    const Filters = {
      ...initFilter,
      [key]: val
    }
    if ([0, 1, 2].includes(val) && key === 'article_status') {
      Filters.article_status = 4
    }
    if ('content_level' in Filters) {
      Filters.article_status = 4
    }
    setFilter(newFilter)
    getData(true, Filters)
  }

  const handleKey = (e: { which: number }) => {
    if (e.which === 13) {
      const newFilter = {
        ...filter,
        ...searchState
      }
      setFilter(newFilter)
      getData(true, newFilter)
    }
  };
  const getFilter = () => {
    let filters = { ...filter }
    if ([0, 1, 2].includes(filter.article_status)) {
      filters.content_level = filter.article_status
    } else {
      delete filters.content_level
    }
    if ('content_level' in filters) {
      filters.article_status = 4
    }
    return filters;
  };
  const getData = (goToFirstPage = false, newFilter = filter) => {
    let cur = goToFirstPage ? 1 : current
    dispatch(getTableList('getArticleList', 'list', { current: cur, size, ...newFilter }));
  }

  const getCircleList = () => {
    api.getCircleList({ current: 1, size: 100 })
      .then((res) => {
        const { list = [] } = res.data as any
        setCircleList(list.records)
      }).catch(() => {

      })
  }

  const submitEnd = (goToFirstPage: boolean) => {
    getData(goToFirstPage)
    setAuthorityDrawerVisiable(false)
  }

  useEffect(() => {
    const { selectKeys, openKeys } = props
    dispatch(
      setConfig({ selectKeys, openKeys })
    );
    getData(true)
    // 获取圈子列表
    getCircleList()
  }, [])

  return (
    <>
      <Row className="layout-infobar">
        <Col span={12}>
        </Col>
        <Col span={12} className="layout-breadcrumb">
          {getCrumb(props.breadCrumb)}
        </Col>
      </Row>
      <div className="component-content">
        <Row style={{ marginBottom: 16 }}>
          <Col span={14}>
            <Select style={{ width: 110 }} value={filter.article_status} onChange={(val: any) => changeFilter('article_status', val)}>
              <Select.Option value="">内容状态</Select.Option>
              <Select.Option value={0}>通过</Select.Option>
              <Select.Option value={2}>推荐</Select.Option>
              <Select.Option value={1}>沉底通过</Select.Option>
              <Select.Option value={3}>待审核</Select.Option>
              <Select.Option value={5}>已驳回</Select.Option>
              <Select.Option value={7}>作者删除</Select.Option>
              <Select.Option value={8}>管理员删除</Select.Option>
              <Select.Option value={9}>作者注销</Select.Option>
            </Select>

            <Select style={{ width: 110, marginLeft: 8 }} value={filter.doc_type} onChange={(val) => changeFilter('doc_type', val)}>
              <Select.Option value="">内容类型</Select.Option>
              <Select.Option value={10}>视频</Select.Option>
              <Select.Option value={12}>短图文</Select.Option>
              <Select.Option value={13}>长文章</Select.Option>
            </Select>
            <Select style={{ width: 110, marginLeft: 8 }} value={filter.recommend_status} onChange={(val) => changeFilter('recommend_status', val)}>
              <Select.Option value="">助推状态</Select.Option>
              <Select.Option value={1}>进行中</Select.Option>
              <Select.Option value={2}>已结束</Select.Option>
            </Select>
          </Col>
          <Col span={10} style={{ textAlign: 'right' }}>
            <Select
              value={searchState.search_type}
              style={{ width: 110, marginRight: 8 }}
              onChange={(search_type: any) => setSearchState({ ...searchState, search_type })}
            >
              <Select.Option value={1}>内容标题</Select.Option>
              <Select.Option value={2}>ID</Select.Option>
              <Select.Option value={3}>创作者平台ID</Select.Option>
            </Select>
            <Input
              value={searchState.keyword}
              style={{ marginRight: 8, width: 160 }}
              onChange={(e: any) => setSearchState({ ...searchState, keyword: e.target.value })}
              onKeyPress={handleKey}
              placeholder="输入搜索内容"
            />
            <Button onClick={() => handleKey({ which: 13 })}>
              <Icon type="search" /> 搜索
            </Button>
          </Col>
        </Row>
        <Table
          func="getArticleList"
          filter={getFilter()}
          index="list"
          pagination={true}
          rowKey={(record: any, index: number) => { return `${record.id}-${index}` }}
          columns={columns}
        />
        <AddCircleAuthorityDrawer
          record={editRecord}
          circleList={circleList}
          visible={authorityDrawerVisiable}
          onClose={() => setAuthorityDrawerVisiable(false)}
          onEnd={submitEnd} />
        <PreviewMCN {...preview} onClose={closePreview} />
        <Modal
          visible={user.visible}
          key={user.key}
          title="用户详情"
          width={800}
          onCancel={() => { setUser({ ...user, visible: false }) }}
          onOk={() => { setUser({ ...user, visible: false }) }}
        >
          {/*{user.visible && getUserDetail(user.detail)}*/}
          {user.visible && <UserDetail detail={user.detail} />}
        </Modal>
        {
          boost.visible && <Modal
            visible={boost.visible}
            key={boost.key}
            title="助推详情"
            width={800}
            onCancel={() => { setBoost({ ...boost, visible: false }) }}
            onOk={() => { setBoost({ ...boost, visible: false }) }}
          >
            {boost.visible && geBoostDetail(boost.detail)}
          </Modal>
        }
      </div>
    </>
  )
}