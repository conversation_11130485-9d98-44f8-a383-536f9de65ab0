import React, { useState, useEffect, useCallback, useMemo, useRef } from 'react';
import { useStore, useDispatch, useSelector } from 'react-redux';
import { useHistory } from 'react-router-dom';
import { Table, OrderColumn, PreviewMCN } from '@components/common';
import { CommonObject } from '@app/types';
import {
  getCrumb,
  objectToQueryString,
  searchToObject,
  setMenuHook,
  UserDetail,
} from '@app/utils/utils';
import {
  Row,
  Col,
  Divider,
  Icon,
  Modal,
  message,
  InputNumber,
  Tooltip,
  Popconfirm,
  DatePicker,
  Select,
  Input,
  Button,
  Dropdown,
  Menu,
  Tag,
} from 'antd';
import { getTableList } from '@app/action/tableList';
import { opApi as api, communityApi, userApi } from '@app/api';
import useXHR from '@utils/useXhr';
import { PermA, PermButton, PermMenuItem } from '@components/permItems';
import Radio from 'antd/es/radio';
import { setConfig } from '@app/action/config';
import moment from 'moment';
import { RangePickerValue } from 'antd/es/date-picker/interface';
import _ from 'lodash';
import ImagePreviewColumn from '@app/components/common/imagePreviewColumn';
import AuditNotPassedModal from './auditNotPassedModal';
import AddAuditBlackUserModal from './addAuditBlackUserModal';
import AuditDetailDrawer from './auditDetailDrawer';
import ReactClipboard from 'react-clipboardjs-copy';
import './audit.scss';

export default function UGCAudit(props: any) {
  const dispatch = useDispatch();
  const store = useStore();
  const history = useHistory();
  const {
    total,
    current,
    size,
    records = [],
    allData,
  } = useSelector((state: any) => state.tableList);
  const { loading, run } = useXHR();

  const [accountOptions, setAccountOptions] = useState([]);
  const [circleOptions, setCircleOptions] = useState([]);
  const [listSelectedKeys, setListSelectedKeys] = useState([]);
  const [filter, setFilter] = useState<any>({
    status: searchToObject().status ?? '3',
    doc_types: '',
    circle_id: '',
    content_level: '',
    sensitive_check: '',
  });
  const [search, setSearch] = useState<any>({
    search_type: 3,
    keyword: '',
  });
  const [notPass, setNotPass] = useState<any>({
    key: '',
    visible: false,
    ids: [],
  });
  const [detailDrawer, setDetailDrawer] = useState<any>({
    key: '',
    visible: false,
    id: null,
  });

  const [addAuditBlackUser, setAddAuditBlackUser] = useState<any>({
    key: '',
    visible: false,
    id: null,
  });

  const [userDetailModal, setUserDetailModal] = useState<any>({
    key: '',
    visible: false,
    id: null,
  });

  const getSeq = (i: number) => (current - 1) * size + i + 1;

  const f = useMemo(() => {
    const x: CommonObject = { ...filter };
    if (!x.keyword) {
      delete x.keyword;
      delete x.search_type;
    }

    return x;
  }, [filter]);

  const getList = useCallback(
    (overlap: CommonObject = {}) => {
      setListSelectedKeys([]);
      const { current, size = 10 } = store.getState().tableList;
      dispatch(
        getTableList('getArticleAuditList', 'release_list', {
          ...f,
          current,
          size,
          ...overlap,
        })
      );
    },
    [f, setListSelectedKeys]
  );

  const handleAccountSearch = _.debounce((val: any) => {
    if (!val?.trim()) {
      setAccountOptions([]);
      return;
    }

    communityApi
      .recommendAccount_Search({ keyword: val })
      .then(({ data }) => {
        const { list } = data as any;
        setAccountOptions(list || []);
      })
      .catch(() => {});
  }, 500);

  const deleteRecord = (record: any) => {
    setNotPass({
      key: Date.now(),
      visible: true,
      ids: [record.id],
      isBatch: false,
      type: 'delete',
    });
  };

  const approved = (ids: string[], content_level: number, isBatch: boolean = false) => {
    if (isBatch) {
      const levelString = ['普通', '沉底', '推荐'];
      Modal.confirm({
        title: `已选中${ids.length}条待审数据，确定批量通过，等级为【${levelString[content_level]}】？`,
        onOk: () => {
          run(
            communityApi.batchOperateAudit,
            { ids: ids.join(','), status: 4, content_level },
            true
          ).then(() => {
            message.success('操作成功');
            getList();
          });
        },
      });
    } else {
      run(
        communityApi.batchOperateAudit,
        { ids: ids.join(','), status: 4, content_level },
        true
      ).then(() => {
        message.success('操作成功');
        getList();
      });
    }
  };

  const changeLevel = (ids: string[], content_level: number, isBatch: boolean = false) => {
    if (isBatch) {
      const levelString = ['普通', '沉底', '推荐'];
      Modal.confirm({
        title: `已选中${ids.length}条数据，确定要改为【${levelString[content_level]}】等级吗？`,
        onOk: () => {
          run(
            communityApi.batchOperateAudit,
            { ids: ids.join(','), status: 4, content_level },
            true
          ).then(() => {
            message.success('操作成功');
            getList();
          });
        },
      });
    } else {
      run(
        communityApi.batchOperateAudit,
        { ids: ids.join(','), status: 4, content_level },
        true
      ).then(() => {
        message.success('操作成功');
        getList();
      });
    }
  };

  const notPassed = (ids: string[], isBatch: boolean = false) => {
    setNotPass({
      key: 'notPassed',
      visible: true,
      ids,
      isBatch,
    });
  };

  const resumeReview = (ids: string[], isBatch: boolean = false) => {
    Modal.confirm({
      title: isBatch
        ? `已选中${ids.length}条数据，确定要恢复待审吗？`
        : '待审帖子仅作者本人可见，确定要恢复待审吗？',
      onOk: () => {
        run(communityApi.batchOperateAudit, { ids: ids.join(','), status: 3 }, true).then(() => {
          message.success('操作成功');
          getList();
        });
      },
    });
  };

  const listSort = (record: any, i: number, position: number = 1) => {
    let data: any = {
      id: record.id,
      sort_flag: i,
    };
    if (i == 2) {
      data.position = position;
    }
    api.rankingSort(data).then((res: any) => {
      message.success('操作成功');
      getList();
    });
  };

  const changeOrder = (record: any, i: any) => {
    let position = i;
    const WAIT_TIME = 1000;
    const positionChange = (v: any) => {
      position = v;
    };

    Modal.confirm({
      title: <p>排序：《{record.article_title}》</p>,
      icon: <Icon type="info-circle" />,
      content: (
        <div>
          <span>请输入位置：</span>
          <InputNumber min={1} max={total} defaultValue={position} onChange={positionChange} />
        </div>
      ),
      onOk: (closeFunc: Function) => {
        if (!position) {
          message.error('请填写位置');
          return;
        }

        dispatch(setConfig({ loading: true }));
        api
          .rankingSort({ id: record.id, sort_flag: 2, position: position })
          .then((res: any) => {
            message.success('操作成功');
            getList();
            dispatch(setConfig({ loading: false }));
            closeFunc();
          })
          .catch(() => {
            dispatch(setConfig({ loading: false }));
          });
      },
    });
  };

  // 显示用户详情
  const showUserDetailModal = (record: any) => {
    run(userApi.getUserDetail, { accountId: record.account_id }, true).then((r: any) => {
      setUserDetailModal({
        key: Date.now(),
        visible: true,
        detail: r.data.account,
      });
    });
  };

  const titleClick = (record: any) => {
    // setPreview({
    //   visible: true,
    //   skey: Date.now(),
    //   data: record
    // })
    // if (!!record.url) {
    //   window.open(record.url, '_blank');
    // }

    setDetailDrawer({
      key: Date.now(),
      visible: true,
      id: record.id,
      record: record,
    });
  };

  const getColumns = () => {
    const columns: any = [
      {
        title: '序号',
        key: 'seq',
        render: (text: any, record: any, i: number) => <span>{getSeq(i)}</span>,
        width: 70,
      },
      {
        title: 'ID',
        dataIndex: 'id',
        width: 90,
      },
      {
        title: '标题',
        key: 'list_title',
        dataIndex: 'list_title',
        render: (text: any, record: any) => (
          // onClick={this.toMlf.bind(this, record.id, 'mlf_detail_url')}
          <Tooltip title={<div>{text}</div>}>
            {/* <div className="line-max-2" dangerouslySetInnerHTML={{ __html: text }}></div> */}
            <span>
              <a onClick={() => titleClick(record)} className="line-max-3 list-title">
                {(!!record.text_check_result ||
                  !!record.img_check_result ||
                  !!record.video_check_result) && <Tag color="#f00">敏</Tag>}
                {record.content_level == 1 && <Tag color="gray">沉底</Tag>}
                {record.content_level == 2 && <Tag color="green">推荐</Tag>}
                {text}
              </a>
            </span>
          </Tooltip>
        ),
      },
      {
        title: '封面图',
        key: 'pic_array',
        dataIndex: 'pic_array',
        width: 150,
        align: 'center',
        render: (text: any, record: any) => (
          <div style={{ height: 60, textAlign: 'center' }}>
            <img
              src={text?.[0]}
              className={'list-pic'}
              style={{
                cursor: 'pointer',
              }}
              onClick={() => titleClick(record)}
            ></img>
          </div>
        ),
      },
      {
        title: '类型',
        dataIndex: 'doc_type',
        width: 90,
        render: (text: any, record: any) => {
          return text == 12 ? '图文帖' : '视频帖';
        },
      },
      {
        title: '圈子',
        dataIndex: 'circle_name',
        width: 90,
      },
      {
        title: '作者',
        dataIndex: 'author',
        width: 100,
        render: (text: any, record: any) => {
          return <a onClick={() => showUserDetailModal(record)}>{text}</a>;
        },
      },
    ];

    if (filter.status == 3) {
      columns.push({
        title: '提交审核时间',
        key: 'submit_audit_at',
        dataIndex: 'submit_audit_at',
        render: (text: any, record: any) => {
          return (
            <div style={{ whiteSpace: 'normal' }}>
              <div>{moment(text).format('YYYY-MM-DD')}</div>
              <div>{moment(text).format('HH:mm:ss')}</div>
            </div>
          );
        },
        width: 110,
      });
      columns.push({
        title: '操作',
        key: 'op',
        fixed: 'right',
        render: (text: any, record: any, i: number) => (
          <span>
            <Dropdown
              overlay={
                <Menu>
                  <PermMenuItem
                    perm="ugc_article:content_audit"
                    onClick={() => approved([record.id], 0)}
                  >
                    普通
                  </PermMenuItem>
                  <PermMenuItem
                    perm="ugc_article:content_audit"
                    onClick={() => approved([record.id], 2)}
                  >
                    推荐
                  </PermMenuItem>
                  <PermMenuItem
                    perm="ugc_article:content_audit"
                    onClick={() => approved([record.id], 1)}
                  >
                    沉底
                  </PermMenuItem>
                </Menu>
              }
            >
              <a className="ant-dropdown-link">
                通过 <Icon type="down" />
              </a>
            </Dropdown>
            <Divider type="vertical" />
            <PermA perm="ugc_article:content_audit" onClick={() => notPassed([record.id])}>
              不通过
            </PermA>
            <Divider type="vertical" />
            <Dropdown
              overlay={
                <Menu>
                  <PermMenuItem
                    perm="ugc_article:content_audit"
                    onClick={() => deleteRecord(record)}
                  >
                    删除
                  </PermMenuItem>
                  <PermMenuItem
                    perm=""
                    onClick={() => {
                      setAddAuditBlackUser({
                        key: Date.now(),
                        visible: true,
                        id: record.account_id,
                      });
                    }}
                  >
                    拉黑
                  </PermMenuItem>
                </Menu>
              }
            >
              <a className="ant-dropdown-link">
                其他 <Icon type="down" />
              </a>
            </Dropdown>
          </span>
        ),
        width: 200,
      });
    } else if (filter.status == 4 || filter.status == 5) {
      columns.push({
        title: '审核人',
        key: 'audit_by',
        dataIndex: 'audit_by',
        width: 110,
      });
      columns.push({
        title: '审核时间',
        key: 'audit_at',
        dataIndex: 'audit_at',
        render: (text: any, record: any) => {
          return (
            <div style={{ whiteSpace: 'normal' }}>
              <div>{moment(text).format('YYYY-MM-DD')}</div>
              <div>{moment(text).format('HH:mm:ss')}</div>
            </div>
          );
        },
        width: 110,
      });
      columns.push({
        title: '操作',
        key: 'op',
        width: 110,
        fixed: 'right',
        render: (text: any, record: any, i: number) => {
          return (
            <Dropdown
              overlay={
                <Menu>
                  <PermMenuItem
                    perm="ugc_article:content_audit"
                    onClick={() => changeLevel([record.id], 0)}
                  >
                    普通
                  </PermMenuItem>
                  <PermMenuItem
                    perm="ugc_article:content_audit"
                    onClick={() => changeLevel([record.id], 2)}
                  >
                    推荐
                  </PermMenuItem>
                  <PermMenuItem
                    perm="ugc_article:content_audit"
                    onClick={() => changeLevel([record.id], 1)}
                  >
                    沉底
                  </PermMenuItem>
                  {filter.status == 4 && (
                    <PermMenuItem
                      perm="ugc_article:content_audit"
                      onClick={() => notPassed([record.id])}
                    >
                      不通过
                    </PermMenuItem>
                  )}
                  <PermMenuItem
                    perm="ugc_article:content_audit"
                    onClick={() => resumeReview([record.id])}
                  >
                    恢复待审
                  </PermMenuItem>
                  <PermMenuItem
                    perm="ugc_article:content_audit"
                    onClick={() => deleteRecord(record)}
                  >
                    删除
                  </PermMenuItem>
                  <PermMenuItem
                    perm=""
                    onClick={() => {
                      setAddAuditBlackUser({
                        key: Date.now(),
                        visible: true,
                        id: record.account_id,
                      });
                    }}
                  >
                    拉黑
                  </PermMenuItem>
                  {filter.status == 4 && (
                    <PermMenuItem perm="">
                      <ReactClipboard
                        action="copy"
                        text={record.url}
                        onSuccess={() => message.success('链接已复制')}
                        onError={() => message.error('复制失败')}
                      >
                        <a>复制链接</a>
                      </ReactClipboard>
                    </PermMenuItem>
                  )}
                </Menu>
              }
            >
              <a className="ant-dropdown-link">
                操作 <Icon type="down" />
              </a>
            </Dropdown>
          );
        },
      });
    } else {
      columns.push({
        title: '删除人',
        key: 'deleted_by',
        dataIndex: 'deleted_by',
        width: 110,
      });
      columns.push({
        title: '删除时间',
        key: 'deleted_at',
        dataIndex: 'deleted_at',
        render: (text: any, record: any) => {
          return (
            <div style={{ whiteSpace: 'normal' }}>
              <div>{moment(text).format('YYYY-MM-DD')}</div>
              <div>{moment(text).format('HH:mm:ss')}</div>
            </div>
          );
        },
        width: 110,
      });
      columns.push({
        title: '操作',
        key: 'op',
        fixed: 'right',
        render: (text: any, record: any, i: number) => (
          <span>
            {record.status == 8 ? (
              '-'
            ) : (
              <PermA perm="ugc_article:content_audit" onClick={() => deleteRecord(record)}>
                彻底删除
              </PermA>
            )}
          </span>
        ),
        width: 110,
      });
    }

    return columns;
  };

  useEffect(() => {
    console.log('props', props);
    setMenuHook(dispatch, props);
    getCircleList();
  }, []);

  useEffect(() => {
    getList({ current: 1 });
  }, [f]);

  const handleListSelectChange = (selectedRowKeys: any, selectedRows: any) => {
    setListSelectedKeys(selectedRowKeys);
  };

  const batchDelete = () => {
    run(api.deleteRankingArticle, { id: listSelectedKeys.join(',') }, true).then(() => {
      message.success('操作成功');
      getList();
    });
  };

  const onChangeType = (v: any, name: any) => {
    let { pathname: path, search } = history.location;
    const obj = searchToObject();
    obj[name] = v;
    path = `${path}?${objectToQueryString(obj)}`;
    history.replace(path);
    const newFilter = {
      ...filter,
      [name]: v,
    };
    if (v == '7,8,9') {
      newFilter.sensitive_check = '';
    }
    if (v != '4') {
      newFilter.content_level = '';
    }

    if (name == 'status') {
      delete newFilter.keyword;
      delete newFilter.search_type;

      setSearch({
        keyword: '',
        search_type: 3,
      });
    }

    setFilter(newFilter);

    setListSelectedKeys([]);
  };

  const onChangeFilter = (v: any, name: any) => {
    setFilter({
      ...filter,
      [name]: v,
    });
  };

  const handleAddArticleOk = () => {
    getList({ current: 1, size: 20 });
  };

  const publish = () => {
    if (current != 1) {
      message.error('请翻到第一页，发布前20条稿件');
      return;
    }

    if (records.length < 20) {
      message.error('稿件少于20条，无法发布');
      return;
    }
    const ids = records
      .slice(0, 20)
      .map((v: any) => v.id)
      .join(',');

    Modal.confirm({
      title: '确定发布前20条稿件吗？',
      onOk: () => {
        run(api.rankingPublish, { data_ids: ids, type: filter.type }, true).then(() => {
          message.success('操作成功');

          setFilter({
            ...filter,
            status: 0,
          });
        });
      },
    });
  };

  const handleRangePickerChange = (dates: RangePickerValue) => {
    if (dates.length === 0) {
      setFilter({
        ...filter,
        begin: '',
        end: '',
      });
    } else {
      setFilter({
        ...filter,
        begin: dates[0] ? dates[0].format('YYYY-MM-DD') : '',
        end: dates[1] ? dates[1].format('YYYY-MM-DD') : '',
      });
    }
  };

  const handleSearchTypeChange = (v: any) => {
    setSearch({
      ...search,
      search_type: v,
    });
  };

  const handleKeywordChange = (e: any) => {
    setSearch({
      ...search,
      keyword: e.target.value,
    });
  };

  const handleKey = (e: any) => {
    if (e.which === 13) {
      const newFilter = {
        ...filter,
        search_type: search.search_type,
        keyword: search.search_type == 9 ? search.id : search.keyword,
      };
      setFilter(newFilter);
    }
  };

  const getCircleList = () => {
    communityApi
      .getCircleList({ current: 1, enabled: 'true', size: 100 })
      .then((res) => {
        const { list = [] } = res.data as any;
        setCircleOptions(list.records);
      })
      .catch(() => {});
  };

  return (
    <>
      <Row className="layout-infobar">
        <Col span={18}>
          <Radio.Group
            defaultValue={filter.status}
            buttonStyle="solid"
            onChange={(e) => onChangeType(e.target.value, 'status')}
            style={{ marginLeft: 8 }}
          >
            <Radio.Button value={'3'}>待审核</Radio.Button>
            <Radio.Button value={'4'}>已通过</Radio.Button>
            <Radio.Button value={'5'}>不通过</Radio.Button>
            <Radio.Button value={'7,8,9'}>已删除</Radio.Button>
          </Radio.Group>
          <PermButton
            perm=""
            style={{ marginLeft: 8 }}
            onClick={() => history.push('/view/ugcAuditBlackList')}
          >
            发布黑名单
          </PermButton>
          <PermButton
            perm=""
            style={{ marginLeft: 8 }}
            onClick={() => history.push('/view/ugcPublishRuleMgr')}
          >
            发帖行为规范
          </PermButton>
        </Col>
        <Col span={6} className="layout-breadcrumb">
          {getCrumb(props.breadCrumb)}
        </Col>
      </Row>
      <div className="component-content">
        <Row style={{ marginBottom: 16 }}>
          <Col span={15}>
            <Row>
              <DatePicker.RangePicker
                format="YYYY-MM-DD"
                onChange={handleRangePickerChange}
                value={filter.begin ? [moment(filter.begin), moment(filter.end)] : []}
                style={{ width: 240 }}
              />
              <Tooltip title="待审列表，按帖子提交审核时间筛选；已通过/不通过列表，按审核时间筛选；已删除列表，按删除时间筛选">
                <Icon type="question-circle" style={{ marginLeft: 8 }} />
              </Tooltip>

              <Select
                value={filter.doc_types}
                onChange={(v) => onChangeFilter(v, 'doc_types')}
                style={{ marginLeft: 8, width: 100 }}
              >
                <Select.Option value="">帖子类型</Select.Option>
                <Select.Option value={12}>图文帖</Select.Option>
                <Select.Option value={10}>视频帖</Select.Option>
              </Select>

              <Select
                value={filter.circle_id}
                onChange={(v) => onChangeFilter(v, 'circle_id')}
                style={{ marginLeft: 8, width: 120 }}
              >
                <Select.Option value="">按圈子筛选</Select.Option>
                <Select.Option value="0">无圈子</Select.Option>
                {circleOptions.map((item: any) => (
                  <Select.Option key={item.id} value={item.id}>
                    {item.name}
                  </Select.Option>
                ))}
              </Select>
            </Row>
            <Row style={{ marginTop: 8 }}>
              {filter.status != '7,8,9' && (
                <Select
                  value={filter.sensitive_check}
                  onChange={(v) => onChangeFilter(v, 'sensitive_check')}
                  style={{ marginRight: 8, width: 100 }}
                >
                  <Select.Option value="">机审结果</Select.Option>
                  <Select.Option value={1}>涉敏</Select.Option>
                  <Select.Option value={2}>未涉敏</Select.Option>
                </Select>
              )}
              {filter.status == 4 && (
                <Select
                  value={filter.content_level}
                  onChange={(v) => onChangeFilter(v, 'content_level')}
                  style={{ width: 120 }}
                >
                  <Select.Option value="">按等级筛选</Select.Option>
                  <Select.Option value={0}>普通</Select.Option>
                  <Select.Option value={2}>推荐</Select.Option>
                  <Select.Option value={1}>沉底</Select.Option>
                </Select>
              )}
            </Row>
          </Col>
          <Col span={9} style={{ textAlign: 'right' }}>
            <Select
              value={search.search_type}
              onChange={handleSearchTypeChange}
              style={{ marginRight: 8, width: 120 }}
            >
              <Select.Option value={3}>标题</Select.Option>
              <Select.Option value={1}>帖子ID</Select.Option>
              <Select.Option value={9}>作者</Select.Option>
            </Select>
            {search.search_type == 9 ? (
              <Select
                style={{ width: 180, marginRight: 8 }}
                placeholder="请输入昵称或用户ID"
                onSearch={handleAccountSearch}
                showSearch
                allowClear
                disabled={!!props.formContent}
                filterOption={false}
                value={search.id}
                onChange={(val: any) => setSearch({ ...search, id: val })}
              >
                {accountOptions.map((item: any) => (
                  <Select.Option className="reporter-item" key={item.chao_id} value={item.chao_id}>
                    {item.nick_name}|用户ID：{item.chao_id}
                  </Select.Option>
                ))}
              </Select>
            ) : (
              <Input
                value={search.keyword}
                onChange={handleKeywordChange}
                style={{ width: 180, marginRight: 8 }}
                onKeyPress={handleKey}
                placeholder="请输入搜索内容"
              />
            )}
            <Button onClick={() => handleKey({ which: 13 })}>
              <Icon type="search" />
              搜索
            </Button>
          </Col>
        </Row>
        {['3', '4', '5'].includes(filter.status) && (
          <Row style={{ marginBottom: 16 }}>
            <Dropdown
              disabled={listSelectedKeys.length == 0}
              overlay={
                <Menu>
                  <PermMenuItem
                    perm="ugc_article:content_audit"
                    onClick={() => {
                      if (filter.status == 4) {
                        changeLevel(listSelectedKeys, 0, true);
                      } else {
                        approved(listSelectedKeys, 0, true);
                      }
                    }}
                  >
                    普通
                  </PermMenuItem>
                  <PermMenuItem
                    perm="ugc_article:content_audit"
                    onClick={() => {
                      if (filter.status == 4) {
                        changeLevel(listSelectedKeys, 2, true);
                      } else {
                        approved(listSelectedKeys, 2, true);
                      }
                    }}
                  >
                    推荐
                  </PermMenuItem>
                  <PermMenuItem
                    perm="ugc_article:content_audit"
                    onClick={() => {
                      if (filter.status == 4) {
                        changeLevel(listSelectedKeys, 1, true);
                      } else {
                        approved(listSelectedKeys, 1, true);
                      }
                    }}
                  >
                    沉底
                  </PermMenuItem>
                </Menu>
              }
            >
              <PermButton type="primary" perm="ugc_article:content_audit" style={{ marginLeft: 8 }}>
                {filter.status == 4 ? '批量改等级' : '批量通过'}
                <Icon type="down" />
              </PermButton>
            </Dropdown>
            {filter.status != 5 && (
              <PermButton
                disabled={listSelectedKeys.length == 0}
                perm="ugc_article:content_audit"
                style={{ marginLeft: 8 }}
                onClick={() => notPassed(listSelectedKeys, true)}
              >
                批量不通过
              </PermButton>
            )}
            {filter.status != 3 && (
              <PermButton
                disabled={listSelectedKeys.length == 0}
                perm="ugc_article:content_audit"
                style={{ marginLeft: 8 }}
                onClick={() => resumeReview(listSelectedKeys, true)}
              >
                批量待审
              </PermButton>
            )}
          </Row>
        )}
        <Table
          func={'getArticleAuditList'}
          index="release_list"
          filter={f}
          columns={getColumns()}
          rowKey="id"
          selectedRowKeys={listSelectedKeys}
          onSelectChange={handleListSelectChange}
          multi
          pagination={true}
          tableProps={{ scroll: { x: 1200 } }}
        />

        <AuditNotPassedModal
          {...notPass}
          onOk={() => {
            setNotPass({ ...notPass, visible: false });
            getList();
          }}
          onCancel={() => {
            setNotPass({ ...notPass, visible: false });
          }}
        />

        <AddAuditBlackUserModal
          {...addAuditBlackUser}
          onOk={() => {
            setAddAuditBlackUser({ ...addAuditBlackUser, visible: false });
            getList();
          }}
          onCancel={() => {
            setAddAuditBlackUser({ ...addAuditBlackUser, visible: false });
          }}
        />

        <AuditDetailDrawer
          {...detailDrawer}
          onOk={() => {
            setDetailDrawer({ ...detailDrawer, visible: false });
          }}
          onClose={(editFlag: boolean) => {
            setDetailDrawer({ ...detailDrawer, visible: false });
            if (editFlag) {
              getList();
            }
          }}
        />

        <Modal
          visible={userDetailModal.visible}
          key={userDetailModal.key}
          title="用户详情"
          width={800}
          onCancel={() => setUserDetailModal({ ...userDetailModal, visible: false })}
          onOk={() => setUserDetailModal({ ...userDetailModal, visible: false })}
        >
          {/*{user.visible && getUserDetail(user.detail)}*/}
          {userDetailModal.visible && <UserDetail detail={userDetailModal.detail} />}
        </Modal>
      </div>
    </>
  );
}
