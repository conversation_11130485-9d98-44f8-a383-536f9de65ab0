import {
  Button,
  Dropdown,
  Form,
  Icon,
  Input,
  Mentions,
  Menu,
  Modal,
  Popover,
  Radio,
  Row,
  Switch,
  message,
} from 'antd';
import React, { forwardRef, useEffect, useImperativeHandle, useRef, useState } from 'react';
import { Drawer, ImageUploader } from '@app/components/common';
import AuditDetail from './AuditDetail';
import AuditInfo from './AuditInfo';
import { PermButton, PermMenuItem } from '@app/components/permItems';
import AddAuditBlackUserModal from './addAuditBlackUserModal';
import AuditNotPassedModal from './auditNotPassedModal';
import { useDispatch } from 'react-redux';
import { communityApi } from '@app/api';
import { setConfig } from '@app/action/config';
import './audit.scss';
import ImgUploadList from '@app/components/common/imgUploadList';
import { getImageRatio } from '@app/utils/utils';
import TopicSearchPopover from './TopicSearchPopover';
import MentionsSearchPopover from './MentionsSearchPopover';

function findSymbols(str: string, symbol = '@') {
  const indices: number[] = [];
  let index = str.indexOf(symbol);

  while (index !== -1) {
    indices.push(index);
    index = str.indexOf(symbol, index + 1);
  }

  return indices;
}

// 返回所有用户
function formatMentionUsers(mention_users: any, content: string) {
  const atSymbols = findSymbols(content);
  const result = [];
  for (let index = 0; index < mention_users?.length; index++) {
    const element = mention_users[index];
    const userIndexList = findSymbols(content, `@${element.nick_name}(${element.chao_id})`);
    for (let j = 0; j < userIndexList.length; j++) {
      const userIndex = userIndexList[j];
      const position = atSymbols.indexOf(userIndex);
      if (position != -1) {
        result.push({
          position,
          account_id: element.id,
          nick_name: element.nick_name,
        });
      }
    }
  }
  return result;
}

// 返回用来提交接口的文本
function formatMentionContent(mention_users: any, content: string) {
  let result = content;
  if (mention_users?.length > 0) {
    for (let index = 0; index < mention_users.length; index++) {
      const element = mention_users[index];
      result = result.replaceAll(
        `@${element.nick_name}(${element.chao_id})`,
        `@${element.nick_name}`
      );
    }
  }
  return result;
}

// 返回用来编辑的文本
function formatEditMentionContent(mention_users: any, content: string) {
  let result = content;
  if (mention_users?.length > 0) {
    const atPositions = findSymbols(content, '@');
    const sortedList = mention_users.sort((a: any, b: any) => {
      return a.position < b.position ? 1 : -1;
    });
    for (let index = 0; index < sortedList.length; index++) {
      const element = sortedList[index];
      const position = atPositions[element.position];
      const length = `@${element.nick_name}`.length;
      result =
        result.slice(0, position) +
        `@${element.nick_name}(${element.chao_id})` +
        result.slice(position + length);
    }
  }
  return result;
}

const AuditEditDrawer = (props: any, ref: any) => {
  const { getFieldDecorator, getFieldValue } = props.form;
  const contentRef = useRef<any>(null);
  const [radioValue, setRadioValue] = useState<number>(0);
  const [formatContent, setFormatContent] = useState<string>('');
  const mentionUsers = useRef<any[]>([]);

  const formLayout = {
    labelCol: { span: 4 },
    wrapperCol: { span: 20 },
  };

  const editFlag = useRef(false);
  const dispatch = useDispatch();

  useEffect(() => {
    // 获取封面图比例
    if (props.visible && props.detail?.doc_type == 10 && props.detail?.list_pics?.length > 0) {
      getImageRatio(props.detail?.list_pics[0]).then((ratio: number) => {
        if (ratio < 1) {
          setRadioValue(0);
        } else {
          setRadioValue(1);
        }
      });
    }
    if (props.visible) {
      let users: any = [];
      (props.detail?.mention_users || [])?.forEach((element: any) => {
        if (users.findIndex((user: any) => element.chao_id == user.chao_id) == -1) {
          users.push({
            id: element.account_id,
            nick_name: element.nick_name,
            chao_id: element.chao_id,
            position: element.position,
          });
        }
      });
      mentionUsers.current = users;

      setFormatContent(formatEditMentionContent(mentionUsers.current, props.detail?.list_title));
    }
  }, [props.visible]);

  const editArticle = () => {
    console.log('editArticle');
  };

  const getOperateLog = () => {
    console.log('getOperateLog');
  };

  const doSubmit = () => {
    const fields = ['content', 'pic_urls', 'image_watermark_status'];
    if (getFieldValue('cover_ratio') == 0) {
      fields.push('cover1');
    } else {
      fields.push('cover2');
    }
    props.form.validateFieldsAndScroll(fields, (err: any, values: any) => {
      if (!err) {
        dispatch(setConfig({ mLoading: true }));
        const params: any = {
          id: props.detail?.id,
        };
        const result = formatMentionUsers(mentionUsers.current, values.content?.trim());
        const content = formatMentionContent(mentionUsers.current, values.content?.trim());
        params.mention_users = result;
        params.content = content;

        if (props.detail?.doc_type == 10) {
          // 视频
          const cover = getFieldValue('cover_ratio') == 0 ? values.cover1 : values.cover2;
          params.cover_id = cover.split(',')[0];
          params.video_id = props.detail?.video_id;
        } else {
          // 图文
          params.image_ids = values.pic_urls?.map((item: any) => item.split(',')[0]) || [];
          params.image_watermark_status = values.image_watermark_status ? 1 : 0;
        }

        communityApi
          .editAuditArticle(params)
          .then((res: any) => {
            message.success('编辑成功');
            dispatch(setConfig({ mLoading: false }));
            props.onEnd && props.onEnd();
          })
          .catch(() => {
            // message.error('添加失败');
            dispatch(setConfig({ mLoading: false }));
          });
      } else {
        message.error('请检查表单内容');
        dispatch(setConfig({ mLoading: false }));
      }
    });
  };

  const handleInsert = (obj: any, str: any, limit = 10000) => {
    console.log('obj', obj, str);
    if (document.selection) {
      const sel = document.selection.createRange();
      if (sel.text > limit) {
        message.error('字数太多了');
        return '';
      }
      sel.text = str;
    } else if (typeof obj.selectionStart === 'number' && typeof obj.selectionEnd === 'number') {
      const startPos = obj.selectionStart;
      const endPos = obj.selectionEnd;
      let cursorPos = startPos;
      const tmpStr = obj.value;
      const text = tmpStr.substring(0, startPos) + str + tmpStr.substring(endPos, tmpStr.length);
      if (text.length > limit) {
        message.error('字数太多了');
        return '';
      }

      obj.value = text;
      cursorPos += str.length;
      obj.selectionStart = obj.selectionEnd = cursorPos;
      return obj.value;
    } else {
      const text = obj.value + str;
      if (text.length > limit) {
        message.error('字数太多了');
        return '';
      }
      obj.value += str;

      return obj.value;
    }
  };

  const handleInsertUser = (data: any) => {
    const content = handleInsert(
      contentRef.current.resizableTextArea.textArea,
      `@${data.nick_name}(${data.chao_id}) `,
      5000
    );
    const index = mentionUsers.current.findIndex((element) => element.chao_id == data.chao_id);
    if (index == -1) {
      mentionUsers.current.push(data);
    }

    if (content) {
      props.form.setFieldsValue({
        content,
      });
    }
  };

  // 插入话题
  const handleInsertSubject = (data: any) => {
    const content = handleInsert(
      contentRef.current.resizableTextArea.textArea,
      `#${data.name}# `,
      5000
    );
    if (content) {
      props.form.setFieldsValue({
        content,
      });
    }
  };

  const short_media_ids = props.detail?.short_media_ids?.split(',') || [];
  const imgList = (props.detail?.list_pics || []).map((item: any, index: number) => {
    return `${short_media_ids[index]},${item}`;
  });

  return (
    <Drawer
      title={`编辑${props.detail?.doc_type == 10 ? '视频' : '图文'}帖`}
      visible={props.visible}
      skey={props.key}
      onClose={() => props.onClose()}
      onOk={doSubmit}
      okPerm="ugc_article:content_save"
    >
      <Form {...formLayout}>
        {props.detail?.doc_type == 10 && (
          <>
            <Form.Item label="封面图">
              {getFieldDecorator('cover_ratio', {
                initialValue: radioValue,
                rules: [{ required: true }],
              })(
                <Radio.Group>
                  <Radio value={0}>3:4</Radio>
                  <Radio value={1}>16:9</Radio>
                </Radio.Group>
              )}
            </Form.Item>

            {getFieldValue('cover_ratio') == 0 ? (
              <Form.Item label="" colon={false} style={{ marginLeft: 130 }}>
                {getFieldDecorator('cover1', {
                  initialValue: radioValue == 0 ? imgList?.[0] : '',
                  preserve: true,
                  rules: [{ required: true, message: '请上传封面图' }],
                })(
                  <ImageUploader
                    needMz={true}
                    ratio={3 / 4}
                    extra="支持jpg,jpeg,png图片格式"
                    accept={['image/jpeg', 'image/png', 'image/jpg']}
                  />
                )}
              </Form.Item>
            ) : (
              <Form.Item label="" colon={false} style={{ marginLeft: 130 }}>
                {getFieldDecorator('cover2', {
                  initialValue: radioValue == 1 ? imgList?.[0] : '',
                  preserve: true,
                  rules: [{ required: true, message: '请上传封面图' }],
                })(
                  <ImageUploader
                    needMz={true}
                    ratio={16 / 9}
                    extra="支持jpg,jpeg,png图片格式"
                    accept={['image/jpeg', 'image/png', 'image/jpg']}
                  />
                )}
              </Form.Item>
            )}
          </>
        )}

        <Form.Item label="文字">
          {getFieldDecorator('content', {
            initialValue: formatContent,
            rules: [
              { required: true, message: '请输入文字' },
              {
                max: props.detail?.doc_type == 12 ? 5000 : 200,
                message: `最多${props.detail?.doc_type == 12 ? 5000 : 200}字`,
              },
            ],
          })(<Input.TextArea placeholder="请输入文字" ref={contentRef} rows={4} />)}
        </Form.Item>

        <p style={{ paddingLeft: 135 }}>
          <TopicSearchPopover onChange={(val: any) => handleInsertSubject(val)} />
          <MentionsSearchPopover onChange={(val: any) => handleInsertUser(val)} />
        </p>
        {props.detail?.doc_type == 12 && (
          <>
            <Form.Item label="图片">
              {getFieldDecorator('pic_urls', {
                initialValue: imgList || [],
                // rules: [
                //   {
                //     required: true,
                //     message: '请上传预览图',
                //   },
                // ],
              })(
                <ImgUploadList
                  max={9}
                  isCutting={true}
                  accept={['image/jpeg', 'image/png', 'image/jpg', 'image/gif']}
                  // tips={'用于让用户预览主题样式，可上传1～10张图片'}
                  extra={'最多可添加9张图，单张图片50MB以内'}
                  // onChange={(v: any) => setImgs(v)}
                  // disable={imgs.length == 9 ? '只能添加9个附件（图片/视频）' : ''}
                  customOp={true}
                  imgSize={50 * 1024}
                  needMz={true}
                />
              )}
            </Form.Item>
            {getFieldValue('pic_urls')?.length > 0 && (
              <Form.Item label="图片水印">
                {getFieldDecorator('image_watermark_status', {
                  initialValue: props.detail?.image_watermark_status ?? false,
                  valuePropName: 'checked',
                })(<Switch />)}
              </Form.Item>
            )}
          </>
        )}
      </Form>
    </Drawer>
  );
};

export default Form.create<any>({ name: 'AuditEditDrawer' })(forwardRef<any, any>(AuditEditDrawer));
