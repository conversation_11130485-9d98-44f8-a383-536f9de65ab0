import {
  getCrumb,
  searchToObject,
  UserDetail,
} from '@app/utils/utils';
import {
  Button,
  Col,
  Divider,
  Icon,
  Modal,
  Row,
  message,
} from 'antd';
import { Table, OrderColumn } from '@components/common';
import { useDispatch, useSelector, useStore } from 'react-redux';
import { useHistory, useParams } from 'react-router';
import { clearTableList, getTableList } from '@app/action/tableList';
import React, { useEffect, useState } from 'react';
import _, { min } from 'lodash';
import { communityApi as api, releaseListApi, userApi } from '@app/api';
import { setConfig } from '@app/action/config';
import { PermA, PermButton } from '@app/components/permItems';
import moment from 'moment';
import useXHR from '@app/utils/useXhr';
import CircleOwnerFormDrawer from './component/circleOwnerFormDrawer';

export default function CircleOwnerMgr(props: any) {
  const dispatch = useDispatch();
  const history = useHistory();
  const { id: circle_id, name } = useParams<any>();
  const { selected = '', board_id = '' } = searchToObject();
  const { session } = useStore().getState();
  const {
    current,
    size,
    total,
    allData: { real_total = 999999 },
  } = useSelector((state: any) => state.tableList);
  const [filter, setFilter] = useState({
    content_level: '',
    selected: selected ? parseInt(selected) : '',
    board_id: board_id ? parseInt(board_id) : '',
    doc_type: '',
    publish_time_start: '',
    publish_time_end: '',
  });

  const { loading, run } = useXHR();

  const [user, setUser] = useState({
    key: Date.now(),
    visible: false,
    detail: {},
  });
  const [circleOwner, setCircleOwner] = useState<any>({
    visible: false,
    formContent: null,
    perms: null,
    circle_id
  });

  // 删除
  const handelDel = (val: any) => {
    Modal.confirm({
      title: '确定删除？',
      onOk: () => {
        dispatch(setConfig({ loading: true }));
        api
          .deleteCircleOwner({ id: val.id, circle_id: circle_id })
          .then(() => {
            message.success('操作成功');
            dispatch(setConfig({ loading: false }));
            getData();
          })
          .catch(() => {
            dispatch(setConfig({ loading: false }));
          });
      },
    });
  };

  const showUserDetail = (record: any) => {
    dispatch(setConfig({ loading: true }));
    userApi
      .getUserDetail({ accountId: record.account_id })
      .then((r: any) => {
        setUser({
          visible: true,
          key: Date.now(),
          detail: r.data.account,
        });
        dispatch(setConfig({ loading: false }));
      })
      .catch(() => dispatch(setConfig({ loading: false })));
  };

  const handleSort = (record: any, flag: 0 | 1) => {
    run(api.sortCircleOwner, { id: record.id, sort_flag: flag }, true).then(() => {
      message.success('操作成功');
      getData();
    });
  };

  const getSeq = (i: number) => (current - 1) * size + i + 1;
  const columns: any = [
    {
      title: '排序',
      key: 'sort',
      render: (text: any, record: any, i: number) => (
        <OrderColumn
          perm="circle_manager:manage"
          pos={i}
          start={0}
          end={total - 1}
          onUp={() => handleSort(record, 0)}
          onDown={() => handleSort(record, 1)}
        />
      ),
      width: 70,
    },
    {
      title: '序号',
      key: 'seq',
      render: (a: any, b: any, index: number) => <span>{getSeq(index)}</span>,
      width: 50,
    },
    // {
    //   title: 'ID',
    //   dataIndex: 'article_id',
    //   width: 80,
    //   render: (text: any, record: any) => (<a onClick={() => showIDDetailModal({
    //     id: record.article_id,
    //     uuid: record.uuid,
    //     metadata_id: record.metadata_id,
    //   })}>{text}</a>)
    // },
    {
      title: '用户昵称',
      dataIndex: 'nick_name',
      width: 180,
      render: (text: string, record: any) => {
        return <a onClick={() => showUserDetail(record)}>{text}</a>
      }
    },
    {
      title: '功能权限',
      dataIndex: 'permission_names',
      width: 200,
    },
    {
      title: '数据权限',
      dataIndex: 'permission_board_names',
      width: 200,
    },
    {
      title: '最后操作人',
      key: 'updated_by',
      dataIndex: 'updated_by',
      width: 140,
    },
    {
      title: '最后操作时间',
      dataIndex: 'updated_at',
      key: 'updated_at',
      width: 160,
      render: (text: any) => (
        <div>{moment(text).format('YYYY-MM-DD HH:mm:ss')}</div>
      ),
    },
    {
      title: '操作',
      key: 'op',
      align: 'center',
      width: 120,
      render: (text: any, record: any) => {
        return <span>
          <PermA
            perm="circle_manager:manage"
            onClick={() => editOwner(record)}>
            编辑
          </PermA>
          <Divider type="vertical" />
          <PermA
            perm="circle_manager:manage"
            onClick={() => handelDel(record)}>
            删除
          </PermA>

        </span>
      },
    },
  ];

  const getData = (goToFirstPage = false, newFilter = filter) => {
    const cur = goToFirstPage ? 1 : current;
    dispatch(
      getTableList('getCircleOwnerList', 'list', { current: cur, size, circle_id })
    );
  };

  const editOwner = (record: any) => {
    api
      .getCircleOwnerPermissionList({ circle_id })
      .then(({ data }) => {
        const { list = [], boards } = data as any;
        const perms = record?.permission_ids?.split(',') || []
        const boardPerms = record?.permission_board_ids?.split(',') || []
        console.log('perms', perms)
        for (const item of list) {
          let length = 0
          for (const childrenItem of item?.children ?? []) {
            childrenItem.checked = perms?.includes(`${childrenItem.id}`)
            if (childrenItem.checked) {
              length += 1
            }
          }
          item.checked = item?.children?.length > 0 && length == item?.children?.length
          item.indeterminate = item?.children?.length > 0 && length > 0 && length < item?.children?.length
        }
        setCircleOwner({
          visible: true,
          formContent: record,
          perms: list,
          boards,
          boardPerms,
          circle_id
        })
      })
      .catch(() => { });
  }

  useEffect(() => {
    getData(true);
  }, []);

  return (
    <>
      <Row className="layout-infobar">
        <Col span={12}>
          <Button onClick={() => {
            dispatch(clearTableList())
            history.goBack()
          }}>
            <Icon type="left-circle" />
            返回圈子管理
          </Button>
          <PermButton
            perm="circle_manager:manage"
            style={{ marginLeft: 8 }}
            onClick={() => editOwner(null)}
          >
            添加圈主
          </PermButton>
        </Col>
        <Col span={12} className="layout-breadcrumb">
          {getCrumb([...props.breadCrumb, name])}
        </Col>
      </Row>
      <div className="component-content">
        <Table
          func="getCircleArticleList"
          index="list"
          filter={{ circle_id }}
          pagination={false}
          rowKey="id"
          columns={columns}
        />
        <Modal
          visible={user.visible}
          key={user.key}
          title="用户详情"
          width={800}
          onCancel={() => setUser({ ...user, visible: false })}
          onOk={() => setUser({ ...user, visible: false })}
        >
          {/*{getUserDetail(user.detail)}*/}
          {<UserDetail detail={user.detail} />}
        </Modal>

        <CircleOwnerFormDrawer
          {...circleOwner}
          onClose={() => setCircleOwner({ visible: false })}
          onEnd={() => {
            setCircleOwner({ ...circleOwner, visible: false })
            getData(true);
          }}
        >

        </CircleOwnerFormDrawer>
      </div>
    </>
  );
}
