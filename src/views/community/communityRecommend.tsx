import { CommonObject, IResReleaseListAllData, IResReleaseListRecordsData } from '@app/types';
import { connectTable as connect } from '@utils/connect';
import {
  Button,
  Col,
  Row,
  Select,
  DatePicker,
  Input,
  Icon,
  Switch,
  Modal,
  message,
  Tag,
  Dropdown,
  Menu,
  InputNumber,
  Checkbox,
} from 'antd';
import React from 'react';
import { withRouter } from 'react-router';
import { setConfig } from '@action/config';
import {
  searchToObject,
  getSupportRecommend,
  recommendTypeText,
  requirePerm,
  resolveNewsType,
  showIDDetailModal,
  UserDetail,
} from '@utils/utils';

import { Table, OrderColumn } from '@components/common';
import { communityApi, userApi } from '@app/api';
import { PermA } from '@app/components/permItems';
import { PreviewMCN } from '@components/common';

import moment from 'moment';
import NewsBaseComponent, { INewsBaseProps } from '../news/baseComponent';
import ContentRecommendDrawer from '@app/components/business/recommend/ContentRecommendDrawer';

import '../community/component/community.scss';
import EditCircleInfoModal from './component/EditCircleInfoModal';
import showImagePreviewModal from '@app/components/common/imagePreviewModal';
import ImagePreviewColumn from '@app/components/common/imagePreviewColumn';

type Props = INewsBaseProps<{}, IResReleaseListRecordsData, IResReleaseListAllData>;
type State = {
  preview: {
    visible: boolean;
    skey: number;
    data: CommonObject;
  };
  typeValue: string;
  searchSortFixed: boolean;
  circleInfo: {
    visible: boolean;
    record: any;
  };
  circleOptions: any;
  user: {
    visible: boolean;
    detail: any;
    key: any;
  };
};
class ReleaseList extends NewsBaseComponent<
  {},
  IResReleaseListRecordsData,
  IResReleaseListAllData,
  State
> {
  constructor(props: Props) {
    super(props);

    const { doc_type, channel_id } = searchToObject();
    const initialFilter = {
      current: 1,
      size: 10,
      search_type: '3',
      channel_id: searchToObject().channel_id,
      doc_type: '',
      keyword: '',
      begin: '',
      end: '',
      community_pushed: '',
      operate_end: '',
      operate_begin: '',
      // typeValue: ''
      circle_id: '',
    };
    this.state = {
      filter: initialFilter,
      pageInput: initialFilter,
      preview: {
        visible: false,
        skey: 0,
        data: {},
      },
      showDragSorter: false,
      fixedCount: 0,
      typeValue: '',
      searchSortFixed: false,
      ContentRecommendDrawerForm: {
        show: false,
        key: Date.now() + 10,
        record: null,
      },
      circleInfo: {
        visible: false,
        record: null,
      },
      circleOptions: [],
      user: {
        key: null,
        visible: false,
        detail: {},
      },
    } as any;
  }

  componentDidMount() {
    this.getData({ current: 1, size: 10 });
    this.getCircleList();
    this.props.dispatch(
      setConfig({
        selectKeys: [`/view/communityRecommend?channel_id=${searchToObject().channel_id}`],
        openKeys: this.props.openKeys,
      })
    );
  }
  componentDidUpdate(prevProps: Props) {
    if (
      prevProps.tableList.timestamp === this.props.tableList.timestamp &&
      searchToObject().channel_id === this.state.filter.channel_id &&
      this.props.location === prevProps.location
    ) {
      return;
    }
    if (
      searchToObject().channel_id !== this.state.filter.channel_id ||
      this.props.location !== prevProps.location
    ) {
      const initialFilter = {
        current: 1,
        size: 10,
        search_type: '3',
        doc_type: '',
        keyword: '',
        begin: '',
        end: '',
        channel_id: searchToObject().channel_id,
        community_pushed: '',
        operate_end: '',
        operate_begin: '',
        circle_id: '',
        // typeValue: ''
      };
      this.props.dispatch(
        setConfig({
          selectKeys: [`/view/communityRecommend?channel_id=${searchToObject().channel_id}`],
        })
      );
      this.setState(
        {
          filter: initialFilter,
          pageInput: initialFilter,
          typeValue: '',
        },

        () => {
          this.getData();
        }
      );
    }
    if (
      prevProps.tableList.timestamp !== this.props.tableList.timestamp &&
      Boolean(this.props.tableList.allData.release_list)
    ) {
      const { channel, release_list } = this.props.tableList.allData;
      this.setState({
        channel,
        filter: { ...this.state.filter, current: release_list.current, size: release_list.size },
        pageInput: {
          ...this.state.pageInput,
          current: release_list.current,
          size: release_list.size,
        },
        fixedCount: release_list.fixed_count || 0,
        total: release_list.total,
      });
    }
  }
  getData = (overlap: CommonObject = {}, filters = this.getFilters()) => {
    this.dispatchTable('getReleaseList', 'release_list', {
      ...filters,
      ...overlap
    });
    this.getSortFixedStatus();
  };

  // 显示用户详情
  showUserDetailModal(record: any, visible: boolean) {
    this.props.dispatch(setConfig({ loading: true }));
    userApi
      .getUserDetail({ accountId: record.account_id })
      .then((r: any) => {
        this.setState({
          user: {
            key: Date.now(),
            visible,
            detail: r.data.account,
          },
        });
        this.props.dispatch(setConfig({ loading: false }));
      })
      .catch(() => this.props.dispatch(setConfig({ loading: false })));
  }

  getCircleList = () => {
    communityApi
      .getCircleList({ current: 1, enabled: 'true', size: 100 })
      .then((res) => {
        const { list = [] } = res.data as any;
        this.setState({ circleOptions: list.records });
      })
      .catch(() => {});
  };

  getFilters = () => {
    const { current, size } = this.props.tableList;
    const { filter } = this.state;
    const filters: CommonObject = { current, size, ...filter, doc_types: filter.doc_type };
    Object.keys(filter).forEach((k: string) => {
      if (!filters[k]) {
        delete filters[k];
      }
    });
    return filters;
  };
  listSort = (record: any, i: number) => {
    let data = {
      id: record.id,
      offset: i,
      current: record.seq,
    };
    communityApi.recommendBannerListMovePosition(data).then((res: any) => {
      message.success('操作成功');
      this.getData();
    });
  };
  sortListFixed = (record: any, text: any) => {
    if (text == '固定') {
      if (this.state.fixedCount >= 40) {
        message.error('最多只能固定40条稿件或推荐位');
        return;
      }
      let data = {
        id: record.id,
        position: record.seq,
        fixed: !!record.fixed_number ? false : true,
      };
      communityApi.recommendBannerListFiexdPosition(data).then((res: any) => {
        message.success('操作成功');
        this.getData();
      });
    } else {
      let data = {
        id: record.id,
      };
      communityApi.recommendBannerListCancelFixed(data).then((res: any) => {
        message.success('操作成功');
        this.getData();
      });
    }
  };
  titleClick = (record: any) => {
    if (record.doc_type === -1) {
      this.toggleContentRecommendDrawerState(true, record);
    } else {
      this.setState({
        preview: {
          visible: true,
          skey: Date.now(),
          data: record,
        },
      });
    }
  };
  cancelRecommend = (record: any, type: string) => {
    Modal.confirm({
      title: <p>确认{type}?</p>,
      onOk: () => {
        let data = {
          id: record.id,
          community_pushed: !!record.community_pushed ? '0' : '1',
          channel_id: searchToObject().channel_id,
        };
        communityApi.updateCommunityPushed(data).then((res: any) => {
          message.success('操作成功');
          this.getData();
        });
      },
    });
  };
  cancelFix = (id: string | number, title: string) => {
    const WAIT_TIME = 1000;
    Modal.confirm({
      title: <p>确认取消固定《{title}》</p>,
      onOk: () => {
        this.props.dispatch(setConfig({ loading: true }));
        communityApi
          .recommendBannerListCancelFixed({ id })
          .then(() => {
            message.success('操作成功');
            setTimeout(() => {
              this.getData();
              this.props.dispatch(setConfig({ loading: false }));
            }, WAIT_TIME);
          })
          .catch(() => {
            this.props.dispatch(setConfig({ loading: false }));
          });
      },
    });
  };
  changeOrder = (record: any) => {
    let position = record.seq;
    let sticky = false;
    const WAIT_TIME = 1000;
    const positionChange = (v: any) => {
      position = v;
    };
    const stickyChange = (e: any) => {
      sticky = e.target.checked;
    };
    const searchStatus =
      this.state.filter.keyword ||
      this.state.filter.doc_type ||
      this.state.filter.begin ||
      this.state.filter.top_pushed ||
      this.state.filter.to_channel_ids ||
      this.state.filter.recommend_enabled === '1' ||
      this.state.filter.sort_asc >= 0 ||
      this.state.filter.circle_id;
    Modal.confirm({
      title: <p>排序：《{record.list_title}》</p>,
      icon: <Icon type="info-circle" />,
      content: (
        <div>
          <span>请输入位置：</span>
          <InputNumber
            min={1}
            max={searchStatus ? 999999999999 : this.state.total + this.state.fixedCount}
            defaultValue={position}
            onChange={positionChange}
          />
          <br key={1} />
          <Checkbox
            disabled={!record.visible}
            style={{ marginTop: 8 }}
            defaultChecked={false}
            onChange={stickyChange}
            key={2}
          >
            固定位置
          </Checkbox>
          <p style={{ color: '#ccc' }} key={3}>
            此选项只对1~100位置有效
            <br />
            最多只能固定40条稿件或推荐位
          </p>
        </div>
      ),
      onOk: (closeFunc: Function) => {
        const MAX_STICKY_POSITION = 100;
        if (!position) {
          message.error('请填写位置');
          return;
        }
        if (sticky && position > MAX_STICKY_POSITION) {
          message.error('固定位置必须为1~100');
          return;
        }
        if (sticky && this.state.fixedCount >= 40) {
          message.error('最多只能固定40条稿件或推荐位');
          return;
        }
        if (this.state.total + this.state.fixedCount < position && !searchStatus) {
          message.error('稿件位置不能大于稿件总数');
          return;
        }
        this.props.dispatch(setConfig({ loading: true }));
        const data: any = { position, id: record.id, fixed: sticky };
        communityApi
          .recommendBannerListFiexdPosition(data)
          .then(() => {
            message.success('操作成功');
            setTimeout(() => {
              this.getData();
              this.props.dispatch(setConfig({ loading: false }));
            }, WAIT_TIME);
            closeFunc();
          })
          .catch(() => {
            this.props.dispatch(setConfig({ loading: false }));
          });
      },
    });
  };

  changeVisible = (id: string | number, visible: boolean) => {
    const WAIT_TIME = 1000;
    this.props.dispatch(setConfig({ loading: true }));
    communityApi
      .releaseChangeVisible({ id, visible: visible ? 0 : 1 })
      .then(() => {
        message.success('操作成功');
        setTimeout(() => {
          this.getData();
          this.props.dispatch(setConfig({ loading: false }));
        }, WAIT_TIME);
      })
      .catch(() => {
        this.props.dispatch(setConfig({ loading: false }));
      });
  };

  handleEditCircleInfo(record: any, visible: boolean) {
    this.setState({
      circleInfo: {
        visible,
        record,
      },
    });
  }

  columns = () => {
    let { total } = this.props.tableList;
    const channelId = searchToObject().channel_id;
    const { permissions } = this.props.session;
    const recommendCanEdit = permissions.indexOf(`content_recommend:${channelId}:update`) >= 0;
    const getDropDown = (record: any) => {
      const recommendMenu = (
        <Menu>
          <Menu.Item onClick={this.titleClick.bind(this, record)} disabled={!recommendCanEdit}>
            编辑
          </Menu.Item>
          {requirePerm(
            this,
            'community:sort'
          )(
            record.fixed_number > 0 ? (
              <Menu.Item onClick={this.cancelFix.bind(this, record.id, record.list_title)}>
                取消固定
              </Menu.Item>
            ) : (
              <Menu.Item disabled={!record.visible} onClick={this.changeOrder.bind(this, record)}>
                排序
              </Menu.Item>
            )
          )}
          {requirePerm(
            this,
            `channel_article:${channelId}:display`
          )(
            <Menu.Item
              disabled={record.fixed_number > 0}
              onClick={this.changeVisible.bind(this, record.id, record.visible)}
            >
              {record.visible === true ? '隐藏' : '取消隐藏'}
            </Menu.Item>
          )}
          {requirePerm(
            this,
            `community:${channelId}:update_community_pushed`
          )(
            <Menu.Item
              onClick={this.cancelRecommend.bind(
                this,
                record,
                `删除推荐位《${record.list_title}》`
              )}
            >
              删除
            </Menu.Item>
          )}
        </Menu>
      );
      const menu = (
        <Menu>
          {requirePerm(
            this,
            `community:${channelId}:update_community_pushed`
          )(
            <Menu.Item
              onClick={this.cancelRecommend.bind(
                this,
                record,
                record.community_pushed == 1 ? '取消推荐' : '设为推荐'
              )}
            >
              {record.community_pushed == 1 ? '取消推荐' : '设为推荐'}
            </Menu.Item>
          )}
          {requirePerm(
            this,
            `circle_article:update_circle`
          )(
            <Menu.Item onClick={this.handleEditCircleInfo.bind(this, record, true)}>
              修改圈子信息
            </Menu.Item>
          )}
        </Menu>
      );
      return (
        <Dropdown overlay={record.doc_type === -1 ? recommendMenu : menu}>
          <a className="ant-dropdown-link">
            操作 <Icon type="down" />
          </a>
        </Dropdown>
      );
    };
    return [
      {
        title: '排序',
        key: 'order',
        render: (text: any, record: any, i: number) => {
          return (
            <OrderColumn
              pos={record.seq}
              start={1}
              end={total + this.state.fixedCount}
              perm={`community:sort`}
              disableUp={!!record.fixed_number || !!this.state.searchSortFixed}
              disableDown={!!record.fixed_number || !!this.state.searchSortFixed}
              onUp={this.listSort.bind(this, record, 1)}
              onDown={this.listSort.bind(this, record, -1)}
            />
          );
        },
        width: 70,
      },
      {
        title: '序号',
        key: 'seq',
        width: 80,
        render: (text: any, record: any, i: number) => {
          const max = 101;
          const allowPin = record.seq < max;
          return (
            <>
              <span style={{ marginRight: 8 }}>{record.seq}</span>
              {allowPin && record.fixed_number > 0 && (
                <PermA
                  perm={`community:sort`}
                  onClick={this.sortListFixed.bind(this, record, '取消固定')}
                  disabled={this.state.searchSortFixed}
                >
                  <Icon type="pushpin" theme="filled" />
                </PermA>
              )}
              {allowPin && record.visible && record.fixed_number == 0 && (
                <PermA
                  perm={`community:sort`}
                  disabled={this.state.searchSortFixed}
                  onClick={this.sortListFixed.bind(this, record, '固定')}
                >
                  <Icon type="pushpin" />
                </PermA>
              )}
            </>
          );
        },
      },
      {
        title: 'ID',
        dataIndex: 'id',
        width: 90,
        render: (text: any, record: any) => <a onClick={() => showIDDetailModal(record)}>{text}</a>,
      },
      // {
      //   title: 'uuid',
      //   dataIndex: 'uuid',
      //   width: 120
      // },
      // {
      //   title: '创作者平台ID',
      //   dataIndex: 'metadata_id',
      //   width: 115,
      //   render: (text: any, record: any) => (
      //     <span>{record.doc_type >= 0 ? text : ''}</span>
      //   ),
      // },
      {
        title: '标题',
        dataIndex: 'list_title',
        key: 'list_title',
        render: (text: any, record: any) => (
          <>
            {record.doc_type < 0 && <Tag color="#e99d42">{recommendTypeText(record.ref_type)}</Tag>}
            <a
              style={{ pointerEvents: record.doc_type < 0 && !recommendCanEdit ? 'none' : 'auto' }}
              onClick={this.titleClick.bind(this, record)}
              className={`list-title ${record.visible ? '' : 'hide-title'} ${
                record.fixed_number > 0 ? 'fixed-title' : ''
              }`}
            >
              {text || '-'}
              {record.visible ? '' : '（隐藏）'}
            </a>
          </>
        ),
      },
      {
        title: '封面图/配图',
        key: 'pic_array',
        dataIndex: 'pic_array',
        width: 150,
        align: 'center',
        render: (text: any, record: any) => (
          <div style={{ height: 60, textAlign: 'center' }}>
            <ImagePreviewColumn text={text?.[0]} imgs={record.pic_array}></ImagePreviewColumn>
            {/* <img src={text?.[0]} className='list-pic' onMouseEnter={() => showImagePreviewModal({ images: record.pic_array })}></img> */}
          </div>
        ),
      },
      {
        title: '类型',
        dataIndex: 'doc_type',
        key: 'doc_type',
        render(doc_type: number) {
          return doc_type === 13 ? '长文章' : resolveNewsType(doc_type, 1);
        },
        width: 90,
      },
      {
        title: '关联圈子',
        key: 'circle_name',
        dataIndex: 'circle_name',
        width: 90,
        render: (circle_name: string, record: any) =>
          circle_name ? `${circle_name}${record.circle_enable ? '' : '(已下线)'}` : '',
      },
      {
        title: '作者',
        dataIndex: 'author',
        key: 'author',
        width: 110,
        render: (text: any, record: any) => (
          <a onClick={() => this.showUserDetailModal(record, true)}>{text}</a>
        ),
      },
      {
        title: '用户ID',
        key: 'chao_id',
        dataIndex: 'chao_id',
        width: 110,
      },
      {
        title: '发布时间',
        dataIndex: 'published_at',
        key: 'created_at',
        render: (text: any, record: any) => (
          <span>{moment(text).format('YYYY-MM-DD HH:mm:ss')}</span>
        ),
        width: 95,
      },
      {
        title: '操作人',
        dataIndex: 'community_pushed_by',
        key: 'community_pushed_by',
        width: 95,
      },
      {
        title: '操作时间',
        dataIndex: 'community_pushed_time',
        key: 'community_pushed_time',
        render: (text: any, record: any) => (
          <span>{moment(text).format('YYYY-MM-DD HH:mm:ss')}</span>
        ),
        width: 95,
      },
      {
        title: '操作',
        key: 'op',
        width: 70,
        fixed: 'right',
        render: (text: any, record: any) => getDropDown(record),
      },
    ];
  };
  goTopicRecommendation = () => {
    this.props.history.push(`/view/topicRecommendation?channel_id=${searchToObject().channel_id}`);
  };
  goRecommendedUserOperations = () => {
    this.props.history.push(
      `/view/recommendedUserOperations?channel_id=${searchToObject().channel_id}`
    );
  };
  openBannerManage = () => {
    this.props.history.push('/view/bannerManage');
  };
  handleFilterChange = (key: any, value: any) => {
    this.setState(
      {
        filter: {
          ...this.state.filter,
          [key]: value,
          current: 1,
        },
        pageInput: { ...this.state.pageInput, [key]: value, current: 1 },
      },
      () => this.getData()
    );
  };
  handleRangePickerChange = (type: any, date: any) => {
    let begin = date[0] ? moment(date[0]).format('YYYY-MM-DD') : '';
    let end = date[1] ? moment(date[1]).format('YYYY-MM-DD') : '';
    let obj = {};
    if (type == '发布') {
      this.setState(
        {
          filter: {
            ...this.state.filter,
            begin,
            end,
          },
          pageInput: {
            ...this.state.pageInput,
            begin,
            end,
          },
        },
        () => this.getSortFixedStatus()
      );
      obj = { ...this.state.filter, begin, end };
    } else {
      this.setState(
        {
          filter: {
            ...this.state.filter,
            operate_begin: begin,
            operate_end: end,
          },
          pageInput: {
            ...this.state.pageInput,
            operate_begin: begin,
            operate_end: end,
          },
        },
        () => this.getSortFixedStatus()
      );
      obj = { ...this.state.filter, operate_begin: begin, operate_end: end };
    }
    this.getData(obj);
  };
  submitCircleBlockEnd = () => {
    this.getData();
    this.setState({
      circleInfo: {
        visible: false,
        record: {},
      },
    });
  };

  onDragEnd = (oldIndex: number, newIndex: number) => {
    const target: any = this.props.tableList.records[newIndex];
    const source: any = this.props.tableList.records[oldIndex];
    const WAIT_TIME = 1000;
    if (target.fixed_number) {
      message.error('当前位置已有固定位置稿件');
      return;
    }
    const body: any = {
      position: target.seq,
      id: source.id,
      fixed: false,
    };
    this.props.dispatch(setConfig({ loading: true }));
    communityApi
      .recommendBannerListFiexdPosition(body)
      .then(() => {
        message.success('操作成功');
        setTimeout(() => {
          this.getData();
          this.props.dispatch(setConfig({ loading: false }));
        }, WAIT_TIME);
      })
      .catch(() => {
        this.props.dispatch(setConfig({ loading: false }));
      });
  };
  closePreview = () => {
    this.setState({
      preview: {
        ...this.state.preview,
        visible: false,
      },
    });
  };
  getSortFixedStatus = () => {
    const { filter } = this.state;
    let searchSortFixed =
      !!filter.begin ||
      !!filter.end ||
      !!filter.keyword ||
      !!filter.operate_begin ||
      !!filter.operate_end ||
      !!filter.doc_type ||
      Boolean(filter.circle_id);
    this.setState({
      searchSortFixed,
    });
  };

  render() {
    const channelId = searchToObject().channel_id;
    const {
      filter,
      showDragSorter,
      preview,
      pageInput,
      ContentRecommendDrawerForm,
      circleInfo,
      circleOptions,
      user,
    } = this.state;
    const searchCondition =
      filter.begin ||
      filter.end ||
      filter.keyword ||
      filter.operate_begin ||
      filter.operate_end ||
      filter.doc_type ||
      filter.circle_id;
    return (
      <>
        <Row className="layout-infobar">
          <Col span={12}>
            {/* <PermButton perm={'type_recommend:32:view'} style={{ marginRight: 8 }} onClick={this.openBannerManage.bind(this)}>轮播图管理</PermButton> */}
            {requirePerm(
              this,
              `content_recommend:${channelId}:create`
            )(
              <Button
                onClick={() => this.toggleContentRecommendDrawerState(true)}
                style={{ marginRight: 8 }}
              >
                创建推荐位
              </Button>
            )}
            {/* <PermButton perm={`new_follow_recommend:${searchToObject().channel_id}:list`} style={{ marginRight: 8 }} onClick={this.goRecommendedUserOperations.bind(this)}>用户推荐位</PermButton> */}
            {/* <PermButton perm={`topic_recommend:${searchToObject().channel_id}:list`} style={{ marginRight: 8 }} onClick={this.goTopicRecommendation.bind(this)}>话题推荐位</PermButton> */}
            <span
              style={{
                display:
                  this.props.session.permissions.indexOf(`community:sort`) === -1
                    ? 'none'
                    : 'inline',
              }}
            >
              拖拽排序&nbsp;
              <Switch
                checked={showDragSorter}
                checkedChildren="开"
                unCheckedChildren="关"
                onChange={(e: any) => {
                  console.log(e);
                  this.setState({
                    showDragSorter: e,
                  });
                }}
              />
            </span>
          </Col>
          <Col span={12} className="layout-breadcrumb">
            {this.getCrumb()}
          </Col>
        </Row>
        <div className="component-content community_recommend_list">
          <Row style={{ marginBottom: 16 }}>
            <Col span={15}>
              <DatePicker.RangePicker
                style={{ width: 210, marginLeft: 8 }}
                format="YYYY-MM-DD"
                placeholder={['发布时间', '发布时间']}
                value={filter.begin ? [moment(filter.begin), moment(filter.end)] : []}
                onChange={this.handleRangePickerChange.bind(this, '发布')}
              />
              <DatePicker.RangePicker
                style={{ width: 210, marginLeft: 8 }}
                format="YYYY-MM-DD"
                value={
                  filter.operate_begin
                    ? [moment(filter.operate_begin), moment(filter.operate_end)]
                    : []
                }
                placeholder={['操作时间', '操作时间']}
                onChange={this.handleRangePickerChange.bind(this, '操作')}
              />
              <Select
                value={this.state.typeValue}
                style={{ width: 100, marginLeft: 8 }}
                onChange={(e: any) => {
                  this.setState(
                    {
                      filter: {
                        ...this.state.filter,
                        doc_type: e,
                      },
                      pageInput: { ...this.state.pageInput, doc_type: e },
                      typeValue: e,
                    },
                    () => {
                      this.getData();
                      this.getSortFixedStatus();
                    }
                  );
                  // // setTypeValue(e)
                  // let obj = { ...this.getFilters() }
                }}
              >
                <Select.Option value="">数据类型</Select.Option>
                <Select.Option value="12">图文帖</Select.Option>
                <Select.Option value="10">视频帖</Select.Option>
                <Select.Option value="-1">推荐位</Select.Option>
              </Select>
              <Select
                value={this.state.filter.circle_id}
                style={{ width: 130, marginLeft: 8 }}
                onChange={(v) => this.handleFilterChange('circle_id', v)}
              >
                <Select.Option value="">按圈子筛选</Select.Option>
                <Select.Option value="0">无圈子</Select.Option>
                {circleOptions.map((item: any) => (
                  <Select.Option key={item.id} value={item.id}>
                    {item.name}
                  </Select.Option>
                ))}
              </Select>
            </Col>
            <Col span={9}>
              <div className="title-right-col">
                <Select
                  value={pageInput.search_type.toString()}
                  style={{ width: 150, marginRight: 8, marginLeft: 8 }}
                  onChange={this.searchInputChange.bind(this, 'search_type', null)}
                >
                  <Select.Option value="3">标题</Select.Option>
                  <Select.Option value="1">帖子ID</Select.Option>
                  <Select.Option value="5">作者</Select.Option>
                </Select>
                <Input
                  style={{ width: 160, marginRight: 8 }}
                  onKeyPress={this.handleKey}
                  value={pageInput.keyword}
                  placeholder="请输入搜索内容"
                  onChange={this.searchInputChange.bind(this, 'keyword')}
                />
                <Button onClick={this.handleKey.bind(this, { which: 13 })}>
                  <Icon type="search" />
                  搜索
                </Button>
              </div>
            </Col>
          </Row>
          <Table
            func="getReleaseList"
            index="release_list"
            pagination={true}
            onDragEnd={(oldIndex: number, newIndex: number) => this.onDragEnd(oldIndex, newIndex)}
            draggable={showDragSorter && !searchCondition}
            rowKey="id"
            columns={this.columns()}
            filter={this.getFilters()}
            total={this.props.tableList.total + this.state.fixedCount}
            getRecordDraggable={(record: IResReleaseListRecordsData) => !record.fixed_number}
            tableProps={{ scroll: { x: 1500 } }}
          />
          <PreviewMCN {...preview} onClose={this.closePreview} />
        </div>
        <ContentRecommendDrawer
          skey={ContentRecommendDrawerForm.key}
          maxPosition={searchCondition ? 999999999999 : this.state.total + this.state.fixedCount}
          record={ContentRecommendDrawerForm.record}
          visible={ContentRecommendDrawerForm.show}
          supportTypes={getSupportRecommend()}
          onEnd={() => {
            this.getData();
            this.toggleContentRecommendDrawerState(false);
          }}
          onClose={() => {
            this.toggleContentRecommendDrawerState(false);
          }}
          isCommunity
        />
        <EditCircleInfoModal
          idKey="id"
          record={circleInfo.record}
          visible={circleInfo.visible}
          onCancel={() => this.handleEditCircleInfo(circleInfo.record, false)}
          onEnd={this.submitCircleBlockEnd}
        />
        <Modal
          visible={user.visible}
          key={user.key}
          title="用户详情"
          width={800}
          onCancel={() => this.setState({ user: { ...user, visible: false } })}
          onOk={() => this.setState({ user: { ...user, visible: false } })}
        >
          {/*{user.visible && getUserDetail(user.detail)}*/}
          {user.visible && <UserDetail detail={user.detail} />}
        </Modal>
      </>
    );
  }
}

export default withRouter(
  connect<IResReleaseListRecordsData, IResReleaseListAllData>()(ReleaseList)
);
