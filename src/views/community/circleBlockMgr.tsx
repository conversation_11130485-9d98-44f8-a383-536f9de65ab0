import { <PERSON><PERSON>, <PERSON>, Di<PERSON><PERSON>, I<PERSON>, Modal, Row, Table, Timeline, Tooltip, message } from "antd"
import React, { useEffect, useState } from "react"
import { useHistory, useParams } from 'react-router';
import { useDispatch, useStore } from 'react-redux';
import { getCrumb } from "@app/utils/utils"
import { OrderColumn } from '@components/common';
import AddCircleBlockModal from "./component/AddCircleBlockModal";
import { communityApi as api, opApi } from '@app/api';
import { setConfig } from "@app/action/config";
import { PermA, PermButton } from "@app/components/permItems";
import moment from "moment";

export default function BlockMgr(props: any) {
  const dispatch = useDispatch();
  const history = useHistory()
  const { session } = useStore().getState()
  const { id: circle_id, name } = useParams<any>()
  const [list, setList] = useState([])
  const [circleBlockModalVisiable, setCircleBlockModalVisiable] = useState(false)
  const [editRecord, setEditRecord] = useState(null)
  const canSeeContent = session.permissions.indexOf('circle_article:list') > -1
  const [operateLog, setOperateLog] = useState<any>({
    visible: false,
    // key: uuid(),
    logs: null,
  })

  const columns: any = [
    {
      title: '排序',
      key: 'order',
      render: (text: any, record: any, i: number) => {
        const index = i + 1
        return <OrderColumn
          pos={index}
          start={1}
          end={list.length}
          perm="circle_board:update_sort"
          onUp={() => exchangeOrder(record.id, index, -1)}
          onDown={() => exchangeOrder(record.id, index, 1)}
        />
      },
      width: 70,
    },
    {
      title: '序号',
      key: 'id',
      render: (a: any, b: any, i: number) => <span>{i + 1}</span>,
      width: 90,
    },
    {
      title: '版块名称',
      key: 'name',
      dataIndex: 'name',
    },
    {
      title: <div>内容数&nbsp;
        <Tooltip overlayStyle={{ maxWidth: 255 }} title="后台统计的内容数包括所有审核通过的内容，包括沉底的；前台圈子下仅显示正常通过的内容，不显示沉底内容" placement="top">
          <Icon type="question-circle" />
        </Tooltip>
      </div>,
      key: 'article_count',
      dataIndex: 'article_count',
      render: (text: string, record: any) => canSeeContent ? <a onClick={() => history.push(`/view/circleContentMgr/${circle_id}/${record.name}?board_id=${record.id}`)}>{text}</a> : text
    },
    {
      title: '创建人',
      dataIndex: 'created_by',
    },
    {
      title: '最后操作时间',
      dataIndex: 'updated_at',
      render: (text: any, record: any) => <a onClick={() => getOperateLog(record)}>{moment(text).format('YYYY-MM-DD HH:mm:ss')}</a>
    },
    {
      title: '操作',
      key: 'op',
      render: (text: any, record: any) => (
        <span>
          <PermA perm="circle_board:update" onClick={() => handleEditCircleBlock(record)}>编辑</PermA>
          <Divider type="vertical" />
          <PermA perm="circle_board:delete" onClick={() => handleDelete(record)}>删除</PermA>
        </span>
      ),
      width: 120,
    },
  ]

  const handleEditCircleBlock = (record: any) => {
    setEditRecord(record)
    setCircleBlockModalVisiable(true)
  }

  const handleDelete = (record: any) => {
    Modal.confirm({
      title: '删除版块后，该版块下的内容将不属于任何版块，只显示在圈子的全部列表下',
      onOk: () => {
        dispatch(setConfig({ loading: true }))
        api.deleteCircleBoard({ id: record.id })
          .then(() => {
            message.success('操作成功')
            dispatch(setConfig({ loading: false }))
            getData()
          })
          .catch(() => {
            dispatch(setConfig({ loading: false }))
          })
      },
    });
  }

  const exchangeOrder = (id: number, current: number, offset: number) => {
    dispatch(setConfig({ loading: true }));
    api.sortCircleBoard({ id, current, offset })
      .then(() => {
        message.success('操作成功');
        getData();
        dispatch(setConfig({ loading: false }));
      })
      .catch(() => {
        dispatch(setConfig({ loading: false }));
      });
  }

  const getData = () => {
    api.getCircleBoardList({ current: 1, size: 20, circle_id })
      .then(({ data }) => {
        message.success('列表获取成功');
        const { list } = data as any
        setList(list)
      })
      .catch(() => { })
  }

  const submitEnd = () => {
    getData()
    setCircleBlockModalVisiable(false)
  }

  useEffect(() => {
    getData()
  }, [])

  const getOperateLog = (record: any) => {
    opApi.getOperateLog({ target_id: record.id, type: 164 })
      .then((r: any) => {
        setOperateLog({
          visible: true,
          logs: r.data.admin_log_list,
          // key: uuid(),
        })
      })
      .catch();
  }

  return (
    <>
      <Row className="layout-infobar">
        <Col span={12}>
          <Button onClick={() => history.push('/view/circleMgr')}><Icon type="left-circle" />返回圈子管理</Button>
          <PermButton
            perm="circle_board:create"
            style={{ marginLeft: 8 }}
            onClick={() => handleEditCircleBlock(null)}>添加版块</PermButton>
        </Col>
        <Col span={12} className="layout-breadcrumb">
          {getCrumb([...props.breadCrumb, name])}
        </Col>
      </Row>
      <div className="component-content">
        <Table
          columns={columns}
          rowKey="id"
          dataSource={list}
          pagination={false}
        />
        <AddCircleBlockModal
          circleId={circle_id}
          record={editRecord}
          visible={circleBlockModalVisiable}
          onCancel={() => setCircleBlockModalVisiable(false)}
          onEnd={submitEnd} />
      </div>

      {/* 操作日志 */}
      <Modal
        visible={operateLog.visible}
        title="操作日志"
        key={operateLog.key}
        cancelText={null}
        onCancel={() => setOperateLog({ ...operateLog, visible: false })}
        onOk={() => setOperateLog({ ...operateLog, visible: false })}
      >
        <div>
          <Timeline>
            {operateLog.logs?.map((v: any, i: number) => [
              <Timeline.Item className="timeline-dot-big" data-show={v.date} key={`time${i}`}>
                &nbsp;
              </Timeline.Item>,
              v.log_list?.map((action: any, index: number) => (
                <Timeline.Item
                  className="timeline-dot"
                  data-show={moment(action.created_at).format('HH:mm:ss')}
                  key={`time${i}-action${index}`}
                >
                  {action.admin_name}&emsp;{action.remark}
                  {/* {action.user}&emsp;&emsp;{action.action}&emsp;&emsp; */}
                </Timeline.Item>
              )),
            ])}
          </Timeline>
        </div>
      </Modal>

    </>
  )
}