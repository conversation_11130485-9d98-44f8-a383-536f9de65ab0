import {
  getCrumb,
  jumpToEdit,
  requirePerm4Function,
  resolveNewsType,
  searchToObject,
  showDataSetModal,
  showIDDetailModal,
  showReadCountDetailModal,
  UserDetail,
} from '@app/utils/utils';
import {
  Button,
  Col,
  DatePicker,
  Dropdown,
  Icon,
  Input,
  Menu,
  Modal,
  Radio,
  Row,
  Select,
  Tag,
  Timeline,
  Tooltip,
  message,
} from 'antd';
import { Table, PreviewMCN } from '@components/common';
import { useDispatch, useSelector, useStore } from 'react-redux';
import { useHistory, useParams } from 'react-router';
import { getTableList } from '@app/action/tableList';
import ReactClipboard from 'react-clipboardjs-copy';
import React, { useEffect, useState } from 'react';
import _, { values } from 'lodash';
import {
  communityApi as api,
  communityApi,
  listApi,
  opApi,
  releaseListApi,
  userApi,
} from '@app/api';
import { setConfig } from '@app/action/config';
import { PermA, PermButton } from '@app/components/permItems';
import moment from 'moment';
import FixedContentSortDrawer from './component/FixedContentSortDrawer';
import CircleContentTopContentDrawer from './component/CircleContentTopContentDrawer';
import EditCircleInfoModal from './component/EditCircleInfoModal';
import EditCircleContentLevelModal from './component/EditCircleContentLevelModal';
import showImagePreviewModal from '@app/components/common/imagePreviewModal';
import ImagePreviewColumn from '@app/components/common/imagePreviewColumn';
import EditFixedSortModal from './component/editFixedSortModal';
import GetVisaModal from '@app/components/business/GetVisaModal';
import AuditEditDrawer from './auditEditDrawer';

export default function CircleContentMgr(props: any) {
  const dispatch = useDispatch();
  const history = useHistory();
  const { id: circle_id, name } = useParams<any>();
  const { selected = '', board_id = '' } = searchToObject();
  const { session } = useStore().getState();
  const {
    current,
    size,
    allData: { real_total = 999999 },
  } = useSelector((state: any) => state.tableList);
  const [filter, setFilter] = useState({
    content_level: '',
    selected: selected ? parseInt(selected) : '',
    board_id: board_id ? parseInt(board_id) : '',
    doc_type: '',
    publish_time_start: '',
    publish_time_end: '',
    topic_id: '',
    retrieved: '',
    circle_id: circle_id,
  });
  const [searchState, setSearchState] = useState({
    search_type: 1,
    keyword: '',
    // chaoID: null,
  });

  const [getVisa, setGetVisa] = useState({
    visible: false,
    record: null,
  });
  const [topicLoading, setTopicLoading] = useState(false);
  const [topicList, setTopicList] = useState([]);
  // const [authorOptions, setAuthorOptions] = useState([]);
  const [blockOptions, setBlockOptions] = useState<any>(null);
  const [levelModalVisiable, setLevelModalVisiable] = useState(false);
  const [circleInfoModalVisiable, setCircleInfoModalVisiable] = useState(false);
  const [editRecord, setEditRecord] = useState(null);
  const [preview, setPreview] = useState({
    visible: false,
    skey: Date.now(),
    data: {},
  });
  const [user, setUser] = useState({
    key: Date.now(),
    visible: false,
    detail: {},
  });

  const [operateLog, setOperateLog] = useState<any>({
    visible: false,
    // key: uuid(),
    logs: null,
  });
  const [accountOptions, setAccountOptions] = useState([]);

  const [topContentDrawerVisiable, setTopContentDrawerVisiable] = useState(false);
  const [fixedContentSortDrawerVisiable, setFixedContentSortDrawerVisiable] = useState(false);
  const canSeeAccount = session.permissions.indexOf('account:detail') > -1;
  const [fixedSort, setFixedSort] = useState<any>({
    visible: false,
    record: null,
  });
  const handleEditContentLevel = (record: any) => {
    setEditRecord(record);
    setLevelModalVisiable(true);
  };

  const handleEditArticle = (record: any) => {
    dispatch(setConfig({ loading: true }));
    communityApi
      .getArticleAuditDetail({ id: record.article_id })
      .then((res: any) => {
        dispatch(setConfig({ loading: false }));
        const article = res.data.article;
        setEditDrawer({
          key: Date.now(),
          visible: true,
          detail: article,
        });
      })
      .catch(() => {
        dispatch(setConfig({ loading: false }));
      });
  };

  const handleEditCircleInfo = (record: any) => {
    setEditRecord(record);
    setCircleInfoModalVisiable(true);
  };

  const handleEditFixedSort = (record: any) => {
    dispatch(setConfig({ loading: true }));
    releaseListApi
      .getCircleArticleFixDetail({ article_id: record.article_id, circle_id: record.circle_id })
      .then((res: any) => {
        dispatch(setConfig({ loading: false }));
        setFixedSort({
          visible: true,
          record,
          skey: Date.now(),
          board_fix_number: res.data?.board_fix_number || 0,
          fix_number: res.data?.fix_number || 0,
        });
      })
      .catch(() => {
        dispatch(setConfig({ loading: false }));
      });
  };

  const toggleWinnowStatus = (record: any) => {
    const { article_id, selected } = record;
    Modal.confirm({
      title: record.selected ? '确定将该内容取消精选？' : '确定将该内容设为精选？',
      onOk: () => {
        dispatch(setConfig({ loading: true }));
        api
          .setCircleContentSelected({ article_id, selected: selected ? 0 : 1 })
          .then(() => {
            dispatch(setConfig({ loading: false }));
            getData();
          })
          .catch(() => {
            dispatch(setConfig({ loading: false }));
          });
      },
    });
  };

  const handleAccountSearch = _.debounce((val: any) => {
    if (!val?.trim()) {
      setAccountOptions([]);
      return;
    }

    communityApi
      .recommendAccount_Search({ keyword: val })
      .then(({ data }) => {
        const { list } = data as any;
        setAccountOptions(list || []);
      })
      .catch(() => {});
  }, 500);

  const handleCancelPub = (record: any) => {
    Modal.confirm({
      title: (
        <p>
          确认取消签发《
          {record.title?.length > 30 ? record.title.slice(0, 30) + '...' : record.title}》？
        </p>
      ),
      content: <p>该帖子将变为待审核状态，仅作者本人可见</p>,
      onOk: () => {
        dispatch(setConfig({ loading: true }));
        releaseListApi
          .chaokeRevokeNews({ id: record.article_id })
          .then(() => {
            dispatch(setConfig({ loading: false }));
            getData();
          })
          .catch(() => {
            dispatch(setConfig({ loading: false }));
          });
      },
    });
  };

  const handlePreview = (record: any) => {
    setPreview({
      visible: true,
      skey: Date.now(),
      data: record,
    });
  };

  const closePreview = () => {
    setPreview({
      ...preview,
      visible: false,
    });
  };

  const showUserDetail = (record: any) => {
    dispatch(setConfig({ loading: true }));
    userApi
      .getUserDetail({ accountId: record.account_id })
      .then((r: any) => {
        setUser({
          visible: true,
          key: Date.now(),
          detail: r.data.account,
        });
        dispatch(setConfig({ loading: false }));
      })
      .catch(() => dispatch(setConfig({ loading: false })));
  };

  const toCommentSystem = (id: number) => {
    const w = window.open();
    releaseListApi
      .getCommentSystemUrl({ article_id: id })
      .then((r: any) => {
        if (w) {
          w.location = r.data.url;
        } else {
          message.error('浏览器可能拦截了新页面');
        }
      })
      .catch(() => {
        w && w.close();
      });
  };

  const getDropDown = (record: any) => {
    const { channel_id } = record;
    const menu = (
      <Menu>
        {requirePerm4Function(
          session,
          `ugc_article:${channel_id}:set_content_level`
        )(<Menu.Item onClick={() => handleEditContentLevel(record)}>修改内容等级</Menu.Item>)}
        {requirePerm4Function(
          session,
          'circle_article:update_circle'
        )(<Menu.Item onClick={() => handleEditCircleInfo(record)}>修改圈子信息</Menu.Item>)}
        {record.down == 0 &&
          requirePerm4Function(session, ['circle_article_fix:edit', 'circle_article_fix:list'])(
            <Menu.Item onClick={() => handleEditFixedSort(record)}>设置固定排序</Menu.Item>
          )}
        {requirePerm4Function(
          session,
          'channel_article:ugc:edit_url'
        )(<Menu.Item onClick={() => handleEditArticle(record)}>编辑作品</Menu.Item>)}
        {requirePerm4Function(
          session,
          'circle_article:set_selected'
        )(
          <Menu.Item onClick={() => toggleWinnowStatus(record)}>
            {record.selected ? '取消精选' : '设为精选'}
          </Menu.Item>
        )}
        {requirePerm4Function(
          session,
          `comment:view:${channel_id}`
        )(<Menu.Item onClick={() => toCommentSystem(record.uuid)}>评论运营</Menu.Item>)}
        {requirePerm4Function(
          session,
          `channel_article:${channel_id}:cancel_release`
        )(<Menu.Item onClick={() => handleCancelPub(record)}>取消签发</Menu.Item>)}
        <Menu.Item>
          {record.url ? (
            <ReactClipboard
              action="copy"
              text={record.url}
              onSuccess={() => message.success('链接已复制')}
              onError={() => message.error('复制失败')}
            >
              <a>复制链接</a>
            </ReactClipboard>
          ) : (
            '复制链接'
          )}
        </Menu.Item>
      </Menu>
    );
    return (
      <Dropdown overlay={menu}>
        <a className="ant-dropdown-link">
          操作 <Icon type="down" />
        </a>
      </Dropdown>
    );
  };

  const getOperateLog = (record: any) => {
    dispatch(setConfig({ loading: true }));
    opApi
      .getOperateLog({ target_id: record.article_id, type: 166 })
      .then((r: any) => {
        if (r.data?.admin_log_list?.length > 0) {
          setOperateLog({
            visible: true,
            logs: r.data.admin_log_list,
            // key: uuid(),
            record: record,
          });
        } else {
          message.info('暂无日志');
        }
        dispatch(setConfig({ loading: false }));
      })
      .catch(() => {
        dispatch(setConfig({ loading: false }));
      });
  };
  const getSeq = (i: number) => (current - 1) * size + i + 1;

  const isSearch =
    filter.content_level !== '' ||
    filter.selected !== '' ||
    !!filter.doc_type ||
    !!filter.publish_time_start ||
    !!filter.publish_time_end ||
    !!filter.keyword ||
    !!filter.topic_id;

  const columns: any = [
    {
      title: '序号',
      dataIndex: 'seq',
      width: 90,
      render: (a: any, record: any, index: number) => {
        const fixed_position = !!filter.board_id ? record.board_fixed_number : record.fixed_number;
        const realIndex = getSeq(index);
        return (
          <>
            <span style={{ marginRight: 8 }}>{realIndex}</span>
            {fixed_position > 0 && !isSearch && realIndex <= 100 && (
              <PermA
                perm={`circle_article_fix:edit`}
                // disabled={record.down}
                onClick={() => {
                  fixPos(record, realIndex, false);
                }}
              >
                <Icon type="pushpin" theme="filled" />
              </PermA>
            )}
            {!record.down && fixed_position == 0 && !isSearch && realIndex <= 100 && (
              <PermA
                perm={`circle_article_fix:edit`}
                disabled={record.down}
                onClick={() => {
                  fixPos(record, realIndex, true);
                }}
              >
                <Icon type="pushpin" />
              </PermA>
            )}
          </>
        );
      },
    },
    {
      title: 'ID',
      dataIndex: 'article_id',
      width: 80,
      render: (text: any, record: any) => (
        <a
          onClick={() =>
            showIDDetailModal({
              id: record.article_id,
              uuid: record.uuid,
              metadata_id: record.metadata_id,
            })
          }
        >
          {text}
        </a>
      ),
    },
    // {
    //   title: '创作者平台ID',
    //   dataIndex: 'metadata_id',
    //   width: 115,
    // },
    {
      title: '标题',
      dataIndex: 'title',
      render(text: string, record: any) {
        const tags = [];
        if (record.selected) {
          tags.push('精选');
        }
        const level = ['', '沉底', '推荐'][parseInt(record.content_level) || 0];
        if (!!level) {
          tags.push(level);
        }
        const colorMap: any = {
          精选: '#f1797a',
          推荐: '#e99d42',
          沉底: 'gray',
        };
        const fixed_position = !!filter.board_id ? record.board_fixed_number : record.fixed_number;

        let titleColor = '';
        if (record.content_level == 1) {
          titleColor = 'gray';
        } else if (fixed_position > 0) {
          titleColor = 'red';
        }
        return (
          <a onClick={() => handlePreview(record)}>
            {tags.length > 0 &&
              tags.map((tag) => {
                return <Tag color={colorMap[tag]}>{tag}</Tag>;
              })}
            <span style={{ color: titleColor }}>
              {(text?.length > 30 ? text.slice(0, 30) + '...' : text) || '-'}
              {fixed_position > 0 ? '(固定)' : ''}
            </span>
          </a>
        );
      },
    },
    {
      title: '封面图/配图',
      key: 'pic_array',
      dataIndex: 'pic_array',
      width: 150,
      align: 'center',
      render: (text: any, record: any) => (
        <div style={{ height: 60, textAlign: 'center' }}>
          <ImagePreviewColumn text={text?.[0]} imgs={record.pic_array}></ImagePreviewColumn>
          {/* <img src={text?.[0]} className='list-pic' onMouseEnter={() => showImagePreviewModal({ images: record.pic_array })}></img> */}
        </div>
      ),
    },
    {
      title: '内容类型',
      dataIndex: 'doc_type',
      render: (doc_type: any) => resolveNewsType(doc_type, 1),
      width: 90,
    },
    // {
    //   title: '内容等级',
    //   dataIndex: 'content_level',
    //   render: (content_level: number) => ['通过', '沉底', '推荐'][content_level],
    //   width: 90,
    // },
    // {
    //   title: '是否精选',
    //   dataIndex: 'selected',
    //   render: (selected: boolean) => (selected ? '精选' : ''),
    //   width: 90,
    // },
    ...(!filter.board_id
      ? [
          {
            title: '所属版块',
            dataIndex: 'board_name',
            width: 90,
          },
        ]
      : []),
    {
      title: '作者',
      dataIndex: 'author',
      width: 140,
      render(text: string, record: any) {
        return canSeeAccount ? <a onClick={() => showUserDetail(record)}>{text}</a> : text;
      },
    },
    // {
    //   title: '用户ID',
    //   key: 'chao_id',
    //   dataIndex: 'chao_id',
    //   width: 110,
    // },
    {
      title: '阅读数',
      key: 'fake_count',
      dataIndex: 'fake_count',
      width: 110,
    },
    {
      title: '点赞数',
      width: 90,
      dataIndex: 'like_count',
      // render: (text: any, record: any) => <a onClick={() => showDataSetModal(record, 0)}>{text || 0}</a>
      render: (text: any, record: any) => text || 0,
    },
    {
      title: '评论数',
      width: 90,
      dataIndex: 'comment_count',
      render: (text: any, record: any) => text || 0,
    },
    {
      title: '发布时间',
      dataIndex: 'published_at',
      width: 105,
      render: (text: any, record: any) => (
        <a style={{ width: 80 }} onClick={() => getOperateLog(record)}>
          {moment(text).format('YYYY-MM-DD HH:mm:ss')}
        </a>
      ),
    },
    {
      title: '操作',
      key: 'op',
      align: 'center',
      width: 90,
      fixed: 'right',
      render: (text: any, record: any) => getDropDown(record),
    },
  ];

  const fixPos = (record: any, pos: any, fix: Boolean) => {
    dispatch(setConfig({ loading: true }));
    const params: any = {
      circle_id: record.circle_id,
      article_id: record.article_id,
      board_id: record.board_id,
    };
    if (record.down == 1) {
      params.fix_number = 0;
      params.board_fix_number = 0;
    } else {
      if (!!filter.board_id) {
        params.fix_number = record.fixed_number || 0;
        params.board_fix_number = fix ? pos : 0;
      } else {
        params.board_fix_number = record.board_fixed_number || 0;
        params.fix_number = fix ? pos : 0;
      }
    }
    releaseListApi
      .editCircleArticleFixDetail(params)
      .then(() => {
        message.success('操作成功');
        dispatch(setConfig({ loading: false }));
        getData();
      })
      .catch(() => {
        dispatch(setConfig({ loading: false }));
      });
  };

  const changeFilter = (key: string, val: any, goToFirstPage = false) => {
    const newFilter = {
      ...filter,
      [key]: val,
    };
    setFilter(newFilter);
    getData(goToFirstPage, newFilter);
  };

  const timeChange = (dates: any) => {
    const newFilter = {
      ...filter,
      publish_time_start: dates.length === 0 ? '' : dates[0].format('YYYY-MM-DD'),
      publish_time_end: dates.length === 0 ? '' : dates[1].format('YYYY-MM-DD'),
    };
    setFilter(newFilter);
    getData(true, newFilter);
  };

  const handleKey = (e: { which: number }) => {
    if (e.which === 13) {
      const searchParams = {
        ...searchState,
      };

      const newFilter = {
        ...filter,
      };

      const keyword = searchParams.search_type == 4 ? searchParams.chaoID : searchParams.keyword;
      if (keyword) {
        newFilter.keyword = keyword;
        newFilter.search_type = searchParams.search_type;
      } else {
        delete newFilter.keyword;
        delete newFilter.search_type;
      }

      setFilter(newFilter);
      getData(true, newFilter);
    }
  };

  // const handleAuthorChange = (val: any) => {
  //   if (!val) {
  //     // 清空了
  //     handleSearchTypeChange(searchState.search_type);
  //   }
  // };

  // const handleAuthorSearch = _.debounce((nick_name: any) => {
  //   if (!nick_name) {
  //     setAuthorOptions([]);
  //     return;
  //   }
  //   opApi
  //     .getUserList({ nick_name, status: 2, current: 1, size: 20, cert_types: '0,1,2' })
  //     .then(({ data }) => {
  //       const {
  //         account_list: { records = [] },
  //       } = data as any;
  //       setAuthorOptions(records);
  //     })
  //     .catch(() => { });
  // }, 500);

  // const handleAuthorSelect = (_: any, option: any) => {
  //   setSearchState({ ...searchState, keyword: option.key });
  // };

  const handleSearchTypeChange = (search_type: any) => {
    setSearchState({ ...searchState, search_type });
    // setAuthorOptions([]);
  };

  const submitContentLevelEnd = (goToFirstPage: boolean) => {
    getData(goToFirstPage);
    setLevelModalVisiable(false);
  };

  const submitCircleBlockEnd = (goToFirstPage: boolean) => {
    getData(goToFirstPage);
    setCircleInfoModalVisiable(false);
  };

  const submitTopContentEnd = (goToFirstPage: boolean) => {
    getData(goToFirstPage);
    setTopContentDrawerVisiable(false);
  };

  const submitFixedSortEnd = (goToFirstPage: boolean) => {
    getData(goToFirstPage);
    setFixedContentSortDrawerVisiable(false);
  };

  const getData = (goToFirstPage = false, newFilter = filter) => {
    const cur = goToFirstPage ? 1 : current;
    dispatch(
      getTableList('getCircleArticleList', 'list', { current: cur, size, circle_id, ...newFilter })
    );
  };

  const getBlockList = () => {
    api
      .getCircleBoardList({ circle_id, current: 1, size: 20 })
      .then(({ data }) => {
        const { list = [] } = data as any;
        setBlockOptions(list.map((item: any) => ({ value: item.id, label: item.name })));
      })
      .catch(() => {});
  };

  useEffect(() => {
    getData(true);
    getBlockList();
  }, []);

  const handleSearch = _.debounce(async (val: string) => {
    if (!val) {
      setTopicList([]);
      return;
    }
    setTopicLoading(true);
    try {
      opApi.getTopicNameList({ keyword: val }).then((res: any) => {
        let records: any = [...res.data.list.records];
        setTopicList(records);
      });
    } catch (error) {
      console.error('Error fetching data:', error);
    }
    setTopicLoading(false);
  }, 500);

  const [editDrawer, setEditDrawer] = useState<any>({
    key: '',
    visible: false,
    detail: null,
  });

  const exportData = () => {
    Modal.confirm({
      title: '单次最多可导出2000行数据',
      content: '（如果当前列表数量超出上限，仅导出前2000行）',
      onOk: () => {
        dispatch(setConfig({ loading: true }));
        api
          .exportCircleArticleList({
            ...filter,
            export_flag: 1,
          })
          .then((res: any) => {
            console.log('res', res);
            const a = document.createElement('a');
            a.href = window.URL.createObjectURL(res.data);
            a.download = `读嘉-${name}-内容列表${moment().format('YYYYMMDD')}.xlsx`;
            a.click();
            dispatch(setConfig({ loading: false }));
          })
          .catch(() => {
            dispatch(setConfig({ loading: false }));
          });
      },
    });
  };

  return (
    <>
      <Row className="layout-infobar">
        <Col span={12}>
          <Button onClick={() => history.goBack()}>
            <Icon type="left-circle" />
            返回
          </Button>
          {/* <PermButton
            perm="circle_article_top:list"
            style={{ marginLeft: 8 }}
            onClick={() => setTopContentDrawerVisiable(true)}
          >
            设置置顶内容
          </PermButton> */}
          {/* <>
            <PermButton
              perm="circle_article_fix:list"
              style={{ marginLeft: 8 }}
              onClick={() => setFixedContentSortDrawerVisiable(true)}
            >
              固定内容排序
            </PermButton>
            &nbsp;
            <Tooltip
              title="最多可设置10条固定内容，并指定内容在列表中的显示位置。同时在全部内容的最新/热门排序下生效。"
              placement="top"
            >
              <Icon type="question-circle" />
            </Tooltip>
          </> */}
          <>
            <PermButton
              perm="common_recommend:list:1"
              style={{ marginLeft: 8 }}
              onClick={() => history.push(`/view/circlePictureMgr/${circle_id}/${name}`)}
            >
              图片推荐位
            </PermButton>
            {/* &nbsp;
            <Tooltip
              overlayStyle={{ maxWidth: 516 }}
              title={<img src="/assets/circle_pic_recommend.png" width={500} height={246.057} />}
              placement="top"
            >
              <Icon type="question-circle" />
            </Tooltip> */}
          </>
          {/* <PermButton perm="" style={{ marginLeft: 8 }} onClick={() => exportData()}>
            导出数据
          </PermButton> */}
        </Col>
        <Col span={12} className="layout-breadcrumb">
          {getCrumb([...props.breadCrumb, name])}
        </Col>
      </Row>
      <div className="component-content">
        {blockOptions?.length > 0 && (
          <Row style={{ marginBottom: 16 }}>
            <Radio.Group
              value={filter.board_id}
              buttonStyle="solid"
              onChange={(e) => changeFilter('board_id', e.target.value, true)}
            >
              <Radio.Button value="">全部</Radio.Button>
              {/* <Radio.Button value={0}>无版块</Radio.Button> */}
              {blockOptions?.map((item: any) => (
                <Radio.Button key={item.value} value={item.value}>
                  {item.label}
                </Radio.Button>
              ))}
            </Radio.Group>
          </Row>
        )}

        <Row style={{ marginBottom: 16 }}>
          <Col span={16}>
            <Select
              style={{ width: 94 }}
              value={filter.selected?.toString()}
              onChange={(val) => changeFilter('selected', val)}
            >
              <Select.Option value="">是否精选</Select.Option>
              <Select.Option value={'1'}>精选</Select.Option>
              <Select.Option value={'0'}>非精选</Select.Option>
            </Select>
            <Select
              style={{ width: 94, marginLeft: 8 }}
              value={filter.doc_type}
              onChange={(val) => changeFilter('doc_type', val)}
            >
              <Select.Option value="">内容类型</Select.Option>
              <Select.Option value={10}>视频帖</Select.Option>
              <Select.Option value={12}>图文帖</Select.Option>
            </Select>
            <Select
              style={{ width: 94, marginLeft: 8 }}
              value={filter.content_level}
              onChange={(val) => changeFilter('content_level', val)}
            >
              <Select.Option value="">内容等级</Select.Option>
              <Select.Option value={2}>推荐</Select.Option>
              <Select.Option value={0}>通过</Select.Option>
              {/* <Select.Option value={3}>圈外沉底</Select.Option> */}
              <Select.Option value={1}>沉底</Select.Option>
            </Select>
            {/* {blockOptions && (
              <Select
                style={{ width: 94, marginLeft: 8 }}
                value={filter.board_id}
                onChange={(val) => changeFilter('board_id', val)}
              >
                <Select.Option value="">所属版块</Select.Option>
                <Select.Option value={0}>无版块</Select.Option>
                {blockOptions.map((item: any) => (
                  <Select.Option key={item.value} value={item.value}>
                    {item.label}
                  </Select.Option>
                ))}
              </Select>
            )} */}
            <DatePicker.RangePicker
              style={{ width: 210, marginLeft: 8 }}
              format="YYYY-MM-DD"
              onChange={timeChange}
            />
            <Select
              style={{ width: 150, marginLeft: 8 }}
              showSearch
              allowClear
              optionFilterProp="children"
              loading={topicLoading}
              placeholder="按话题搜索"
              onSearch={handleSearch}
              notFoundContent={null}
              filterOption={false}
              onChange={(value: string) => {
                if (!value) {
                  setTopicList([]);
                }

                const newFilter = {
                  ...filter,
                  topic_id: value || '',
                };
                setFilter(newFilter);
                getData(true, newFilter);
              }}
              value={filter.topic_id || undefined}
              // ref={ref} // 将 ref 属性传递给 Select 组件
            >
              {topicList.map((item: any) => (
                <Select.Option key={item.id} value={item.id}>
                  {item.name}
                </Select.Option>
              ))}
            </Select>
          </Col>
          <Col span={8} style={{ textAlign: 'right' }}>
            <Select
              value={searchState.search_type}
              style={{ width: 94, marginRight: 8 }}
              onChange={handleSearchTypeChange}
            >
              <Select.Option value={1}>内容标题</Select.Option>
              {/* <Select.Option value={2}>作者</Select.Option>
              <Select.Option value={3}>ID</Select.Option>
              <Select.Option value={4}>用户ID</Select.Option> */}
              <Select.Option value={4}>作者</Select.Option>
            </Select>

            {searchState.search_type == 4 ? (
              <Select
                style={{ width: 160 }}
                placeholder="请输入昵称或用户ID"
                onSearch={handleAccountSearch}
                showSearch
                allowClear
                disabled={!!props.formContent}
                filterOption={false}
                value={searchState.chaoID}
                onChange={(val: any) => setSearchState({ ...searchState, chaoID: val })}
              >
                {accountOptions.map((item: any) => (
                  <Select.Option
                    style={{
                      whiteSpace: 'pre-wrap',
                    }}
                    key={item.chao_id}
                    value={item.chao_id}
                  >
                    {item.nick_name}|用户ID：{item.chao_id}
                  </Select.Option>
                ))}
              </Select>
            ) : (
              <Input
                value={searchState.keyword}
                style={{ width: 140 }}
                onChange={(e: any) =>
                  setSearchState({ ...searchState, keyword: e.target.value?.trim() })
                }
                onKeyPress={handleKey}
                placeholder="输入搜索内容"
              />
            )}
            <Button style={{ marginLeft: 8 }} onClick={() => handleKey({ which: 13 })}>
              <Icon type="search" /> 搜索
            </Button>
          </Col>
        </Row>
        <Table
          func="getCircleArticleList"
          index="list"
          filter={{ ...filter, circle_id }}
          pagination={true}
          rowKey="id"
          columns={columns}
          tableProps={{ scroll: { x: 1500 } }}
        />
        <EditCircleContentLevelModal
          record={editRecord}
          visible={levelModalVisiable}
          onCancel={() => setLevelModalVisiable(false)}
          onEnd={submitContentLevelEnd}
        />
        <EditCircleInfoModal
          record={editRecord}
          visible={circleInfoModalVisiable}
          onCancel={() => setCircleInfoModalVisiable(false)}
          onEnd={submitCircleBlockEnd}
        />
        <PreviewMCN {...preview} onClose={closePreview} />
        <Modal
          visible={user.visible}
          key={user.key}
          title="用户详情"
          width={800}
          onCancel={() => setUser({ ...user, visible: false })}
          onOk={() => setUser({ ...user, visible: false })}
        >
          {/*{getUserDetail(user.detail)}*/}
          {<UserDetail detail={user.detail} />}
        </Modal>
        <CircleContentTopContentDrawer
          circle_id={circle_id}
          visible={topContentDrawerVisiable}
          onClose={() => setTopContentDrawerVisiable(false)}
          onEnd={submitTopContentEnd}
        />
        <FixedContentSortDrawer
          max={real_total}
          circle_id={circle_id}
          visible={fixedContentSortDrawerVisiable}
          onClose={() => setFixedContentSortDrawerVisiable(false)}
          onEnd={submitFixedSortEnd}
        />

        {/* 操作日志 */}
        <Modal
          visible={operateLog.visible}
          title="操作日志"
          key={operateLog.key}
          cancelText={null}
          onCancel={() => setOperateLog({ ...operateLog, visible: false })}
          onOk={() => setOperateLog({ ...operateLog, visible: false })}
        >
          <div>
            <h3 className="line-max" style={{}}>
              ID：{operateLog.record?.article_id}&nbsp;标题：{operateLog.record?.title}
            </h3>
            <Timeline>
              {operateLog.logs?.map((v: any, i: number) => [
                <Timeline.Item className="timeline-dot-big" data-show={v.date} key={`time${i}`}>
                  &nbsp;
                </Timeline.Item>,
                v.log_list?.map((action: any, index: number) => (
                  <Timeline.Item
                    className="timeline-dot"
                    data-show={moment(action.created_at).format('HH:mm:ss')}
                    key={`time${i}-action${index}`}
                  >
                    {action.admin_name}&emsp;{action.remark}
                    {/* {action.user}&emsp;&emsp;{action.action}&emsp;&emsp; */}
                  </Timeline.Item>
                )),
              ])}
            </Timeline>
          </div>
        </Modal>

        <GetVisaModal
          record={getVisa.record}
          visible={getVisa.visible}
          onCancel={() => setGetVisa({ ...getVisa, visible: false })}
        />

        <EditFixedSortModal
          {...fixedSort}
          onCancel={() => setFixedSort({ visible: false })}
          onOk={() => {
            setFixedSort({ visible: false });
            getData();
          }}
        ></EditFixedSortModal>

        <AuditEditDrawer
          {...editDrawer}
          onClose={() => setEditDrawer({ ...editDrawer, visible: false })}
          onEnd={() => {
            setEditDrawer({ ...editDrawer, visible: false });
            getData();
          }}
        />
      </div>
    </>
  );
}
