import { userApi } from '@app/api';
import useXHR from '@app/utils/useXhr';
import { UserDetail } from '@app/utils/utils';
import { Col, Icon, Modal, Row } from 'antd';
import React, { useState } from 'react';
import EditCircleInfoModal from './component/EditCircleInfoModal';
import SensitiveImage from '../operates/SensitiveImage';
import SensitiveImagePreview from '@app/components/common/sensitiveImagePreview';

export default function AuditInfo(props: any) {
  const { run } = useXHR();
  const [userDetailModal, setUserDetailModal] = useState<any>({
    key: '',
    visible: false,
    id: null,
  });

  const [preview, setPreview] = useState<any>({
    visible: false,
    data: null,
  });

  // 显示用户详情
  const showUserDetailModal = (record: any) => {
    run(userApi.getUserDetail, { accountId: record.account_id }, true).then((r: any) => {
      setUserDetailModal({
        key: Date.now(),
        visible: true,
        detail: r.data.account,
      });
    });
  };

  const previewImage = (item: any) => {
    setPreview({
      visible: true,
      data: item,
    });
  };

  return (
    <div className={props.className}>
      <div className="section-title">
        <i></i>
        <span>基础信息</span>
      </div>
      <div>
        <Row>
          <Col span={4}>作者</Col>
          <Col span={20}>
            <a onClick={() => showUserDetailModal(props.detail)}>{props.detail?.author}</a>
          </Col>
        </Row>
        <Row>
          <Col span={4}>圈子</Col>
          <Col span={20}>
            <span>{props.detail?.circle?.name}</span>
            &nbsp;
            {props.detail?.status < 7 && <a onClick={() => props.changeCircle()}>修改</a>}
          </Col>
        </Row>
      </div>

      <div className="section-title" style={{ marginTop: 10 }}>
        <i></i>
        <span>涉敏信息</span>
      </div>

      {!!props.textSensitiveWords.length && (
        <div>
          <div>
            <Icon type="file-text" style={{ marginRight: 10 }} />
            文本涉敏
          </div>
          {props.textSensitiveWords.map((item: any) => {
            return (
              <div className="sensitive-card">
                <div className="sensitive-card-title">{item.label}</div>
                <div className="sensitive-card-content sensitive-card-word-content">
                  {item.words.map((word: any) => {
                    return <span className="sensitive-card-word">{word.word}</span>;
                  })}
                </div>
              </div>
            );
          })}
        </div>
      )}

      {!!props.imgSensitiveWords.length && (
        <div>
          <div>
            <Icon type="file-image" style={{ marginRight: 10 }} />
            图片涉敏
          </div>
          {props.imgSensitiveWords.map((item: any) => {
            return (
              <div className="sensitive-card">
                <div className="sensitive-card-title">{item.type}</div>
                <div className="sensitive-card-content sensitive-card-content-media">
                  {item.items.map((item: any) => {
                    return (
                      <div
                        className="sensitive-card-content-media-item"
                        onClick={() => previewImage(item)}
                      >
                        <SensitiveImage
                          className="sensitive-card-content-media-item-img"
                          src={item.imgUrl}
                          evidence={item.evidence}
                        ></SensitiveImage>
                        <div className="sensitive-card-content-media-item-type">{item.type}</div>
                      </div>
                    );
                  })}
                </div>
              </div>
            );
          })}
        </div>
      )}

      {!!props.videoSensitiveWords.length && (
        <div>
          <div>
            <Icon type="video-camera" style={{ marginRight: 10 }} />
            视频涉敏
          </div>
          {props.videoSensitiveWords.map((item: any) => {
            return (
              <div className="sensitive-card">
                <div className="sensitive-card-title">{item.type}</div>
                <div className="sensitive-card-content sensitive-card-content-media">
                  {item.items.map((item: any) => {
                    return (
                      <div
                        className="sensitive-card-content-media-item"
                        onClick={() => previewImage(item)}
                      >
                        <SensitiveImage
                          className="sensitive-card-content-media-item-img"
                          src={item.imgUrl}
                          evidence={item.evidence}
                        ></SensitiveImage>
                        <div className="sensitive-card-content-media-item-type">{item.type}</div>
                        <div className="sensitive-card-content-media-item-type">{item.time}</div>
                      </div>
                    );
                  })}
                </div>
              </div>
            );
          })}
        </div>
      )}

      <Modal
        visible={userDetailModal.visible}
        key={userDetailModal.key}
        title="用户详情"
        width={800}
        onCancel={() => setUserDetailModal({ ...userDetailModal, visible: false })}
        onOk={() => setUserDetailModal({ ...userDetailModal, visible: false })}
      >
        {/*{user.visible && getUserDetail(user.detail)}*/}
        {userDetailModal.visible && <UserDetail detail={userDetailModal.detail} />}
      </Modal>

      <SensitiveImagePreview
        visible={preview.visible}
        data={preview.data}
        closeByMask={true}
        onClose={() => {
          setPreview({ visible: false, data: null });
        }}
      />
    </div>
  );
}
