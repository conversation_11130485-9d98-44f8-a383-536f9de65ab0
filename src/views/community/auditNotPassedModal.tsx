import NewNewsSearchAndInput from '@app/components/common/newNewsSearchAndInput';
import {
  Button,
  Checkbox,
  Form,
  Icon,
  Input,
  InputNumber,
  Modal,
  Radio,
  Select,
  Spin,
  Tooltip,
  message,
} from 'antd';
import React, { forwardRef, useEffect, useImperativeHandle, useState } from 'react';
import _ from 'lodash';
import { communityApi, opApi } from '@app/api';
import { FileUploader } from '@app/components/common';
import { iMReportTypeMap } from '@app/utils/utils';

const reasons = [
  '内容与话题无关',
  '涉黄信息',
  '涉政/反动/暴恐',
  '违法/有害信息',
  '不实信息',
  '欺诈/恶意营销',
  '抄袭/盗录/侵权',
  '违背公序良俗',
  '涉及危险驾驶',
  '无意义',
  '带商业平台logo',
  '其它'
];

const AuditNotPassedModal = (props: any, ref: any) => {
  const [loading, setLoading] = useState(false);
  const { getFieldDecorator, getFieldValue } = props.form;
  const [accountOptions, setAccountOptions] = useState([]);

  const formLayout = {
    labelCol: { span: 4 },
    wrapperCol: { span: 18 },
  };

  useEffect(() => {
    if (props.visible) {
      // handleAccountSearch('');
    }
  }, [props.visible]);

  const handleAccountSearch = _.debounce((val: any) => {
    if (!val) {
      setAccountOptions([]);
      return;
    }
    // type: 1, current: 1, size: 50, biz_type: props.formContent?.biz_type,
    communityApi
      .recommendAccount_Search({ keyword: val })
      .then((res) => {
        setAccountOptions(res?.data?.list || []);
      })
      .catch(() => {});
  }, 500);

  const handleSubmit = (e: any) => {
    e.preventDefault();
    props.form.validateFieldsAndScroll((err: any, values: any) => {
      if (!err) {
        setLoading(true);
        const params: any = {
          ids: props.ids?.join(','),
          status: props.type == 'delete' ? 8 : 5,
          reason: values.reason,
          remark: values.remark?.trim()?.replaceAll('\n', '') || '',
        };

        communityApi
          .batchOperateAudit(params)
          .then((res: any) => {
            message.success('操作成功');
            setLoading(false);
            props.onOk && props.onOk();
          })
          .catch(() => {
            // message.error('添加失败');
            setLoading(false);
          });
      } else {
        message.error('请检查表单内容');
        setLoading(false);
      }
    });
  };

  return (
    <Modal
      width={500}
      visible={props.visible}
      title={props.type == 'delete' ? '删除' : '审核不通过'}
      key={props.key}
      onCancel={() => {
        props.onCancel && props.onCancel();
      }}
      onOk={handleSubmit}
      maskClosable={false}
      destroyOnClose={true}
    >
      <Spin spinning={false}>
        {props.isBatch && <p>已勾选{props.ids?.length}条数据，确定审核不通过？</p>}
        <Form {...formLayout} onSubmit={handleSubmit}>
          <Form.Item label="原因">
            {getFieldDecorator('reason', {
              rules: [{ required: true, message: '请选择原因' }],
            })(
              <Select placeholder="请选择原因">
                {reasons.map((reason, index) => {
                  return <Select.Option value={reason}>{reason}</Select.Option>;
                })}
              </Select>
            )}
          </Form.Item>

          <Form.Item label="说明">
            {getFieldDecorator('remark', {
              initialValue: '',
              rules: [{ max: 50, message: '最多50字' }],
            })(
              <Input.TextArea
                rows={4}
                placeholder="补充说明详细原因，该信息将会通知作者，最多50字，选填"
              />
            )}
          </Form.Item>
        </Form>
      </Spin>
    </Modal>
  );
};

export default Form.create<any>({ name: 'AuditNotPassedModal' })(
  forwardRef<any, any>(AuditNotPassedModal)
);
