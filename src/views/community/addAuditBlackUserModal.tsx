import NewNewsSearchAndInput from '@app/components/common/newNewsSearchAndInput';
import {
  Button,
  Checkbox,
  Form,
  Icon,
  Input,
  InputNumber,
  Modal,
  Radio,
  Select,
  Spin,
  Tooltip,
  message,
} from 'antd';
import React, { forwardRef, useEffect, useImperativeHandle, useState } from 'react';
import _ from 'lodash';
import { communityApi, opApi } from '@app/api';
import { FileUploader } from '@app/components/common';

const reasonMap: any = {
  '1': '垃圾信息与违规营销',
  '2': '违法违规',
  '3': '色情低俗',
  '4': '破坏平台氛围',
  '5': '破坏或扰乱平台秩序',
};

const AddAuditBlackUserModal = (props: any, ref: any) => {
  const [loading, setLoading] = useState(false);
  const { getFieldDecorator, getFieldValue } = props.form;
  const [accountOptions, setAccountOptions] = useState([]);

  const formLayout = {
    labelCol: { span: 4 },
    wrapperCol: { span: 18 },
  };

  useEffect(() => {
    if (props.visible) {
      handleAccountSearch('');
    }
  }, [props.visible]);

  const handleAccountSearch = _.debounce((val: any) => {
    if (!val) {
      setAccountOptions([]);
      return;
    }
    // type: 1, current: 1, size: 50, biz_type: props.formContent?.biz_type,
    communityApi
      .recommendAccount_Search({ keyword: val })
      .then((res) => {
        setAccountOptions(res?.data?.list || []);
      })
      .catch(() => {});
  }, 500);

  const handleSubmit = (e: any) => {
    e.preventDefault();
    props.form.validateFieldsAndScroll((err: any, values: any) => {
      if (!err) {
        setLoading(true);
        const remark = values.remark?.trim()?.replaceAll('\n', '') || '';
        const params: any = {
          block_days: values.block_days,
          reason: values.reason,
          remark: remark,
        };

        if (props.id) {
          params.account_id = props.id;
        } else {
          params.account_id = values.account_id;
        }

        communityApi
          .addAuditBlackUser(params)
          .then((res: any) => {
            message.success('添加成功');
            setLoading(false);
            props.onOk && props.onOk();
          })
          .catch(() => {
            // message.error('添加失败');
            setLoading(false);
          });
      } else {
        message.error('请检查表单内容');
        setLoading(false);
      }
    });
  };

  return (
    <Modal
      width={500}
      visible={props.visible}
      title={'加入发布黑名单'}
      key={props.key}
      onCancel={() => {
        props.onCancel && props.onCancel();
      }}
      onOk={handleSubmit}
      maskClosable={false}
      destroyOnClose={true}
    >
      <Spin spinning={false}>
        <Form {...formLayout} onSubmit={handleSubmit}>
          {!props.id && (
            <Form.Item label="拉黑账号">
              {getFieldDecorator('account_id', {
                initialValue: undefined,
                rules: [{ required: true, message: '请选择用户' }],
              })(
                <Select
                  // value={reporter}
                  placeholder="输入昵称或用户ID查找"
                  onSearch={handleAccountSearch}
                  showSearch
                  filterOption={false}
                >
                  {accountOptions.map((d: any) => (
                    <Select.Option key={d.id} value={d.id}>
                      {props.optionMap
                        ? props.optionMap(d)
                        : `${
                            ['潮客 - ', '潮鸣号 - ', '潮鸣号 - '][d.cert_type] + d.nick_name
                          } | 用户ID： ${d.chao_id}`}
                    </Select.Option>
                  ))}
                </Select>
              )}
            </Form.Item>
          )}

          <Form.Item label="时长" extra="期间不能发布帖子， 其余操作如评论/点赞等不受影响">
            {getFieldDecorator('block_days', {
              initialValue: '1',
              rules: [{ required: true, message: '请选择添加方式' }],
            })(
              <Radio.Group>
                <Radio value="1">1天</Radio>
                <Radio value="3">3天</Radio>
                <Radio value="7">7天</Radio>
                <Radio value="30">30天</Radio>
                <Radio value="-1">永久</Radio>
              </Radio.Group>
            )}
          </Form.Item>

          <Form.Item label="原因">
            {getFieldDecorator('reason', {
              rules: [{ required: true, message: '请选择原因' }],
            })(
              <Select placeholder="请选择原因">
                {Object.keys(reasonMap).map((key, index) => {
                  return (
                    <Select.Option value={reasonMap[key]}>
                      {reasonMap[key]}
                    </Select.Option>
                  );
                })}
              </Select>
            )}
          </Form.Item>

          <Form.Item label="说明">
            {getFieldDecorator('remark', {
              initialValue: '',
              rules: [{ max: 50, message: '最多50字' }],
            })(
              <Input.TextArea
                rows={4}
                placeholder="补充说明详细原因，该信息将会通知作者，最多50字，选填"
              />
            )}
          </Form.Item>
        </Form>
      </Spin>
    </Modal>
  );
};

export default Form.create<any>({ name: 'AddAuditBlackUserModal' })(
  forwardRef<any, any>(AddAuditBlackUserModal)
);
