import { Button, Popover, Select } from 'antd';
import React, { useState } from 'react';
import _ from 'lodash';
import { communityApi, searchApi } from '@app/api';

export default function TopicSearchPopover(props: any) {
  const [visible, setVisible] = useState(false);
  const [accountOptions, setAccountOptions] = useState([]);

  const handleAccountSearch = _.debounce((val: any) => {
    if (!val) {
      setAccountOptions([]);
      return;
    }
    // type: 1, current: 1, size: 50, biz_type: props.formContent?.biz_type,
    searchApi
      .searchUGCTopic({ name: val, type: 10 })
      .then((res: any) => {
        setAccountOptions(res?.data?.list?.records || []);
      })
      .catch(() => {});
  }, 500);

  return (
    <Popover
      content={
        <div style={{ width: 300 }}>
          <Select
            style={{ width: '100%' }}
            // value={reporter}
            value={undefined}
            placeholder="输入名称搜索话题"
            onSearch={handleAccountSearch}
            showSearch
            filterOption={false}
            onChange={(val: any) => {
              props.onChange(JSON.parse(val));
              setVisible(false);
            }}
          >
            {accountOptions.map((d: any) => (
              <Select.Option key={d.id} value={JSON.stringify(d)}>
                {d.name}
              </Select.Option>
            ))}
          </Select>
        </div>
      }
      title=""
      trigger="click"
      visible={visible}
      onVisibleChange={(visible: boolean) => {
        if (visible) {
          setAccountOptions([]);
        }
        setVisible(visible);
      }}
    >
      <Button style={{ marginRight: 10 }}>#话题</Button>
    </Popover>
  );
}
