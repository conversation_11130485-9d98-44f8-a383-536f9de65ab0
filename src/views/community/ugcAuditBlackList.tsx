import React, { useState, useEffect, useCallback, useMemo, useRef } from 'react';
import { useStore, useDispatch, useSelector } from 'react-redux';
import { useHistory } from 'react-router-dom';
import { Table, OrderColumn, PreviewMCN } from '@components/common';
import { CommonObject } from '@app/types';
import {
  getCrumb,
  objectToQueryString,
  searchToObject,
  setMenuHook,
  UserDetail,
} from '@app/utils/utils';
import {
  Row,
  Col,
  Divider,
  Icon,
  Modal,
  message,
  InputNumber,
  Tooltip,
  Popconfirm,
  DatePicker,
  Select,
  Input,
  Button,
  Dropdown,
  Menu,
  Timeline,
} from 'antd';
import { getTableList } from '@app/action/tableList';
import { opApi as api, communityApi, opApi, userApi } from '@app/api';
import useXHR from '@utils/useXhr';
import { PermA, PermButton, PermMenuItem } from '@components/permItems';
import Radio from 'antd/es/radio';
import { setConfig } from '@app/action/config';
import moment from 'moment';
import { RangePickerValue } from 'antd/es/date-picker/interface';
import _ from 'lodash';
import ImagePreviewColumn from '@app/components/common/imagePreviewColumn';
import AddIMBlackUserModal from '../operates/addIMBlackUserModal';
import AddAuditBlackUserModal from './addAuditBlackUserModal';

export default function UGCAuditBlackList(props: any) {
  const dispatch = useDispatch();
  const store = useStore();
  const history = useHistory();
  const {
    total,
    current,
    size,
    records = [],
    allData,
  } = useSelector((state: any) => state.tableList);
  const { loading, run } = useXHR();

  const [listSelectedKeys, setListSelectedKeys] = useState([]);
  const [operationRecord, setOperationRecord] = useState<any>({
    visible: false,
    key: '',
    logs: [],
  });
  const [userDetail, setUserDetail] = useState<any>({
    visible: false,
    key: '',
    detail: null,
  });

  const getSeq = (i: number) => (current - 1) * size + i + 1;
  const [addIMBlackUserModal, setAddIMBlackUserModal] = useState<any>({
    visible: false,
    key: '',
  });

  const getList = useCallback(
    (overlap: CommonObject = {}) => {
      setListSelectedKeys([]);
      const { current, size = 10 } = store.getState().tableList;
      dispatch(
        getTableList('getArticleAuditBlackList', 'page', {
          current,
          size,
          ...overlap,
        })
      );
    },
    [setListSelectedKeys]
  );

  const deleteRecord = (record: any) => {
    Modal.confirm({
      title: `确认删除“${record.article_title}”？`,
      onOk: () => {
        run(api.deleteRankingArticle, { id: record.id }, true).then(() => {
          message.success('操作成功');
          getList();
        });
      },
    });
  };

  const listSort = (record: any, i: number, position: number = 1) => {
    let data: any = {
      id: record.id,
      sort_flag: i,
    };
    if (i == 2) {
      data.position = position;
    }
    api.rankingSort(data).then((res: any) => {
      message.success('操作成功');
      getList();
    });
  };

  const changeOrder = (record: any, i: any) => {
    let position = i;
    const WAIT_TIME = 1000;
    const positionChange = (v: any) => {
      position = v;
    };

    Modal.confirm({
      title: <p>排序：《{record.article_title}》</p>,
      icon: <Icon type="info-circle" />,
      content: (
        <div>
          <span>请输入位置：</span>
          <InputNumber min={1} max={total} defaultValue={position} onChange={positionChange} />
        </div>
      ),
      onOk: (closeFunc: Function) => {
        if (!position) {
          message.error('请填写位置');
          return;
        }

        dispatch(setConfig({ loading: true }));
        api
          .rankingSort({ id: record.id, sort_flag: 2, position: position })
          .then((res: any) => {
            message.success('操作成功');
            getList();
            dispatch(setConfig({ loading: false }));
            closeFunc();
          })
          .catch(() => {
            dispatch(setConfig({ loading: false }));
          });
      },
    });
  };

  const titleClick = (record: any) => {
    // setPreview({
    //   visible: true,
    //   skey: Date.now(),
    //   data: record
    // })
    if (!!record.url) {
      window.open(record.url, '_blank');
    }
  };

  const removeBlackUser = (record: any) => {
    Modal.confirm({
      title: `确认取消拉黑吗？`,
      onOk: () => {
        run(communityApi.deleteAuditBlackUser, { account_id: record.account_id }, true).then(() => {
          message.success('操作成功');
          getList();
        });
      },
    });
  };

  const showOperationRecord = (record: any) => {
    opApi
      .getOperateLog({ type: 200801, target_id: record.account_id })
      .then((res: any) => {
        setOperationRecord({
          visible: true,
          key: Date.now(),
          logs: res?.data?.admin_log_list || [],
        });
      })
      .catch(() => {});
  };

  const getColumns = () => {
    return [
      {
        title: '账号昵称',
        dataIndex: 'nick_name',
        width: 100,
        render: (text: any, record: any) => {
          return (
            <PermA perm="" onClick={() => showUserDetailModal(record)}>
              {text}
            </PermA>
          );
        },
      },
      {
        title: 'ID',
        dataIndex: 'chao_id',
        width: 100,
      },
      {
        title: '状态',
        dataIndex: 'status_str',
        width: 100,
      },
      {
        title: '最后操作时间',
        dataIndex: 'updated_at',
        width: 100,
        render: (text: any) => {
          return moment(text).format('YYYY-MM-DD HH:mm:ss');
        },
      },
      {
        title: '操作',
        dataIndex: 'op',
        width: 100,
        render: (text: any, record: any) => {
          return (
            <span>
              {record.status == 1 && (
                <>
                  <PermA perm="" onClick={() => removeBlackUser(record)}>
                    取消拉黑
                  </PermA>
                  <Divider type="vertical" />
                </>
              )}
              <PermA perm="" onClick={() => showOperationRecord(record)}>
                查看记录
              </PermA>
            </span>
          );
        },
      },
    ];
  };

  useEffect(() => {
    setMenuHook(dispatch, props);
    getList({ current: 1 });
  }, []);

  const addBlackUser = () => {
    setAddIMBlackUserModal({
      visible: true,
      key: Date.now(),
    });
  };

  // 显示用户详情
  const showUserDetailModal = (record: any) => {
    run(userApi.getUserDetail, { accountId: record.account_id }, true).then((r: any) => {
      setUserDetail({
        key: Date.now(),
        visible: true,
        detail: r.data.account,
      });
    });
  };

  return (
    <>
      <Row className="layout-infobar">
        <Col span={18}>
          <PermButton perm="" onClick={() => history.goBack()}>
            <Icon type="left-circle" /> 返回
          </PermButton>
          <PermButton perm="" style={{ marginLeft: 8 }} onClick={addBlackUser}>
            添加账号
          </PermButton>
        </Col>
        <Col span={6} className="layout-breadcrumb">
          {getCrumb(props.breadCrumb)}
        </Col>
      </Row>
      <div className="component-content">
        <Table
          func={'getArticleAuditBlackList'}
          index="page"
          columns={getColumns()}
          rowKey="id"
          pagination={true}
        />

        <AddAuditBlackUserModal
          {...addIMBlackUserModal}
          onCancel={() =>
            setAddIMBlackUserModal({
              visible: false,
              key: '',
            })
          }
          onOk={() => {
            setAddIMBlackUserModal({
              visible: false,
              key: '',
            });
            getList();
          }}
        ></AddAuditBlackUserModal>

        <Modal
          visible={userDetail.visible}
          key={userDetail.key}
          title="用户详情"
          width={800}
          onCancel={() => setUserDetail({ ...userDetail, visible: false })}
          onOk={() => setUserDetail({ ...userDetail, visible: false })}
        >
          {/*{user.visible && getUserDetail(user.detail)}*/}
          {userDetail.visible && <UserDetail detail={userDetail.detail} />}
        </Modal>

        {/* 操作日志 */}
        <Modal
          visible={operationRecord.visible}
          title="操作日志"
          key={operationRecord.key}
          cancelText={null}
          onCancel={() => setOperationRecord({ ...operationRecord, visible: false })}
          onOk={() => setOperationRecord({ ...operationRecord, visible: false })}
        >
          <div>
            <Timeline>
              {operationRecord.logs?.map((v: any, i: number) => [
                <Timeline.Item className="timeline-dot-big" data-show={v.date} key={`time${i}`}>
                  &nbsp;
                </Timeline.Item>,
                v.log_list?.map((action: any, index: number) => (
                  <Timeline.Item
                    className="timeline-dot"
                    data-show={moment(action.created_at).format('HH:mm:ss')}
                    key={`time${i}-action${index}`}
                  >
                    {action.admin_name}&emsp;{action.remark}
                    {/* {action.user}&emsp;&emsp;{action.action}&emsp;&emsp; */}
                  </Timeline.Item>
                )),
              ])}
            </Timeline>
          </div>
        </Modal>
      </div>
    </>
  );
}
