import React from 'react';
import { Drawer, ImageUploader } from '@components/common';
import { Form, Icon, Input, Switch, Tooltip, message } from 'antd';
import connect from '@utils/connectSession';
import { communityApi as api } from '@app/api';
import { setMLoading } from '@app/utils/utils';
import Radio from 'antd/es/radio';

@connect
@(Form.create({ name: 'AddCircleDrawer' }) as any)
export default class AddCircleDrawer extends React.Component<any, any> {
  constructor(props: any) {
    super(props);

    // console.log(props.record)
    this.state = {
      dataSrc: props.record || {},
    };
    // console.log(this.state)
  }

  // componentDidUpdate(prevProps: Readonly<any>, prevState: Readonly<any>, snapshot?: any): void {
  //   const { record, visible } = this.props
  //   if (!prevProps.visible && visible && record) {
  //     this.setState({ dataSrc: record })
  //   }
  // }

  handleOkClick = () => {
    const {
      form: { validateFields },
      onEnd,
      record,
    } = this.props;
    validateFields((err: any, values: any) => {
      if (!err) {
        setMLoading(this, true);
        const params = { ...values };
        if (record) {
          params.id = record.id;
        }

        params.notify_standard = params.enable_notify_standard
          ? params.notify_standard?.trim()
          : '';

        api
          .editCircle(params)
          .then(() => {
            setMLoading(this, false);
            message.success(record ? '修改成功' : '添加成功');
            onEnd(false);
          })
          .catch(() => {
            setMLoading(this, false);
          });
      }
    });
  };
  handleDescKeydown = (e: any) => {
    if (e.key === 'Enter') {
      e.preventDefault();
    }
  };

  handleContentSpecChange = (e: any) => {
    this.setState({ dataSrc: { ...this.state.dataSrc, enable_notify_standard: e } });
  };

  // componentDidUpdate(prevProps: Readonly<any>, prevState: Readonly<any>, snapshot?: any): void {
  //   const { record, visible, form: { setFieldsValue } } = this.props
  //   if (!prevProps.visible && visible && record) {
  //     setFieldsValue({ type: record.type })
  //   }
  // }

  render() {
    const {
      record,
      visible,
      onClose,
      form: { getFieldDecorator, getFieldsValue, setFieldsValue },
    } = this.props;
    const {
      name = '',
      logo_url = '',
      background_url = '',
      description = '',
      type = 0,
      show_style = 0,
      sort_type = 0,
      style_url = '',
      enable_notify_standard = false,
      notify_standard = '',
      show_comment = true,
    } = this.state.dataSrc;
    const formLayout = {
      labelCol: { span: 3 },
      wrapperCol: { span: 19 },
    };
    return (
      <Drawer
        title={`${record ? '编辑' : '新增'}圈子`}
        onClose={onClose}
        visible={visible}
        onOk={this.handleOkClick}
        skey={'AddCircleDrawer'}
      >
        <Form {...formLayout}>
          <Form.Item label="圈子名称">
            {getFieldDecorator('name', {
              initialValue: name,
              rules: [
                {
                  required: true,
                  message: '请输入圈子名称',
                },
              ],
            })(<Input placeholder="请输入圈子名称，最多6个字" maxLength={6} />)}
          </Form.Item>
          <Form.Item label="圈子头像" extra="支持jpg,jpeg,png,gif图片格式，比例为 1:1">
            {getFieldDecorator('logo_url', {
              initialValue: logo_url,
              rules: [
                {
                  required: true,
                  message: '请上传圈子头像',
                },
              ],
            })(
              <ImageUploader
                ratio={1 / 1}
                accept={['image/jpeg', 'image/png', 'image/jpg', 'image/gif']}
              />
            )}
          </Form.Item>
          <Form.Item label="背景图" extra="支持jpg,jpeg,png图片格式，比例为 25:18">
            {getFieldDecorator('background_url', {
              initialValue: background_url,
            })(<ImageUploader ratio={25 / 18} />)}
          </Form.Item>
          <Form.Item label="圈子简介">
            {getFieldDecorator('description', {
              initialValue: description,
              rules: [
                {
                  required: true,
                  message: '请输入圈子简介',
                },
              ],
            })(
              <Input.TextArea
                placeholder="请输入圈子简介，最多30字"
                rows={3}
                maxLength={30}
                onKeyDown={this.handleDescKeydown}
              />
            )}
          </Form.Item>
          {/* <Form.Item label="圈子类型">
          {getFieldDecorator('type', {
            initialValue: type,
            rules: [{
              required: true,
              message: '请选择圈子类型',
            }],
          })(
            <Radio.Group onChange={() => setFieldsValue({ show_style: 0 })} disabled={!!record}>
              <Radio value={0}>标准</Radio>
              <Radio value={1}>记者帮</Radio>
            </Radio.Group>
          )}
        </Form.Item> */}
          <Form.Item label="展示样式">
            {getFieldDecorator('show_style', {
              initialValue: show_style,
              rules: [
                {
                  required: true,
                  message: '请选择展示样式',
                },
              ],
            })(
              <Radio.Group>
                <Radio value={0}>单列（适合展示文字/讨论）</Radio>
                <Radio value={1}>双列（适合展示图片）</Radio>
              </Radio.Group>
            )}
          </Form.Item>
          {getFieldsValue().show_style == 0 && (
            <Form.Item label="外显跟帖">
              {getFieldDecorator('show_comment', {
                initialValue: show_comment,
                valuePropName: 'checked',
                rules: [
                  {
                    required: true,
                    // message: '请选择默认排序',
                  },
                ],
              })(<Switch></Switch>)}
              &nbsp;
              <Tooltip title="将第一条热评/最新评论显示在圈子内容列表中">
                <Icon type="question-circle" />
              </Tooltip>
            </Form.Item>
          )}
          {/* <Form.Item label="默认排序">
          {getFieldDecorator('sort_type', {
            initialValue: sort_type,
            rules: [{
              required: true,
              message: '请选择默认排序',
            }],
          })(
            <Radio.Group>
              <Radio value={0}>最新</Radio>
              <Radio value={1}>热门</Radio>
            </Radio.Group>
          )}
        </Form.Item> */}
          {/* <Form.Item label="内容卡片样式" extra="支持jpg,jpeg,png图片格式，比例为 375:96">
          {getFieldDecorator('style_url', {
            initialValue: style_url,
          })(<ImageUploader ratio={375 / 96} />)}
        </Form.Item> */}
          {/* <Form.Item label="内容规范">
          {getFieldDecorator('enable_notify_standard', {
            initialValue: enable_notify_standard,
            valuePropName: 'checked',
            // rules: [{
            //   required: true,
            //   message: '请选择默认排序',
            // }],
          })(<div>
            <Switch defaultChecked={enable_notify_standard} onChange={this.handleContentSpecChange}></Switch>&emsp;
            <Tooltip title="打开后，用户首次发布内容到圈子时将出现提示，告知当前圈子可以/不可以发哪些内容">
              <Icon type="question-circle" />
            </Tooltip>
          </div>)}
        </Form.Item> */}
          {/* {enable_notify_standard && (<Form.Item label="规范说明">
          {getFieldDecorator('notify_standard', {
            initialValue: notify_standard,
            rules: [
              {
                required: true,
                message: '请输入规范说明',
                whitespace: true,
              },
            ],
          })(<Input.TextArea placeholder="请说明当前圈子可以发布哪些内容、不可以发布哪些内容，支持换行，最多200字" rows={3} maxLength={200} />)}
        </Form.Item>)} */}
        </Form>
      </Drawer>
    );
  }
}
