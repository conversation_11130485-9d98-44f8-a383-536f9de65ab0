import React from "react";
import { Form, message, Modal, Input } from "antd";
import connect from '@utils/connectSession';
import { communityApi as api } from '@app/api';

@connect
@(Form.create({ name: 'AddCircleBlockModal' }) as any)
export default class AddCircleBlockModal extends React.Component<any, any> {

  state = {
    loading: false,
  }

  handleOkClick = () => {
    const { form: { validateFields }, onEnd, record } = this.props
    validateFields((err: any, values: any) => {
      if (!err) {
        this.setState({ loading: true })
        const params = { ...values, circle_id: this.props.circleId }
        if (record) {
          params.id = record.id
        }
        api.editCircleBoard(params)
          .then(() => {
            this.setState({ loading: false })
            message.success(record ? '修改成功' : '添加成功');
            onEnd(false)
          })
          .catch(() => {
            this.setState({ loading: false })
          })
      }
    })
  }

  render() {
    const { record, visible, onCancel, form: { getFieldDecorator } } = this.props
    const dataSrc = record || {}
    const { name = '' } = dataSrc
    const formLayout = {
      labelCol: { span: 5 },
      wrapperCol: { span: 16 },
    };
    return (
      <Modal
        visible={visible}
        key="AddCircleBlockModal"
        title={`${record ? '编辑' : '添加'}圈子版块`}
        confirmLoading={this.state.loading}
        width="500px"
        maskClosable={false}
        onCancel={onCancel}
        onOk={this.handleOkClick}
        destroyOnClose
      >
        <Form {...formLayout}>
          <Form.Item label="版块名称">
            {getFieldDecorator('name', {
              initialValue: name,
              rules: [{
                required: true,
                message: '请输入版块名称'
              }],
            })(
              <Input maxLength={10} placeholder="请输入版块名称，最多10个字" />
            )}
          </Form.Item>

        </Form>
      </Modal>
    )
  }
}