import React from "react";
import { Drawer, NewTable } from '@components/common';
import { Form, Input, Select, Spin, message, DatePicker, Button, Modal } from "antd";
import connect from '@utils/connectSession';
import { communityApi as api, searchApi } from '@app/api';
import { setMLoading } from "@app/utils/utils";
import Radio from "antd/es/radio";
import _ from "lodash";
import moment, { Moment } from 'moment';

// 获取当前日期
const today = moment();
@connect
@(Form.create({ name: 'CircleContentTopContentDrawer' }) as any)
export default class CircleContentTopContentDrawer extends React.Component<any, any> {

  state = {
    loading: false,
    topContentOptions: [],
    detail: {},
    operationRecord: {
      visible: false,
      logs: []
    }
  }

  getItemTitle = (item: any) => (item.list_title || item.content || '-').slice(0, 30) //item[item.doc_type === 12 ? 'content' : 'list_title']
  handleOkClick = () => {
    const { form: { validateFields }, onEnd, circle_id } = this.props
    validateFields((err: any, values: any) => {
      if (!err) {
        setMLoading(this, true);
        const { type, title, link, content, time } = values
        const params: any = { circle_id, type }
        if (type !== 0) {
          if (time[1] < new Date()) {
            setMLoading(this, false);
            return message.error('置顶结束时间不能小于当前时间');
          }
          if (time.length > 0) {
            params.show_time_start = time.length === 0 ? '' : time[0].format('YYYY-MM-DD HH:mm:ss');
            params.show_time_end = time.length === 0 ? '' : time[1].format('YYYY-MM-DD HH:mm:ss')
          }
        }
        if (type === 1) {
          params.value = content
        } else {
          if (type === 2) {
            params.title = title
            params.value = link
          }
        }
        api.editCircleTopContent(params)
          .then(() => {
            setMLoading(this, false);
            message.success('操作成功');
            onEnd()
          })
          .catch(() => {
            setMLoading(this, false);
          })
      }
    })
  }

  handleArticleSearch = _.debounce((keyword: any, callback?: any) => {
    if (!keyword) {
      this.setState({ topContentOptions: [] })
      return
    }
    searchApi.searchCircleArticle({ circle_id: this.props.circle_id, keyword })
      .then(({ data }) => {
        const { list } = data as any
        this.setState({ topContentOptions: list })
        if (callback) {
          callback(list)
        }
      })
      .catch(() => { })
  }, 500)

  componentDidUpdate(prevProps: Readonly<any>, prevState: Readonly<any>, snapshot?: any): void {
    if (!prevProps.visible && this.props.visible) {
      // 显示请求详情接口
      const { circle_id } = this.props
      this.setState({ loading: true, detail: {}, topContentOptions: [] })
      api.getCircleTopDetail({ circle_id })
        .then(({ data }) => {
          this.setState({ loading: false })
          const { detail = {} } = data as any
          if (detail.type === 1) {
            this.handleArticleSearch(detail.value, (list: any) => {
              const [item] = list
              // detail.content = 
              detail.title = ''
              this.setState({ detail })
            })
          } else if (detail.type === 2) {
            detail.link = detail.value
            this.setState({ detail })
          } else {
            detail.title = ''
            this.setState({ detail: { type: 0 } })
          }
        })
        .catch(() => {
          this.setState({ loading: false })
        })
    }
  }

  disabledDate = (current) => {
    return current && current < today.startOf('day')
  }
  range = (start: number, end: number) => {
    const result = [];
    for (let i = start; i < end; i++) {
      result.push(i);
    }
    return result;
  };

  showOperationRecord = () => {
    api.operationRecord({ type: 165, target_id: this.props.circle_id }).then((res: any) => {
      this.setState({
        operationRecord: {
          visible: true,
          logs: res?.data?.admin_log_list || []
        }
      })
    }).catch(() => {
    })
  }

  getColumns = () => {
    return [
      {
        title: '操作时间',
        dataIndex: 'created_at',
        key: 'created_at',
        render: (text: any, record: any) => {
          return <>
            <div>{moment(text).format('YYYY-MM-DD HH:mm:ss')}</div>
          </>
        },
        width: 170
      },
      {
        title: '操作人',
        dataIndex: 'admin_name',
        key: 'admin_name',
        // width: 100
      },
      {
        title: '操作项',
        dataIndex: 'remark',
        key: 'remark',
        width: 200
      }
    ]
  }

  render() {
    const { visible, onClose, form: { getFieldDecorator, getFieldsValue } } = this.props
    const detail: any = this.state.detail || { type: 0 }
    const { type = detail.type } = getFieldsValue()
    const formLayout = {
      labelCol: { span: 3 },
      wrapperCol: { span: 19 },
    };
    return (
      <Drawer
        // title="设置置顶内容"
        title={
          <div style={{ display: 'flex', alignItems: 'center' }}>
            <div style={{ marginRight: 8 }}>设置置顶内容</div>
            <Button style={{ width: 120, marginRight: 8 }} onClick={() => this.showOperationRecord()}>最近操作记录</Button>
          </div>
        }
        onClose={onClose}
        visible={visible}
        onOk={this.handleOkClick}
        okPerm={'circle_article_top:edit'}
        skey={'CircleContentTopContentDrawer'}
      >
        <Spin
          tip="正在加载..."
          spinning={this.state.loading}>
          <Form {...formLayout}>
            <Form.Item label="置顶类型">
              {getFieldDecorator('type', {
                initialValue: detail.type,
                rules: [
                  {
                    required: true,
                  },
                ],
              })(
                <Radio.Group>
                  <Radio value={0}>无置顶</Radio>
                  <Radio value={1}>置顶内容</Radio>
                  <Radio value={2}>置顶链接</Radio>
                </Radio.Group>
              )}
            </Form.Item>
            {
              type === 1 && <><Form.Item label="置顶内容">
                {getFieldDecorator('content', {
                  initialValue: detail.value,
                  rules: [
                    {
                      required: true,
                      message: '请输入置顶内容',
                    },
                  ],
                })(
                  <Select style={{ width: 400 }}
                    placeholder="请输入ID或标题，只支持置顶本圈内容"
                    onSearch={this.handleArticleSearch}
                    showSearch>
                    {this.state.topContentOptions.map((item: any) =>
                      <Select.Option key={item.id} value={`${item.id}`}>{`${item.id}-${this.getItemTitle(item)}`}</Select.Option>
                    )}
                  </Select>
                )}
              </Form.Item>
              </>
            }
            {
              type === 2 && <Form.Item label="链接标题">
                {getFieldDecorator('title', {
                  initialValue: detail.title,
                  rules: [
                    {
                      required: true,
                      message: '请输入链接标题'
                    },
                  ],
                })(
                  <Input style={{ width: 400 }} placeholder="请输入链接标题，最多30字" maxLength={30} />
                )}
              </Form.Item>
            }
            {
              type === 2 && <Form.Item label="跳转链接">
                {getFieldDecorator('link', {
                  initialValue: detail.link,
                  rules: [
                    {
                      required: true,
                      message: "请输入跳转链接"
                    },
                    {
                      pattern: /^https?:\/\//,
                      message: "请输入正确的链接格式"
                    }
                  ],
                })(
                  <Input style={{ width: 400 }} placeholder="请输入跳转链接，支持端内页面及h5链接" maxLength={200} />
                )}
              </Form.Item>
            }
            {
              type !== 0 && <>
                <Form.Item label="置顶时间">
                  {getFieldDecorator('time', {
                    initialValue: detail.show_time_start ? [moment(detail.show_time_start), moment(detail.show_time_end)] : [],
                    rules: [
                      {
                        required: true,
                        message: '请设置置顶时间',
                      },
                    ],
                  })(
                    <DatePicker.RangePicker
                      format="YYYY-MM-DD HH:mm:ss"
                      showTime={{ format: 'HH:mm:ss' }}
                      disabledDate={this.disabledDate}
                      value={
                        detail.show_time_start ? [moment(detail.show_time_start), moment(detail.show_time_end)] : []
                      }
                    />
                  )}
                </Form.Item></>
            }
          </Form>
        </Spin>

        <Modal
          width={700}
          visible={this.state.operationRecord.visible}
          title="最近操作记录"
          // key={props.skey}
          onCancel={() => {
            this.setState({
              operationRecord: {
                visible: false,
                logs: []
              }
            })
          }}
          footer={null}
          maskClosable={false}
          destroyOnClose={true}
        >
          <NewTable
            rowKey={'id'}
            columns={this.getColumns()}
            pagination={false}
            tableList={{
              records: this.state.operationRecord.logs,
            }}
          >
          </NewTable>
        </Modal>

      </Drawer>
    )
  }
}