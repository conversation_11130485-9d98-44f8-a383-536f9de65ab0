import { Button, Modal, message } from "antd";
import Radio from "antd/es/radio";
import React, { useEffect, useState } from "react";
import { communityApi as api } from "@app/api";

export default function EditCircleContentLevelModal(props: any) {
  const { visible, onCancel, record, onEnd, idKey = 'article_id' } = props
  const [loading, setLoading] = useState(false)
  const [content_level, setContent_level] = useState("2")

  useEffect(() => {
    if (visible) {
      setContent_level(String(record.content_level))
    }
  }, [visible])

  const handleOk = () => {
    setLoading(true)
    const { channel_id } = record
    api.setCircleContentLevel({ article_id: record[idKey], content_level, channel_id })
      .then(() => {
        setLoading(false)
        message.success('修改成功');
        onEnd()
      })
      .catch(() => {
        setLoading(false)
      })
  }
  return (
    <Modal
      visible={visible}
      key="EditCircleContentLevelModal"
      title="修改内容等级"
      confirmLoading={loading}
      width="500px"
      maskClosable={false}
      onCancel={onCancel}
      onOk={handleOk}
    >
      等级：<Radio.Group value={content_level} onChange={(e) => setContent_level(e.target.value)}>
        <Radio value="2">通过并推荐</Radio>
        <Radio value="0">正常通过</Radio>
        {/* <Radio value="3">圈外沉底</Radio> */}
        <Radio value="1">沉底</Radio>
      </Radio.Group>
    </Modal>
  )
}