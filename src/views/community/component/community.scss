.community {
    .title_show_hide {
        min-width: 30%;
        max-width: 70%;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
        -o-text-overflow: ellipsis;
    }

    .edit_flex {
        display: flex;
        height: 40px;
        margin-bottom: 24px;
        line-height: 40px;
    }

    .change_btn {
        color: #1890ff;
        cursor: pointer;
    }

    .verification_icon {
        color: #f5222d;
    }

    .verification_text {
        font-size: 14;
        color: #000000D9;
    }

    .title_or_change {
        display: flex;
        width: 100%;
    }
}

.community_recommend_list {

    .list-title {
        word-break: break-all;
        vertical-align: bottom;
    }

    .fixed-title {
        color: red
    }

    .hide-title {
        color: #ccc;
    }
}

.edit_circle_info {
    display: flex;
    height: 160px;

    ul {
        flex: 1;
        margin: 0;
        padding: 0;
        height: 100%;
        overflow-y: auto;
        list-style: none;

        li {
            height: 35px;
            padding-left: 8px;
            line-height: 35px;
            cursor: pointer;

            &.selected {
                color: #fff;
                background-color: #1890ff;
            }
        }
    }
}

.user_recommend_preview {
    position: relative;
    width: 150px;
    height: 112px;
    overflow: hidden;

    .user_recommend_preview_head {
        position: absolute;
        right: 0;
        top: 0;
        width: 64px;
        height: 64px;
        border-radius: 50%;
        border-bottom-right-radius: 0;
        overflow: hidden;

        img {
            width: 100%;
            height: 100%;
            vertical-align: middle;
        }

        .user_recommend_preview_head_mask {
            position: absolute;
            left: 0;
            top: 0;
            width: 64px;
            height: 64px;
        }
    }

    .user_recommend_preview_content {
        height: 91px;
        margin-top: 21px;
        padding: 10px;
        border-radius: 10px;
        
        h5 {
            width: 70px;
            color: #fff;
            font: 500 14px / 20px "PingFangSC-Medium", "PingFang SC";
            white-space: nowrap;
            text-overflow: ellipsis;
            overflow: hidden;
        }

        p {
            width: 90px;
            height: 32px;
            margin-top: 7px;
            font-size: 12px;
            line-height: 16px;
            color: #fff;
            overflow: hidden;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            word-break: break-all;
            text-overflow: ellipsis;
            white-space: normal;
        }
    }

    .user_recommend_review_focus {
        position: absolute;
        right: 0;
        bottom: 17px;
        width: 47px;
        height: 18px;
        background-color: #fff;
        border-radius: 9px 0px 0px 9px;
        color: #151515;
        font-size: 12px;
        line-height: 18px;
        &::before {
            content: "";
            display: inline-block;
            width: 8px;
            height: 8px;
            margin-left: 9px;
            margin-right: 1px;
            background: url("/assets/black_add_icon.png") no-repeat center / contain;
        }
    }
}

.generate_recommend3_preview {
    position: relative;
    width: 97px;
    height: 123px;
    border-radius: 10px 0px 10px 0px;
    overflow: hidden;
    img {
        width: 100%;
        height: 100%;
        vertical-align: middle;
    }
    .generate_recommend3_preview_mask {
        position: absolute;
        left: 0;
        bottom: 0;
        width: 100%;
        height: 70px;
        padding: 5px;
        display: flex;
        flex-direction: column;
        justify-content: flex-end;
        h3 {
            margin: 0;
            color: #fff;
            font: 600 13px / 20px "PingFangSC-Semibold", "PingFang SC";
            white-space: nowrap;
            text-overflow: ellipsis;
            overflow: hidden;
        }
        p {
            margin: 0;
            color: #fff;
            font-size: 12px;
            white-space: nowrap;
            text-overflow: ellipsis;
            overflow: hidden;
        }
    }
}