import React, { useMemo, useState, useRef, useEffect } from 'react'
import { Row, Col, Select, DatePicker, Input, Button, Divider, Icon, Drawer, Tooltip, message ,Modal} from 'antd'
import { Table } from '@components/common';
import { getCrumb, setMenuHook, searchToObject } from '@app/utils/utils';
import { useHistory } from 'react-router-dom';
import { PermA, PermButton } from '@components/permItems';
import AddForm from '@components/business/addForm';
import { recommendApi, releaseListApi, communityApi } from '@app/api';
import { useSelector, useDispatch } from 'react-redux';
import { getTableList } from '@app/action/tableList';
import { CommonObject,RequestBody} from '@app/types';
import moment from 'moment';
const { Option } = Select
const { Search } = Input;
const { MonthPicker, RangePicker, WeekPicker } = DatePicker;

export default function RecommendedUserOperations(props: any) {
  const { total, current, size, records = [], allData } = useSelector((state: any) => state.tableList)
  const dispatch = useDispatch();
  const [iconLoading, setIconLoading] = useState(false)
  const [visibleShow, setVisibleShow] = useState(false)
  const [editId, setEditId] = useState('')
  const [joggleType, setJoggleType] = useState(true)
  let joggleCreate=communityApi.recommendFollowCreate
  let joggleUpdate=communityApi.recommendFollowEdit
  const [drawerTitle, setDrawerTitle] = useState('添加运营位')
  const [recommendedList, setRecommendedList] = useState([{}])
  const [recommendedObj, setRecommendedObj] = useState({ recommend_name: '', position: '' })
  const formRef = useRef({} as any);
  const history = useHistory()
  const tooltipText = () => {
    return (
      <div >
        运营位将直接在输入的指定位置上显示，不参与内容排序，且优先于内容展示。
      </div>
    )
  }
  useEffect(() => {
    getData()
  }, [])
  const getColumns = useMemo(() => {  
  const getSeq = (i: number) =>(current - 1) * size + i + 1
    return [
      {
        title: '序号',
        key: 'seq',
        render: (text: any, record: any, i: number) => <span>{getSeq(i)}</span>,
        width: 70,
      },
      // {
      //   title: (<b>ID</b>),
      //   dataIndex: 'title',
      //   width: 150,
      // },
      {
        title: '推荐位名称',
        dataIndex: 'title',
        width: 150,
      },
      {
        title: <span>显示位置<Tooltip placement="topLeft" title={tooltipText()}>
          <Icon style={{ paddingLeft: '5px' }} type="question-circle" />
        </Tooltip></span>,
        dataIndex: 'position',
        width: 110,
      },
      {
        title: '推荐用户数',
        dataIndex: 'article_number',
        width: 150,
      },
      {
        title: '状态',
        dataIndex: 'status',
        width: 90,
        render: (text: any) => <span>{text ? '展示中' : '未展示'}</span>
      },
      {
        title: '创建人',
        dataIndex: 'creator',
        width: 200,
      },
      {
        title: '最后操作时间',
        dataIndex: 'published_at',
        width: 200,
        render:(text: any)=>(<span>{moment(text).format('YYYY-MM-DD HH:mm:ss')}</span> )
      },
      {
        title: '操作',
        key: 'op',
        render: (text: any, record: any) => (
          <>
            <PermA
              perm={`new_follow_recommend:${searchToObject().channel_id}:update`}
              onClick={() => editBanner(record)}
              style={{ marginRight: 5 }}
            >
              编辑
            </PermA>
            <PermA
              perm={`new_follow_recommend:${searchToObject().channel_id}:update_status`}
              onClick={() => moveStatus(record,record.status ? '下线' : '上线')}
              style={{ marginRight: 5 }}
            >
              {record.status ? '下线' : '上线'}
            </PermA>
            <PermA
              perm={`new_follow_recommend:${searchToObject().channel_id}:delete`}
              onClick={() => deleteAd(record)}
              style={{ marginRight: 5 }}
            >
              删除
            </PermA>
          </>
        ),
        width: 140,
      },
    ];
  }, [records]);
  const deleteAd = (record: any) => {
    Modal.confirm({
      title: <p>确认删除吗？</p>,
      onOk: () => {
        let data = {
          id: record.id,
          // status: record.status ? '0' : '1'
          channel_id: searchToObject().channel_id
        }
        communityApi.recommendFollowDelete(data).then((res: any) => {
          message.success('操作成功')
          getData()
        })
      },
  });
  }
  const initialFilter = {
    current: 1,
    size: 10,
    type: '33',
    // search_type: '1',
    // doc_type: '',
    // keyword: '',
    // begin: false,
    // end: false,
    channel_id: searchToObject().channel_id,
    // community_pushed: 1
  };
  const [filter, setFilter] = useState<CommonObject>(initialFilter)
  const getFilter = () => {
    const result: CommonObject = {};
    Object.keys(filter).forEach((k: string) => {
      if (filter[k]) {
        result[k] = filter[k];
      }
    });
    return result;
  };
  const getData = (filter = getFilter()) => {
    dispatch(getTableList('recommendFollowList', 'recommend_list', filter));
  };
  const moveStatus = (record: any,type: string) => {
    if (allData.on_show_count>=5 && type == '上线') return message.error('最多展示5组推荐')
    let data = {
      channel_id: searchToObject().channel_id,
      id: record.id,
      status: record.status ? '0' : '1'
    }
    communityApi.recommendFollowUpdateStatus(data).then((res: any) => {
      message.success('操作成功')
      getData()
    })
  }
  const editBanner = (record: any) => {
    setEditId(record.id)
    let data = {
      id: record.id,
    }
    communityApi.recommendFollowDetail(data).then((res: any) => {
      setJoggleType(false)
      setRecommendedObj({position:res.data.recommend_detail.position,recommend_name :  res.data.recommend_detail.title   })
      setRecommendedList([...res.data.recommend_detail.recommend_acct])
      // getData()
      setVisibleShow(true)
    })
    setDrawerTitle('编辑用户推荐位')
  }
  const onClose = () => {
    setVisibleShow(false)
  }
  const onOk = (e: any) => {
    setIconLoading(true)
    // setconLoading(false)
    formRef.current.handleSubmit(e)
  }
  //取消提交
  const cancleSubmit = () => {
    setVisibleShow(false)
    setIconLoading(false)
    // props.changeVisible(false)
  }
  const setFormItem = () => {
    return {
      nick_name: '',
      reason: '',
    }
  }
  //提交参数整合
  const getUpdateDate = (values: any, bannerData: any = {}, ids: any) => {
    let titles: any = []
    let ref_ids: any = []
    let customize_titles: any=[]
    values.json.forEach((el: any) => {
      // let id = el['source_list_title'].split('-')[0].trim()
      ref_ids.push(el.id.trim())
      titles.push(el['nick_name'])
      customize_titles.push(el.reason)
    });

    return {
      title: values.recommend_name,
      ref_ids,
      position: values.position,
      channel_id: searchToObject().channel_id,
      customize_titles: customize_titles.toString(),
      type: 33,
      nick_name:titles ,
      id: editId
      // customize_titles,
    }

  }
  const closeLoding = (v: boolean) => {
    setIconLoading(v)
  }
  //添加推荐用户
  const addRecommendedUser = () => {
    setEditId('')
    setRecommendedObj({ recommend_name: '', position: '' })
    setVisibleShow(true);
    setDrawerTitle('添加用户推荐位')
    let list = [{ reason: '', nick_name: '' },{ reason: '', nick_name: '' }]
    setRecommendedList([...list])
    setJoggleType(true)
  }
    //创建、编辑成功后，关闭抽屉
    const changeVisibleShow = ()=>{
      setVisibleShow(false)
      getData()
    }
  return (
    <>
      <Row>
        <Col span={12} >
          <Button onClick={() => history.go(-1)} style={{ margin: '8px 8px 0 0 ' }}>
            <Icon type="left-circle-o" />
            返回
          </Button>
          <PermButton perm={`new_follow_recommend:${searchToObject().channel_id}:create`} onClick={() => addRecommendedUser()}>添加推荐用户</PermButton>
        </Col>
        <Col span={12} className="layout-breadcrumb">
          {getCrumb(props.breadCrumb)}
        </Col>
      </Row>
      <div className="component-content">
        <Row>
          <Col>
            <Table
              func="recommendFollowList"
              index="recommend_list"
              filter={getFilter()}
              columns={getColumns}
              rowKey="id"
              pagination={true}
              total={total}
            />
          </Col>
        </Row>
      </div>
      <Drawer
        title={drawerTitle}
        width={'60%'}
        maskClosable={false}
        onClose={onClose}
        visible={visibleShow}
        bodyStyle={{ paddingBottom: 80 }}
        destroyOnClose
      >
        <div className='box'>
          <AddForm
            bannerData={recommendedList} //复现数据
            bannerObj={recommendedObj} //复现数据
            changeVisible={changeVisibleShow}
            closeLoding={closeLoding}
            wrappedComponentRef={(instance: any) => (formRef.current = instance)}
            getUpdateDate={getUpdateDate} //获取要提交的数据
            setFormItem={setFormItem()} //设置表单item
            joggle={joggleType ? joggleCreate : joggleUpdate} // 接口
            componentNames={'用户管理位'} //判断使用的组件
            addLabel={'添加一个用户'}
            headName={'用户'}
            listLength={20}
            disabled={2}
          />
        </div>
        <div
          style={{
            position: 'absolute',
            right: 0,
            bottom: 0,
            width: '100%',
            borderTop: '1px solid #e9e9e9',
            padding: '10px 16px',
            background: '#fff',
            textAlign: 'right',
          }}
        >
          <Button onClick={() => cancleSubmit()} style={{ marginRight: 8 }}>
            取消
          </Button>
          <PermButton
            perm={''}
            type="primary" loading={iconLoading}
            onClick={(e) => onOk(e)}
          >
            确定
          </PermButton>
          {/* <Button onClick={(e)=>onOk(e)} type="primary" loading={iconLoading}>
                确定
                </Button> */}
        </div>
      </Drawer>

    </>
  )
}
