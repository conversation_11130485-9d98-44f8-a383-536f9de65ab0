import { Button, Form, Icon, InputNumber, Modal, Radio, Spin, Tooltip, message } from 'antd'
import React, { forwardRef, useImperativeHandle, useState } from 'react'

import { releaseListApi, reportApi } from '@app/api';

const EditFixedSortModal = (props: any, ref: any) => {

  const [loading, setLoading] = useState(false)
  const { getFieldDecorator } = props.form;
  useImperativeHandle(
    ref,
    () => ({
      ...props.form,
    }),
    []
  );

  const formLayout = {
    labelCol: { span: 6 },
    wrapperCol: { span: 16 },
  };

  const handleSubmit = (e: any) => {
    e.preventDefault();
    props.form.validateFieldsAndScroll((err: any, values: any) => {
      if (!err) {
        setLoading(true)
        console.log('values.board_type', values.board_type)
        const parmas = {
          circle_id: props.record.circle_id,
          article_id: props.record.article_id,
          board_id: props.record.board_id,
          fix_number: (values.type || 0) == 0 ? 0 : Math.max(1, (values.fix_number || 0)),
          board_fix_number: (values.board_type || 0) == 0 ? 0 : Math.max(1, (values.board_fix_number || 0))
        }
        releaseListApi.editCircleArticleFixDetail(parmas).then((res: any) => {
          message.success('修改成功');
          setLoading(false)
          props.onOk && props.onOk(props.type)
        }).catch(() => {
          // message.error('添加失败');
          setLoading(false)
        })
      } else {
        message.error('请检查表单内容');
        setLoading(false)
      }
    });
  }

  return <Modal
    width={500}
    visible={props.visible}
    title={'设置固定排序'}
    key={props.skey}
    onCancel={() => { if (!loading) { props.onCancel && props.onCancel() } }}
    onOk={handleSubmit}
    maskClosable={false}
    destroyOnClose={true}
  >
    <Spin spinning={false}>
      <Form {...formLayout}>
        <h3>在全部列表下</h3>
        <Form.Item label="排序方式">
          {getFieldDecorator('type', {
            initialValue: props.fix_number == 0 ? 0 : 1,
            rules: [
              {
                required: true,
                message: '请选择排序方式',
              }
            ],
          })(<Radio.Group>
            <Radio value={0}>自动排序</Radio>
            <Radio value={1}>固定排序</Radio>
          </Radio.Group>
          )}
        </Form.Item>

        {props.form.getFieldValue('type') == 1 && <Form.Item label="排序值">
          {getFieldDecorator('fix_number', {
            initialValue: props.fix_number,
            rules: [
              {
                required: true,
                message: '请输入排序值',
              }
            ],
          })(<InputNumber
            placeholder="请输入1～100的数字"
            min={1}
            max={100}
            style={{ width: '100%' }}
            precision={0}
          />)}
        </Form.Item>}
        {!!props.record?.board_id &&
          <>
            <h3>在{props.record?.board_name}列表下</h3>
            <Form.Item label="排序方式">
              {getFieldDecorator('board_type', {
                initialValue: props.board_fix_number == 0 ? 0 : 1,
                rules: [
                  {
                    required: true,
                    message: '请选择排序方式',
                  }
                ],
              })(<Radio.Group>
                <Radio value={0}>自动排序</Radio>
                <Radio value={1}>固定排序</Radio>
              </Radio.Group>
              )}
            </Form.Item>

            {props.form.getFieldValue('board_type') == 1 && <Form.Item label="排序值">
              {getFieldDecorator('board_fix_number', {
                initialValue: props.board_fix_number,
                rules: [
                  {
                    required: true,
                    message: '请输入排序值',
                  }
                ],
              })(<InputNumber
                placeholder="请输入1～100的数字"
                min={1}
                max={100}
                style={{ width: '100%' }}
                precision={0}
              />)}
            </Form.Item>}

          </>
        }
      </Form>
    </Spin>
  </Modal>

}

export default Form.create<any>({ name: 'EditFixedSortModal' })(forwardRef<any, any>(EditFixedSortModal));
