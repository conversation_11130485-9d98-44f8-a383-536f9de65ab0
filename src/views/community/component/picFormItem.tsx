import React, { useState, useEffect, useRef } from 'react';
import { Input } from 'antd';
import _ from 'lodash';
import './community.scss';
import { ImageUploader } from '@app/components/common';
import Radio from 'antd/es/radio';
import { SearchAndInput } from '@components/common';
import NewSearchAndInput from '@components/common/newNewsSearchAndInput';
import moment from 'moment';
import NewImageUploader from '@app/components/common/newImageUploader';

// @connectSession
// @(Form.create({ name: 'activityForm' }) as any)
export default function PicFormItem(props: any) {
  // console.log(props,'props======');
  const getFieldDecorator = props.getFieldDecorator;
  const getFieldsValue = props.getFieldsValue;
  const v = props.v;
  const Form = props.Form;
  const [link_type, setLink_type] = useState(props.json[v].link_type);
  const style: 0 | 61 | 62 | 63 = getFieldsValue().style;
  const isFirstRender = useRef(true);
  const fixJson = (json: any) => {
    const newJson = { ...json };
    Object.keys(newJson).forEach((key) => {
      if (!newJson[key].pic) {
        newJson[key].pic = json[key].pic_url && {
          url: json[key].pic_url,
          ratio: { 0: 2 / 1, 61: 3 / 2, 62: 1, 63: 16 / 9 }[style],
        };
      }
    });
    return newJson;
  };
  const [stateList, setStateList] = useState({ ...fixJson(props.json) });

  useEffect(() => {
    const newJson = fixJson(props.json);
    setStateList(newJson);
    setLink_type(newJson[props.v].link_type || 0);
  }, [props.json]);

  useEffect(() => {
    if (isFirstRender.current) {
      isFirstRender.current = false;
      return;
    }
    props.form?.validateFields();
  }, [style]);

  const columns1 = [
    { title: '地方号名称', dataIndex: 'name' },
    { title: '创建人', dataIndex: 'created_user_name' },
    {
      title: '创建时间',
      dataIndex: 'created_at',
      render: (text: number) => moment(text).format('YYYY-MM-DD'),
    },
  ];
  const columns2 = [
    {
      title: 'ID',
      dataIndex: 'id',
      width: 80,
      render: (_: any, v: any) => v.uuid || v.id,
    },
    {
      title: '新闻频道',
      key: 'type',
      dataIndex: 'channel_name',
      width: 90,
    },
    {
      title: '新闻标题',
      key: 'list_title',
      dataIndex: 'list_title',
    },
  ];

  return (
    <div className="community">
      <Form.Item
        label="上传图片"
        extra={`支持jpg,jpeg,png图片格式，比例为${{ 0: '2:1', 61: '3:2', 62: '1:1', 63: '16:9' }[style]}`}
      >
        {getFieldDecorator(`json[${v}].pic`, {
          initialValue: stateList[v]?.pic,
          rules: [
            {
              required: true,
              message: '请选择图片',
            },
            {
              validator: (rule: any, value: any, callback: any) => {
                if (value && !!value.url && value?.ratio != { 0: 2 / 1, 61: 3 / 2, 62: 1, 63: 16 / 9 }[style]) {
                  callback('图片比例不正确,请重新上传');
                } else {
                  callback();
                }
              },
            },
          ],
        })(
          <NewImageUploader
            ratio={{ 0: 2 / 1, 61: 3 / 2, 62: 1, 63: 16 / 9 }[style]}
            accept={['image/png', 'image/jpeg', 'image/jpg']}
          />
        )}
      </Form.Item>
      <Form.Item label="跳转">
        {getFieldDecorator(`json[${v}].link_type`, {
          initialValue: stateList[v]?.link_type || 0,
          rules: [
            {
              required: true,
              message: '请选择样式',
            },
          ],
        })(
          <Radio.Group onChange={(e) => setLink_type(e.target.value)}>
            <Radio value={0}>链接</Radio>
            <Radio value={1}>地方号</Radio>
            <Radio value={2}>稿件</Radio>
          </Radio.Group>
        )}
      </Form.Item>
      {link_type === 0 && (
        <Form.Item label="跳转链接：">
          {getFieldDecorator(`json[${v}].link_url`, {
            initialValue: stateList[v]?.link_url,
            rules: [
              {
                required: true,
                message: '请输入跳转链接',
              },
              {
                pattern: /^https?:\/\//,
                message: '请填写正确的URL地址',
              },
            ],
          })(<Input style={{ width: '80%' }} placeholder="请输入跳转链接" />)}
        </Form.Item>
      )}
      {link_type === 1 && (
        <Form.Item label="关联地方号">
          {getFieldDecorator(`json[${v}].columnList`, {
            initialValue: stateList[v]?.columnList,
            rules: [
              {
                required: true,
                message: '请关联地方号',
                type: 'array',
              },
              {
                max: 1,
                message: '最多关联1条地方号',
                type: 'array',
              },
            ],
          })(
            <SearchAndInput
              max={1}
              func="searchUGCTopic"
              columns={columns1}
              placeholder="输入名称搜索地方号"
              initialValues={{ list: stateList[v]?.columnList }}
              body={{ type: 0 }}
              order={true}
              addOnTop={true}
              searchKey="name"
              funcIndex="list"
              apiWithPagination={true}
              selectOptionDisplay={(record: any) => `#${record.name}#`}
              detailMode
            />
          )}
        </Form.Item>
      )}
      {link_type === 2 && (
        <Form.Item label="关联稿件">
          {getFieldDecorator(`json[${v}].channelArticles`, {
            initialValue: stateList[v]?.channelArticles,
            rules: [
              {
                required: true,
                message: '请关联稿件',
                type: 'array',
              },
              {
                max: 1,
                message: '最多关联1条稿件',
                type: 'array',
              },
            ],
          })(
            <NewSearchAndInput
              max={1}
              func="listArticleRecommendSearch"
              columns={columns2}
              placeholder="输入ID或标题关联稿件"
              body={{ doc_types: '2,3,4,5,8,9,10,11,12,13' }}
              order={true}
              addOnTop={true}
            />
          )}
        </Form.Item>
      )}
      <Form.Item label="推荐理由：" style={{ display: 'none' }}>
        {getFieldDecorator(`json[${v}].id`, {
          initialValue: stateList[v]?.id,
          rules: [{ message: '请输入推荐理由，最多15字' }],
        })(
          <Input style={{ width: '80%' }} maxLength={15} placeholder="请输入推荐理由，最多15字" />
        )}
      </Form.Item>
    </div>
  );
}
