import { Button, Popover, Select } from 'antd';
import React, { useState } from 'react';
import _ from 'lodash';
import { communityApi } from '@app/api';

export default function MentionsSearchPopover(props: any) {
  const [visible, setVisible] = useState(false);
  const [accountOptions, setAccountOptions] = useState([]);

  const handleAccountSearch = _.debounce((val: any) => {
    if (!val) {
      setAccountOptions([]);
      return;
    }
    // type: 1, current: 1, size: 50, biz_type: props.formContent?.biz_type,
    communityApi
      .recommendAccount_Search({ keyword: val })
      .then((res) => {
        setAccountOptions(res?.data?.list || []);
      })
      .catch(() => {});
  }, 500);

  return (
    <Popover
      content={
        <div style={{ width: 300 }}>
          <Select
            style={{ width: '100%' }}
            // value={reporter}
            value={undefined}
            placeholder="输入用户昵称或用户ID"
            onSearch={handleAccountSearch}
            showSearch
            filterOption={false}
            onChange={(val: any) => {
              props.onChange(JSON.parse(val));
              setVisible(false);
            }}
          >
            {accountOptions.map((d: any) => (
              <Select.Option key={d.id} value={JSON.stringify(d)}>
                {`${d.nick_name} | 用户ID： ${d.chao_id}`}
              </Select.Option>
            ))}
          </Select>
        </div>
      }
      title=""
      trigger="click"
      visible={visible}
      onVisibleChange={(visible: boolean) => {
        if (visible) {
          setAccountOptions([]);
        }
        setVisible(visible);
      }}
    >
      <Button>@用户</Button>
    </Popover>
  );
}
