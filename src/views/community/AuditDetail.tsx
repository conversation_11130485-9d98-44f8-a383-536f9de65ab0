import React, { useState } from 'react';
import { PhotoSlider } from 'react-photo-view';

export default function AuditDetail(props: any) {
  const [imagePreview, setImagePreview] = useState({
    visible: false,
    imgs: [],
    index: 0,
  });

  let videoUrl = '';
  if (props.detail?.doc_type == 10) {
    videoUrl = props.detail?.video_url;
  }

  let textSensitiveWords: any = [];
  const words = props.textSensitiveWords?.flatMap((item: any) => {
    return item.words;
  });
  words.forEach((item: any) => {
    if (textSensitiveWords.filter((word: any) => word.word == item.word).length == 0) {
      textSensitiveWords.push(item);
    }
  });

  let content = props.detail?.list_title;
  for (let index = 0; index < words?.length; index++) {
    const element = words[index];
    content = content?.replaceAll(
      element.word,
      `<span style="background-color: #fff2f0;color: #ff4c4c;">${element.word}</span>`
    );
  }

  return (
    <div className={props.className}>
      <div
        style={{
          marginBottom: '10px',
        }}
        dangerouslySetInnerHTML={{ __html: content }}
      ></div>
      {props.detail?.doc_type == 12 &&
        props.detail?.list_pics?.map((item: any, index: number) => {
          return (
            <img
              width="100%"
              style={{ maxHeight: '300px', objectFit: 'contain', marginBottom: '10px' }}
              key={item}
              src={item}
              alt={item}
              onClick={() => {
                setImagePreview({
                  ...imagePreview,
                  visible: true,
                  imgs: props.detail?.list_pics,
                  index: index,
                });
              }}
            />
          );
        })}
      {props.detail?.doc_type == 10 && videoUrl && (
        <video
          width="100%"
          style={{ maxHeight: '300px', objectFit: 'contain', marginBottom: '10px' }}
          src={videoUrl}
          poster={props.detail?.first_cover}
          controls
        />
      )}

      <PhotoSlider
        maskOpacity={0.5}
        images={imagePreview.imgs?.map((v: any) => ({ src: v, key: v })) || []}
        visible={imagePreview.visible}
        onClose={() =>
          setImagePreview({
            ...imagePreview,
            visible: false,
          })
        }
        index={imagePreview.index}
        onIndexChange={(index) =>
          setImagePreview({
            ...imagePreview,
            index,
          })
        }
      />
    </div>
  );
}
