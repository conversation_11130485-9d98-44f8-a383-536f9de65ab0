import { getCrumb } from '@app/utils/utils';
import {
  Button,
  Col,
  Icon,
  Modal,
  Row,
  Switch,
  Tooltip,
  message,
} from 'antd';
import { Table, OrderColumn } from '@components/common';
import { useDispatch, useSelector, useStore } from 'react-redux';
import { useHistory, useParams } from 'react-router';
import { getTableList } from '@app/action/tableList';
import React, { useEffect, useState } from 'react';
import _ from 'lodash';
import { communityApi as api, communityApi, opApi, releaseListApi, userApi } from '@app/api';
import { setConfig } from '@app/action/config';
import { PermA, PermButton, PermSwitch } from '@app/components/permItems';
import moment from 'moment';
import CirclePictureFormDrawer from './component/circlePictureFormDrawer';

export default function CirclePictureMgr(props: any) {
  const dispatch = useDispatch();
  const history = useHistory();
  const { id: circle_id, name } = useParams<any>();
  // const { selected = '', board_id = '' } = searchToObject();
  const { session } = useStore().getState();
  // const { total, current, size, records = [] } = useSelector((state: any) => state.tableList);
  const {
    total,
    current,
    size,
    allData,
  } = useSelector((state: any) => state.tableList);

  const [singlePic, setSinglePic] = useState(false);

  const [pictureFormDrawer, setPictureFormDrawer] = useState<any>({
    visible: false,
    record: null,
    circle_id: circle_id,
    type: 0
  })

  const getSeq = (i: number) => (current - 1) * size + i + 1;
  const onShowCount = allData?.list?.records?.filter((item: any) => item.status == 1)?.length || 0
  const columns: any = [
    {
      title: '排序',
      key: 'order',
      render: (text: any, record: any, i: number) => {
        const pos = getSeq(i);
        return (
          <OrderColumn
            pos={pos}
            start={1}
            end={onShowCount}
            perm="common_recommend:mgr:1"
            disableUp={record.status != 1}
            disableDown={record.status != 1}
            onUp={() => exchangeOrder(record.id, 0)}
            onDown={() => exchangeOrder(record.id, 1)}
          />
        );
      },
      width: 70,
    },
    {
      title: '序号',
      key: 'id',
      render: (a: any, b: any, index: number) => <span>{getSeq(index)}</span>,
      width: 50,
    },
    {
      title: '图片',
      key: 'image_url',
      dataIndex: 'image_url',
      width: 150,
      align: 'center',
      render: (text: any, record: any) => <img src={text} className="list-pic" />
    },
    {
      title: '链接',
      dataIndex: 'url',
      // width: 90,
      render: (text: any) => {
        return <a href={text}>{text}</a>
      }
    },
    {
      title: '状态',
      dataIndex: 'status',
      width: 90,
      render: (text: any) => {
        return <span>{text == 1 ? '展示中' : '未展示'}</span>
      }
    },
    {
      title: '最后操作人',
      dataIndex: 'updated_by',
      width: 140,
    },
    {
      title: '最后操作时间',
      dataIndex: 'updated_at',
      width: 160,
      render: (text: any, record: any) => (
        <span>{moment(text).format('YYYY-MM-DD HH:mm:ss')}</span>
      ),
    },
    {
      title: '操作',
      key: 'op',
      align: 'center',
      width: 150,
      render: (text: any, record: any) => {
        return <>
          <PermA perm='common_recommend:mgr:1' style={{ marginRight: 5 }} onClick={() => editPictureRecommend(record)}>编辑</PermA>
          <PermA perm='common_recommend:mgr:1' style={{ marginRight: 5 }} onClick={() => { handleUpdateStatus(record) }}>{record.status == 1 ? '下架' : '上架'}</PermA>
          <PermA perm='common_recommend:mgr:1' onClick={() => { handelDel(record) }}>删除</PermA>
        </>
      },
    },
  ];

  const handleUpdateStatus = (val: any) => {
    api
      .updateCirclePicRecommendStatus({ id: val.id, status: val.status == 1 ? 0 : 1 })
      .then(() => {
        message.success('操作成功');
        // dispatch(setConfig({ loading: false }));
        getData();
      })
      .catch(() => {
        // dispatch(setConfig({ loading: false }));
      });
  }

  // 删除
  const handelDel = (val: any) => {
    Modal.confirm({
      title: '确定删除？',
      onOk: () => {
        dispatch(setConfig({ loading: true }));
        api
          .deleteCirclePicRecommend({ id: val.id })
          .then(() => {
            message.success('操作成功');
            dispatch(setConfig({ loading: false }));
            getData();
          })
          .catch(() => {
            dispatch(setConfig({ loading: false }));
          });
      },
    });
  };

  // 排序
  const exchangeOrder = (id: number, sort_flag: number) => {
    dispatch(setConfig({ loading: true }));
    api
      .sortCirclePicRecommend({ id, sort_flag })
      .then(() => {
        message.success('操作成功');
        getData();
        dispatch(setConfig({ loading: false }));
      })
      .catch(() => {
        dispatch(setConfig({ loading: false }));
      });
  };

  const getData = (goToFirstPage = false) => {
    const cur = goToFirstPage ? 1 : current;
    dispatch(
      getTableList('getCircleRecommendList', 'list', { current: cur, size, circle_id })
    );
  };

  useEffect(() => {
    getData(true);
    getRecommendStyle()

  }, []);

  const getRecommendStyle = () => {
    communityApi.getCirclePicRecommendStyle({ circle_id }).then((res) => {
      setSinglePic(Boolean(res.data))
    }).catch(() => {

    })

  }

  const editPictureRecommend = (record: any) => {
    setPictureFormDrawer({
      visible: true,
      record,
      circle_id: circle_id,
      type: singlePic ? 1 : 0
    })
  }

  return (
    <>
      <Row className="layout-infobar">
        <Col span={12}>
          <Button onClick={() => history.goBack()}>
            <Icon type="left-circle" />
            返回圈子管理
          </Button>
          <div style={{
            display: 'inline-flex', alignItems: 'center'
          }}>
            <span style={{ marginLeft: 8 }}>切换到单图模式&nbsp;</span>
            <PermSwitch
              perm='common_recommend:mgr:1'
              style={{ marginRight: 8 }}
              checked={singlePic}
              checkedChildren="开"
              unCheckedChildren="关"
              onChange={(e: any) => {
                // console.log(e);
                // this.setState({
                //   showDragSorter: e
                // })
                // setSinglePic(e)

                communityApi.setCirclePicRecommendStyle({ style: e ? 1 : 0, circle_id }).then((res) => {
                  setSinglePic(e)
                  getData(true)
                }).catch(() => {

                })

              }}
            />
            <Tooltip
              overlayStyle={{ maxWidth: 516 }}
              title={
                <div>
                  单个模式下，只能配置一个图片推荐位；多个模式，可配置多个图片推荐位，样式对比如下：
                  <img src='/assets/circle_pic_recommend.png' width={500} height={208} />
                </div>
              }
              placement="top"
            >
              <Icon type="question-circle" />
            </Tooltip>
          </div>
          <PermButton
            perm="common_recommend:mgr:1"
            style={{ marginLeft: 8 }}
            onClick={() => editPictureRecommend(null)}
          >
            添加{singlePic ? '单' : '多'}图推荐位
          </PermButton>
        </Col>
        <Col span={12} className="layout-breadcrumb">
          {getCrumb([...props.breadCrumb, name])}
        </Col>
      </Row>
      <div className="component-content">
        <div>注：本列表显示的是{singlePic ? '单' : '多'}图模式下的推荐位数据</div>
        <Table
          func="getCircleRecommendList"
          index="list"
          filter={{ circle_id }}
          pagination={true}
          rowKey="id"
          columns={columns}
        />

        {/* 编辑爆料 */}
        <CirclePictureFormDrawer
          {...pictureFormDrawer}
          onClose={() => setPictureFormDrawer({ visible: false })}
          onEnd={() => {
            setPictureFormDrawer({ visible: false })
            getData()
          }}
        >
        </CirclePictureFormDrawer>

      </div>
    </>
  );
}
