import { Button, Dropdown, Icon, Menu, Modal, Timeline, message } from 'antd';
import React, { forwardRef, useEffect, useImperativeHandle, useRef, useState } from 'react';
import { Drawer } from '@app/components/common';
import AuditDetail from './AuditDetail';
import AuditInfo from './AuditInfo';
import { PermButton, PermMenuItem } from '@app/components/permItems';
import AddAuditBlackUserModal from './addAuditBlackUserModal';
import AuditNotPassedModal from './auditNotPassedModal';
import { useDispatch } from 'react-redux';
import { communityApi, opApi } from '@app/api';
import { setConfig } from '@app/action/config';
import './audit.scss';
import AuditEditDrawer from './auditEditDrawer';
import EditCircleInfoModal from './component/EditCircleInfoModal';
import { formatTextCheckResult, formatVideoCheckResult, getSensitiveWords } from '@app/utils/utils';
import moment from 'moment';

const AuditDetailDrawer = (props: any, ref: any) => {
  const editFlag = useRef(false);
  const dispatch = useDispatch();
  const [addAuditBlackUser, setAddAuditBlackUser] = useState<any>({
    key: '',
    visible: false,
    id: null,
  });

  const [notPass, setNotPass] = useState<any>({
    key: '',
    visible: false,
    ids: [],
  });

  const [operateLog, setOperateLog] = useState<any>({
    visible: false,
    key: null,
    logs: null,
  });

  const [editDrawer, setEditDrawer] = useState<any>({
    key: '',
    visible: false,
    detail: null,
  });

  const [detail, setDetail] = useState<any>(null);

  const auditStatus = detail?.status;

  const [textSensitiveWords, setTextSensitiveWords] = useState<any>([]);
  const [imgSensitiveWords, setImgSensitiveWords] = useState<any>([]);
  const [videoSensitiveWords, setVideoSensitiveWords] = useState<any>([]);
  useEffect(() => {
    if (props.visible) {
      setTextSensitiveWords([]);

      getDetail();
    }
  }, [props.visible]);

  const getDetail = () => {
    dispatch(setConfig({ mLoading: true }));
    communityApi
      .getArticleAuditDetail({ id: props.id })
      .then((res: any) => {
        dispatch(setConfig({ mLoading: false }));
        const article = res.data.article;
        setDetail(article);

        const textCheckResult =
          article?.text_check_result && JSON.parse(article?.text_check_result);
        const imgCheckResult = article?.img_check_result && JSON.parse(article?.img_check_result);
        const videoCheckResult =
          article?.video_check_result && JSON.parse(article?.video_check_result);
        if (textCheckResult) {
          const textSensitiveWords = formatTextCheckResult(
            textCheckResult.checkResult,
            textCheckResult.platform
          );
          setTextSensitiveWords(textSensitiveWords);
        }
        if (imgCheckResult) {
          const imgSensitiveWords = getSensitiveWords(imgCheckResult);
          setImgSensitiveWords(imgSensitiveWords);
        }
        if (videoCheckResult) {
          const videoSensitiveWords = formatVideoCheckResult(videoCheckResult);
          console.log('videoSensitiveWords', videoSensitiveWords);
          setVideoSensitiveWords(videoSensitiveWords);
        }
      })
      .catch(() => {
        dispatch(setConfig({ mLoading: false }));
      });
  };

  const editArticle = () => {
    setEditDrawer({
      key: Date.now(),
      visible: true,
      detail: detail,
    });
  };

  const getOperateLog = () => {
    dispatch(setConfig({ mLoading: true }));
    opApi
      .getOperateLog({ target_id: detail.id, type: 166 })
      .then((r: any) => {
        setOperateLog({
          key: Date.now(),
          visible: true,
          logs: r.data.admin_log_list,
        });
        dispatch(setConfig({ mLoading: false }));
      })
      .catch(() => {
        dispatch(setConfig({ mLoading: false }));
      });
  };

  const resumeReview = (ids: string[]) => {
    Modal.confirm({
      title: '待审帖子仅作者本人可见，确定要恢复待审吗？',
      onOk: () => {
        dispatch(setConfig({ mLoading: true }));
        communityApi
          .batchOperateAudit({ ids: ids.join(','), status: 3 })
          .then(() => {
            dispatch(setConfig({ mLoading: false }));
            message.success('操作成功');
            getDetail();
            editFlag.current = true;
          })
          .catch(() => {
            dispatch(setConfig({ mLoading: false }));
          });
      },
    });
  };

  const approved = (ids: string[], content_level: number) => {
    dispatch(setConfig({ mLoading: true }));
    communityApi
      .batchOperateAudit({ ids: ids.join(','), status: 4, content_level })
      .then(() => {
        dispatch(setConfig({ mLoading: false }));
        message.success('操作成功');
        getDetail();
        editFlag.current = true;
      })
      .catch(() => {
        dispatch(setConfig({ mLoading: false }));
      });
  };

  const [circleInfo, setCircleInfo] = useState<any>({
    key: '',
    visible: false,
    record: null,
  });

  const changeCircle = () => {
    setCircleInfo({
      key: Date.now(),
      visible: true,
      record: detail,
    });
  };

  return (
    <Drawer
      width={1000}
      title={
        <div style={{ display: 'flex', alignItems: 'center' }}>
          <div style={{ marginRight: 8 }}>帖子详情</div>
          <Button style={{ width: 100, marginRight: 8 }} onClick={getOperateLog}>
            操作日志
          </Button>
        </div>
      }
      visible={props.visible}
      skey={props.key}
      onClose={() => props.onClose(editFlag.current)}
      maskClosable={true}
      footer={
        <div
          style={{
            width: '100%',
            display: 'flex',
            justifyContent: 'space-between',
          }}
        >
          <div>
            {detail?.status < 7 && (
              <PermButton perm={''} style={{ width: 100 }} onClick={editArticle}>
                编辑帖子
              </PermButton>
            )}
          </div>

          <div>
            {detail?.status == 3 && (
              <Dropdown
                overlay={
                  <Menu>
                    <PermMenuItem
                      perm="ugc_article:content_audit"
                      onClick={() => {
                        setNotPass({
                          key: Date.now(),
                          visible: true,
                          ids: [props.id],
                          isBatch: false,
                          type: 'delete',
                        });
                      }}
                    >
                      删除
                    </PermMenuItem>
                    <PermMenuItem
                      perm=""
                      onClick={() => {
                        setAddAuditBlackUser({
                          key: Date.now(),
                          visible: true,
                          id: detail.account_id,
                        });
                      }}
                    >
                      拉黑
                    </PermMenuItem>
                  </Menu>
                }
              >
                <PermButton perm={''} style={{ marginRight: 8 }}>
                  其他 <Icon type="up" />
                </PermButton>
              </Dropdown>
            )}
            {(detail?.status == 4 || detail?.status == 5) && (
              <>
                <PermButton
                  style={{ marginRight: 8 }}
                  perm="ugc_article:content_audit"
                  onClick={() =>
                    setNotPass({
                      key: Date.now(),
                      visible: true,
                      ids: [props.id],
                      isBatch: false,
                      type: 'delete',
                    })
                  }
                >
                  删除
                </PermButton>
                <PermButton
                  style={{ marginRight: 8 }}
                  perm="ugc_article:content_audit"
                  onClick={() => resumeReview([props.id])}
                >
                  恢复待审
                </PermButton>
              </>
            )}
            {(detail?.status == 3 || detail?.status == 4) && (
              <PermButton
                perm="ugc_article:content_audit"
                style={{ marginRight: 8 }}
                onClick={() =>
                  setNotPass({
                    key: 'notPassed',
                    visible: true,
                    ids: [props.id],
                  })
                }
              >
                不通过
              </PermButton>
            )}
            {detail?.status < 7 && (
              <Dropdown
                overlay={
                  <Menu>
                    <PermMenuItem
                      perm="ugc_article:content_audit"
                      onClick={() => {
                        approved([props.id], 0);
                      }}
                    >
                      普通
                    </PermMenuItem>
                    <PermMenuItem
                      perm="ugc_article:content_audit"
                      onClick={() => {
                        approved([props.id], 2);
                      }}
                    >
                      推荐
                    </PermMenuItem>
                    <PermMenuItem
                      perm="ugc_article:content_audit"
                      onClick={() => {
                        approved([props.id], 1);
                      }}
                    >
                      沉底
                    </PermMenuItem>
                  </Menu>
                }
              >
                <PermButton
                  type="primary"
                  perm="ugc_article:content_audit"
                  // style={{ marginLeft: 8 }}
                >
                  {detail?.status == 3 ? '通过' : detail?.status == 4 ? '修改等级' : '重新通过'}
                  <Icon type="up" />
                </PermButton>
              </Dropdown>
            )}
            {(detail?.status == 7 || detail?.status == 9) && (
              <PermButton
                perm="ugc_article:content_audit"
                onClick={() => {
                  setNotPass({
                    key: Date.now(),
                    visible: true,
                    ids: [props.id],
                    isBatch: false,
                    type: 'delete',
                  });
                }}
              >
                彻底删除
              </PermButton>
            )}
          </div>
        </div>
      }
    >
      <div className="drawer_detail_content">
        <AuditDetail
          className="report_content"
          detail={detail}
          textSensitiveWords={textSensitiveWords}
        ></AuditDetail>
        <AuditInfo
          className="report_info"
          detail={detail}
          changeCircle={changeCircle}
          textSensitiveWords={textSensitiveWords}
          imgSensitiveWords={imgSensitiveWords}
          videoSensitiveWords={videoSensitiveWords}
        ></AuditInfo>
      </div>

      <AuditNotPassedModal
        {...notPass}
        onOk={() => {
          setNotPass({ ...notPass, visible: false });
          getDetail();
          editFlag.current = true;
        }}
        onCancel={() => {
          setNotPass({ ...notPass, visible: false });
        }}
      />

      <AddAuditBlackUserModal
        {...addAuditBlackUser}
        onOk={() => {
          setAddAuditBlackUser({ ...addAuditBlackUser, visible: false });
          getDetail();
        }}
        onCancel={() => {
          setAddAuditBlackUser({ ...addAuditBlackUser, visible: false });
        }}
      />

      <AuditEditDrawer
        {...editDrawer}
        onClose={() => setEditDrawer({ ...editDrawer, visible: false })}
        onEnd={() => {
          setEditDrawer({ ...editDrawer, visible: false });
          getDetail();
          editFlag.current = true;
        }}
      />

      <EditCircleInfoModal
        idKey="id"
        record={circleInfo.record}
        visible={circleInfo.visible}
        onCancel={() => setCircleInfo({ ...circleInfo, visible: false })}
        onEnd={() => {
          setCircleInfo({ ...circleInfo, visible: false });
          editFlag.current = true;
          getDetail();
        }}
      />

      {/* 操作日志 */}
      <Modal
        visible={operateLog.visible}
        title="操作日志"
        key={operateLog.key}
        cancelText={null}
        onCancel={() => setOperateLog({ ...operateLog, visible: false })}
        onOk={() => setOperateLog({ ...operateLog, visible: false })}
      >
        <div>
          <Timeline>
            {operateLog.logs?.map((v: any, i: number) => [
              <Timeline.Item className="timeline-dot-big" data-show={v.date} key={`time${i}`}>
                &nbsp;
              </Timeline.Item>,
              v.log_list?.map((action: any, index: number) => (
                <Timeline.Item
                  className="timeline-dot"
                  data-show={moment(action.created_at).format('HH:mm:ss')}
                  key={`time${i}-action${index}`}
                >
                  {action.admin_name}&emsp;{action.remark}
                  {/* {action.user}&emsp;&emsp;{action.action}&emsp;&emsp; */}
                </Timeline.Item>
              )),
            ])}
          </Timeline>
        </div>
      </Modal>
    </Drawer>
  );
};

export default AuditDetailDrawer;
