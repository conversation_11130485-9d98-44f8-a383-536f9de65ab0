.drawer_detail_content {
  width: 100%;
  height: 100%;
  overflow: hidden;
  display: flex;
  flex-direction: row;
  align-items: stretch;

  .report_content {
    width: 65%;
    border-right: 1px solid #e8e8e8;
    padding-right: 20px;
    overflow: auto;
    padding-bottom: 20px;
  }

  .report_info {
    width: 35%;
    padding-left: 20px;
    padding-right: 20px;
    overflow: auto;
    padding-bottom: 20px;
  }

  .section-title {
    font-size: 16px;
    font-weight: bold;
    margin-bottom: 10px;
    display: flex;
    align-items: center;

    i {
      width: 4px;
      height: 16px;
      background-color: #1890ff;
      margin-right: 10px;
    }
  }

  .sensitive-card {
    border-radius: 4px;
    border: 1px solid #ecf0f6;
    margin-top: 9px;
    width: 100%;
  }

  .sensitive-card-title {
    height: 28px;
    background: #f6f8fa;
    border-radius: 3px 3px 0px 0px;
    font-size: 14px;
    font-weight: 400;
    color: #151a1d;
    line-height: 28px;
    padding-left: 8px;
  }

  .sensitive-card-content {
    padding: 10px;
  }

  .sensitive-card-content-media {
    display: flex;
    overflow: auto;

    .sensitive-card-content-media-item {
      display: flex;
      flex-direction: column;
      align-items: center;
      cursor: pointer;
    }

    .sensitive-card-content-media-item + .sensitive-card-content-media-item {
      margin-left: 10px;
    }

    .sensitive-card-content-media-item-img {
      width: 100px;
      height: 134px;
      border: 1px solid #e8e8e8;
    }
  }

  .sensitive-card-word-content {
    display: flex;
    flex-wrap: wrap;
  }

  .sensitive-card-word {
    margin-right: 8px;
    border-radius: 15px;
    font-size: 14px;
    font-weight: 400;
    line-height: 20px;
    padding: 5px 12px;
    margin-top: 8px;
    color: #ff4c4c;
    background: #fff2f0;
  }
}

.reporter-item {
  white-space: pre-wrap;
}
