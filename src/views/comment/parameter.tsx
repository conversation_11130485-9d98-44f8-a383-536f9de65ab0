import React, { useState, useEffect, useMemo, memo } from 'react';
import { releaseListApi as api } from '@app/api';
import { useDispatch } from 'react-redux';
import { setMenuHook } from '@app/utils/utils';
import { Row, Col } from 'antd';
import { getCrumb } from '@app/utils/utils';

export default function parameter(props: any) {
  const dispatch = useDispatch();
  const [url, setUrl] = useState('');

  useEffect(() => {
    setMenuHook(dispatch, props);
    api
      .getCommentSystemUrl({path: '/#/parameter'})
      .then((r: any) => {
        const originalUrl = r.data.url;
        // let newUrl = 'http://localhost:8888/#/parameter';
        
        // // 从原始URL中提取access_token
        // const tokenMatch = originalUrl.match(/access_token=([^&]*)/);
        // if (tokenMatch && tokenMatch[1]) {
        //   // 将access_token拼接到新URL
        //   newUrl = `${newUrl}?access_token=${tokenMatch[1]}`;
        // }
        
        // // 使用新的URL替换原始URL
        // r.data.url = newUrl;
        setUrl(originalUrl);
      })
      .catch(() => {
        
      });
  }, []);

  return url ? (
    <>
      <Row className="layout-infobar">
        <Col span={12}>
        </Col>
        <Col span={12} className="layout-breadcrumb">
          {getCrumb(props.breadCrumb)}
        </Col>
      </Row>
      <div className="operations" style={{ width: '100%', height: '100%', marginTop: 16, border: '0' }}>
        <iframe src={url} style={{ width: '100%', height: '100%', border: '0' }}></iframe>
      </div>
    </>
  ) : (
    <></>
  );
}
