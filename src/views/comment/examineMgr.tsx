import React, { useState, useEffect, useMemo, memo } from 'react';
import { releaseListApi as api } from '@app/api';
import { useDispatch } from 'react-redux';
import { setMenuHook } from '@app/utils/utils';
import { Row, Col, Button } from 'antd';
import { getCrumb } from '@app/utils/utils';

export default function ExamineMgr(props: any) {
  const dispatch = useDispatch();
  const [url, setUrl] = useState('');

  useEffect(() => {
    setMenuHook(dispatch, props);
    api
      .getCommentSystemUrl({ path: '/#/regularAudit' })
      .then((r: any) => {
        const originalUrl = r.data.url;
        let newUrl = 'http://localhost:8888/#/regularAudit';
        const tokenMatch = originalUrl.match(/access_token=([^&]*)/);
        if (tokenMatch && tokenMatch[1]) {
          // 将access_token拼接到新URL
          newUrl = `${newUrl}?access_token=${tokenMatch[1]}`;
        }

        // 检查地址栏是否包含id参数
        const urlParams = new URLSearchParams(window.location.search);
        const id = urlParams.get('id');
        console.log('id', id);
        // 如果存在id参数，则拼接到URL后面
        if (id) {
          // 判断原始URL是否已经包含参数
          const separator = originalUrl.includes('?') ? '&' : '?';
          setUrl(`${originalUrl}${separator}id=${id}`);
        } else {
          setUrl(originalUrl);
        }
      })
      .catch(() => {});
  }, []);

  return url ? (
    <>
      <Row className="layout-infobar">
        <Col span={12}>
        </Col>
        <Col span={12} className="layout-breadcrumb">
          {getCrumb(props.breadCrumb)}
        </Col>
      </Row>
      <div className="operations" style={{ width: '100%', height: '100%', marginTop: 16, border: '0' }}>
        <iframe src={url} style={{ width: '100%', height: '100%', border: '0' }}></iframe>
      </div>
    </>
  ) : (
    <></>
  );
}
