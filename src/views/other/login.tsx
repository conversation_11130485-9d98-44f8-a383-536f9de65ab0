import { setConfig } from '@action/config';
import { systemApi as api } from '@app/api';
import { ICommonProps, IResLoginRes } from '@app/types';
import { connectSession as connect } from '@app/utils/connect';
import { message } from 'antd';
import React from 'react';
import { withRouter, RouteComponentProps } from 'react-router';

import { setSession } from '@app/action/session';
import { searchToObject } from '@app/utils/utils.tsx';

import './index.scss';

interface LoginState {
  readonly trsidsssosessionid: string;
  readonly gSessionId: string;
  readonly accessToken: string;
}

class Login extends React.Component<RouteComponentProps & ICommonProps, LoginState> {
  constructor(props: any) {
    super(props);
    const p = searchToObject();
    this.state = {
      trsidsssosessionid: p.trsidsssosessionid,
      gSessionId: p['com.trs.idm.gSessionId'] || '',
      accessToken: p.accessToken,
    };
  }

  componentDidMount() {
    this.props.dispatch(setConfig({ openKeys: [], selectKeys: [] }));
    this.handleLogin();
  }

  handleLogin() {
    const { trsidsssosessionid, gSessionId, accessToken } = this.state;
    const params = !!accessToken ? { accessToken } : { trsidsssosessionid, 'com.trs.idm.gSessionId': gSessionId }
    api
      .login(params)
      .then((r: IResLoginRes) => {
        if (r.data.permissions.length === 0) {
          message.error(
            '抱歉，您没有该页面权限，可联系读嘉运维管理人员（电话：85310049）开通权限。'
          );
          return;
        }
        this.props.dispatch(setSession(r.data));
        setTimeout(() => {
          this.props.history.push('/view/dashboard');
        }, 1000);
      })
      .catch();
  }

  render() {
    const height = document.body.clientHeight;
    const top = height / 2;
    return (
      <div id="zjxw-login">
        <div className="login-frame" style={{ marginTop: top }}>
          正在登录读嘉App管理后台...
        </div>
      </div>
    );
  }
}

export default withRouter(connect(Login));
