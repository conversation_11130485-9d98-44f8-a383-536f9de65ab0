/* eslint-disable react/no-array-index-key */
import { setConfig } from '@app/action/config';
import { releaseListApi, systemApi } from '@app/api';
import BaseComponent from '@components/common/baseComponent';
import {
  CommonObject,
  ITableProps,
  ISysChannel,
  IBaseProps,
  IOperationLogRecordData,
  CommonResponse,
  IOperationLogRes,
  IOperationActionData,
} from '@app/types';
import {
  Button,
  Icon,
  Input,
  message,
  Modal,
  Select,
  Timeline,
  Tooltip,
  Radio,
  Row,
  Form,
  Switch,
  InputNumber,
} from 'antd';
import React from 'react';
import { searchToObject } from '@utils/utils';
import _ from 'lodash';

export type IReleaseListState = {
  filter: CommonObject;
  pageInput: CommonObject;
  channel: ISysChannel;
  operateLog: {
    logs: IOperationLogRecordData[];
    visible: boolean;
    scId: number;
    mlfId: number;
    articleTitle: string;
    key: number;
    isRecommend: boolean;
  };
  newOperateLog: {
    visible: boolean;
    key: number;
    record: any;
  };
  fixedCount?: any;
  onCount?: any;
  total: number;
  displayPos?: number;
  positionId?: number;
  PVForm: {
    visible: boolean;
    key: number;
    article_id: number;
    type: 0 | 1;
    show_pv: 0 | 1;
    base_pv: number;
    min_factor: number;
    max_factor: number;
  };
  RecommendVideoForm: {
    show: boolean;
    key: number;
    id: number;
    title: string;
    data: any;
  };
  ActiviryForm: {
    show: boolean;
    key: number;
    articleId: any;
    activityId: any;
  };
  LiveTypeForm: {
    visible: boolean; //内容连接弹窗是否显示
    key: number; //唯一值
    type: any;
    id: any;
  };
  LuckBagForm: {
    visible: boolean; //内容连接弹窗是否显示
    key: number; //唯一值
    channel_id: any; //channel_id频道id
    article_id: any; //稿件id
    luckBagList: any;
  };
  AnswerRewardForm: {
    visible: boolean; //内容连接弹窗是否显示
    key: number; //唯一值
    channel_id: any; //channel_id频道id
    article_id: any; //稿件id
    answerList: any;
  };
  LivePlayForm: {
    visible: boolean; //内容连接弹窗是否显示
    key: number; //唯一值
    channel_id: any; //channel_id频道id
    article_id: any; //稿件id
    id: any;
    content: any;
    status: number;
  };
  FloatingForm: {
    visible: boolean; //内容连接弹窗是否显示
    key: number; //唯一值
    channel_id: any; //channel_id频道id
    article_id: any; //稿件id
    id: any;
    shrink_img_url: any;
    expand_img_url: any;
    url: any;
    status: number;
  };
  HideForm: {
    visibleShow: boolean; //内容连接弹窗是否显示
    key: number; //唯一值
    visible: boolean;
    id: any;
    start_show_time: any;
    end_show_time: any;
    is_timing: any;
  };
  PushChannelRecordForm: {
    show: boolean; //推至频道签发记录弹窗是否显示
    key: number;
    record: any;
  };
  PushChannelDrawerForm: {
    show: boolean;
    key: number;
    record: any;
  };
  ContentRecommendDrawerForm: {
    show: boolean;
    key: number;
    record: any;
  };
  RelatedServiceForm: {
    show: boolean;
    checked: boolean;
    key: number;
    articleId: null;
    activityId: null;
  };
  activityList: any;
  showActivity: any;
  selectValue: any;
  showDragSorter: boolean;
  toChannelList: any[];
  originToChannelList?: any[];
  toChannelCheckedList: any[];
  pageType?: string;
  openDatePickerPanel?: boolean;
  readSortBaseDate?: any;
  YaYunBannerDrawerShow: boolean;
  loopArticleDrawerShow: boolean;
  batchSortDialog: {
    visible: boolean;
    key?: any;
    data?: any;
  };
};

export type INewsBaseProps<
  TMatch = {},
  TTableContent = CommonObject,
  TTableAllData = CommonObject
> = IBaseProps<ITableProps<TTableContent, TTableAllData>, TMatch>;

/*
 ** @params T: for Router Match Props
 ** @params TTableContent: for Table List Records Content
 ** @params TTableAllData: for API Response Content
 */
class NewsBaseComponent<
  TMatch = {},
  TTableContent = CommonObject,
  TTableAllData = CommonObject,
  TState = {}
> extends BaseComponent<
  ITableProps<TTableContent, TTableAllData>,
  IReleaseListState & TState,
  TMatch
> {
  getData = (filter: CommonObject = this.getFilter()) => {
    console.error('YOU HAVE TO IMPLEMENT getData FUNCTION IN NewsBaseComponent');
  };

  refreshFilter = () => {
    const { pageInput } = this.state;
    const filter: any = {
      ...pageInput,
      keyword: pageInput.keyword,
    };
    if (pageInput.keyword) {
      filter.to_channel_ids = '';
    }
    this.setState({ filter }, () => {
      this.getData();
    });
  };

  handleArticleClick = (
    record: any,
    type: 'mlf_edit_url' | 'mlf_detail_url',
    channelID?: string
  ) => {
    if (this.state.channel.code == 'xxqg' && record.doc_type != -1) {
      window.open(record.url, '_blank');
    } else if (record.doc_type === -1) {
      this.toggleContentRecommendDrawerState(true, record);
    } else {
      this.toMlf(record.id, type, record, channelID);
    }
  };

  unlockArticle = (id: any, record: any, channelID?: string) => {
    releaseListApi
      .articleUnlock({ id })
      .then(() => {
        this.toMlf(id, 'mlf_edit_url', record, channelID);
      })
      .catch();
  };

  toMlf = (
    id: number,
    type: 'mlf_edit_url' | 'mlf_detail_url',
    record?: any,
    channelID?: string
  ) => {
    releaseListApi
      .toMlf(type, { id })
      .then((r: any) => {
        window.open(r.data.url);
      })
      .catch((error) => {
        if (type == 'mlf_edit_url' && error.code == 35023) {
          const res = error.message?.slice(6, error.message.length - 1) ?? '';
          const hasPerm = res.endsWith(':true');
          const editor = res.slice(0, res.length - (hasPerm ? 5 : 6));
          const title = record?.list_title?.substring(0, 10) ?? '';
          const ellipsis = record?.list_title?.length > 10 ? '…' : '';
          if (hasPerm) {
            // 有权限
            Modal.confirm({
              title: `稿件【${title}${ellipsis}】正被【${editor}】编辑，处于锁定状态，解锁打开编辑页？`,
              content: (
                <span style={{ color: 'red' }}>
                  解锁后可与他人共同编辑，稿件以最后保存结果为准。
                </span>
              ),
              okText: '解锁',
              cancelText: '取消',
              onOk: () => {
                this.unlockArticle(id, record, channelID);
              },
            });
          } else {
            // 没有权限
            Modal.confirm({
              title: `稿件【${title}${ellipsis}】正被【${editor}】编辑，处于锁定状态，解锁打开编辑页？`,
              content: <span style={{ color: 'red' }}>您暂无权限解锁，请等待其完成编辑。</span>,
              cancelButtonProps: { style: { display: 'none' } },
              okText: '知道了',
            });
          }
        }
      });
  };

  toCommentSystem = (id: number) => {
    const w = window.open();
    releaseListApi
      .getCommentSystemUrl({ article_id: id })
      .then((r: any) => {
        if (w) {
          w.location = r.data.url;
        } else {
          message.error('浏览器可能拦截了新页面');
        }
      })
      .catch(() => {
        // eslint-disable-next-line @typescript-eslint/no-unused-expressions
        w && w.close();
      });
  };

  getFilter = (filter = this.state.filter) => {
    const result: CommonObject = {};
    Object.keys(filter).forEach((k: string) => {
      if (filter[k]) {
        result[k] = filter[k];
      }
    });
    return result;
  };

  getOperateLog = (id: number, mid: number, title: string, record: any) => {
    if (
      this.props.session.permissions.indexOf(
        `channel_article:${this.state.channel.id}:view_log`
      ) === -1
    ) {
      return;
    }
    const isRecommend = record && record.doc_type === -1;
    releaseListApi[isRecommend ? 'getRecommendOperateLog' : 'getOperateLog']({ id })
      .then((r: CommonResponse<IOperationLogRes>) => {
        this.setState({
          operateLog: {
            isRecommend,
            visible: true,
            scId: id,
            mlfId: mid,
            articleTitle: title,
            logs: r.data.logs,
            key: Date.now(),
          } as any,
        });
      })
      .catch();
  };

  hideOperateLog = () => {
    this.setState({
      operateLog: { ...this.state.operateLog, visible: false },
    });
  };

  normalRevokeNews = (
    id: string | number,
    title: string,
    isDelete: boolean = false,
    record: any
  ) => {
    const WAIT_TIME = 1000;
    // const toChannel = record.to_article_list
    //   ?.filter((item: any) => {
    //     return item.pushed == 2;
    //   })
    //   ?.map((item: any) => {
    //     return `${item.channel_name}频道`;
    //   })
    //   ?.join('、');

    Modal.confirm({
      title: <p>{`确认${isDelete ? '删除推荐位' : '取消签发'}《${title}》？`}</p>,
      content: isDelete ? (
        ''
      ) : (
        <p>
          {record.real_metadata_id > 0 && record.original_id == 0 && record.top_push > 0
            ? `该稿件已被头条频道使用。取消签发后，头条频道稿将同步取消签发，请谨慎操作`
            : record.real_metadata_id > 0 &&
              record.original_id == 0 &&
              record.top_push == 0 &&
              record.multi_channel_count > 0
            ? '取消签发后，该稿件和多签频道的稿子都会被同步取消签发，请谨慎操作'
            : record.real_metadata_id == 0 && record.original_id > 0
            ? `取消签发操作，仅将${this.state.channel.name}频道内的该稿件取消签发，不影响源稿件`
            : '请谨慎操作'}
        </p>
      ),
      onOk: () => {
        this.props.dispatch(setConfig({ loading: true }));
        releaseListApi
          .revokeNews({ id })
          .then(() => {
            message.success('操作成功');
            setTimeout(() => {
              this.getData();
              this.props.dispatch(setConfig({ loading: false }));
            }, WAIT_TIME);
          })
          .catch(() => {
            this.props.dispatch(setConfig({ loading: false }));
          });
      },
    });
  };

  multiChannelConfig = (record: any, show: boolean = true) => {
    // 多频道展示
    this.setState({ PushChannelDrawerForm: { ...this.state.PushChannelDrawerForm, show, record } });
  };

  revokeNews = (id: string | number, title: string, record: any = {}) => {
    const WAIT_TIME = 1000;
    let modal: any;
    let isAll = false;
    const radioChange = (e: any) => {
      isAll = e.target.value;
      modal.update({
        content: (
          <div>
            <Radio.Group buttonStyle="solid" value={isAll} onChange={radioChange}>
              <Radio.Button value={false}>仅撤{this.state.channel.name}</Radio.Button>
              <Radio.Button value={true}>一键全撤</Radio.Button>
            </Radio.Group>
            <Row style={{ marginTop: 8 }}>
              {isAll ? (
                <p>取消签发操作会将头条频道内的该稿件和源稿件一起取消签发</p>
              ) : (
                <p>取消签发操作，仅将头条频道内的该稿件取消签发，不影响源稿件</p>
              )}
            </Row>
          </div>
        ),
      });
    };
    if (record.real_metadata_id > 0 && record.original_id > 0) {
      modal = Modal.confirm({
        title: <p>确认取消签发《{title}》？</p>,
        content: (
          <div>
            <Radio.Group buttonStyle="solid" value={isAll} onChange={radioChange}>
              <Radio.Button value={false}>仅撤头条</Radio.Button>
              <Radio.Button value={true}>一键全撤</Radio.Button>
            </Radio.Group>
            <Row style={{ marginTop: 8 }}>
              {isAll ? (
                <p>取消签发操作会将头条频道内的该稿件和源稿件一起取消签发</p>
              ) : (
                <p>取消签发操作，仅将头条频道内的该稿件取消签发，不影响源稿件</p>
              )}
            </Row>
          </div>
        ),
        onOk: (closeFunc: Function) => {
          this.props.dispatch(setConfig({ loading: true }));
          releaseListApi
            .revokeNews({ id, isAll })
            .then(() => {
              message.success('操作成功');
              setTimeout(() => {
                this.getData();
                this.props.dispatch(setConfig({ loading: false }));
                closeFunc();
              }, WAIT_TIME);
            })
            .catch(() => {
              this.props.dispatch(setConfig({ loading: false }));
            });
        },
      });
    } else {
      this.normalRevokeNews(id, title, false, record);
    }
  };

  withoutRevokeNews = (record: any) => {
    Modal.confirm({
      title: '将该稿件从所有外部平台撤稿？',
      icon: <Icon type="info-circle" />,
      content: '稿件在App内保留已签发状态',
      onOk: () => {
        this.props.dispatch(setConfig({ loading: true }));
        releaseListApi
          .withoutRevokeNews({ id: record.id, sign_type: 2 })
          .then(() => {
            message.success('已提交撤稿');
            this.props.dispatch(setConfig({ loading: false }));
          })
          .catch(() => {
            this.props.dispatch(setConfig({ loading: false }));
          });
      },
    });
  };

  pushTop = (id: number, channel_ids: string, callback?: (success: boolean) => void) => {
    const WAIT_TIME = 1000;
    this.props.dispatch(setConfig({ loading: true }));
    releaseListApi
      .pushTop({ id, channel_ids })
      .then(() => {
        message.success('操作成功');
        setTimeout(() => {
          if (callback) {
            callback(true);
          }
          this.getData();
          this.props.dispatch(setConfig({ loading: false }));
        }, WAIT_TIME);
      })
      .catch(() => {
        this.props.dispatch(setConfig({ loading: false }));
        if (callback) {
          callback(false);
        }
      });
  };

  togglePushChannelRecordModalState = (show: boolean, record: any) => {
    this.setState({ PushChannelRecordForm: { ...this.state.PushChannelRecordForm, show, record } });
  };

  togglePushChannelDrawerState = (show: boolean, record: any) => {
    // 推头条
    this.props.dispatch(setConfig({ loading: true }));
    releaseListApi
      .pushTop({ id: record.id })
      .then(() => {
        message.success('操作成功');
        this.getData();
        this.props.dispatch(setConfig({ loading: false }));
      })
      .catch(() => {
        this.props.dispatch(setConfig({ loading: false }));
      });
  };

  toggleContentRecommendDrawerState = (show: boolean, record?: any) => {
    const data = { ...this.state.ContentRecommendDrawerForm, show };
    if (record) {
      data.record = record;
    }
    this.setState({ ContentRecommendDrawerForm: data });
    if (!show) {
      setTimeout(() => {
        data.record = null;
        this.setState({ ContentRecommendDrawerForm: data });
      }, 500);
    }
  };

  handleRecommendCreate = (val: any) => {
    this.toggleContentRecommendDrawerState(true, { _createRecommendCode: val });
  };

  sourceChannelInfo = (record: any) => {
    return (
      <div style={{ maxWidth: '600px' }}>
        <div>
          <span>源稿件标题：</span>
          <span>{record.source_list_title}</span>
        </div>
        <div>
          <span>源稿件频道：</span>
          <span>{record.source_channel_name}</span>
        </div>
        <div>
          <span>源稿件ID：</span>
          <span>{record.original_id}</span>
        </div>
      </div>
    );
  };

  mapToChannelListTreeData = (channel_list: any = [], channel_id: string) => {
    return channel_list.map((item: any) => {
      return {
        title: item.name,
        value: item.id,
        key: item.id,
        children: this.mapToChannelListTreeData(item.children, channel_id),
        disabled: channel_id === item.id || (item.children && item.children.length > 0),
      };
    });
  };

  getChannelList = () => {
    const channel_id = searchToObject().channel_id;
    systemApi
      .getAllChannelsTree()
      .then((r: any) => {
        const {
          data: { channel_list = [] },
        } = r;
        const originToChannelList = channel_list.filter(
          (item: any) => item.name !== '潮客' && item.name !== '潮鸣号'
        );
        this.setState({
          originToChannelList,
          toChannelList: this.mapToChannelListTreeData(originToChannelList, channel_id),
        });
      })
      .catch(() => {});
  };

  searchInputChange(
    key: string,
    e: React.FormEvent<HTMLInputElement> | null,
    value: string | number = ''
  ) {
    this.setState({
      pageInput: { ...this.state.pageInput, [key]: e ? e.currentTarget.value : value },
    });
  }

  copySuccess = () => {
    message.success('链接已复制');
  };

  copyFail = () => {
    message.error('复制失败');
  };

  handleKey = (e: { which: number }) => {
    const { pageInput, filter } = this.state;
    if (e.which === 13) {
      const clearStatus: any = {
        pageInput: { ...pageInput, current: 1, sort_asc: -1, sort_by: 0 },
      };
      if (pageInput.keyword) {
        clearStatus.toChannelCheckedList = [];
      }
      this.setState(clearStatus, () => this.refreshFilter());
    }
  };

  getPVDetail = (articleId: number, type: 0 | 1 = 0) => {
    this.props.dispatch(setConfig({ loading: true }));
    releaseListApi
      .getArticlePVDetail({
        article_id: articleId,
        type,
      })
      .then((res: any) => {
        this.props.dispatch(setConfig({ loading: false }));
        this.setState({
          PVForm: {
            visible: true,
            key: Date.now(),
            type,
            ...res.data.detail,
          },
        });
      })
      .catch(() => {
        this.props.dispatch(setConfig({ loading: false }));
      });
  };

  renderOperateLog = () => {
    const log = this.state.operateLog;
    const getOpinion = (opinion: string) => (
      <p>
        送审意见：
        <br />
        {opinion}
      </p>
    );
    return (
      <Modal
        visible={log.visible}
        title="操作日志"
        key={log.key}
        cancelText={null}
        onCancel={this.hideOperateLog}
        onOk={this.hideOperateLog}
      >
        <p>
          新闻ID：{log.scId}&emsp;&emsp;&emsp;&emsp;
          {log.isRecommend ? '' : `天目蓝云ID：${log.mlfId}`}
        </p>
        <p>标题：{log.articleTitle}</p>
        <br />
        <div>
          <Timeline>
            {log.logs.map((v: any, i: number) => [
              <Timeline.Item className="timeline-dot-big" data-show={v.date} key={`time${i}`}>
                &nbsp;
              </Timeline.Item>,
              v.actions.map((action: IOperationActionData, index: number) => (
                <Timeline.Item
                  className="timeline-dot"
                  data-show={action.time}
                  key={`time${i}-action${index}`}
                >
                  {action.user}&emsp;&emsp;{action.action}&emsp;&emsp;
                  {action.opinion && (
                    <Tooltip title={getOpinion(action.opinion)}>
                      <a>送审意见</a>
                    </Tooltip>
                  )}
                </Timeline.Item>
              )),
            ])}
          </Timeline>
        </div>
      </Modal>
    );
  };

  renderSearch = () => {
    const { pageInput } = this.state;
    const text = (
      <span>
        <p>1）按照署名作者搜索：输入关键词与稿件中的“署名作者”字段匹配，支持模糊搜索。</p>
        <p>2）按照稿件来源搜索：输入关键词与稿件中的“稿件来源”字段匹配，支持模糊搜索。</p>
      </span>
    );
    return (
      <div className="title-right-col">
        <Tooltip placement="bottom" title={text} arrowPointAtCenter={true}>
          <Icon type="question-circle" />
        </Tooltip>
        <Select
          value={pageInput.search_type.toString()}
          style={{ width: 150, marginRight: 8, marginLeft: 8 }}
          onChange={this.searchInputChange.bind(this, 'search_type', null)}
        >
          <Select.Option value="1">搜索稿件ID</Select.Option>
          <Select.Option value="2">搜索天目蓝云ID</Select.Option>
          <Select.Option value="3">搜索标题</Select.Option>
          <Select.Option value="8">搜索正文</Select.Option>
          <Select.Option value="4">搜索发稿人</Select.Option>
          <Select.Option value="5">搜索署名作者</Select.Option>
          <Select.Option value="6">搜索稿件来源</Select.Option>
          <Select.Option value="9">搜索标题或正文</Select.Option>
        </Select>
        <Input
          style={{ width: 155, marginRight: 8 }}
          onKeyPress={this.handleKey}
          value={pageInput.keyword}
          placeholder="请输入搜索内容"
          onChange={this.searchInputChange.bind(this, 'keyword')}
        />
        <Button onClick={this.handleKey.bind(this, { which: 13 })}>
          <Icon type="search" />
          搜索
        </Button>
      </div>
    );
  };

  renderAltPV = () => {
    const { PVForm } = this.state;
    const setPVForm = (key: keyof IReleaseListState['PVForm'], value: any) => {
      this.setState({
        PVForm: {
          ...PVForm,
          [key]: value,
        },
      });
    };
    const updatePV = () => {
      releaseListApi
        .updateArticlePV({
          article_id: PVForm.article_id,
          type: PVForm.type,
          show_pv: PVForm.show_pv,
          base_pv: PVForm.base_pv,
          min_factor: PVForm.min_factor,
          max_factor: PVForm.max_factor,
        })
        .then(() => {
          message.success('操作成功');
          setPVForm('visible', false);
        });
    };
    return (
      <Modal
        visible={PVForm.visible}
        key={PVForm.key}
        title="直播观看人数配置"
        onOk={updatePV}
        onCancel={() => setPVForm('visible', false)}
      >
        <Form labelCol={{ span: 6 }} wrapperCol={{ span: 16 }}>
          <Form.Item label="显示观看人数">
            <Switch
              checked={PVForm.show_pv === 1}
              onChange={(checked) => setPVForm('show_pv', checked ? 1 : 0)}
            />
          </Form.Item>
          <Form.Item label="观看基数">
            <InputNumber
              min={0}
              value={PVForm.base_pv}
              step={1}
              onChange={(e: any) => setPVForm('base_pv', parseInt(e, 10) || 0)}
            />
          </Form.Item>
          <Form.Item label="观看系数">
            <InputNumber
              min={0}
              max={PVForm.max_factor}
              value={PVForm.min_factor}
              step={1}
              onChange={(e: any) => setPVForm('min_factor', parseInt(e, 10) || 0)}
            />
            &emsp;&emsp;~&emsp;&emsp;
            <InputNumber
              min={PVForm.min_factor}
              step={1}
              value={PVForm.max_factor}
              onChange={(e: any) => setPVForm('max_factor', parseInt(e, 10) || PVForm.min_factor)}
            />
          </Form.Item>
        </Form>
        <div style={{ color: 'red' }}>
          <p>注意：</p>
          <p>
            1. 客户端的直播观看人数=直播观看基数+观看系数（最小系数与最大系数之间随机）*真实阅读数
          </p>
          <p>2. 观看人数不会影响稿件本身的阅读数等</p>
        </div>
      </Modal>
    );
  };
  aboutAct = (id: any) => {
    releaseListApi.getActList({}).then((res: any) => {
      this.setState({
        activityList: res.data.list,
      });
    });
    releaseListApi.getReadyAct({ articleId: id }).then((res: any) => {
      if (res.data.data) {
        const { activity_id, article_id } = res.data.data;
        let arr = [];
        arr.push(res.data.data);
        this.setState({
          ActiviryForm: {
            ...this.state.ActiviryForm,
            articleId: article_id,
            activityId: activity_id,
            show: true,
          },
          showActivity: arr,
        });
      } else {
        this.setState({
          ActiviryForm: {
            ...this.state.ActiviryForm,
            articleId: id,
            show: true,
          },
          showActivity: [],
        });
      }
    });
  };
}

export default NewsBaseComponent;
