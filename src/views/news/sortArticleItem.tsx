import React from 'react';
import './batch_sort.scss';
import { reportTypeMap } from '@app/utils/utils';
import qs from 'querystring';

const ArticleItemWrapper = (props: any) => {
  return <div className="item-wrapper">{props.children}</div>;
};

const DefaultRecommend = (props: any) => {
  return <div className="default-recommed">{props.title || '此处为推荐位'}</div>;
};

export function formatDuration(time: any) {
  const sec = (time % 60).toString().padStart(2, '0');
  const min = (time / 60 >= 60 ? Math.floor(time / 60) % 60 : Math.floor(time / 60))
    .toString()
    .padStart(2, '0');
  if (time / 60 >= 60) {
    return `${Math.floor(time / 3600)
      .toString()
      .padStart(2, '0')}:${min}:${sec}`;
  }
  return `${min}:${sec}`;
}

// 直播状态标签
function LiveStatusTag({ live_notice, live_status, showLiveNotice = true }: any) {
  return (
    <div className={`live_status ${`live_status_${live_status}`}`}>
      <span className="status_label">{['回放', '直播中', '预告'][live_status]}</span>
    </div>
  );
}

function getHeightByWidthAndScale(width: any, defaultHeight: any, scale: any) {
  let height = typeof defaultHeight === 'string' ? defaultHeight : `${defaultHeight}px`;
  if (!scale) {
    return height;
  }
  const scaleComponents = scale.split(':');
  if (scaleComponents.length >= 2) {
    const ratio = parseInt(scaleComponents[1]) / parseInt(scaleComponents[0]);
    if (isFinite(ratio)) {
      height = `${width * ratio}px`;
    }
  }
  return height;
}

function RecommendTitle({ recommend_widget, bottomSpace, style = {} }: any) {
  const { title, title_show, title_style, title_pic } = recommend_widget;
  return (
    title_show && (
      <div className="recommend_title" style={{ marginBottom: bottomSpace || 0, ...style }}>
        <div>
          {title_style === 0 && <i></i>}
          {title_style >= 1 && (
            <img
              className={title_style === 2 ? 'recommend_title_pic' : 'recommend_title_only_pic'}
              src={title_pic}
            />
          )}
          {title_style !== 1 ? title : ''}
        </div>
        {/* {showMore && <span onClick={moreHandler}>更多</span>} */}
      </div>
    )
  );
}

export const OnlyWord = (props: any) => {
  const article = props.article;
  return (
    <ArticleItemWrapper>
      <div className="title">{article.list_title}</div>
    </ArticleItemWrapper>
  );
};

export const ImageText = (props: any) => {
  const article = props.article;
  const { doc_type, live_notice, live_status, video_duration } = article;
  let videoDuration = 0;
  if (doc_type === 9 || doc_type === 10) {
    videoDuration = video_duration;
  }

  return (
    <ArticleItemWrapper>
      {props.showAvatar && article.account_id && (
        <div className="author">
          <div className="avatar">
            <img className="blank-head" src={article.author_img}></img>
          </div>
          <h6 className="name">{article.account_nick_name}</h6>
        </div>
      )}
      <div className="image-text">
        <div className="img-wrapper">
          <img src={article?.list_pics?.[0]} />
          {doc_type === 8 && (
            <LiveStatusTag live_notice={live_notice} live_status={live_status}></LiveStatusTag>
          )}
          {videoDuration > 0 && (
            <div className="duration">
              <span>{formatDuration(videoDuration)}</span>
            </div>
          )}
        </div>
        <div className="title-wrapper">
          <div className="title">{article.list_title}</div>
        </div>
      </div>
    </ArticleItemWrapper>
  );
};

export const BigPic = (props: any) => {
  const article = props.article;
  const {
    list_title,
    doc_title,
    list_pics,
    list_pic_custom_scale = '16:9',
    doc_type,
    live_notice,
    live_status,
    video_duration,
    hidden_list_title,
  } = article;
  const height = getHeightByWidthAndScale(345, 194, list_pic_custom_scale);
  let videoDuration = 0;
  if (doc_type === 9 || doc_type === 10) {
    videoDuration = video_duration;
  }

  return (
    <ArticleItemWrapper>
      <div className="big-pic">
        <div className="img_wrapper" style={{ height }}>
          <img src={list_pics && list_pics[0]}></img>
          {doc_type === 8 && (
            <LiveStatusTag live_notice={live_notice} live_status={live_status}></LiveStatusTag>
          )}
          {(doc_type === 9 || doc_type === 10) && <div className="play_icon"></div>}
          {videoDuration > 0 && <span className="duration">{formatDuration(videoDuration)}</span>}
        </div>
        {parseInt(hidden_list_title) !== 1 && (
          <div className="detail_wrapper" style={{ marginTop: '8px' }}>
            <div className="title">{list_title || doc_title}</div>
          </div>
        )}
      </div>
    </ArticleItemWrapper>
  );
};

export const ThreePics = (props: any) => {
  const article = props.article;
  const { list_pics, list_title, doc_title, circle } = article;
  const title = list_title || doc_title;
  return (
    <ArticleItemWrapper {...props}>
      <div className="tp_wrapper">
        <div className="imgs_wrapper">
          {list_pics &&
            list_pics
              .slice(0, 3)
              .map((item: any, index: number) => <img key={index} className="img" src={item} />)}
        </div>
        <div className="detail_wrapper">
          <div className="title">{title}</div>
        </div>
      </div>
    </ArticleItemWrapper>
  );
};

const CommunityDynamicItem = (props: any) => {
  const article = props.article;
  const { list_title, doc_title, circle } = article;
  const title = list_title || doc_title;

  const isVertical = (url = '') => {
    if (!url) {
      return true;
    }
    const query: any = qs.parse(url.substring(url.indexOf('?') + 1));
    if (query.height && query.width) {
      return parseInt(query.height) > parseInt(query.width);
    }
    return true;
  };

  const formatDuration = (time: any) => {
    const sec = (time % 60).toString().padStart(2, '0');
    const min = (time / 60 >= 60 ? Math.floor(time / 60) % 60 : Math.floor(time / 60))
      .toString()
      .padStart(2, '0');
    if (time / 60 >= 60) {
      return `${Math.floor(time / 3600)
        .toString()
        .padStart(2, '0')}:${min}:${sec}`;
    }
    return `${min}:${sec}`;
  };

  const parseImgWH = (url = '') => {
    const defaultWH = {
      width: '5rem',
      height: '5rem',
    };
    if (!url) {
      return defaultWH;
    }
    const { width, height }: any = qs.parse(url.substring(url.indexOf('?') + 1));
    const w = parseInt(width);
    const h = parseInt(height);
    if (!w || !h) {
      return defaultWH;
    }
    let scaleW = 0;
    let scaleH = 0;
    const maxValue = 250;
    const minValue = 194;
    if (h >= w) {
      scaleH = Math.max(minValue, Math.min(h, maxValue));
      scaleW = Math.max(minValue, Math.min((w / h) * scaleH, maxValue));
    } else {
      scaleW = Math.max(minValue, Math.min(w, maxValue));
      scaleH = Math.max(minValue, Math.min((h / w) * scaleW, maxValue));
    }
    return {
      width: scaleW,
      height: scaleH,
    };
  };
  const pics = article.list_pics?.slice(0, 3);
  return (
    <ArticleItemWrapper {...props}>
      <div className="community-dynamic-item">
        {article.account_id && (
          <div className="author">
            <div className="avatar">
              <img className="blank-head" src={article.author_img}></img>
            </div>
            <h6 className="name">{article.account_nick_name}</h6>
          </div>
        )}
        <div className={'community_content'}>{article.list_title}</div>
        <div className="community_attachment">
          {article.doc_type == 10 ? (
            <div className={`video ${isVertical(pics[0]) ? 'is_vertical' : ''} blank-bg`}>
              <img src={pics[0]} />
              <div className="play_button"></div>
              {!!article.video_duration && <span>{formatDuration(article.video_duration)}</span>}
            </div>
          ) : (
            pics.length > 0 &&
            (pics.length === 1 ? (
              <div className={`single_img blank-bg`} style={parseImgWH(pics[0])}>
                <img src={pics[0]} />
              </div>
            ) : (
              <div className={`imgs ${pics.length === 4 ? 'four_imgs' : ''}`}>
                {pics.map((url: any, index: any) => (
                  <div key={index} className="blank-bg">
                    <img src={url} />
                    {index === pics.length - 1 && pics !== article.list_pics && (
                      <div className={'img_more'}>+{article.list_pics.length - pics.length}</div>
                    )}
                  </div>
                ))}
              </div>
            ))
          )}
        </div>
      </div>
    </ArticleItemWrapper>
  );
};

const SubjectRecommend = (props: any) => {
  const { article } = props;
  const { list_title, list_tag, article_pic, summary, subject_article_list = [] } = article;
  const articleList = [...subject_article_list];
  return (
    <div>
      <div className="subject-recommend">
        <div className="img-wrapper">
          <img src={article_pic}></img>
        </div>

        <div className="flag">{list_tag || '专题'}</div>

        <div className="title-wrapper">
          <div className="title">{list_title}</div>
          {summary && (
            <div className="desc">
              <div className={`desc_txt`}>{summary}</div>
            </div>
          )}
        </div>

        {/* <div className="article-list">
        {articleList.map((item: any, index: number) => {
          const { list_pics, list_title, timeline, read_total, doc_type } = item;
          // console.log('article', article)
          return (
            <div className="article-item" key={`${item.id}-${index}`}>
              <img src={list_pics?.[0]}></img>
              <div className="subject_item_bd">
                <div className="title">{list_title}</div>
                <div className="subinfo">
                  <span>{timeline}</span>
                </div>
              </div>
            </div>
          );
        })}
      </div> */}
      </div>
      <div>*&nbsp;此为专题卡片样式，为方便排序，后台精简展示</div>
    </div>
  );
};

function SubjectRecommend2Item(props: any) {
  const { article } = props;
  const { list_title, doc_title, doc_type } = article;
  const title = list_title || doc_title;
  return (
    <div className={`hnrow_wrapper ${`doc_${doc_type}`}`}>
      <span>{title}</span>
    </div>
  );
}

const SubjectRecommend2 = (props: any) => {
  const {
    list_title,
    list_tag,
    summary,
    subject_article_list = [],
    list_pics,
    read_total,
    list_pic_custom_scale = '16:9',
    hidden_list_title,
    subject_show_type,
    subject_show_style,
    subject_show_article,
    doc_title,
  } = props.article;
  const articleList = [...subject_article_list];

  const title = list_title || doc_title;
  const height = getHeightByWidthAndScale(345, 194, list_pic_custom_scale);
  let listPic = list_pics && list_pics[0];

  return (
    <ArticleItemWrapper>
      <div className="subject-recommend-2">
        <div className="title">
          {!!list_tag && (
            // {list_tag == '专题' ? 'subject_icon' : 'icon'}
            <span className="icon">{list_tag}</span>
          )}
          <span>{title}</span>
        </div>
        <div className="subject_content">
          <img style={{ height }} src={listPic}></img>
          {/* <div className="article_list">
            {articleList.map((v) => (
              <SubjectRecommend2Item key={v.id} article={v} />
            ))}
          </div> */}
        </div>
        <div>*&nbsp;此为专题1+N样式，为方便排序，后台精简展示</div>
      </div>
    </ArticleItemWrapper>
  );
};

const VideoRecommend = (props: any) => {
  const { article } = props;
  const { recommend_widget } = article;

  return (
    <div className="video-recommend-1">
      <RecommendTitle recommend_widget={recommend_widget} />
      <div className="video-list">
        {recommend_widget.articles.map((item: any, index: any) => {
          const { list_pics, list_title, list_title_without_topic, timeline, doc_type } = item;
          return (
            <div className={'video-item'} key={`${item.id}-${index}`}>
              <img src={list_pics.length > 0 && list_pics[0]} />
              <div className="video_icon"></div>
              <div className="info">
                <div className="title">
                  {doc_type == 10 ? list_title_without_topic : list_title}
                </div>
              </div>
            </div>
          );
        })}
      </div>
    </div>
  );
};

const VideoRecommend2 = (props: any) => {
  const { article } = props;
  const { recommend_widget } = article;

  const articleList = [
    ...recommend_widget.articles.slice(-2),
    ...recommend_widget.articles.slice(0, 3),
  ];

  return (
    <div className="video-recommend-2">
      <RecommendTitle recommend_widget={recommend_widget} bottomSpace={'11px'} />
      <div className="video-list">
        {articleList.map((item, index) => {
          const { list_title, list_title_without_topic, doc_type, quality_type, cert_type } = item;

          const type = -2 + index;
          const opacity = Math.min(0.9, Math.abs(type) * 0.44);

          return (
            <div
              className={'video-item'}
              key={`${item.id}-${index}`}
              style={{
                transform: `translateX(${type * 50}px) scale(${1 - Math.abs(type) * 0.05})`,
                zIndex: 99 - Math.abs(type),
              }}
            >
              <img src={item.list_pics?.[0]}></img>
              <div className="video_icon"></div>
              {/* 底部信息 */}
              <div className="info">
                {/* 标题 */}
                <div className="title">
                  {doc_type == 10 ? list_title_without_topic : list_title}
                </div>
              </div>

              <div
                className="mask"
                style={{
                  opacity,
                }}
              ></div>
            </div>
          );
        })}
      </div>
    </div>
  );
};

const VideoRecommend3 = (props: any) => {
  const { article } = props;
  const { recommend_widget } = article;

  return (
    <div className="video-recommend-3">
      <RecommendTitle recommend_widget={recommend_widget} />
      <div className="video-list">
        {recommend_widget.articles.map((item: any, index: any) => {
          const { list_pics, list_title, list_title_without_topic, timeline, doc_type } = item;
          return (
            <div className={'video-item'} key={`${item.id}-${index}`}>
              <img src={list_pics.length > 0 && list_pics[0]} />
              <div className="video_icon"></div>
              <div className="info">
                <div className="time">{timeline}</div>
                <div className="title">
                  {doc_type == 10 ? list_title_without_topic : list_title}
                </div>
              </div>
            </div>
          );
        })}
      </div>
    </div>
  );
};

const ReadpaperRecommed = (props: any) => {
  const { article } = props;
  const { recommend_widget } = article;

  const { time_type, url, title, morning_slogan, evening_slogan, cover_image, pub_date } =
    recommend_widget.read_paper;
  const dateString = String(pub_date);
  const monthMap: any = {
    1: 'Jan',
    2: 'Feb',
    3: 'Mar',
    4: 'Apr',
    5: 'May',
    6: 'Jun',
    7: 'Jul',
    8: 'Aug',
    9: 'Sept',
    10: 'Oct',
    11: 'Nov',
    12: 'Dec',
  };
  let day;
  let month;
  if (dateString.length == 8) {
    day = dateString.slice(6, 8);
    month = monthMap[parseInt(dateString.slice(4, 6))];
  }
  const slogan = time_type == 1 ? morning_slogan : evening_slogan;

  return (
    <div className="readpaper_wrapper">
      <div className="header">
        <div className="date_wrapper">
          <div className="day">{day}</div>
          <div className="month">{month}</div>
        </div>
        <img
          className="type"
          src={time_type == 1 ? '/assets/monring_post_icon.png' : '/assets/evening_post_icon.png'}
        ></img>
        {!!slogan && <div className="slogan">&nbsp;|&nbsp;{slogan}</div>}
      </div>
      <div className="cover_wrapper">
        <img className="cover" src={cover_image}></img>
        <div className="mask"></div>
        <div className="title">{title}</div>
      </div>
    </div>
  );
};

const ReadpaperRecommed2 = (props: any) => {
  const { article } = props;
  const {
    recommend_widget: { read_paper, articles },
  } = article;

  const { time_type, url, title, morning_slogan, evening_slogan, cover_image, pub_date } =
    read_paper;
  const dateString = String(pub_date);
  const monthMap: any = {
    1: 'Jan',
    2: 'Feb',
    3: 'Mar',
    4: 'Apr',
    5: 'May',
    6: 'Jun',
    7: 'Jul',
    8: 'Aug',
    9: 'Sept',
    10: 'Oct',
    11: 'Nov',
    12: 'Dec',
  };
  let day;
  let month;
  if (dateString.length == 8) {
    day = dateString.slice(6, 8);
    month = monthMap[parseInt(dateString.slice(4, 6))];
  }
  const slogan = time_type == 1 ? morning_slogan : evening_slogan;

  return (
    <div className="readpaper_wrapper_2">
      <div className="read_paper_content">
        <div className={`${'read_paper_head'} ${time_type == 1 ? 'm_head' : 'e_head'}`}>
          <div className="date_wrapper">
            <span>{day}</span>
            <span>/{month}</span>
          </div>
        </div>
        <div className="paper_articles">
          {Array(Math.ceil((articles?.length || 0) / 2))
            .fill(0)
            .map((v, index) => {
              const article1 = articles[index * 2];
              const article2 = articles[index * 2 + 1];
              return (
                <div key={article1.id} className="paper_articles_item">
                  <div key={1}>
                    <span className="dot"></span>
                    <div className="article-title">{article1.list_title || article1.doc_title}</div>
                  </div>
                  {!!article2 && (
                    <div key={2}>
                      <span className="dot"></span>
                      <div className="article-title">
                        {article2.list_title || article2.doc_title}
                      </div>
                    </div>
                  )}
                </div>
              );
            })}
        </div>
      </div>
    </div>
  );
};

const ReadpaperRecommed3 = (props: any) => {
  const { article } = props;
  const { recommend_widget } = article;

  const { time_type, url, title, morning_slogan, evening_slogan, cover_image, pub_date } =
    recommend_widget.read_paper;
  const dateString = String(pub_date);
  const monthMap: any = {
    1: 'Jan',
    2: 'Feb',
    3: 'Mar',
    4: 'Apr',
    5: 'May',
    6: 'Jun',
    7: 'Jul',
    8: 'Aug',
    9: 'Sept',
    10: 'Oct',
    11: 'Nov',
    12: 'Dec',
  };
  let day;
  let month;
  if (dateString.length == 8) {
    day = dateString.slice(6, 8);
    month = monthMap[parseInt(dateString.slice(4, 6))];
  }

  return (
    <div className="readpaper3_wrapper">
      <div className="read_paper_content">
        <div className={`read_paper_cover ${time_type == 1 ? 'm_logo' : 'e_logo'}`}>
          <div className="date_wrapper">
            <span>{day}</span>
            <span>/{month}</span>
          </div>
        </div>
        <div className="info_wrapper">
          <div className="readpaper_title">{title}</div>
          <div className="date">{`${dateString.slice(0, 4)}-${dateString.slice(
            4,
            6
          )}-${dateString.slice(6, 8)}`}</div>
        </div>
      </div>
    </div>
  );
};

const NewsRecommendCard = (props: any) => {
  const { article } = props;
  const { recommend_widget } = article;
  const { articles, round_carousel } = recommend_widget;
  const articlesGroup = [];
  // 分组转换
  for (let i = 0; i < articles.length; i += 2) {
    const group = [articles[i]];
    if (i + 1 < articles.length) {
      group.push(articles[i + 1]);
    }
    articlesGroup.push(group);
  }

  return (
    <div className="news-recommend-card">
      <RecommendTitle recommend_widget={recommend_widget} />
      <div className="card-list">
        {articlesGroup.map((articles) => (
          <div className="nrc_wrapper" key={articles[0].id}>
            <div className="top">
              <div className="text">{articles[0].list_title}</div>
            </div>
            {articles.length > 1 && (
              <div className="bottom">
                <div className="text">{articles[1].list_title}</div>
              </div>
            )}
          </div>
        ))}
      </div>
    </div>
  );
};

const BulletinRecommend = (props: any) => {
  const { article } = props;
  const { recommend_widget } = article;
  const { articles, pic_url, scheme_url } = recommend_widget;

  return (
    <div className="bulletin_recommend">
      <RecommendTitle recommend_widget={recommend_widget} bottomSpace={8} />
      <div className="bulletin_content">
        <div className={'bulletin_logo'}>
          <img
            src={
              pic_url ||
              'https://zjolapp-dev.oss-cn-hangzhou.aliyuncs.com/assets/20250210/1739177658400_67a9bebae9c22e4e2b18b3bc.png?width=440&height=330'
            }
          />
        </div>
        <div className={'bulletin_articles'}>
          <div className={`title multi-ellipsis`}>
            {articles[0].list_title || articles[0].doc_title}
          </div>
        </div>
      </div>
    </div>
  );
};

const NewsRecommendImageText = (props: any) => {
  const { article } = props;
  const { recommend_widget } = article;
  const { articles, round_carousel } = recommend_widget;

  return (
    <div className="news-recommend-imagetext">
      <RecommendTitle recommend_widget={recommend_widget} />
      <div className="card-list">
        {articles.map((item: any) => {
          const { list_pics, list_title } = item;
          return (
            <div className="nrlp_wrapper" key={item.id}>
              <img className="cover" src={list_pics?.[0]} />
              <div className="info">
                <div className="title">{list_title}</div>
              </div>
            </div>
          );
        })}
      </div>
    </div>
  );
};

function HotNewsRecommendOnlyWord(props: any) {
  const { article } = props;
  const { list_title, doc_title, doc_type } = article;
  const title = list_title || doc_title;
  return <div className={`hnrow_wrapper ${`doc_${doc_type}`}`}>{title}</div>;
}

const HotNewsRecommend = (props: any) => {
  const { article } = props;
  const { recommend_widget } = article;
  const { title, articles: elements = [], style, scheme_url: url } = recommend_widget;
  const isOnlyWord = style === 21;

  function ListMoreOne() {
    if (elements.length <= 1) {
      return null;
    }
    return isOnlyWord ? (
      elements.slice(1).map((v: any) => <HotNewsRecommendOnlyWord key={v.id} article={v} />)
    ) : (
      <div>
        {elements
          .slice(1)
          .map((v: any) => getArticleItem(v, v.list_pics && v.list_pics.length > 0 ? 2 : 1))}
      </div>
    );
  }
  return (
    <div className="hnr_wrapper">
      <RecommendTitle recommend_widget={recommend_widget} bottomSpace={8} />
      <div>
        {[elements[0]].map((v) => {
          return <div key={v.id}>{getArticleItem(v)}</div>;
        })}
      </div>
      {/* <ListMoreOne /> */}
      <div style={{ paddingLeft: 15 }}>*此为热点新闻推荐位，为方便排序，后台精简展示。</div>
    </div>
  );
};

const ChaokeCommentsRecommend = (props: any) => {
  const { article } = props;
  const { recommend_widget } = article;

  return (
    <div className="comment-recommend">
      <RecommendTitle
        style={{ paddingTop: '8px', marginTop: 0 }}
        recommend_widget={recommend_widget}
      />
      <div className="comment-list">
        {recommend_widget.comments?.map((comment: any) => {
          const { content, title, expression_url, pic_url } = comment;
          return (
            <div className="ckcr_wrapper" key={comment.comment_id}>
              <div className="comment_wrapper">
                <div
                  className="title"
                  style={{
                    WebkitLineClamp: !!expression_url || !!pic_url ? 2 : 3,
                    whiteSpace: 'pre-wrap',
                  }}
                >
                  {content}
                </div>
                {(!!expression_url || !!pic_url) && <span className="img_tag">[图片]</span>}
                <div className="subinfo">
                  {/* {comment.account_id && <SmallAccount article={comment} />} */}
                  <span className="add_comment">
                    跟评讨论<i></i>
                  </span>
                </div>
              </div>
              <p className="origin_article">
                <span className="tag">原文</span>
                {title}
              </p>
            </div>
          );
        })}
      </div>
    </div>
  );
};

const ColumnRecommendStyle1 = (props: any) => {
  const { article } = props;
  const { recommend_widget } = article;
  const { columns, round_carousel } = recommend_widget;

  return (
    <div className="column-recommend-1">
      <RecommendTitle recommend_widget={recommend_widget} />
      <div className="column-list">
        {columns.map((column: any) => (
          <div className="column-item" key={column.id}>
            {column.name}
          </div>
        ))}
      </div>
    </div>
  );
};

const ColumnRecommendStyle2 = (props: any) => {
  const { article } = props;
  const { recommend_widget } = article;
  const { columns, round_carousel } = recommend_widget;

  return (
    <div className="column-recommend-2">
      <RecommendTitle recommend_widget={recommend_widget} style={{ marginBottom: 10 }} />
      <div className="column-list">
        {columns.map((column: any) => (
          <div className="column-item" key={column.id}>
            <img className="icon_wrapper" src={column.logo_url} />
            <div className="column-title">{column.name}</div>
          </div>
        ))}
      </div>
    </div>
  );
};

const ColumnRecommendStyle3 = (props: any) => {
  const { article } = props;
  const { recommend_widget } = article;
  const { columns, round_carousel } = recommend_widget;

  return (
    <div className="column-recommend-3">
      <RecommendTitle recommend_widget={recommend_widget} />
      <div className="column-list">
        {columns.map((column: any) => (
          <div className="column-item" key={column.id}>
            <div className="info">
              <img className="icon_wrapper" src={column.logo_url}></img>
              <div className="column-title">{column.name}</div>
            </div>
            <p>{column.description}</p>
          </div>
        ))}
      </div>
    </div>
  );
};

const ColumnRecommendStyle4 = (props: any) => {
  const { article } = props;
  const { recommend_widget } = article;
  const { columns, round_carousel } = recommend_widget;

  return (
    <div className="column-recommend-4">
      <RecommendTitle recommend_widget={recommend_widget} />
      <div className="column-list">
        {columns.map((column: any) => (
          <div className="crs4_wrapper" key={column.id}>
            <div className="crs4_content">
              <div className={'crs4_avatar_wrap'}>
                <img className={'icon_wrapper'} src={column.logo_url} />
              </div>
              <div className={'crs4_name'}>{column.name}</div>
              {column.is_studio ? (
                <div className={'crs4_focus_btn'}>+&nbsp;关注</div>
              ) : (
                <div className={'crs4_view_btn'}>查看</div>
              )}
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

const UserRecommend = (props: any) => {
  const { article } = props;
  const { recommend_widget } = article;
  const { users } = recommend_widget;

  return (
    <div className="ur_wrapper">
      <RecommendTitle recommend_widget={recommend_widget} />
      <div className="user_list_wrapper">
        {users.map((user: any) => {
          const { id, author_url, nick_name, reason, author_img, cert_type, quality_type } = user;
          return (
            <div key={id} className="user">
              {/* {user.official_cert_status == 1 && (
                <Icon
                  type="official_certification"
                  className={styles.official_certification}
                ></Icon>
              )} */}
              <div className="head">
                <img src={author_img} />
              </div>
              <div className="nick_name">{nick_name}</div>
              <p className="reason">{reason}</p>
              <div className="focus_wrapper">
                <span className="focus">
                  <i></i>关注
                </span>
              </div>
            </div>
          );
        })}
      </div>
    </div>
  );
};

const UserRecommend2 = (props: any) => {
  const { article } = props;
  const { recommend_widget } = article;
  const { users } = recommend_widget;

  return (
    <div style={{ padding: '11px 0' }}>
      <div className="ur_wrapper_2">
        <RecommendTitle recommend_widget={recommend_widget} bottomSpace={'11px'} />
        <div className="user_list">
          {users.map((user: any) => {
            const {
              id,
              author_url,
              cert_type,
              bg_color,
              quality_type,
              official_cert_status,
              official_cert_info,
            } = user;
            return (
              <div key={id} className="user2_item">
                <div className="info" style={{ backgroundColor: bg_color }}>
                  <h5 className="name">{user.nick_name}</h5>
                  {/* 潮鸣号 */}
                  {cert_type > 0 && user.cert_information && (
                    <div className="cert">
                      {/* <Icon type="cert-white" className={styles.cert_icon}></Icon> */}
                      <span className="cert_text">{user.cert_information}</span>
                    </div>
                  )}
                  {/* 官方认证 */}
                  {official_cert_status == 1 && !!official_cert_info && (
                    <div key={'quality_icon'} className="cert">
                      <span className="cert_text">{official_cert_info}</span>
                    </div>
                  )}
                  {/* 潮客 */}
                  {Boolean(
                    cert_type <= 0 &&
                      quality_type > 0 &&
                      !(official_cert_status == 1 && !!official_cert_info)
                  ) && (
                    <div key={'quality_icon'} className="cert">
                      <span className="cert_text">
                        {quality_type == 1 ? '活跃潮客' : '品质潮客'}
                      </span>
                    </div>
                  )}

                  <div
                    className="reason"
                    style={{
                      WebkitLineClamp:
                        (cert_type > 0 && user.cert_information) ||
                        quality_type > 0 ||
                        (official_cert_status == 1 && !!official_cert_info)
                          ? 1
                          : 2,
                    }}
                  >
                    {user.reason}
                  </div>
                </div>

                <img className={`avatar blank-head`} src={user.author_img}></img>
                <div
                  className="mask"
                  style={{
                    background: `linear-gradient(182deg, ${bg_color}00 53%, ${bg_color}3B 70%, ${bg_color}FF 100%)`,
                  }}
                ></div>

                <div className="focus_wrapper">
                  <span className="focus">关注</span>
                </div>
              </div>
            );
          })}
        </div>
      </div>
    </div>
  );
};

const AdvertiseRecommend = (props: any) => {
  const { article } = props;
  const { recommend_widget } = article;
  const {
    advertisements,
    ad_source,
    ad_corner_show,
    title,
    title_show,
    pic_custom_scale,
    cycle_carousel,
  } = recommend_widget;
  const height = getHeightByWidthAndScale(345, 194, pic_custom_scale);
  const key = Date.now();

  return (
    <div>
      <RecommendTitle recommend_widget={recommend_widget} />
      <div className="advr_wrapper">
        {/* {title_show && <h5>{title}</h5>} */}
        <div className="advr_swiper">
          <div className="ad-list" style={{ height }}>
            {advertisements.map((advertisement: any, index: any) => (
              <div key={index} className="ad-item">
                {advertisement.pic_url && <img src={advertisement.pic_url} />}
              </div>
            ))}
          </div>
          {ad_corner_show && (
            <div className="tag">
              <span>广告</span>
            </div>
          )}
        </div>
        {ad_source && <p>{ad_source}</p>}
      </div>
    </div>
  );
};

const DataRecommend = (props: any) => {
  const { article } = props;
  const { recommend_widget } = article;
  const { url, width, height, jump_enabled, jump_model_url } = recommend_widget;
  const webHeight = getHeightByWidthAndScale(345, 194, `${width}:${height}`);
  return (
    <div className="dr_wrapper">
      <RecommendTitle recommend_widget={recommend_widget} bottomSpace={8} />
      <div className="content">
        <iframe src={url} style={{ height: webHeight }} scrolling="no" />
      </div>
    </div>
  );
};

// 话题样式1推荐位
const TopicStyle1Recommend = (props: any) => {
  const { article } = props;
  const { recommend_widget } = article;
  const { title, topics } = recommend_widget;
  return (
    <div className="topic_style1_wrapper">
      <div className="topic_style1_title">
        <div className="topic_title">{title}</div>
        {/* <i></i> */}
      </div>
      <div className="topic_style1_list">
        {topics.map((item: any) => (
          <div className="topic_item" key={item.id}>
            <div className="item">
              <span>{item.name}</span>
              {item.show_new && <i className="new"></i>}
              {item.show_hot && <i className="prize"></i>}
              {item.rank_logo && <img className="rank" src={item.rank_logo} />}
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

const TopicStyle2Recommend = (props: any) => {
  const { article } = props;
  const { recommend_widget } = article;

  return (
    <div className="topic_style2">
      <RecommendTitle recommend_widget={recommend_widget} />
      <div className="topic_list">
        {recommend_widget.topics.map((topic: any) => {
          const {
            logo_url,
            name,
            like_count_general,
            article_count_general,
            description,
            topic_style = 1,
          } = topic;
          return (
            <div className={`topic_style2_wrapper ${`style${topic_style}`}`}>
              <div className="hd">
                <div className="main_content">
                  <img src={logo_url} />
                  <div className="topic_info">
                    <div className="topic_title">{name}</div>
                    {(article_count_general || like_count_general) && (
                      <div className="subinfo">
                        {article_count_general && (
                          <span>
                            {article_count_general}
                            {topic.type === 1 ? '视频' : '参与'} |{' '}
                          </span>
                        )}
                        {like_count_general && <span>{like_count_general}点赞</span>}
                      </div>
                    )}
                  </div>
                </div>
                {description && (
                  <div className="desc">
                    <div className="desc_txt">{description}</div>
                  </div>
                )}
              </div>
              <div className="bd"></div>
            </div>
          );
        })}
      </div>
    </div>
  );
};

const PicStyle1Recommend = (props: any) => {
  const { article } = props;
  const { recommend_widget } = article;
  const ref_extensions = JSON.parse(recommend_widget.ref_extensions);
  return (
    <div className="pic_recommend">
      <RecommendTitle recommend_widget={recommend_widget} />
      <div className="pic_list">
        {ref_extensions.map((ref_extension: any) => {
          const { pic_url, link_url } = ref_extension;
          return (
            <div className="pic_style1_wrapper">
              <img src={pic_url} />
            </div>
          );
        })}
      </div>
    </div>
  );
};

const PicStyle2Recommend = (props: any) => {
  const { article } = props;
  const { recommend_widget } = article;
  const ref_extensions = JSON.parse(recommend_widget.ref_extensions);
  return (
    <div className="pic_recommend_2">
      <RecommendTitle recommend_widget={recommend_widget} />
      <div className="pic_style2_wrapper">
        <div className="img_wrapper">
          {ref_extensions.map((item: any, index: any) => {
            const { pic_url, link_url } = item;
            return (
              <img
                key={pic_url}
                src={pic_url}
                style={{
                  position: 'absolute',
                  left: 0,
                  top: 0,
                  width: '100%',
                  height: '100%',
                  visibility: index == 0 ? 'visible' : 'hidden',
                }}
              />
            );
          })}
        </div>
        <div className="scrollbar">
          <div className="img_list">
            {ref_extensions.map((item: any, index: any) => {
              const { pic_url } = item;
              return (
                <div key={index} className={`scrollbar_item ${0 === index ? 'active' : ''}`}>
                  <img src={pic_url} />
                </div>
              );
            })}
          </div>
          <div className="pager">
            <span>1</span>
            <span>/{ref_extensions.length}</span>
          </div>
        </div>
      </div>
    </div>
  );
};

const PicStyle3Recommend = (props: any) => {
  const { article } = props;
  const { recommend_widget } = article;
  const ref_extensions = JSON.parse(recommend_widget.ref_extensions);
  return (
    <div className="pic_recommend_style3_wrapper" style={{ padding: '11px 0' }}>
      <RecommendTitle recommend_widget={recommend_widget} bottomSpace={'11px'} />
      <div className="pic_list">
        {ref_extensions.map((ref_extension: any) => {
          const { pic_url, link_url } = ref_extension;
          return (
            <div className="pic_style3_wrapper">
              <img src={pic_url} />
            </div>
          );
        })}
      </div>
    </div>
  );
};

const CommonRecommend = (props: any) => {
  const { article } = props;
  const { recommend_widget } = article;
  const { commons, round_carousel } = recommend_widget;
  return (
    <div className="common_recommend">
      <RecommendTitle recommend_widget={recommend_widget} />
      <div className="common_list">
        {commons.map((common: any) => (
          <div key={common.title} className="crs1_wrapper">
            {common.title}
          </div>
        ))}
      </div>
    </div>
  );
};

const CommonRecommend2 = (props: any) => {
  const { article } = props;
  const { recommend_widget } = article;
  const { commons, round_carousel } = recommend_widget;
  return (
    <div className="common_recommend_2">
      <RecommendTitle style={{ marginBottom: 10 }} recommend_widget={recommend_widget} />
      <div className="common_list">
        {commons.map((common: any) => (
          <div key={common.title} className="crs2_wrapper">
            <img className="icon_wrapper" src={common.pic_url} />
            <div>{common.title}</div>
          </div>
        ))}
      </div>
    </div>
  );
};

const CommonRecommend3 = (props: any) => {
  const { article } = props;
  const { recommend_widget } = article;
  const { commons, round_carousel } = recommend_widget;
  return (
    <div className="common_recommend_3">
      <RecommendTitle style={{ marginBottom: 10 }} recommend_widget={recommend_widget} />
      <div className="common_list">
        {commons.map((common: any) => (
          <div className="crs_common_wrapper" key={common.title}>
            <img className="icon_wrapper" src={common.pic_url} />
            <div
              className="info_wrapper"
              style={{
                background: `linear-gradient(180deg, ${common.bg_color}00 0%, ${common.bg_color}ff 100%)`,
              }}
            >
              <div className="title">{common.title}</div>
              {!!common?.reason?.length && <div className="desc">{common.reason}</div>}
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

const CommunitySmallCardRecommend = (props: any) => {
  const { article } = props;
  const { recommend_widget } = article;
  const { articles } = recommend_widget;
  return (
    <div className="ugc_recommend_1">
      <RecommendTitle recommend_widget={recommend_widget} />
      <div className="article_list">
        {articles.map((common: any) => {
          const { list_pics, url } = common;
          return (
            <div key={common.id} className="cscr_wrapper">
              <div className="info_wrapper">
                {list_pics.length > 0 && (
                  <div className="img_wrapper">
                    <img src={list_pics[0]}></img>
                    {common.doc_type == 10 && <i></i>}
                    {list_pics.length > 1 && <span>{list_pics.length}</span>}
                  </div>
                )}
                <h6>
                  {common.list_title || `来自用户昵称${common.account_nick_name}分享的图片内容`}
                </h6>
              </div>
              <div className="subinfo">
                {common.account_id && (
                  <div className="account">
                    <img src={common.author_img} />
                    <span className="account_name">
                      {common.account_nick_name || common.nick_name}
                    </span>
                  </div>
                )}
              </div>
            </div>
          );
        })}
      </div>
    </div>
  );
};

// 社区大卡推荐位item
function CommunityBigCardItem(props: any) {
  const { article, className, hidden_list_title } = props;
  const imgLength = article.list_pics.length ?? 0;
  const isVideo = article.doc_type == 9 || article.doc_type == 10;

  return (
    <div className={`${'cbc_item'} ${className}`}>
      <div className="cover">
        <img src={article.list_pics?.[0]}></img>
      </div>
      <div className={'info_wrapper'}>
        {/* 标题 */}
        {!hidden_list_title && <h6 className="title">{article.list_title}</h6>}

        {/* 作者信息 */}
        <div className="author">
          <div className="avatar">
            <img className="blank-head" src={article.author_img}></img>
          </div>
          <h6 className="name">{article.account_nick_name}</h6>
        </div>
      </div>

      {/* 播放按钮 */}
      {isVideo && <div className={'play_btn'}></div>}

      {imgLength > 1 && <div className={'img_length'}>{article.list_pics.length}</div>}
    </div>
  );
}

const CommunityBigCardRecommend = (props: any) => {
  const { article } = props;
  const { recommend_widget } = article;
  const { jump_enabled, jump_model_url, title, articles, cycle_carousel } = recommend_widget;
  if (articles.length < 4) {
    return null;
  }
  // 每三个数据为一组 将数据分组
  const articlesGroup = [];
  for (let i = 0; i < articles.length; i += 3) {
    articlesGroup.push(articles.slice(i, i + 3));
  }

  return (
    <div className="ugc_recommend_2">
      <RecommendTitle recommend_widget={recommend_widget} />
      <div className="list">
        {articlesGroup.map((articleTuple, index) => {
          const article1 = articleTuple[0];
          const article2 = articleTuple[1];
          const article3 = articleTuple[2];

          return (
            <div
              className="cbcr_wrapper"
              style={{ width: articleTuple.length > 1 ? '294px' : '194px' }}
            >
              {article1 && <CommunityBigCardItem article={article1} className="item1" />}
              {article2 && (
                <CommunityBigCardItem article={article2} className="item2" hidden_list_title />
              )}
              {article3 && (
                <CommunityBigCardItem article={article3} className="item3" hidden_list_title />
              )}
            </div>
          );
        })}
      </div>
    </div>
  );
};

const Aggregated = (props: any) => {
  const { article } = props;
  const { recommend_widget } = article;
  const forecastList = recommend_widget.groups;
  const list = forecastList && forecastList.length > 1 ? forecastList : [];

  return (
    <div className="live_forecast">
      <div className="content">
        <div className="top">
          {list.map((item: any, index: any) => (
            <div className="top_item" key={item.selected_pic_url}>
              <img src={0 === index ? item.selected_pic_url : item.unselected_pic_url} />
            </div>
          ))}
        </div>
        <div className="bottom">
          <div className="forecast_list">
            {list.map((item: any) => (
              <div
                className="forecast_slide"
                key={item.selected_pic_url}
                style={{
                  padding: `${item.articles?.length === 3 ? '.3rem' : '.2rem'} 0`,
                }}
              >
                {item.articles?.map((forecast: any, index: any) => (
                  <div className="forecast" key={forecast.url + index}>
                    <div className="forecast_disc"></div>
                    <div
                      className={`${
                        item.articles.length === 3 ? 'forecast_content_1' : 'forecast_content'
                      }`}
                    >
                      {forecast.list_title}
                    </div>
                  </div>
                ))}
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

const BooksRanking = (props: any) => {
  const { forecastList, title_pic } = props;
  const list = forecastList && forecastList.length > 0 ? forecastList : [];
  return (
    <div className="book_rank">
      <div className="content">
        <div className="left">
          <img src={title_pic}></img>
        </div>

        <div className="line"></div>

        <div className="right">
          <div className="forecast_list">
            {list.map((forecast: any, index: any) => (
              <div className="forecast" key={index}>
                <div>{forecast.book_name}</div>
                <div>{forecast.author}</div>
                {/*<span>{forecast.timeline}</span>*/}
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

const CityRanking = (props: any) => {
  const { forecastList, title_pic } = props;

  const list = forecastList && forecastList.length > 1 ? forecastList : [];
  return (
    <div className="ranking_wrapper">
      <div className="content">
        <div className="left">
          <img src={title_pic}></img>
        </div>
        <div className="right">
          <div className="forecast_list">
            {list.map((forecast: any, index: any) => (
              <div className="forecast" key={index}>
                <div className="list_title">{forecast.list_title}</div>
                {/*<span>{forecast.timeline}</span>*/}
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

const ReportRecommend = (props: any) => {
  const { article } = props;
  const { recommend_widget } = article;
  const { reports, title_show, title_slogan, round_carousel, article_id } = recommend_widget;

  return (
    <div className="report_wrapper">
      {Boolean(!!title_slogan || title_show) && (
        <div className="header_wrapper">
          <div className="header_title">
            <RecommendTitle
              recommend_widget={recommend_widget}
              style={{
                marginTop: 0,
                padding: 0,
              }}
            />
            <span>{title_slogan}</span>
          </div>
          <div className="next">前往专区</div>
        </div>
      )}

      <div className="report_list">
        {reports.map((report: any) => {
          const tag = reportTypeMap(report.biz_type, '');
          return (
            <div className="report_card">
              <div className="title_wrapper">
                <div className="report_title">
                  {!!tag && <span className={`tag_${report.biz_type}`}>{tag}</span>}
                  {reportTypeMap(report.report_type, '')}&nbsp;|&nbsp;
                  {report.title}
                </div>
              </div>

              <div className={`status_lable ellipsis`}>{report.process_tip}</div>

              <div className="report_status">
                <img src={`/assets/status_${report.status}.png`} />
              </div>
            </div>
          );
        })}
      </div>
    </div>
  );
};

const SubjectLongPic = (props: any) => {
  const { article } = props;
  const { list_title, doc_title, web_subject_list_pics, hidden_list_title } = article;

  const title = list_title || doc_title;
  return (
    <ArticleItemWrapper>
      <div className="slp_wrapper">
        <img src={web_subject_list_pics && web_subject_list_pics[0]} />
        {parseInt(hidden_list_title) !== 1 && (
          <div className="detail_wrapper">
            <div className="title">{title}</div>
          </div>
        )}
      </div>
    </ArticleItemWrapper>
  );
};

const VerticalLiveRecommend = (props: any) => {
  const { article } = props;
  const { recommend_widget } = article;
  const { articles } = recommend_widget;
  if (!articles || articles.length === 0) {
    return null;
  }

  return (
    <div className="vertical_lr_wrapper">
      <RecommendTitle recommend_widget={recommend_widget} />
      {/* <h5>{title}</h5> */}
      <div className={'live_list_wrapper'}>
        {articles.map((article: any) => {
          const { id, custom_list_pic_url, list_title, live_status, live_notice } = article;
          return (
            <div key={id} className="live">
              <div className={'cover'}>
                <img src={custom_list_pic_url}></img>
                <LiveStatusTag live_notice={live_notice} live_status={live_status}></LiveStatusTag>
              </div>
              <h6>{list_title}</h6>
            </div>
          );
        })}
      </div>
    </div>
  );
};

const LiveRecommend = (props: any) => {
  const { article } = props;
  const { recommend_widget } = article;
  const { articles } = recommend_widget;
  if (!articles || articles.length === 0) {
    return null;
  }

  return (
    <div className="lr_wrapper">
      <RecommendTitle recommend_widget={recommend_widget} />
      {/* <h5>{title}</h5> */}
      <div className={'live_list_wrapper'}>
        {articles.map((article: any) => {
          const { id, list_pics, list_title, live_status, live_notice } = article;
          return (
            <div key={id} className="live">
              <div className={'cover'}>
                <img src={list_pics.length > 0 && list_pics[0]}></img>
                <LiveStatusTag live_notice={live_notice} live_status={live_status}></LiveStatusTag>
              </div>
              <h6>{list_title}</h6>
            </div>
          );
        })}
      </div>
    </div>
  );
};

const CommunitySingleRecommend = (props: any) => {
  const { article } = props;
  const { recommend_widget } = article;
  const { articles } = recommend_widget;
  if (articles.length > 0) {
    return getArticleItem(articles[0], 0, true);
  }
  return <DefaultRecommend />;
};

export const getArticleItem = (
  article: any,
  fixedListStyle: number = 0,
  shortVideoUseDynamicItem = false
) => {
  let { doc_type, list_style, subject_display_type } = article;
  if (fixedListStyle) {
    list_style = fixedListStyle;
  } else {
    if (doc_type == -1) {
      const { recommend_widget } = article;
      const { ref_type, style } = recommend_widget;
      switch (ref_type) {
        case 11:
          switch (style) {
            case 0:
              return <VideoRecommend article={article}></VideoRecommend>;
            case 81:
              return <VideoRecommend2 article={article}></VideoRecommend2>;
            case 82:
              return <VideoRecommend3 article={article}></VideoRecommend3>;
            default:
              return null;
          }
          break;
        case 12: {
          switch (style) {
            // case 11: // 新闻汇总推荐位-走马灯轮播
            // return <NewsRecommendMarquee {...recommendWidgetProps} />
            case 12: // 新闻汇总推荐位-卡片轮播
              return <NewsRecommendCard article={article} />;
            case 13: // 新闻汇总推荐位-显示列表图
              return <NewsRecommendImageText article={article} />;
            case 14: // 快讯推荐位
              if (article?.recommend_widget?.articles?.length > 0) {
                return <BulletinRecommend article={article} />;
              }
              return null;
            default:
              return null;
          }
        }
        case 13: {
          // 热点新闻
          return <HotNewsRecommend article={article} />;
        }
        case 14: {
          console.log('xxxxxxxx', style);
          switch (style) {
            case 101:
              return <VerticalLiveRecommend article={article} />;
            default:
              return <LiveRecommend article={article} />;
          }
        }
        case 21: // 潮客潮语
          return <ChaokeCommentsRecommend article={article} />;
        case 22: {
          switch (style) {
            case 31: // 地方号推荐位样式一
              return <ColumnRecommendStyle1 article={article} />;
            case 32: // 地方号推荐位样式二
              return <ColumnRecommendStyle2 article={article} />;
            case 33: // 地方号推荐位样式三
              return <ColumnRecommendStyle3 article={article} />;
            case 34: // 地方号推荐位样式四
              return <ColumnRecommendStyle4 article={article} />;
            default:
              return null;
          }
        }
        case 23: {
          switch (style) {
            case 0:
              return <UserRecommend article={article} />;
            case 91:
              return <UserRecommend2 article={article} />;
            default:
              return null;
          }
        }
        case 24:
          return <AdvertiseRecommend article={article} />;
        case 25:
          return <DataRecommend article={article} />;
        case 26: {
          switch (style) {
            case 0:
              return <TopicStyle1Recommend article={article}></TopicStyle1Recommend>;
            case 51:
              return <TopicStyle2Recommend article={article}></TopicStyle2Recommend>;
            default:
              return null;
          }
        }
        case 27: {
          switch (style) {
            case 41:
              return <CommunitySmallCardRecommend article={article} />;
            case 42:
              return <CommunityBigCardRecommend article={article} />;
            default:
              return null;
          }
        }
        case 28:
          return <CommunitySingleRecommend article={article} />;
        case 29: {
          switch (style) {
            case 0:
              return <PicStyle1Recommend article={article} />;
            case 61:
              return <PicStyle2Recommend article={article} />;
            case 62:
              return <PicStyle3Recommend article={article} />;
            case 63:
              return <DefaultRecommend title="此处为图片推荐位样式四" />;
            default:
              return null;
          }
        }
        case 33: {
          switch (style) {
            case 71:
              return <CommonRecommend article={article} />;
            case 72:
              return <CommonRecommend2 article={article} />;
            case 73:
              return <CommonRecommend3 article={article} />;
            default:
              return null;
          }
        }
        case 34: {
          // 早晚报推荐位
          const read_paper = recommend_widget?.read_paper;
          if (!!read_paper) {
            if (read_paper?.paper_list_type == 2) {
              return <ReadpaperRecommed2 article={article} />;
            } else if (read_paper?.paper_list_type == 3) {
              // 图文样式
              return <ReadpaperRecommed3 article={article}></ReadpaperRecommed3>;
            }
            return <ReadpaperRecommed article={article} />;
          } else {
            return null;
          }
        }
        // 聚合推荐位
        case 35: {
          return <Aggregated article={article} />;
        }
        // 榜单推荐位
        case 36: {
          if (recommend_widget.ref_ids_type == 1) {
            if (!recommend_widget.books || recommend_widget.books.length <= 0) {
              return null;
            }
            // 春风悦读榜
            return (
              <BooksRanking
                forecastList={recommend_widget.books}
                title_pic={recommend_widget.title_pic}
              ></BooksRanking>
            );
          } else if (recommend_widget.articles && recommend_widget.articles.length > 0) {
            return (
              <CityRanking
                forecastList={recommend_widget.articles}
                title_pic={recommend_widget.title_pic}
              />
            );
          } else {
            return null;
          }
        }
        // 报料推荐位
        case 37: {
          if (article?.recommend_widget?.reports?.length > 0) {
            return <ReportRecommend article={article}></ReportRecommend>;
          }
          return null;
        }
        default:
          return <DefaultRecommend />;
          break;
      }

      return <OnlyWord article={article}></OnlyWord>;
    }

    if (article.subject_article_list?.length > 0) {
      if (list_style === 51) {
        // 横向滑动
        return <SubjectRecommend article={article} />;
      } else if (list_style == 50) {
        // 专题带附加稿件
        return <SubjectRecommend2 article={article} />;
      }
    }

    // console.log('xxxxxxxxxxxx', article)
    if (doc_type === 12 || (doc_type === 10 && shortVideoUseDynamicItem)) {
      // 短图文
      return <CommunityDynamicItem article={article} />;
    }

    if (doc_type == 13) {
      return list_style === 3 ? (
        <ThreePics article={article} showAvatar={true} />
      ) : (
        <ImageText article={article} showAvatar={true} />
      );
    }
  }
  console.log('list_style', list_style);
  switch (list_style) {
    case 1:
      // 纯文字
      return <OnlyWord article={article}></OnlyWord>;
    case 3:
      return <ThreePics article={article}></ThreePics>;
    case 4:
    case 7:
      // 大图
      return <BigPic article={article} />;
    case 8:
      // 长图, 目前只有专题稿
      return <SubjectLongPic article={article} />;
  }
  return <ImageText article={article}></ImageText>;
};
