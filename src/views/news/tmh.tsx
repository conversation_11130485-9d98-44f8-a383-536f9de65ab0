/* eslint-disable @typescript-eslint/no-unused-expressions */
import { setConfig } from '@app/action/config';
import { getTableList } from '@app/action/tableList';
import { releaseListApi, recommendApi, userApi, opApi, listApi } from '@app/api';
import { CommonObject, IResReleaseListAllData, IResReleaseListRecordsData } from '@app/types';
import { DOC_TYPE } from '@app/utils/constants';
import { A, Table, Drawer, PreviewIframe, PreviewMCN } from '@components/common';
import { connectTable as connect } from '@utils/connect';
import {
  getCrumb,
  requirePerm,
  searchToObject,
  resolveNewsType,
  showIDDetailModal,
  showReadCountDetailModal,
  UserDetail,
  showDataSetModal,
  jumpToEdit,
} from '@utils/utils';
import {
  Button,
  Input,
  Col,
  DatePicker,
  Dropdown,
  Icon,
  InputNumber,
  Menu,
  message,
  Modal,
  Row,
  Select,
  Tag,
  Tooltip,
  Radio,
  Timeline,
} from 'antd';
import moment from 'moment';
import React from 'react';
import { withRouter } from 'react-router';
import ReactClipboard from 'react-clipboardjs-copy';
import NewsBaseComponent, { INewsBaseProps } from './baseComponent';
// import BannerDrawer from '../recommend/components/bannerDrawer';
import { communityApi } from '@app/api';
import EditCircleContentLevelModal from '../community/component/EditCircleContentLevelModal';
import EditCircleInfoModal from '../community/component/EditCircleInfoModal';
import GetVisaModal from '@app/components/business/GetVisaModal';
import './index.scss';
import showImagePreviewModal from '@app/components/common/imagePreviewModal';
import SortableColumn from '@app/components/common/sortableColumn';
import ImagePreviewColumn from '@app/components/common/imagePreviewColumn';
import uuid from 'uuid';

type Props = INewsBaseProps<{}, IResReleaseListRecordsData, IResReleaseListAllData>;
type State = {
  preview: {
    visible: boolean;
    key: number;
    data: CommonObject;
  };
  frequencyType: string;
  // visibleShow: boolean,
  // bannerData: any,
  contentLevel: {
    visible: boolean;
    record: any;
  };
  circleInfo: {
    visible: boolean;
    record: any;
  };
  getVisa: {
    visible: boolean;
    record: any;
  };
  user: {
    visible: boolean;
    detail: any;
    key: any;
  };
  circleOptions: any;
  operateLog: {
    visible: boolean;
    logs: any;
  };
};
class MCNList extends NewsBaseComponent<
  {},
  IResReleaseListRecordsData,
  IResReleaseListAllData,
  State
> {
  constructor(props: Props) {
    super(props);
    const { doc_type } = searchToObject();
    const begin = moment().subtract(30, 'days').format('YYYY-MM-DD');
    const end = moment().format('YYYY-MM-DD');

    this.state = {
      filter: this.getInitialFilter(),
      pageInput: this.getInitialFilter(),
      channel: {
        category_id: 1,
        focus_carousel: false,
        id: '1',
        mode: 0,
        name: '',
        tou_tiao: false,
        focus_position: 0,
      },
      operateLog: {
        visible: false,
        logs: [],
      },
      preview: {
        visible: false,
        key: Date.now(),
        data: {},
      },
      PVForm: {
        visible: false,
        key: Date.now() + 3,
        article_id: 1,
        type: 0,
        show_pv: 0,
        base_pv: 0,
        min_factor: 0,
        max_factor: 0,
      },
      fixedCount: 0,
      total: 0,
      frequencyType: '视频',
      contentLevel: {
        visible: false,
        record: null,
      },
      circleInfo: {
        visible: false,
        record: null,
      },
      getVisa: {
        visible: false,
        record: null,
      },
      user: {
        key: null,
        visible: false,
        detail: {},
      },
      circleOptions: [],
      // visibleShow: false,
      // bannerData: {}
    };
    this.props.dispatch(
      setConfig({ selectKeys: [`/view/tmh?channel_id=${searchToObject().channel_id}`] })
    );
  }

  getInitialFilter() {
    const { doc_type } = searchToObject();
    const begin = moment().subtract(30, 'days').format('YYYY-MM-DD');
    const end = moment().format('YYYY-MM-DD');
    const initialFilter = {
      current: 1,
      size: 10,
      search_type: '3',
      doc_type: doc_type ? parseInt(doc_type) : 10,
      keyword: '',
      begin: begin,
      end: end,
      channel_id: searchToObject().channel_id,
      community_pushed: '',
      content_level: '',
      circle_id: '',
      retrieved: '',
    };

    return initialFilter;
  }

  componentDidMount() {
    this.getData();
    this.getCircleList();
    this.props.dispatch(setConfig({ openKeys: this.props.openKeys }));
    this.props.dispatch(
      setConfig({ selectKeys: [`/view/tmh?channel_id=${searchToObject().channel_id}`] })
    );
  }

  componentDidUpdate(prevProps: Props) {
    if (
      prevProps.tableList.timestamp === this.props.tableList.timestamp &&
      searchToObject().channel_id === this.state.filter.channel_id &&
      this.props.location.pathname === prevProps.location.pathname
    ) {
      return;
    }
    if (
      searchToObject().channel_id !== this.state.filter.channel_id ||
      this.props.location !== prevProps.location
    ) {
      this.props.dispatch(
        setConfig({ selectKeys: [`/view/tmh?channel_id=${searchToObject().channel_id}`] })
      );
      this.setState(
        {
          filter: this.getInitialFilter(),
          pageInput: this.getInitialFilter(),
        },
        () => this.getData()
      );
    }
    if (
      prevProps.tableList.timestamp !== this.props.tableList.timestamp &&
      Boolean(this.props.tableList.allData.release_list)
    ) {
      const { channel, release_list } = this.props.tableList.allData;
      this.setState({
        channel,
        filter: { ...this.state.filter, current: release_list.current, size: release_list.size },
        pageInput: {
          ...this.state.pageInput,
          current: release_list.current,
          size: release_list.size,
        },
        fixedCount: release_list.fixed_count || 0,
        total: release_list.total,
      });
    }
  }

  handleRangePickerChange = (dates: any) => {
    if (dates.length === 0) {
      this.setState(
        {
          filter: { ...this.state.filter, begin: false, end: false, sort_asc: -1, sort_by: 0 },
          pageInput: {
            ...this.state.pageInput,
            begin: false,
            end: false,
            sort_asc: -1,
            sort_by: 0,
          },
          openDatePickerPanel: false,
        },
        () => this.getData()
      );
    } else {
      this.setState(
        {
          filter: {
            ...this.state.filter,
            begin: dates[0].format('YYYY-MM-DD'),
            end: dates[1].format('YYYY-MM-DD'),
          },
          pageInput: {
            ...this.state.pageInput,
            begin: dates[0].format('YYYY-MM-DD'),
            end: dates[1].format('YYYY-MM-DD'),
          },
        },
        () => this.getData()
      );
    }
  };

  changeReadCountOrder = (sort_asc: number, sort_by: number) => {
    let {
      filter: { sort_by: old_sort_by, begin, end },
      openDatePickerPanel,
    } = this.state;
    if (sort_asc === -1) {
      // 当前选中其他排序的时候 另外一个的重置不生效
      if (old_sort_by != sort_by) {
        return;
      }
      openDatePickerPanel = false;
      // begin = false
      // end = false
      begin = moment().subtract(30, 'days').format('YYYY-MM-DD');
      end = moment().format('YYYY-MM-DD');
      sort_by = 0;
      sort_asc = -1;
    } else if (old_sort_by != sort_by) {
      // 进入排序模式，显示时间框，默认选中今天
      openDatePickerPanel = true;
      const today = moment();
      begin = today.format('YYYY-MM-DD');
      end = today.format('YYYY-MM-DD');
    }

    this.setState(
      {
        filter: {
          ...this.getInitialFilter(),
          sort_by,
          sort_asc,
          current: 1,
          begin,
          end,
        },
        pageInput: {
          ...this.getInitialFilter(),
          sort_by,
          sort_asc,
          current: 1,
          begin,
          end,
        },
        openDatePickerPanel,
      },
      () => this.getData()
    );
  };

  getCircleList = () => {
    communityApi
      .getCircleList({ current: 1, enabled: 'true', size: 100 })
      .then((res) => {
        const { list = [] } = res.data as any;
        this.setState({ circleOptions: list.records });
      })
      .catch(() => {});
  };

  handleRecommendChange = (value: any) => {
    this.handleFilterChange('community_pushed', value);
  };

  handleFilterChange = (key: any, value: any) => {
    this.setState(
      {
        filter: {
          ...this.state.filter,
          [key]: value,
          current: 1,
          sort_asc: -1,
          sort_by: 0,
        },
        pageInput: { ...this.state.pageInput, [key]: value, current: 1, sort_asc: -1, sort_by: 0 },
      },
      () => this.getData()
    );
  };

  handleDocTypeChange = (value: any) => {
    this.setState(
      {
        filter: { ...this.state.filter, doc_type: value },
        pageInput: { ...this.state.pageInput, doc_type: value },
      },
      () => this.getData()
    );
  };

  exchangeOrder = (id: string | number, seq: number, offset: number) => {
    const WAIT_TIME = 1000;
    this.props.dispatch(setConfig({ loading: true }));
    releaseListApi
      .chaokeExchangeOrder({ id, offset, current: seq })
      .then(() => {
        message.success('操作成功');
        this.getData();
        this.props.dispatch(setConfig({ loading: false }));
      })
      .catch(() => {
        this.props.dispatch(setConfig({ loading: false }));
      });
  };

  changeOrder = (record: any) => {
    let position = record.seq;
    const sticky = false;
    const WAIT_TIME = 1000;
    const positionChange = (v: any) => {
      position = v;
    };
    const searchStatus =
      this.state.filter.keyword || this.state.filter.doc_type || this.state.filter.begin;
    Modal.confirm({
      title: <p>排序：《{record.list_title}》</p>,
      icon: <Icon type="info-circle" />,
      content: (
        <div>
          <span>请输入位置：</span>
          <InputNumber
            min={1}
            max={searchStatus ? 999999999999 : this.state.total + this.state.fixedCount}
            defaultValue={position}
            onChange={positionChange}
          />
        </div>
      ),
      onOk: (closeFunc: Function) => {
        if (!position) {
          message.error('请填写位置');
          return;
        }
        if (this.state.total + this.state.fixedCount < position && !searchStatus) {
          message.error('稿件位置不能大于稿件总数');
          return;
        }
        this.props.dispatch(setConfig({ loading: true }));
        releaseListApi
          .chaokeChangeOrder({ position, id: record.id, fixed: sticky })
          .then(() => {
            message.success('操作成功');
            setTimeout(() => {
              this.getData();
              this.props.dispatch(setConfig({ loading: false }));
            }, WAIT_TIME);
            closeFunc();
          })
          .catch(() => {
            this.props.dispatch(setConfig({ loading: false }));
          });
      },
    });
  };

  changeVisible = (id: string | number, visible: boolean) => {
    const WAIT_TIME = 1000;
    this.props.dispatch(setConfig({ loading: true }));
    releaseListApi
      .chaokeChangeVisible({ id, visible: visible ? 0 : 1 })
      .then(() => {
        message.success('操作成功');
        setTimeout(() => {
          this.getData();
          this.props.dispatch(setConfig({ loading: false }));
        }, WAIT_TIME);
      })
      .catch(() => {
        this.props.dispatch(setConfig({ loading: false }));
      });
  };

  revokeNews = (id: string | number, title: string) => {
    const WAIT_TIME = 1000;
    Modal.confirm({
      title: <p>确认取消签发《{title?.length > 30 ? title.slice(0, 30) + '...' : title}》？</p>,
      content: <p>该作品将变为待审核状态，仅作者本人可见</p>,
      onOk: () => {
        this.props.dispatch(setConfig({ loading: true }));
        releaseListApi
          .chaokeRevokeNews({ id })
          .then(() => {
            message.success('操作成功');
            setTimeout(() => {
              this.getData();
              this.props.dispatch(setConfig({ loading: false }));
            }, WAIT_TIME);
          })
          .catch(() => {
            this.props.dispatch(setConfig({ loading: false }));
          });
      },
    });
  };

  toCommentSystem = (id: number) => {
    const w = window.open();
    releaseListApi
      .getCommentSystemUrl({ article_id: id })
      .then((r: any) => {
        if (w) {
          w.location = r.data.url;
        } else {
          message.error('浏览器可能拦截了新页面');
        }
      })
      .catch(() => {
        w && w.close();
      });
  };

  handleEditArticle = (record: any) => {
    this.props.dispatch(setConfig({ loading: true }));
    releaseListApi
      .getArticleEditUrl({ id: record.id })
      .then((res: any) => {
        this.props.dispatch(setConfig({ loading: false }));
        if (res.data.token) {
          jumpToEdit(res.data.token, record.doc_type);
          Modal.confirm({
            title: '是否已经操作完成，需要刷新页面？',
            okText: '确定',
            cancelText: '取消',
            onOk: () => {
              this.getData();
            },
          });
        }
      })
      .catch(() => {
        this.props.dispatch(setConfig({ loading: false }));
      });
  };

  handleEditContentLevel(record: any, visible: boolean) {
    this.setState({
      contentLevel: {
        visible,
        record,
      },
    });
  }

  submitContentLevelEnd = () => {
    this.getData();
    this.setState({
      contentLevel: {
        visible: false,
        record: {},
      },
    });
  };

  submitCircleBlockEnd = () => {
    this.getData();
    this.setState({
      circleInfo: {
        visible: false,
        record: {},
      },
    });
  };

  handleEditCircleInfo(record: any, visible: boolean) {
    this.setState({
      circleInfo: {
        visible,
        record,
      },
    });
  }

  showGetVisaModal(record: any, visible: boolean) {
    this.setState({
      getVisa: {
        visible,
        record,
      },
    });
  }

  // 显示用户详情
  showUserDetailModal(record: any, visible: boolean) {
    this.props.dispatch(setConfig({ loading: true }));
    userApi
      .getUserDetail({ accountId: record.account_id })
      .then((r: any) => {
        this.setState({
          user: {
            key: Date.now(),
            visible,
            detail: r.data.account,
          },
        });
        this.props.dispatch(setConfig({ loading: false }));
      })
      .catch(() => this.props.dispatch(setConfig({ loading: false })));
  }

  updateCommunityPushed(id: string, community_pushed: number) {
    const WAIT_TIME = 1000;
    this.props.dispatch(setConfig({ loading: true }));
    communityApi
      .updateCommunityPushed({ id, community_pushed, channel_id: searchToObject().channel_id })
      .then(() => {
        message.success('操作成功');
        setTimeout(() => {
          this.getData();
          this.props.dispatch(setConfig({ loading: false }));
        }, WAIT_TIME);
      })
      .catch(() => {
        this.props.dispatch(setConfig({ loading: false }));
      });
  }

  exportData = () => {
    Modal.confirm({
      title: '单次最多可导出2000行数据',
      content: '（如果当前列表数量超出上限，仅导出前2000行）',
      onOk: () => {
        releaseListApi
          .exportReleaseList({
            ...this.getFilter(),
            export_flag: 1,
          })
          .then((res: any) => {
            const a = document.createElement('a');
            a.href = window.URL.createObjectURL(res.data);
            a.download = `潮鸣号内容列表${moment().format('YYYYMMDD')}.xlsx`;
            a.click();
          });
      },
    });
  };

  // recommendStatus = (record: any) => {
  //   if (record.community_pushed) {
  //     Modal.confirm({
  //       title: '确定不推荐该内容？',
  //       onOk: () => {
  //         this.updateCommunityPushed(record.id, 0)
  //       },
  //     });
  //   } else if (record.down) {
  //     Modal.confirm({
  //       title: '该内容为沉底通过，确定设为推荐？',
  //       onOk: () => {
  //         this.updateCommunityPushed(record.id, 1)
  //       },
  //     });
  //   } else {
  //     this.updateCommunityPushed(record.id, 1)
  //   }
  // };
  tooltipText = () => {
    return (
      <>
        <div>潮鸣号/潮客内容的签发时间即审核通过时间；</div>
        <div>内容重新编辑/审核签发，展示最后签发时间</div>
      </>
    );
  };
  tooltipTextCreat = () => {
    return (
      <>
        <div>内容创建时间，即潮鸣号/潮客用户上传该内容的时间</div>
      </>
    );
  };
  tooltipManuscript = () => {
    return (
      <>
        <div>将该内容审核通过的操作人；</div>
        <div>如果内容多次编辑/审核签发，显示最后一次的审核人</div>
      </>
    );
  };
  getColumns = () => {
    const { current, size } = this.props.tableList;
    const getSeq = (i: number) => (current - 1) * size + i + 1;
    const channelId = this.state.channel.id;
    const searchStatus =
      this.state.filter.keyword || this.state.filter.doc_type || this.state.filter.begin;
    const getDropDown = (record: any) => {
      const menu = (
        <Menu>
          {/* {requirePerm(this, `channel_article:${channelId}:sort`)(
            <Menu.Item disabled={!record.visible} onClick={this.changeOrder.bind(this, record)}>
              排序
            </Menu.Item>
          )} */}
          {/* {requirePerm(
            this,
            `channel_article:${channelId}:display`
          )(
            <Menu.Item
              disabled={record.fixed_number > 0}
              onClick={this.changeVisible.bind(this, record.id, record.visible)}
            >
              {record.visible === true ? '隐藏' : '取消隐藏'}
            </Menu.Item>
          )} */}
          {requirePerm(
            this,
            `channel_article:ugc:edit_url`
          )(
            <Menu.Item
              disabled={record.doc_type === DOC_TYPE.SUBJECT.code}
              onClick={this.handleEditArticle.bind(this, record)}
            >
              编辑作品
            </Menu.Item>
          )}
          {requirePerm(
            this,
            `comment:view:${channelId}`
          )(
            <Menu.Item
              disabled={record.doc_type === DOC_TYPE.SUBJECT.code}
              onClick={() => {
                const id = record.uuid;
                this.props.history.push(`/view/comment/examineMgr?id=${id}`);
              }}
            >
              评论审核
            </Menu.Item>
          )}
          {requirePerm(
            this,
            `channel_article:${channelId}:cancel_release`
          )(
            <Menu.Item onClick={this.revokeNews.bind(this, record.id, record.list_title)}>
              取消签发
            </Menu.Item>
          )}
          <Menu.Item disabled={record.url === ''}>
            <a href={record.url} target="_blank">
              查看链接
            </a>
          </Menu.Item>
          <Menu.Item disabled={record.url === ''}>
            {record.url === '' ? (
              '复制分享链接'
            ) : (
              <ReactClipboard
                action="copy"
                text={record.url}
                onSuccess={this.copySuccess}
                onError={this.copyFail}
              >
                <a>复制分享链接</a>
              </ReactClipboard>
            )}
          </Menu.Item>
          {requirePerm(
            this,
            `ugc_article:${channelId}:set_content_level`
          )(
            <Menu.Item onClick={this.handleEditContentLevel.bind(this, record, true)}>
              修改内容等级
            </Menu.Item>
          )}
          {requirePerm(
            this,
            `circle_article:update_circle`
          )(
            <Menu.Item onClick={this.handleEditCircleInfo.bind(this, record, true)}>
              修改圈子信息
            </Menu.Item>
          )}
        </Menu>
      );
      return (
        <Dropdown overlay={menu}>
          <a className="ant-dropdown-link">
            操作 <Icon type="down" />
          </a>
        </Dropdown>
      );
    };

    const column = [
      {
        title: '序号',
        key: 'seq',
        dataIndex: 'seq',
        render: (text: any, record: any, i: number) => <span>{getSeq(i)}</span>,
        width: 70,
      },
      {
        title: 'ID',
        key: 'id',
        dataIndex: 'id',
        width: 75,
        render: (text: any, record: any) => <a onClick={() => showIDDetailModal(record)}>{text}</a>,
      },
      // {
      //   title: 'uuid',
      //   dataIndex: 'uuid',
      //   width: 120
      // },
      // {
      //   title: '创作者平台ID',
      //   key: 'metadata_id',
      //   dataIndex: 'metadata_id',
      //   width: 115,
      // },
      {
        title: '标题',
        key: 'list_title',
        dataIndex: 'list_title',
        render: (text: any, record: any) => (
          <div style={{ display: 'flex', alignItems: 'center' }}>
            <a
              onClick={this.preview.bind(this, record)}
              className={`list-title ${record.visible ? '' : 'hide-title'} ${
                record.fixed_number > 0 ? 'fixed-title' : ''
              }`}
            >
              {(text?.length > 30 ? text.slice(0, 30) + '...' : text) || '-'}
              {record.visible ? '' : '（隐藏）'}
              {record.fixed_number > 0 ? '（固定）' : ''}
            </a>
          </div>
        ),
      },
      {
        title: '封面图/配图',
        key: 'list_pics',
        dataIndex: 'list_pics',
        width: 150,
        align: 'center',
        render: (text: any, record: any) => (
          <div style={{ height: 60, textAlign: 'center' }}>
            <ImagePreviewColumn text={text} imgs={record.pic_array}></ImagePreviewColumn>
          </div>
        ),
      },
      {
        title: '关联圈子',
        key: 'circle_name',
        dataIndex: 'circle_name',
        width: 90,
        render: (circle_name: string, record: any) =>
          circle_name ? `${circle_name}${record.circle_enable ? '' : '(已下线)'}` : '',
      },
      {
        title: '内容等级',
        key: 'content_level',
        dataIndex: 'content_level',
        render: (content_level: number) => ['通过', '沉底', '推荐'][content_level],
        width: 75,
      },
      {
        title: (
          <span>
            取签状态
            <Tooltip
              placement="top"
              title={
                <span>
                  已取稿——内容被取稿但未签发；
                  <br />
                  已取签——内容被取稿且被签发
                </span>
              }
            >
              <Icon style={{ paddingLeft: '5px' }} type="question-circle" />
            </Tooltip>
          </span>
        ),
        key: 'retrieved',
        dataIndex: 'retrieved',
        width: 100,
        render: (retrieved: number, record: any) => (
          <a onClick={this.showGetVisaModal.bind(this, record, true)}>
            {['', '已取稿', '已取签'][retrieved]}
          </a>
        ),
      },
      {
        title: (
          <SortableColumn
            title="阅读数"
            sort_by={this.state.filter.sort_by}
            currentSortBy={1}
            pointerEvents={!this.state.filter.keyword}
            onChange={this.changeReadCountOrder}
          />
        ),
        key: 'fake_count',
        dataIndex: 'fake_count',
        width: 90,
        render: (text: any, record: any) => (
          <a onClick={() => showReadCountDetailModal(record)}>{text}</a>
        ),
      },
      {
        title: '点赞数',
        width: 90,
        dataIndex: 'like_count',
        // render: (text: any, record: any) => <a onClick={() => showDataSetModal(record, 0)}>{text || 0}</a>
        render: (text: any, record: any) => text || 0,
      },
      {
        title: '评论数',
        width: 90,
        dataIndex: 'comment_count',
        render: (text: any, record: any) => text || 0,
      },
      {
        title: '作者',
        key: 'account_nick_name',
        dataIndex: 'account_nick_name',
        width: 110,
        render: (text: any, record: any) => (
          <a onClick={() => this.showUserDetailModal(record, true)}>{text}</a>
        ),
      },
      {
        title: '用户ID',
        key: 'chao_id',
        dataIndex: 'chao_id',
        width: 110,
      },
      // {
      //   title: '作者状态',
      //   key: 'author_status',
      //   dataIndex: 'author_status',
      //   render: (text: number) => <span>{text === 0 ? '正常' : '注销'}</span>,
      //   width: 75,
      // },
      {
        title: (
          <span>
            发布时间
            <Tooltip placement="top" title={this.tooltipTextCreat()}>
              <Icon style={{ paddingLeft: '5px' }} type="question-circle" />
            </Tooltip>
          </span>
        ),
        key: 'published_at',
        dataIndex: 'published_at',
        render: (text: any, record: any) => (
          <a onClick={() => this.getOperateLog(record)}>
            {moment(text).format('YYYY-MM-DD HH:mm:ss')}
          </a>
        ),
        width: 95,
      },
      {
        title: (
          <span>
            审核人
            <Tooltip placement="top" title={this.tooltipManuscript()}>
              <Icon style={{ paddingLeft: '5px' }} type="question-circle" />
            </Tooltip>
          </span>
        ),
        key: 'creator',
        dataIndex: 'creator',
        width: 80,
      },
      {
        title: (
          <span>
            签发时间
            <Tooltip placement="top" title={this.tooltipText()}>
              <Icon style={{ paddingLeft: '5px' }} type="question-circle" />
            </Tooltip>
          </span>
        ),
        key: 'audit_at',
        dataIndex: 'audit_at',
        render: (text: any, record: any) => (
          <span>{moment(text).format('YYYY-MM-DD HH:mm:ss')}</span>
        ),
        width: 95,
      },
      {
        title: '操作',
        key: 'op',
        render: (text: any, record: any) => getDropDown(record),
        width: 70,
        fixed: 'right',
      },
    ];
    // 根据阅读PV权限过滤菜单
    const { permissions } = this.props.session;
    const showReading = permissions.indexOf('pv:show') === -1;
    if (showReading) {
      return column.filter((v) => v.title !== '阅读数');
    } else {
      return column;
    }
  };

  getOperateLog = (record: any) => {
    opApi
      .getOperateLog({ target_id: record.id, type: 166 })
      .then((r: any) => {
        this.setState({
          operateLog: {
            visible: true,
            logs: r.data.admin_log_list,
          },
        });
      })
      .catch();
  };

  getFilter = (filter = this.state.filter) => {
    const result: CommonObject = {};
    Object.keys(filter).forEach((k: string) => {
      if (filter[k]) {
        result[k] = filter[k];
      }
    });

    if (!result['sort_by']) {
      delete result['sort_asc'];
    }
    return result;
  };

  preview = (record: any) => {
    this.setState({
      preview: {
        visible: true,
        key: Date.now(),
        data: record,
      },
    });
  };

  closePreview = () => {
    this.setState({
      preview: { ...this.state.preview, visible: false },
    });
  };

  getData = (filter: CommonObject = this.getFilter()) => {
    this.props.dispatch(getTableList('getReleaseList', 'release_list', filter));
  };
  toTopping = () => {
    this.props.history.push(`/view/tmhTopping/${this.state.filter.channel_id}`);
  };
  //轮播图管理
  // RotationManagement = () => {
  //   let data = { channel_id: this.state.filter.channel_id }
  //   recommendApi.getChannelFocusList(data).then((res: any) => {
  //     console.log(res.data.channel.focus_carousel,);
  //     this.setState({
  //       bannerData: res.data
  //     })
  //     this.setState({
  //       visibleShow: true
  //     })
  //     // setDataObj(res.data)
  //   })

  // }
  //视频/图文
  onChangeType = (e: any) => {
    let { pathname: path, search } = this.props.location;
    const index = search.lastIndexOf('&doc_type');
    if (index >= 0) {
      search = search.substring(0, index);
    }
    path = `${path}${search}&doc_type=${e.target.value}`;
    this.props.history.replace(path);
    this.setState(
      {
        filter: {
          ...this.state.filter,
          doc_type: e.target.value,
          current: 1,
        },
        pageInput: { ...this.state.pageInput, doc_type: e.target.value, current: 1 },
      },
      () => this.getData()
    );
  };
  // changeVisibleDrawer = () => {
  //   this.setState({
  //     visibleShow: false
  //   })
  // }
  render() {
    const {
      preview,
      pageInput,
      channel,
      filter: { doc_type },
      contentLevel,
      circleInfo,
      getVisa,
      circleOptions,
      user,
    } = this.state;
    // const hideAd = channel.name === 'English'
    const { breadCrumb } = this.props;
    const pageName = breadCrumb[breadCrumb.length - 1];
    return (
      <>
        <Row className="layout-infobar">
          <Col span={16}>
            <Radio.Group
              defaultValue={pageInput.doc_type}
              buttonStyle="solid"
              onChange={this.onChangeType.bind(this)}
            >
              <Radio.Button value={10}>视频</Radio.Button>
              <Radio.Button value={13}>长文章</Radio.Button>
              <Radio.Button value={12}>短图文</Radio.Button>
            </Radio.Group>
          </Col>
          <Col span={8} className="layout-breadcrumb">
            {getCrumb(this.props.breadCrumb)}
          </Col>
        </Row>
        <Row className="layout-infobar">
          <Col span={16}>
            {requirePerm(
              this,
              `user_video:${channel.id}:list_deleted`
            )(
              <Button
                style={{ marginRight: 8 }}
                onClick={this.changeRoute.bind(
                  this,
                  `/view/userDeletedContents/${this.state.channel.id}/${pageName}?doc_type=${doc_type}`
                )}
              >
                <Icon type="right-circle" /> 用户发起的删除内容
              </Button>
            )}
            {requirePerm(
              this,
              `account_complaint:${channel.id}:list`
            )(
              <Button
                style={{ marginRight: 8 }}
                onClick={this.changeRoute.bind(
                  this,
                  `/view/userComplaintContents/${this.state.channel.id}/${pageName}?doc_type=${doc_type}`
                )}
              >
                <Icon type="right-circle" /> 举报内容
              </Button>
            )}
            {this.state.filter.doc_type == 10 &&
              requirePerm(
                this,
                `article_video_top:${channel.id}:view`
              )(
                <Button style={{ marginRight: 8 }} onClick={this.toTopping}>
                  置顶稿件管理
                </Button>
              )}
            {requirePerm(
              this,
              `channel_article:tianmuhao:export`
            )(<Button onClick={this.exportData}>导出数据</Button>)}
          </Col>
        </Row>

        <div className="component-content news-pages">
          <Row style={{ marginBottom: 16 }}>
            <Col span={14}>
              <DatePicker.RangePicker
                key={`${this.state.filter.begin}-${this.state.filter.end}`}
                style={{ width: 210 }}
                format="YYYY-MM-DD"
                onChange={this.handleRangePickerChange}
                open={this.state.openDatePickerPanel}
                onOpenChange={(openDatePickerPanel) => {
                  this.setState({ openDatePickerPanel });
                }}
                value={
                  this.state.filter.begin
                    ? [moment(this.state.filter.begin), moment(this.state.filter.end)]
                    : []
                }
                ranges={
                  this.state.filter.sort_by > 0
                    ? {
                        当日: [moment(), moment()],
                        近3天: [moment().subtract(2, 'days'), moment()],
                        近一周: [moment().subtract(6, 'days'), moment()],
                      }
                    : {}
                }
              />
              <Select
                value={pageInput.community_pushed}
                style={{ width: 94, marginRight: 8, marginLeft: 8 }}
                onChange={(v) => this.handleFilterChange('community_pushed', v)}
              >
                <Select.Option value="">推荐状态</Select.Option>
                <Select.Option value="1">已推荐</Select.Option>
                <Select.Option value="0">未推荐</Select.Option>
              </Select>
              <Select
                value={this.state.filter.content_level}
                style={{ width: 94, marginLeft: 8 }}
                onChange={(v) => this.handleFilterChange('content_level', v)}
              >
                <Select.Option value="">内容等级</Select.Option>
                <Select.Option value="0">通过</Select.Option>
                <Select.Option value="2">推荐</Select.Option>
                <Select.Option value="1">沉底</Select.Option>
              </Select>
              <Select
                value={this.state.filter.circle_id}
                style={{ width: 130, marginLeft: 8 }}
                onChange={(v) => this.handleFilterChange('circle_id', v)}
              >
                <Select.Option value="">关联圈子</Select.Option>
                <Select.Option value="0">无圈子</Select.Option>
                {circleOptions.map((item: any) => (
                  <Select.Option key={item.id} value={item.id}>
                    {item.name}
                  </Select.Option>
                ))}
              </Select>
              <Select
                value={this.state.filter.retrieved}
                style={{ width: 94, marginLeft: 8 }}
                onChange={(v) => this.handleFilterChange('retrieved', v)}
              >
                <Select.Option value="">取签状态</Select.Option>
                <Select.Option value="0">未取稿</Select.Option>
                <Select.Option value="1">已取稿</Select.Option>
                <Select.Option value="2">已取签</Select.Option>
              </Select>
            </Col>
            <Col span={10}>
              <div className="title-right-col">
                <Select
                  value={pageInput.search_type.toString()}
                  style={{ width: 150, marginRight: 8, marginLeft: 8 }}
                  onChange={this.searchInputChange.bind(this, 'search_type', null)}
                >
                  <Select.Option value="1">搜索ID</Select.Option>
                  <Select.Option value="8">搜索uuid</Select.Option>
                  <Select.Option value="2">搜索创作者平台ID</Select.Option>
                  {/* <Select.Option value="2">搜索创作者平台ID</Select.Option> */}
                  <Select.Option value="3">搜索标题</Select.Option>
                  <Select.Option value="6">搜索正文</Select.Option>
                  <Select.Option value="4">搜索发稿人</Select.Option>
                  <Select.Option value="5">搜索作者</Select.Option>
                  <Select.Option value="9">搜索用户ID</Select.Option>
                </Select>
                <Input
                  style={{ width: 160, marginRight: 8 }}
                  onKeyPress={this.handleKey}
                  value={pageInput.keyword}
                  placeholder="请输入搜索内容"
                  onChange={this.searchInputChange.bind(this, 'keyword')}
                />
                <Button onClick={this.handleKey.bind(this, { which: 13 })}>
                  <Icon type="search" /> 搜索{pageInput.community_pushed}
                </Button>
              </div>
            </Col>
          </Row>
          <Table
            filter={this.getFilter()}
            index="release_list"
            func="getReleaseList"
            columns={this.getColumns()}
            rowKey="id"
            pagination={true}
            total={this.props.tableList.total + this.state.fixedCount}
            tableProps={{ scroll: { x: 1500 } }}
          />
          <PreviewMCN skey={preview.key} {...preview} onClose={this.closePreview} />
          {/* <BannerDrawer visibleShow={this.state.visibleShow} changeVisible={this.changeVisibleDrawer} bannerData={[]} componentNames={'潮鸣号'} /> */}
          <EditCircleContentLevelModal
            idKey="id"
            record={contentLevel.record}
            visible={contentLevel.visible}
            onCancel={() => this.handleEditContentLevel(contentLevel.record, false)}
            onEnd={this.submitContentLevelEnd}
          />
          <EditCircleInfoModal
            idKey="id"
            record={circleInfo.record}
            visible={circleInfo.visible}
            onCancel={() => this.handleEditCircleInfo(circleInfo.record, false)}
            onEnd={this.submitCircleBlockEnd}
          />
          <GetVisaModal
            record={getVisa.record}
            visible={getVisa.visible}
            onCancel={() => this.showGetVisaModal(getVisa.record, false)}
          />
          <Modal
            visible={user.visible}
            key={user.key}
            title="用户详情"
            width={800}
            onCancel={() => this.setState({ user: { ...user, visible: false } })}
            onOk={() => this.setState({ user: { ...user, visible: false } })}
          >
            {/*{user.visible && getUserDetail(user.detail)}*/}
            {user.visible && <UserDetail detail={user.detail} />}
          </Modal>

          {/* 操作日志 */}
          <Modal
            visible={this.state.operateLog.visible}
            title="操作日志"
            // key={this.state.operateLog.key}
            cancelText={null}
            onCancel={() =>
              this.setState({
                operateLog: {
                  ...this.state.operateLog,
                  visible: false,
                },
              })
            }
            onOk={() =>
              this.setState({
                operateLog: {
                  ...this.state.operateLog,
                  visible: false,
                },
              })
            }
          >
            <div>
              <Timeline>
                {this.state.operateLog.logs?.map((v: any, i: number) => [
                  <Timeline.Item className="timeline-dot-big" data-show={v.date} key={`time${i}`}>
                    &nbsp;
                  </Timeline.Item>,
                  v.log_list?.map((action: any, index: number) => (
                    <Timeline.Item
                      className="timeline-dot"
                      data-show={moment(action.created_at).format('HH:mm:ss')}
                      key={`time${i}-action${index}`}
                    >
                      {action.admin_name}&emsp;{action.remark}
                      {/* {action.user}&emsp;&emsp;{action.action}&emsp;&emsp; */}
                    </Timeline.Item>
                  )),
                ])}
              </Timeline>
            </div>
          </Modal>
        </div>
      </>
    );
  }
}

export default withRouter(connect<IResReleaseListRecordsData, IResReleaseListAllData>()(MCNList));
