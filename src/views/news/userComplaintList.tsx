/* eslint-disable array-callback-return */
import { releaseListApi as api, communityApi, releaseListApi } from '@app/api';
import { ITableProps, IBaseProps, CommonResponse, CommonObject } from '@app/types';
import { A, Drawer, Table, BaseComponent, PreviewMCN } from '@components/common';
import { connectTable as connect } from '@utils/connect';
import {
  Button,
  Col,
  DatePicker,
  Icon,
  message,
  Modal,
  Row,
  Select,
  Input,
  Menu,
  Dropdown,
} from 'antd';
import moment from 'moment';
import React from 'react';
import { withRouter } from 'react-router';
import { ColumnProps } from 'antd/es/table';
import { RangePickerValue } from 'antd/es/date-picker/interface';
import {
  TUserComplaintListFilter,
  TUserComplaintNewsResData,
  TUserComplaintNewsRecord,
  TUserComplaintTag,
} from './news';
import { jumpToEdit, requirePerm, resolveNewsType, searchToObject } from '@app/utils/utils';
import ImagePreviewColumn from '@app/components/common/imagePreviewColumn';
import { DOC_TYPE } from '@app/utils/constants';
import ReactClipboard from 'react-clipboardjs-copy';
import EditCircleContentLevelModal from '../community/component/EditCircleContentLevelModal';
import EditCircleInfoModal from '../community/component/EditCircleInfoModal';
import { setConfig } from '@app/action/config';
import AuditEditDrawer from '../community/auditEditDrawer';

type Record = TUserComplaintNewsRecord;

type State = {
  filters: TUserComplaintListFilter;
  preview: {
    visible: boolean;
    key: number;
    data: CommonObject;
  };
  currentType: TUserComplaintListFilter['search_type'];
  currentKeyword: string;
  id: string;
  tagList: TUserComplaintTag[];
  name: string;
};

type Props = IBaseProps<
  ITableProps<TUserComplaintNewsRecord, TUserComplaintNewsResData>,
  { id: string; name: string }
>;

class UserComplaintList extends BaseComponent<
  ITableProps<TUserComplaintNewsRecord, TUserComplaintNewsResData>,
  State,
  { id: string; name: string }
> {
  constructor(props: Props) {
    super(props);
    const { id, name } = props.match.params;
    const { doc_type } = searchToObject();
    this.state = {
      filters: {
        begin: '',
        end: '',
        search_type: 1,
        keyword: '',
        tag_id: '',
        // doc_type: doc_type ? parseInt(doc_type) : 0,
        doc_type: 0,
      },
      preview: {
        visible: false,
        key: Date.now(),
        data: {},
      },
      contentLevel: {
        visible: false,
        record: null,
      },
      circleInfo: {
        visible: false,
        record: null,
      },
      currentType: 1,
      currentKeyword: '',
      id,
      name,
      tagList: [],
    };
  }

  componentDidMount() {
    this.getTags();
    this.getData({ current: 1 });
  }

  getTags = () => {
    api.getUserComplaintTags().then((r: CommonResponse<{ list: TUserComplaintTag[] }>) => {
      this.setState({
        tagList: r.data.list,
      });
    });
  };

  getData = (overlap: TUserComplaintListFilter = {}, filters = this.getFilters()) => {
    this.dispatchTable('getUserComplaintList', 'list', { ...filters, ...overlap });
  };

  getFilters = (): TUserComplaintListFilter => {
    const { current, size } = this.props.tableList;
    let filters: TUserComplaintListFilter = { current, size, channel_id: this.state.id };
    (Object.keys(this.state.filters) as (keyof TUserComplaintListFilter)[]).map(
      (k: keyof TUserComplaintListFilter) => {
        if (this.state.filters[k]) {
          filters = { ...filters, [k]: this.state.filters[k] };
        }
      }
    );
    return filters;
  };

  revokeNews = (id: string | number, title: string) => {
    const WAIT_TIME = 1000;
    Modal.confirm({
      title: <p>确认取消签发《{title?.length > 30 ? title.slice(0, 30) + '...' : title}》？</p>,
      content: <p>该帖子将变为待审核状态，仅作者本人可见</p>,
      onOk: () => {
        this.props.dispatch(setConfig({ loading: true }));
        releaseListApi
          .chaokeRevokeNews({ id })
          .then(() => {
            message.success('操作成功');
            setTimeout(() => {
              this.getData();
              this.props.dispatch(setConfig({ loading: false }));
            }, WAIT_TIME);
          })
          .catch(() => {
            this.props.dispatch(setConfig({ loading: false }));
          });
      },
    });
  };

  handleEditContentLevel(record: any, visible: boolean) {
    this.setState({
      contentLevel: {
        visible,
        record: {
          ...record,
          content_level: record.ugc_article?.content_level,
          channel_id: this.state.id,
        },
      },
    });
  }
  handleEditArticle = (record: any) => {
    this.props.dispatch(setConfig({ loading: true }));
    communityApi
      .getArticleAuditDetail({ id: record.article_id })
      .then((res: any) => {
        this.props.dispatch(setConfig({ loading: false }));
        this.editArticle(res.data.article);
      })
      .catch(() => {
        this.props.dispatch(setConfig({ loading: false }));
      });
  };

  editArticle = (record: any) => {
    this.setState({
      editDrawer: {
        key: Date.now(),
        visible: true,
        detail: record,
      },
    });
  };

  handleEditCircleInfo(record: any, visible: boolean) {
    this.setState({
      circleInfo: {
        visible,
        record: {
          ...record,
          ...record.ugc_article,
        },
      },
    });
  }

  getColumns = (): ColumnProps<Record>[] => {
    const { current, size } = this.props.tableList;
    const getSeq = (i: number) => (current - 1) * size + i + 1;

    const getDropDown = (record: any) => {
      const menu = (
        <Menu>
          {requirePerm(
            this,
            `channel_article:ugc:edit_url`
          )(
            <Menu.Item
              disabled={record.doc_type === DOC_TYPE.SUBJECT.code}
              onClick={this.handleEditArticle.bind(this, record)}
            >
              编辑帖子
            </Menu.Item>
          )}
          {/* {requirePerm(
            this,
            `comment:view:${channelId}`
          )(
            <Menu.Item
              disabled={record.doc_type === DOC_TYPE.SUBJECT.code}
              onClick={this.toCommentSystem.bind(this, record.uuid)}
            >
              评论运营
            </Menu.Item>
          )} */}
          {requirePerm(
            this,
            `channel_article:${this.state.id}:cancel_release`
          )(
            <Menu.Item onClick={this.revokeNews.bind(this, record.article_id, record.list_title)}>
              恢复待审
            </Menu.Item>
          )}
          <Menu.Item disabled={record.ugc_article?.url === ''}>
            <a href={record.ugc_article?.url} target="_blank">
              查看链接
            </a>
          </Menu.Item>
          <Menu.Item disabled={record.ugc_article?.url === ''}>
            {record.ugc_article?.url === '' ? (
              '复制分享链接'
            ) : (
              <ReactClipboard
                action="copy"
                text={record.ugc_article?.url}
                onSuccess={() => message.success('稿件ID已复制')}
                onError={() => message.error('复制失败')}
              >
                <a>复制分享链接</a>
              </ReactClipboard>
            )}
          </Menu.Item>
          {requirePerm(
            this,
            `ugc_article:${this.state.id}:set_content_level`
          )(
            <Menu.Item onClick={this.handleEditContentLevel.bind(this, record, true)}>
              更改等级
            </Menu.Item>
          )}
          {requirePerm(
            this,
            `circle_article:update_circle`
          )(
            <Menu.Item onClick={this.handleEditCircleInfo.bind(this, record, true)}>
              更改圈子
            </Menu.Item>
          )}
        </Menu>
      );
      return (
        <Dropdown overlay={menu}>
          <a className="ant-dropdown-link">
            操作 <Icon type="down" />
          </a>
        </Dropdown>
      );
    };

    return [
      {
        title: '序号',
        key: 'seq',
        render: (text: undefined, record: Record, index: number) => <span>{getSeq(index)}</span>,
        width: 70,
      },
      {
        title: '帖子ID',
        key: 'article_id',
        dataIndex: 'article_id',
        width: 115,
      },
      {
        title: '标题',
        key: 'list_title',
        dataIndex: 'list_title',
        render: (text: string, record: Record) => (
          <a onClick={this.preview.bind(this, record)}>{text}</a>
        ),
      },
      {
        title: '封面图/配图',
        key: 'list_pics',
        dataIndex: 'list_pics',
        width: 150,
        align: 'center',
        render: (text: any, record: any) => {
          const list = record.list_pics?.split(',') || [];
          return (
            <div style={{ height: 60, textAlign: 'center' }}>
              <ImagePreviewColumn text={list[0]} imgs={list}></ImagePreviewColumn>
            </div>
          );
        },
      },
      {
        title: '类型',
        dataIndex: 'doc_type',
        render: (text: number) => resolveNewsType(text, 1),
        width: 80,
      },
      {
        title: '举报原因',
        key: 'tag_contents',
        dataIndex: 'tag_contents',
        width: 200,
        render: (text: string[]) => <span>{text ? text.join('、') : ''}</span>,
      },
      {
        title: '举报人',
        key: 'author',
        dataIndex: 'account_nick_name',
        width: 120,
      },
      {
        title: '举报时间',
        key: 'create_date',
        dataIndex: 'created_at',
        render: (text: number) => <span>{moment(text).format('YYYY-MM-DD HH:mm:ss')}</span>,
        width: 170,
      },
      {
        title: '操作',
        key: 'op',
        fixed: 'right',
        render: (text: any, record: any) => getDropDown(record),
        width: 70,
      },
    ];
  };

  preview = (record: any) => {
    const data = { ...record };
    delete data.id;
    this.setState({
      preview: {
        visible: true,
        key: Date.now(),
        data: data,
      },
    });
  };

  handleRangePickerChange = (dates: RangePickerValue) => {
    if (dates.length === 0) {
      this.setState(
        {
          filters: { ...this.state.filters, begin: '', end: '' },
        },
        () => this.getData({ current: 1 })
      );
    } else {
      this.setState(
        {
          filters: {
            ...this.state.filters,
            begin: dates[0] ? dates[0].format('YYYY-MM-DD') : '',
            end: dates[1] ? dates[1].format('YYYY-MM-DD') : '',
          },
        },
        () => this.getData({ current: 1 })
      );
    }
  };

  typeChange = (value: TUserComplaintListFilter['search_type']) => {
    this.setState({
      currentType: value,
    });
  };

  tagChange = (id: number | '') => {
    this.setState(
      {
        filters: {
          ...this.state.filters,
          tag_id: id,
        },
      },
      () => this.getData({ current: 1 })
    );
  };

  handleDocTypeChange = (value: number) => {
    this.setState(
      {
        filters: { ...this.state.filters, doc_type: value },
      },
      () => this.getData()
    );
  };

  keywordChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    this.setState({
      currentKeyword: e.target.value,
    });
  };

  doSearch = () => {
    this.setState(
      {
        filters: {
          ...this.state.filters,
          search_type: this.state.currentType,
          keyword: this.state.currentKeyword,
        },
      },
      () => this.getData({ current: 1 })
    );
  };

  closePreview = () => {
    this.setState({
      preview: { ...this.state.preview, visible: false },
    });
  };

  backToList = () => {
    this.props.history.go(-1);
    // if (this.state.name === '潮鸣号' || this.state.name === '读嘉号') {
    //   this.changeRoute(`/view/tmh?channel_id=${this.state.id}`);
    // } else {
    //   this.changeRoute(`/view/chaoke?channel_id=${this.state.id}`);
    // }
  };

  submitContentLevelEnd = () => {
    this.getData();
    this.setState({
      contentLevel: {
        visible: false,
        record: {},
      },
    });
  };

  submitCircleBlockEnd = () => {
    this.getData();
    this.setState({
      circleInfo: {
        visible: false,
        record: {},
      },
    });
  };

  render() {
    const { filters, currentType, currentKeyword, preview, tagList } = this.state;
    return (
      <>
        <Row className="layout-infobar">
          <Col span={12}>
            <Button onClick={this.backToList}>
              <Icon type="left-circle" /> 返回
            </Button>
          </Col>
          <Col span={12} className="layout-breadcrumb">
            {this.getCrumb(['用户内容管理', this.state.name, '举报内容'])}
          </Col>
        </Row>
        <div className="component-content">
          <Row style={{ marginBottom: 16 }}>
            <Col span={16}>
              <DatePicker.RangePicker
                format="YYYY-MM-DD"
                onChange={this.handleRangePickerChange}
                value={filters.begin ? [moment(filters.begin), moment(filters.end)] : []}
              />
              <Select
                value={filters.tag_id}
                onChange={this.tagChange}
                style={{ width: 180, marginLeft: 8 }}
              >
                <Select.Option value="">举报原因</Select.Option>
                {tagList.map((v: TUserComplaintTag) => (
                  <Select.Option value={v.id} key={v.id}>
                    {v.tag}
                  </Select.Option>
                ))}
              </Select>
              <Select
                value={filters.doc_type}
                onChange={this.handleDocTypeChange}
                style={{ width: 120, marginLeft: 8 }}
              >
                <Select.Option value={0}>全部类型</Select.Option>
                <Select.Option value={12}>图文帖</Select.Option>
                <Select.Option value={10}>视频帖</Select.Option>
              </Select>
            </Col>
            <Col span={8} style={{ textAlign: 'right' }}>
              <Select
                value={currentType}
                onChange={this.typeChange}
                style={{ width: 120, marginRight: 8 }}
              >
                <Select.Option value={1}>标题</Select.Option>
                <Select.Option value={2}>举报人</Select.Option>
              </Select>
              <Input
                value={currentKeyword}
                onChange={this.keywordChange}
                style={{ width: 180, marginRight: 8 }}
                onKeyPress={this.handleKey}
                placeholder="请输入搜索内容"
              />
              <Button onClick={this.doSearch}>
                <Icon type="search" />
                搜索
              </Button>
            </Col>
          </Row>
          <Table
            filter={this.getFilters()}
            index="list"
            func="getUserComplaintList"
            columns={this.getColumns()}
            rowKey="id"
            pagination={true}
          />
          <PreviewMCN skey={preview.key} {...preview} onClose={this.closePreview} />

          <EditCircleContentLevelModal
            idKey="article_id"
            record={this.state.contentLevel.record}
            visible={this.state.contentLevel.visible}
            onCancel={() => this.handleEditContentLevel(this.state.contentLevel.record, false)}
            onEnd={this.submitContentLevelEnd}
          />
          <EditCircleInfoModal
            idKey="article_id"
            record={this.state.circleInfo.record}
            visible={this.state.circleInfo.visible}
            onCancel={() => this.handleEditCircleInfo(this.state.circleInfo.record, false)}
            onEnd={this.submitCircleBlockEnd}
          />
          <AuditEditDrawer
            {...this.state.editDrawer}
            onClose={() =>
              this.setState({ editDrawer: { ...this.state.editDrawer, visible: false } })
            }
            onEnd={() => {
              this.setState({ editDrawer: { ...this.state.editDrawer, visible: false } });
              this.getData();
            }}
          />
        </div>
      </>
    );
  }
}

export default withRouter(
  connect<TUserComplaintNewsRecord, TUserComplaintNewsResData>()(UserComplaintList)
);
