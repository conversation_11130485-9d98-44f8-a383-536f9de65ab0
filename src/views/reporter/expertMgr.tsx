import {
  Button,
  Col,
  Divider,
  Icon,
  Input,
  Modal,
  Row,
  Select,
  Tooltip,
  message,
  Radio,
  Menu,
  Dropdown,
  DatePicker,
  Checkbox,
  InputNumber,
} from 'antd';
import {
  geBoostDetail,
  getCrumb,
  resolveNewsType,
  showIDDetailModal,
  UserDetail,
  requirePerm4Function,
} from '@app/utils/utils';
import ReactClipboard from 'react-clipboardjs-copy';
import { useHistory } from 'react-router';
import { useSelector, useDispatch, useStore } from 'react-redux';
import { getTableList } from '@app/action/tableList';
import { Table, OrderColumn } from '@components/common';
import React, { useEffect, useState } from 'react';
import { reportApi as api, sysApi, userApi } from '@app/api';
import { PermA, PermButton } from '@app/components/permItems';
import { setConfig } from '@app/action/config';
import moment from 'moment';
import HelpExpertForm from './component/helpExpertForm';
import uuid from 'uuid';

export default function expertMgr(props: any) {
  const history = useHistory();
  const dispatch = useDispatch();
  const { total, current, size, records = [] } = useSelector((state: any) => state.tableList);
  const { TextArea } = Input;
  const { session } = useStore().getState();
  const [filter, setFilter] = useState({
    type: 2,
    tag_name: '',
  });
  const [keyword, setKeyword] = useState('');
  const [tagList, setTagList] = useState([]);

  const [reporterFormDrawer, setReporterFormDrawer] = useState<any>({
    visible: false,
    formContent: null,
  });

  const [userDetailModal, setUserDetailModal] = useState<any>({
    visible: false,
    key: uuid(),
    detail: null,
  });

  const getSeq = (i: number) => (current - 1) * size + i + 1;

  const getDropDown = (record: any, i: any) => {
    const menu = (
      <Menu>
        {requirePerm4Function(
          session,
          `reporter:2:update`
        )(<Menu.Item onClick={() => handleEdit(record)}>编辑信息</Menu.Item>)}

        {requirePerm4Function(
          session,
          `reporter:2:sort`
        )(<Menu.Item onClick={() => handelSort(record, getSeq(i))}>调整排序</Menu.Item>)}

        {requirePerm4Function(
          session,
          `reporter:2:delete`
        )(<Menu.Item onClick={() => handelDel(record)}>删除</Menu.Item>)}

        <Menu.Item>
          {!!record.url ? (
            <ReactClipboard
              action="copy"
              text={record.url}
              onSuccess={() => message.success('链接已复制')}
              onError={() => message.error('复制失败')}
            >
              <a>复制链接</a>
            </ReactClipboard>
          ) : (
            '复制链接'
          )}
        </Menu.Item>
      </Menu>
    );
    return (
      <Dropdown overlay={menu}>
        <a className="ant-dropdown-link">
          操作 <Icon type="down" />
        </a>
      </Dropdown>
    );
  };
  const columns: any = [
    {
      title: '排序',
      key: 'order',
      render: (text: any, record: any, i: number) => {
        const pos = getSeq(i);
        return (
          <OrderColumn
            pos={pos}
            start={1}
            end={total}
            perm="reporter:2:sort"
            disableUp={!!filter.tag_name || !!filter.keyword}
            disableDown={!!filter.tag_name || !!filter.keyword}
            onUp={() => exchangeOrder(record.id, getSeq(i), 1)}
            onDown={() => exchangeOrder(record.id, getSeq(i), -1)}
          />
        );
      },
      width: 70,
    },
    {
      title: '序号',
      key: 'id',
      render: (a: any, b: any, index: number) => <span>{getSeq(index)}</span>,
      width: 50,
    },
    // {
    //   title: '用户ID',
    //   dataIndex: 'chao_id',
    // },
    // {
    //   title: '用户昵称',
    //   dataIndex: 'nick_name',
    //   render: (text: any, record: any, i: number) => <a onClick={() => showUserDetailModal(record)}>{text}</a>
    // },
    // {
    //   title: '领域分类',
    //   dataIndex: 'classify_id',
    //   render: (classify_id: any) => resolveType(classify_id),
    // },
    {
      title: '头像',
      dataIndex: 'head_img',
      render: (text: any, record: any, i: number) => {
        return (
          <div style={{ height: 60 }}>
            <img src={text} className="list-pic"></img>
          </div>
        );
      },
      width: 80,
    },
    {
      title: '名称',
      dataIndex: 'nick_name',
      width: 160,
    },
    {
      title: '一句话介绍',
      dataIndex: 'short_desc',
    },
    {
      title: '标签',
      dataIndex: 'tag_name',
      width: 160,
    },
    {
      title: '最后操作人',
      dataIndex: 'operator_name',
      width: 160,
    },
    {
      title: '最后操作时间',
      key: 'updated_at',
      dataIndex: 'updated_at',
      render: (text: number, record: any) => moment(text).format('YYYY-MM-DD HH:mm:ss'),
      width: 160,
    },
    {
      title: '操作',
      key: 'op',
      align: 'center',
      width: 90,
      render: (text: any, record: any, i: any) => getDropDown(record, i),
    },
  ];
  // 排序
  const exchangeOrder = (id: number, current: number, offset: number) => {
    dispatch(setConfig({ loading: true }));
    api
      .sortReportUser({ id, current, offset, type: 2 })
      .then(() => {
        message.success('操作成功');
        getData();
        dispatch(setConfig({ loading: false }));
      })
      .catch(() => {
        dispatch(setConfig({ loading: false }));
      });
  };
  // 编辑
  const handleEdit = (val: any) => {
    setReporterFormDrawer({
      visible: true,
      formContent: val,
    });
  };
  // 排序
  // 排序
  const handelSort = (record: any, pos: any) => {
    const posChange = (value: number | undefined) => {
      pos = value;
    };
    // if (param.keyword || param.status) {
    //   newSort = undefined
    // }
    Modal.confirm({
      title: `调整排序`,
      icon: null,
      content: (
        <div>
          <InputNumber
            placeholder="请输入修改序号"
            min={1}
            max={total}
            onChange={posChange}
            defaultValue={pos}
            style={{ width: '100%' }}
            precision={0}
          />
        </div>
      ),
      onOk: (closeFunc: Function) => {
        if (!pos) {
          message.error('请填写序号');
          return;
        }
        api
          .moveReportUserPosition({
            id: record.id,
            position: pos,
            type: 2,
          })
          .then(() => {
            message.success('操作成功');
            getData();
            closeFunc();
          });
      },
    });
  };

  // 删除
  const handelDel = (val: any) => {
    Modal.confirm({
      title: '确定删除？',
      onOk: () => {
        dispatch(setConfig({ loading: true }));
        api
          .delReportUser({ id: val.id, type: 2 })
          .then(() => {
            message.success('操作成功');
            dispatch(setConfig({ loading: false }));
            getData();
          })
          .catch(() => {
            dispatch(setConfig({ loading: false }));
          });
      },
    });
  };

  const changeFilter = (key: string, val: any, goToFirstPage = true) => {
    let newFilter = {
      ...filter,
      [key]: val,
    };
    setFilter(newFilter);
    getData(goToFirstPage, newFilter);
  };

  const handleKey = (e: { which: number }) => {
    if (e.which === 13) {
      const newFilter = {
        ...filter,
        keyword,
      };
      setFilter(newFilter);
      getData(true, newFilter);
    }
  };

  // 获取table
  const getData = (goToFirstPage = false, newFilter = filter) => {
    let cur = goToFirstPage ? 1 : current;
    dispatch(getTableList('getReportUserList', 'list', { current: cur, size, ...newFilter }));
  };

  const getTagList = () => {
    api
      .getTagList({})
      .then((res: any) => {
        setTagList(res?.data?.list || []);
      })
      .catch(() => {});
  };

  // 添加
  const add = () => {
    setReporterFormDrawer({
      visible: true,
      formContent: null,
    });
  };

  useEffect(() => {
    const { selectKeys, openKeys } = props;
    dispatch(setConfig({ selectKeys, openKeys }));
    getData(true);
    getTagList();
  }, []);

  return (
    <>
      <Row className="layout-infobar">
        <Col span={12}>
          <PermButton
            perm="reporter:2:create"
            onClick={() => {
              add();
            }}
            style={{ marginLeft: 8 }}
          >
            添加专家
          </PermButton>
        </Col>
        <Col span={12} className="layout-breadcrumb">
          {getCrumb(props.breadCrumb)}
        </Col>
      </Row>
      <div className="component-content">
        <Row style={{ marginBottom: 20 }}>
          <Col span={12}>
            <Select
              style={{ width: 150, marginRight: 10 }}
              value={filter.tag_name}
              onChange={(val: any) => changeFilter('tag_name', val)}
            >
              <Select.Option value="">标签</Select.Option>
              {tagList.map((tag: any) => {
                return (
                  <Select.Option value={tag} key={tag}>
                    {tag}
                  </Select.Option>
                );
              })}
              {/* <Select.Option value={1}>问政</Select.Option>
              <Select.Option value={2}>帮办</Select.Option>
              <Select.Option value={3}>帮帮团</Select.Option> */}
            </Select>
            <span>注：记者帮专区的【帮帮团】模块，默认显示前15个专家</span>
          </Col>
          <Col span={12} style={{ textAlign: 'right' }}>
            <Input
              style={{ marginRight: 8, width: 160 }}
              onKeyPress={handleKey}
              placeholder="搜索专家名称"
              value={keyword}
              onChange={(e: any) => setKeyword(e.target.value)}
            />
            <Button onClick={() => handleKey({ which: 13 })}>
              <Icon type="search" /> 搜索
            </Button>
          </Col>
        </Row>

        <Table
          func="getReportUserList"
          index="list"
          pagination={true}
          rowKey="id"
          columns={columns}
          filter={filter}
        />
      </div>

      {/* 编辑爆料 */}
      <HelpExpertForm
        {...reporterFormDrawer}
        type={2}
        onClose={() => setReporterFormDrawer({ visible: false })}
        onEnd={() => {
          setReporterFormDrawer({ visible: false });
          getData();
          getTagList();
        }}
      ></HelpExpertForm>

      {/* 用户详情弹窗 */}
      <Modal
        visible={userDetailModal.visible}
        key={userDetailModal.key}
        title="用户详情"
        width={800}
        onCancel={() => setUserDetailModal({ visible: false })}
        onOk={() => setUserDetailModal({ visible: false })}
      >
        {/*{user.visible && getUserDetail(user.detail)}*/}
        {userDetailModal.visible && <UserDetail detail={userDetailModal.detail} />}
      </Modal>
    </>
  );
}
