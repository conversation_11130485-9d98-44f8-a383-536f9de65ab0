import { Button, Col, Divider, Icon, Input, Modal, Row, Select, Switch, message } from 'antd';
import { getCrumb, UserDetail } from '@app/utils/utils';
import { useHistory } from 'react-router';
import { useSelector, useDispatch, useStore } from 'react-redux';
import { getTableList } from '@app/action/tableList';
import { Table, OrderColumn } from '@components/common';
import React, { useEffect, useState } from 'react';
import { reportApi, userApi } from '@app/api';
import { PermA, PermButton } from '@app/components/permItems';
import { setConfig } from '@app/action/config';
import moment from 'moment';
import uuid from 'uuid';

// 导入自定义组件
import FirstTierFormDrawer from './component/FirstTierFormDrawer';
import InvestigationFormDrawer from './component/InvestigationFormDrawer';

export default function FirstTier(props: any) {
  const history = useHistory();
  const dispatch = useDispatch();
  const { total, current, size, records = [] } = useSelector((state: any) => state.tableList);
  const { session } = useStore().getState();
  const [filter, setFilter] = useState({});
  const [autoPlaySwitch, setAutoPlaySwitch] = useState(false);
  const {
    allData: { real_total = 999999 },
  } = useSelector((state: any) => {
    return state.tableList;
  });
  const { allData } = useSelector((state: any) => state.tableList);
  useEffect(() => {
      setAutoPlaySwitch(allData.round_carousel);
  }, [allData]);
  // 获取序号
  const getSeq = (i: number) => (current - 1) * size + i + 1;

  // 表单抽屉状态
  const [firstTierDrawer, setFirstTierDrawer] = useState<any>({
    visible: false,
    formContent: null,
    article: null,
  });

  // 一线调查模块抽屉状态
  const [investigationDrawer, setInvestigationDrawer] = useState<any>({
    visible: false,
    formContent: null,
    article: null,
  });

  // 表格列配置
  const columns: any = [
    {
      title: '排序',
      key: 'order',
      render: (text: any, record: any, i: number) => {
        const pos = getSeq(i);
        return (
          <OrderColumn
            pos={pos}
            start={1}
            end={total}
            perm="report_carousel:58:update_sort"
            onUp={() => exchangeOrder(record.id, 0)}
            onDown={() => exchangeOrder(record.id, 1)}
            disable={record.status === 0}
          />
        );
      },
      width: 70,
    },
    {
      title: '序号',
      key: 'id',
      render: (a: any, b: any, index: number) => <span>{getSeq(index)}</span>,
      width: 50,
    },
    {
      title: '图片',
      dataIndex: 'pic_url',
      render: (text: any, record: any) => (
        <div style={{ height: 60 }}>
          <img
            src={text}
            alt={record.title || '一线关注图片'}
            className="list-pic"
            style={{ maxHeight: '100%' }}
          />
        </div>
      ),
      width: 100,
    },
    {
      title: '文字',
      dataIndex: 'title',
      render: (text: any, record: any) => (
        <div>
          <div>{record.title}</div>
          <div style={{ color: '#999', fontSize: '12px' }}>{record.description}</div>
        </div>
      ),
    },
    {
      title: '状态',
      dataIndex: 'status',
      render: (text: any) => (text === 1 ? '展示中' : text === 0 ? '未展示' : '未知状态'),
      width: 100,
    },
    {
      title: '操作人',
      dataIndex: 'updated_by',
      width: 120,
    },
    {
      title: '最后操作时间',
      key: 'updated_at',
      dataIndex: 'updated_at',
      render: (text: number) => moment(text).format('YYYY-MM-DD HH:mm:ss'),
      width: 170,
    },
    {
      title: '操作',
      key: 'op',
      render: (text: any, record: any) => (
        <span>
          <PermA perm="report_carousel:58:update" onClick={() => handleEdit(record)}>
            编辑
          </PermA>
          <Divider type="vertical" />
          <PermA perm="report_carousel:58:update_status" onClick={() => handleChangeStatus(record)}>
            {record.status === 0 ? '上架' : '下架'}
          </PermA>
          <Divider type="vertical" />
          <PermA perm="report_carousel:58:delete" onClick={() => handleDelete(record)}>
            删除
          </PermA>
        </span>
      ),
      width: 180,
    },
  ];

  // 排序
  const exchangeOrder = (id: number, sort_flag: number) => {
    // 先获取记录信息，检查状态
    const record = records.find((item: any) => item.id === id);
    if (record && record.status === 0) {
      message.warning('未展示状态的数据不允许排序');
      return;
    }
    
    dispatch(setConfig({ loading: true }));
    reportApi
      .sortFirstTierList({ id, sort_flag })
      .then(() => {
        message.success('排序成功');
        getData();
      })
      .catch(() => {
        message.error('排序失败');
      })
      .finally(() => {
        dispatch(setConfig({ loading: false }));
      });
  };

  // 编辑
  const handleEdit = (record: any) => {
    dispatch(setConfig({ loading: true }));
    reportApi
      .getFirstTierListDetail({ id: record.id })
      .then((res: any) => {
        setFirstTierDrawer({
          visible: true,
          formContent: {
            id: res.data.recommend.id,
            pic_url: res.data.recommend.pic_url,
            title: res.data.recommend.title,
            ref_ids: res.data.recommend.ref_ids,
            url: res.data.recommend.url,
            jump_type: res.data.recommend.url ? 1 : res.data.recommend.ref_ids ? 2 : 1,
          },
          article: res.data.article || null,
        });
      })
      .catch(() => {
        message.error('获取详情失败');
      })
      .finally(() => {
        dispatch(setConfig({ loading: false }));
      });
  };

  // 添加
  const handleAdd = () => {
    setFirstTierDrawer({
      visible: true,
      formContent: null,
      article: null,
    });
  };

  // 修改状态（上/下架）
  const handleChangeStatus = (record: any) => {
    const newStatus = record.status === 0 ? 1 : 0;
    const statusText = newStatus === 1 ? '上架' : '下架';

    Modal.confirm({
      title: `确定${statusText}该内容？`,
      onOk: () => {
        dispatch(setConfig({ loading: true }));
        reportApi
          .changeFirstTierListStatus({ id: record.id, status: newStatus })
          .then(() => {
            message.success(`${statusText}成功`);
            getData();
          })
          .catch(() => {
            message.error(`${statusText}失败`);
          })
          .finally(() => {
            dispatch(setConfig({ loading: false }));
          });
      },
    });
  };

  // 删除
  const handleDelete = (record: any) => {
    Modal.confirm({
      title: '确定删除该内容？',
      onOk: () => {
        dispatch(setConfig({ loading: true }));
        reportApi
          .deleteFirstTierList({ id: record.id })
          .then(() => {
            message.success('删除成功');
            getData();
          })
          .catch(() => {
            message.error('删除失败');
          })
          .finally(() => {
            dispatch(setConfig({ loading: false }));
          });
      },
    });
  };

  // 切换自动轮播开关
  const toggleAutoPlay = (checked: boolean) => {
    dispatch(setConfig({ loading: true }));
    reportApi
      .saveFirstTierCarouselConfig({ switch: checked })
      .then(() => {
        setAutoPlaySwitch(checked);
        message.success('轮播设置成功');
      })
      .catch(() => {
        message.error('轮播设置失败');
      })
      .finally(() => {
        dispatch(setConfig({ loading: false }));
      });
  };

  // 获取表格数据
  const getData = (goToFirstPage = false, newFilter = filter) => {
    let cur = goToFirstPage ? 1 : current;
    // 使用一线关注列表API
    dispatch(getTableList('getFirstTierList', 'list', { current: cur, size, ...newFilter }));
  };

  // 初始化
  useEffect(() => {
    const { selectKeys, openKeys } = props;
    dispatch(setConfig({ selectKeys, openKeys }));
    getData(true);
  }, []);

  // 管理一线调查模块
  const openInvestigationDrawer = () => {
    dispatch(setConfig({ loading: true }));
    // 获取一线调查配置
    reportApi
      .getFirstTierModuleDetail({})
      .then((res: any) => {
        console.log(res);
        setInvestigationDrawer({
          visible: true,
          formContent: {
            status: res.data.recommend.status,
            ref_ids: res.data.recommend.ref_ids,
          },
          article: res.data.article || null,
        });
        dispatch(setConfig({ loading: false }));
      })
      .catch(() => {
        setInvestigationDrawer({
          visible: true,
          formContent: {
            status: 0,
            ref_ids: '',
          },
          article: null,
        });
        dispatch(setConfig({ loading: false }));
      });
  };

  return (
    <>
      <Row className="layout-infobar">
        <Col span={12}>
          <PermButton perm="" onClick={handleAdd}>
            添加一线关注
          </PermButton>
          <PermButton perm="" onClick={openInvestigationDrawer} style={{ marginLeft: 8 }}>
            管理一线调查模块
          </PermButton>
        </Col>
        <Col span={12} className="layout-breadcrumb">
          {getCrumb(props.breadCrumb)}
        </Col>
      </Row>
      <div className="component-content">
        <PermA perm="report:carousel">
          <Row style={{ marginBottom: 16 }}>
            <Col span={24}>
              <span style={{ marginRight: 8 }}>自动轮播：</span>
              <Switch
                checked={autoPlaySwitch}
                onChange={toggleAutoPlay}
                checkedChildren="开"
                unCheckedChildren="关"
              />
            </Col>
          </Row>
        </PermA>

        <Table
          func="getFirstTierList"
          index="list"
          pagination={true}
          rowKey="id"
          columns={columns}
          filter={filter}
        />

        {/* 一线关注表单抽屉 */}
        <FirstTierFormDrawer
          visible={firstTierDrawer.visible}
          formContent={firstTierDrawer.formContent}
          article={firstTierDrawer.article}
          onClose={() => setFirstTierDrawer({ visible: false })}
          onEnd={() => {
            setFirstTierDrawer({ visible: false });
            getData();
          }}
        />

        {/* 一线调查模块表单抽屉 */}
        <InvestigationFormDrawer
          visible={investigationDrawer.visible}
          formContent={investigationDrawer.formContent}
          article={investigationDrawer.article}
          onClose={() => setInvestigationDrawer({ visible: false })}
          onEnd={() => {
            setInvestigationDrawer({ visible: false });
            message.success('一线调查模块设置成功');
          }}
        />
      </div>
    </>
  );
}
