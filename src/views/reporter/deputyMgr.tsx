// 帮办 biz_type = 2
import {
  <PERSON><PERSON>,
  Col,
  Divider,
  Icon,
  Input,
  Modal,
  Row,
  Select,
  Tooltip,
  message,
  Radio,
  Menu,
  Dropdown,
  DatePicker,
  Checkbox,
  InputNumber,
} from 'antd';
import {
  getCrumb,
  UserDetail,
  requirePerm4Function,
  searchToObject,
  showReportIDDetailModal,
  reportTypeMap,
} from '@app/utils/utils';
import ReactClipboard from 'react-clipboardjs-copy';
import { useHistory } from 'react-router';
import { useSelector, useDispatch, useStore } from 'react-redux';
import { getTableList } from '@app/action/tableList';
import { A, PreviewMCN, Table } from '@components/common';
import React, { useEffect, useState } from 'react';
import { reportApi as api, reportApi, userApi } from '@app/api';
import { PermA, PermButton } from '@app/components/permItems';
import { setConfig } from '@app/action/config';
import moment from 'moment';
import ReportDetailDrawer from './component/reportDetailDrawer';
import AddDomainModal from './component/addDomainModal';
import uuid from 'uuid';
import AddReporterModal from './component/addReporterModal';
import RelevanceArticle from './component/relevanceArticle';
import PicConfigFormDrawer from './component/PicConfigFormDrawer';

export default function deputyMgr(props: any) {
  const { biz_type } = props;
  const history = useHistory();
  const dispatch = useDispatch();
  const { total, current, size, records = [] } = useSelector((state: any) => state.tableList);
  const { session } = useStore().getState();
  const [fieldsList, setFieldsList] = useState([]);
  const [filter, setFilter] = useState({
    classify_id: '',
    report_type: '',
    status: 0,
    search_type: 1,
    keyword: '',
    show_related_article: false,
    show_own: false,
    start_time: '',
    end_time: '',
    biz_type: biz_type,
    field_list: '',
    from_source: '',
  });
  const [searchState, setSearchState] = useState({
    search_type: 1,
    keyword: '',
  });
  // 通过弹窗
  const [domain, setDomain] = useState<any>({
    visible: false,
    type: 0,
    formContent: null,
  });

  const [reporterModal, setReporterModal] = useState<any>({
    visible: false,
    formContent: null,
  });
  const [relevanceArticleModal, setRelevanceArticleModal] = useState<any>({
    visible: false,
    formContent: null,
  });
  const [userDetailModal, setUserDetailModal] = useState<any>({
    visible: false,
    key: uuid(),
    detail: null,
  });

  const [picConfig, setPicConfig] = useState<any>({
    visible: false,
    key: uuid(),
    detail: null,
  });

  // 记者弹窗
  const [reporter, setReporter] = useState({
    visible: false,
    id: '',
    reporter_id: '',
    options: [],
  });
  // 详情
  const [reportDetailDrawer, setReportDetailDrawer] = useState<any>({
    visible: false,
    record: null,
    skey: Date.now(),
  });
  const getSeq = (i: number) => (current - 1) * size + i + 1;
  const columns: any = [
    {
      title: '序号',
      key: 'id',
      render: (a: any, b: any, index: number) => <span>{getSeq(index)}</span>,
      width: 50,
    },
    {
      title: '编号',
      dataIndex: 'number',
      width: 120,
      render: (text: any, record: any) => (
        <a onClick={() => showReportIDDetailModal(record)}>{text}</a>
      ),
    },
    // {
    //   title: '领域分类',
    //   dataIndex: 'classify_id',
    //   render: (classify_id: any) => resolveType(classify_id),
    //   width: 80,
    // },
    {
      title: '报料类型',
      dataIndex: 'report_type',
      render: (report_type: any) => reportTypeMap(report_type),
      width: 80,
    },
    {
      title: '关联领域',
      dataIndex: 'field_name',
      width: 120,
    },
    {
      title: '报料标题',
      key: 'title',
      dataIndex: 'title',
      render: (title: any, record: any) => (
        <a onClick={() => showDetailModal(record, true)}> {title}</a>
      ),
    },
    {
      title: '报料人',
      key: 'nick_name',
      dataIndex: 'nick_name',
      width: 110,
      render: (nick_name: any, record: any) => (
        <a onClick={() => showUserDetailModal(record)}>{nick_name}</a>
      ),
    },
    {
      title: (
        <span>
          报料时间
          <Tooltip title={'如果修改过报料时间，显示修改的时间'}>
            <Icon type="question-circle" style={{ marginLeft: 8, marginRight: 8 }} />
          </Tooltip>
        </span>
      ),
      key: 'created_at',
      dataIndex: 'created_at',
      render: (text: number, record: any) => {
        const date = moment(record.created_at);
        return (
          <div>
            <div>{date.format('YYYY-MM-DD')}</div>
            <div>{date.format('HH:mm:ss')}</div>
          </div>
        );
      },
      width: 120,
    },
    {
      title: '操作',
      key: 'op',
      align: 'center',
      width: 90,
      render: (text: any, record: any) => getDropDown(record),
    },
  ];

  const getDropDown = (record: any) => {
    const menu = (
      <Menu>
        {/* {requirePerm4Function(
          session,
          `report:${biz_type}:change_audit_status`
        )(<Menu.Item onClick={() => handleReAudit(record)}>重新审核</Menu.Item>)} */}
        {/* {requirePerm4Function(
          session,
          `report:${biz_type}:update_classify`
        )(<Menu.Item onClick={() => handleChangeDomain(record)}>修改领域</Menu.Item>)} */}
        {requirePerm4Function(
          session,
          `report:${biz_type}:update_reporter`
        )(<Menu.Item onClick={() => handleAssign(record)}>指派记者</Menu.Item>)}
        <Menu.Item>
          {!!record.url ? (
            <ReactClipboard
              action="copy"
              text={record.url}
              onSuccess={() => message.success('链接已复制')}
              onError={() => message.error('复制失败')}
            >
              <a>复制链接</a>
            </ReactClipboard>
          ) : (
            '复制链接'
          )}
        </Menu.Item>
      </Menu>
    );
    return (
      <Dropdown overlay={menu}>
        <a className="ant-dropdown-link">
          操作 <Icon type="down" />
        </a>
      </Dropdown>
    );
  };
  const columns2: any = [
    {
      title: '序号',
      key: 'id',
      render: (a: any, b: any, index: number) => <span>{getSeq(index)}</span>,
      width: 50,
    },
    {
      title: '编号',
      dataIndex: 'number',
      width: 120,
      render: (text: any, record: any) => (
        <a onClick={() => showReportIDDetailModal(record)}>{text}</a>
      ),
    },
    // {
    //   title: '领域分类',
    //   dataIndex: 'classify_id',
    //   render: (classify_id: any) => resolveType(classify_id),
    //   width: 80,
    // },
    {
      title: '报料类型',
      dataIndex: 'report_type',
      render: (report_type: any) => reportTypeMap(report_type),
      width: 80,
    },
    {
      title: '关联领域',
      dataIndex: 'field_name',
      width: 120,
    },
    {
      title: '报料标题',
      key: 'title',
      dataIndex: 'title',
      render: (title: any, record: any) => (
        <a onClick={() => showDetailModal(record, true)}> {title}</a>
      ),
    },
    {
      title: '报料人',
      key: 'nick_name',
      dataIndex: 'nick_name',
      width: 110,
      render: (nick_name: any, record: any) => (
        <a onClick={() => showUserDetailModal(record)}>{nick_name}</a>
      ),
    },
    {
      title: (
        <span>
          报料时间
          <Tooltip title={'如果修改过报料时间，显示修改的时间'}>
            <Icon type="question-circle" style={{ marginLeft: 8, marginRight: 8 }} />
          </Tooltip>
        </span>
      ),
      key: 'created_at',
      dataIndex: 'created_at',
      render: (text: number, record: any) => {
        return (
          <div>
            <div>{moment(record.created_at).format('YYYY-MM-DD')}</div>
            <div>{moment(record.created_at).format('HH:mm:ss')}</div>
          </div>
        );
      },
      width: 120,
    },
    {
      title: '负责人',
      key: 'reporter_name',
      dataIndex: 'reporter_name',
      width: 110,
      // render: (reporter_name: any, record: any) => (
      //   <a onClick={() => showUserDetailModal(record)}>{reporter_name}</a>
      // ),
    },
    {
      title: filter.status == 1 || filter.status == 3 ? '最新进展时间' : '办结时间',
      key: 'process_time',
      dataIndex: 'process_time',
      render: (text: number, record: any) => {
        const time =
          filter.status == 1 || filter.status == 3
            ? moment(record.process_time)
            : moment(record.finish_time);
        // const time = moment(record.process_time)
        return (
          <div>
            <div>{time.format('YYYY-MM-DD')}</div>
            <div>{time.format('HH:mm:ss')}</div>
          </div>
        );
      },
      width: 100,
    },
    {
      title: '关联稿件',
      key: 'related_article_title',
      dataIndex: 'related_article_title',
      width: 200,
      render: (related_article_title: any, record: any) => (
        <a onClick={() => window.open(record.related_article_url, '_blank')}>
          {' '}
          {related_article_title}
        </a>
      ),
    },
    {
      title: '操作',
      key: 'op',
      align: 'center',
      width: 90,
      render: (text: any, record: any) => {
        return filter.status == 1 || filter.status == 3
          ? getDropDown2(record)
          : getDropDown3(record);
      },
    },
  ];
  const getDropDown2 = (record: any) => {
    const menu = (
      <Menu>
        {/* {requirePerm4Function(
          session,
          `report:${biz_type}:change_audit_status`
        )(<Menu.Item onClick={() => handleReAudit(record)}>重新审核</Menu.Item>)} */}
        {/* {requirePerm4Function(
          session,
          `report:${biz_type}:update_classify`
        )(<Menu.Item onClick={() => handleChangeDomain(record)}>修改领域</Menu.Item>)} */}
        {requirePerm4Function(
          session,
          `report:${biz_type}:update_reporter`
        )(<Menu.Item onClick={() => handleAssign(record)}>指派记者</Menu.Item>)}
        <Menu.Item>
          {!!record.url ? (
            <ReactClipboard
              action="copy"
              text={record.url}
              onSuccess={() => message.success('链接已复制')}
              onError={() => message.error('复制失败')}
            >
              <a>复制链接</a>
            </ReactClipboard>
          ) : (
            '复制链接'
          )}
        </Menu.Item>
        {requirePerm4Function(
          session,
          `report:${biz_type}:update_article`
        )(<Menu.Item onClick={() => handleRelavanceArticle(record)}>关联稿件</Menu.Item>)}
        {requirePerm4Function(
          session,
          filter.status == 1 ? `report:${biz_type}:focus` : `report:${biz_type}:process`
        )(
          <Menu.Item onClick={() => changeReportFocusStatus(record)}>
            {filter.status == 1 ? '设为持续关注' : '设为跟进中'}
          </Menu.Item>
        )}

        {requirePerm4Function(
          session,
          `report:${biz_type}:finish`
        )(<Menu.Item onClick={() => handleFinish(record)}>设为办结</Menu.Item>)}
      </Menu>
    );
    return (
      <Dropdown overlay={menu}>
        <a className="ant-dropdown-link">
          操作 <Icon type="down" />
        </a>
      </Dropdown>
    );
  };
  const getDropDown3 = (record: any) => {
    const menu = (
      <Menu>
        {/* {requirePerm4Function(
          session,
          `report:${biz_type}:change_audit_status`
        )(<Menu.Item onClick={() => handleReAudit(record)}>重新审核</Menu.Item>)} */}
        <Menu.Item>
          {!!record.url ? (
            <ReactClipboard
              action="copy"
              text={record.url}
              onSuccess={() => message.success('链接已复制')}
              onError={() => message.error('复制失败')}
            >
              <a>复制链接</a>
            </ReactClipboard>
          ) : (
            '复制链接'
          )}
        </Menu.Item>
        {requirePerm4Function(
          session,
          `report:${biz_type}:update_article`
        )(<Menu.Item onClick={() => handleRelavanceArticle(record)}>关联稿件</Menu.Item>)}
      </Menu>
    );
    return (
      <Dropdown overlay={menu}>
        <a className="ant-dropdown-link">
          操作 <Icon type="down" />
        </a>
      </Dropdown>
    );
  };

  const reportTypeList =
    biz_type == 5
      ? [
          { key: 7, value: '求助力' },
          { key: 8, value: '求表扬' },
          { key: 9, value: '求流量' },
        ]
      : biz_type != 4
      ? [
          { key: 1, value: '投诉举报' },
          { key: 2, value: '咨询求助' },
          { key: 3, value: '建言献策' },
        ]
      : [
          { key: 4, value: '防汛应急' },
          { key: 5, value: '防台应急' },
          { key: 6, value: '其它应急' },
        ];
  // 领域分类转义
  const resolveType = (val: any) => {
    let name = '未知';
    switch (val) {
      case 1:
        name = '营商环境';
        break;
      case 2:
        name = '医疗';
        break;
      case 3:
        name = '教育';
        break;
      case 4:
        name = '城建';
        break;
      case 5:
        name = '交通';
        break;
      case 6:
        name = '政务';
        break;
      case 7:
        name = '金融';
        break;
      case 8:
        name = '企业';
        break;
      case 9:
        name = '就业';
        break;
      case 10:
        name = '文娱';
        break;
      case 11:
        name = '旅游';
        break;
      case 12:
        name = '治安';
        break;
      case 13:
        name = '环保';
        break;
      case 14:
        name = '三农';
        break;
    }
    return name;
  };

  const changeFilter = (key: string, val: any, goToFirstPage = true) => {
    let newFilter = {
      ...filter,
      [key]: val,
    };
    setFilter(newFilter);
  };

  const handleKey = (e: { which: number }) => {
    if (e.which === 13) {
      const newFilter = {
        ...filter,
        ...searchState,
      };
      setFilter(newFilter);
    }
  };

  // 报料标题点击
  const showDetailModal = (record: any, visible: boolean) => {
    setReportDetailDrawer({
      record,
      visible,
      skey: Date.now(),
    });
  };
  // 爆料人/审核人点击
  const showUserDetailModal = (record: any) => {
    dispatch(setConfig({ loading: true }));
    userApi
      .getUserDetail({ accountId: record.account_id })
      .then((r: any) => {
        dispatch(setConfig({ loading: false }));
        setUserDetailModal({
          key: uuid(),
          visible: true,
          detail: r.data.account,
        });
      })
      .catch(() => dispatch(setConfig({ loading: false })));
  };

  // 设为办结
  const handleFinish = (val: any) => {
    Modal.confirm({
      title: '确定将该报料设为办结？',
      onOk: () => {
        // setLoading(this, true);
        api
          .setReportFinish({ id: val.id, biz_type: val.biz_type })
          .then(() => {
            message.success('操作成功');
            // setLoading(this, false);
            // this.getData();
            // getTableList()
            getData();
          })
          .catch(() => {});
      },
    });
  };

  const changeReportFocusStatus = (val: any) => {
    Modal.confirm({
      title: `确定将该报料设为${filter.status == 1 ? '持续关注' : '跟进中'}？`,
      onOk: () => {
        // setLoading(this, true);
        const request = filter.status == 1 ? api.setReportFocus : api.setReportProcess;
        request({ id: val.id, biz_type: val.biz_type })
          .then(() => {
            message.success('操作成功');
            // setLoading(this, false);
            // this.getData();
            // getTableList()
            getData();
          })
          .catch(() => {});
      },
    });
  };

  const getFilter = () => {
    const body: any = {
      ...filter,
    };

    if (!body.keyword) {
      delete body.keyword;
      delete body.search_type;
    }

    return body;
  };

  // 获取table
  const getData = (goToFirstPage = false, newFilter = getFilter()) => {
    let cur = goToFirstPage ? 1 : current;
    dispatch(getTableList('getFollowList', 'list', { current: cur, size, ...newFilter }));
  };
  // 推荐报料
  const recommend = () => {
    history.push(`/view/reportHelpMgr/recommend?type=${biz_type}`);
  };

  // 指派记者
  const handleAssign = (val: any) => {
    setReporterModal({
      visible: true,
      formContent: val,
    });
  };
  // 关联稿件
  const handleRelavanceArticle = (val: any) => {
    setRelevanceArticleModal({
      visible: true,
      formContent: val,
    });
  };

  // 修改领域
  const handleChangeDomain = (val: any) => {
    setDomain({
      visible: true,
      type: 0,
      formContent: val,
    });
  };
  // 重新审核
  const handleReAudit = (val: any) => {
    Modal.confirm({
      title: '确定将该报料重新设为【待审核】状态？',
      onOk: () => {
        dispatch(setConfig({ loading: true }));
        api
          .changeAuditStatus({ id: val.id, status: 0, biz_type: val.biz_type })
          .then(() => {
            message.success('操作成功');
            dispatch(setConfig({ loading: false }));
            getData();
          })
          .catch(() => {
            dispatch(setConfig({ loading: false }));
          });
      },
    });
  };

  // 通过确认
  const handleAddDomainOk = () => {
    setDomain({ visible: false });
    getData();
  };

  // 时间筛选
  const dateChange = (_: any, dateString: string[], goToFirstPage = true) => {
    let newFilter = {
      ...filter,
      start_time: dateString[0],
      end_time: dateString[1],
    };
    setFilter(newFilter);
  };

  // 勾选管理稿件
  const onChangeDraft = (val: any, goToFirstPage = true) => {
    val.target.checked;
    let newFilter = {
      ...filter,
      show_related_article: val.target.checked,
    };
    setFilter(newFilter);
  };
  // 勾选只看我自己
  const onChangeMy = (val: any, goToFirstPage = true) => {
    val.target.checked;
    let newFilter = {
      ...filter,
      show_own: val.target.checked,
    };
    setFilter(newFilter);
  };
  // 指派记者确认
  const handleReporterOk = () => {
    if (!reporter.reporter_id) {
      message.error('请选择指派记者');
      return;
    }
  };
  // 远程搜索记者
  const handleReporterSearch = (val: any) => {};
  // 选择指派记者
  const handleReporterChange = (val: any) => {
    let newObj = {
      ...reporter,
      reporter_id: val,
    };
    setReporter(newObj);
  };

  const getFieldsList = () => {
    api
      .getSimpleReportFieldList({})
      .then((res: any) => {
        setFieldsList(res.data.list || []);
      })
      .catch(() => {});
  };

  useEffect(() => {
    const { selectKeys, openKeys } = props;
    dispatch(setConfig({ selectKeys, openKeys }));

    getFieldsList();
  }, []);

  useEffect(() => {
    getData(true);
  }, [filter]);

  const addRecommendReportCount = () => {
    let pos = 1;
    const valueChange = (v: any) => {
      // msg = v.replace(/\r\n/g, '\n').slice(0, 40)
      pos = v;
      modal.update({
        content: (
          <>
            <div>最新报道模块显示的稿件数量：</div>
            <InputNumber
              placeholder="请输入1～10的数字"
              min={1}
              max={10}
              onChange={(e) => valueChange(e)}
              value={pos}
              style={{ width: '100%' }}
              precision={0}
            />
          </>
        ),
      });
    };

    var modal = Modal.confirm({
      title: '最新报道',
      icon: null,
      content: (
        <>
          <div>最新报道模块显示的稿件数量：</div>
          <InputNumber
            placeholder="请输入1～10的数字"
            min={1}
            max={10}
            onChange={(e) => valueChange(e)}
            defaultValue={pos}
            style={{ width: '100%' }}
            precision={0}
          />
        </>
      ),
      onOk: (destroy: Function) => {
        const { permissions } = session;
        const disabled = permissions.indexOf('report:articlenum_save') === -1;
        if (disabled) {
          message.error('没有权限');
          destroy();
          return;
        }

        if (pos == null) {
          message.error('请输入1～10的数字');
          return;
        }

        reportApi
          .saveRecommendReportCount({ num: pos || 1 })
          .then((data: any) => {
            message.success('操作成功');
          })
          .catch((e) => {});

        // run(api.gptQuestionWelcomeSave, { welcome: msg }, true).then(() => {
        //   message.success('操作成功');
        // });
        destroy();
      },
    });

    reportApi
      .getRecommendReportCount({ biz_type })
      .then((data: any) => {
        const {
          data: { num = 1 },
        } = data;
        valueChange(num);
      })
      .catch((e) => {});
  };

  const showPicConfig = () => {
    reportApi
      .getPicConfig({ type: biz_type == 5 ? 50 : 45 })
      .then((data: any) => {
        setPicConfig({
          visible: true,
          formContent: (biz_type == 5 ? data?.data?.recommend : data?.data?.pic_config) || {},
          type: biz_type == 5 ? 50 : 45,
        });
      })
      .catch((e) => {});
  };

  return (
    <>
      <Row className="layout-infobar">
        <Col span={12}>
          <Radio.Group
            defaultValue={filter.status}
            buttonStyle="solid"
            onChange={(val: any) => changeFilter('status', val.target.value)}
          >
            <Radio.Button value={0}>待处理</Radio.Button>
            <Radio.Button value={1}>跟进中</Radio.Button>
            <Radio.Button value={3}>持续关注</Radio.Button>
            <Radio.Button value={2}>已办结</Radio.Button>
          </Radio.Group>
          {biz_type < 4 && (
            <>
              <PermButton
                perm={`report:${biz_type}:recommend:mgr`}
                onClick={() => {
                  recommend();
                }}
                style={{ marginLeft: 8, marginRight: 8 }}
              >
                推荐报料
              </PermButton>
              <Tooltip title={'推荐报料将在记者帮首页的对应业务模块里优先显示'}>
                <Icon type="question-circle" />
              </Tooltip>
            </>
          )}
          {biz_type > 3 && (
            <PermButton
              perm={`report:${biz_type}:pic_mgr`}
              onClick={() => showPicConfig()}
              style={{ marginLeft: 8, marginRight: 8 }}
            >
              功能图片配置
            </PermButton>
          )}
          {biz_type == 2 && (
            <>
              <PermButton
                perm=""
                onClick={() => {
                  addRecommendReportCount();
                }}
                style={{ marginLeft: 8, marginRight: 8 }}
              >
                最新报道
              </PermButton>
              <Tooltip title={'用于管理在记者帮首页的最新报道模块，显示的稿件数量'}>
                <Icon type="question-circle" />
              </Tooltip>
            </>
          )}
        </Col>
        <Col span={12} className="layout-breadcrumb">
          {getCrumb(props.breadCrumb)}
        </Col>
      </Row>
      <div className="component-content">
        <Row style={{ marginBottom: 16 }}>
          <Col span={14}>
            <DatePicker.RangePicker
              style={{ width: 240, marginRight: 8 }}
              onChange={dateChange}
              format="YYYY-MM-DD"
            />
            <Tooltip title={'按报料时间筛选；如果修改过报料时间，使用修改的时间'}>
              <Icon type="question-circle" style={{ marginLeft: 8, marginRight: 8 }} />
            </Tooltip>
            {/* <Select
              style={{ width: 110 }}
              value={filter.classify_id}
              onChange={(val: any) => changeFilter('classify_id', val)}
            >
              <Select.Option value="">领域分类</Select.Option>
              <Select.Option value={1}>营商环境</Select.Option>
              <Select.Option value={2}>医疗</Select.Option>
              <Select.Option value={3}>教育</Select.Option>
              <Select.Option value={4}>城建</Select.Option>
              <Select.Option value={5}>交通</Select.Option>
              <Select.Option value={6}>政务</Select.Option>
              <Select.Option value={7}>金融</Select.Option>
              <Select.Option value={8}>企业</Select.Option>
              <Select.Option value={9}>就业</Select.Option>
              <Select.Option value={10}>文娱</Select.Option>
              <Select.Option value={11}>旅游</Select.Option>
              <Select.Option value={12}>治安</Select.Option>
              <Select.Option value={13}>环保</Select.Option>
              <Select.Option value={14}>三农</Select.Option>
            </Select> */}

            <Select
              style={{ width: 110, marginLeft: 8 }}
              value={filter.report_type}
              onChange={(val: any) => changeFilter('report_type', val)}
            >
              <Select.Option value="">报料类型</Select.Option>
              {reportTypeList.map((item: any) => {
                return <Select.Option value={+item.key}>{item.value}</Select.Option>;
              })}
            </Select>
            {filter.status !== 0 && (
              <>
                <Checkbox style={{ marginLeft: 8 }} onChange={onChangeDraft}>
                  关联稿件
                </Checkbox>
                <Checkbox onChange={onChangeMy}>只看我负责的</Checkbox>
              </>
            )}
            <Select
              style={{ width: 110, marginLeft: 8 }}
              value={filter.field_list}
              onChange={(val: any) => changeFilter('field_list', val)}
            >
              <Select.Option key={-2} value="">
                关联领域
              </Select.Option>
              {/* <Select.Option key={'0'} value="0">无领域</Select.Option> */}
              {fieldsList?.map((item: any) => {
                return (
                  <Select.Option key={`${item.id}`} value={`${item.id}`}>
                    {item.name}
                  </Select.Option>
                );
              })}
            </Select>

            <Select
              style={{ width: 110, marginLeft: 8 }}
              value={filter.from_source}
              onChange={(val: any) => changeFilter('from_source', val)}
            >
              <Select.Option key={-2} value="">
                报料渠道
              </Select.Option>
              <Select.Option key={'app'} value="app">
                客户端
              </Select.Option>
              <Select.Option key={'h5'} value="h5">
                端外h5
              </Select.Option>
            </Select>
          </Col>
          <Col span={10} style={{ textAlign: 'right' }}>
            <Select
              value={searchState.search_type}
              style={{ width: 110, marginRight: 8 }}
              onChange={(search_type: any) => setSearchState({ ...searchState, search_type })}
            >
              <Select.Option value={1}>标题</Select.Option>
              <Select.Option value={2}>编号</Select.Option>
              <Select.Option value={5}>报料ID</Select.Option>
              <Select.Option value={3}>昵称</Select.Option>
              <Select.Option value={4}>用户ID</Select.Option>
            </Select>
            <Input
              value={searchState.keyword}
              style={{ marginRight: 8, width: 160 }}
              onChange={(e: any) => setSearchState({ ...searchState, keyword: e.target.value })}
              onKeyPress={handleKey}
              placeholder="输入搜索内容"
            />
            <Button onClick={() => handleKey({ which: 13 })}>
              <Icon type="search" /> 搜索
            </Button>
          </Col>
        </Row>
        <Table
          func="getFollowList"
          index="list"
          pagination={true}
          rowKey="id"
          filter={getFilter()}
          columns={filter.status === 0 ? columns : columns2}
        />

        {/* 详情 */}
        <ReportDetailDrawer
          record={reportDetailDrawer.record}
          key={reportDetailDrawer.skey}
          visible={reportDetailDrawer.visible}
          onClose={() => setReportDetailDrawer({ visible: false })}
          type={1}
          onReload={() => getData(false)}
        ></ReportDetailDrawer>

        {/* 指派领域 审核通过 */}
        <AddDomainModal
          {...domain}
          onCancel={() => setDomain({ visible: false })}
          onOk={handleAddDomainOk}
        ></AddDomainModal>

        {/* 指派记者 */}
        <AddReporterModal
          {...reporterModal}
          onCancel={() => setReporterModal({ visible: false })}
          onOk={() => {
            setReporterModal({ visible: false });
            getData();
          }}
        ></AddReporterModal>

        {/* 关联稿件 */}
        <RelevanceArticle
          {...relevanceArticleModal}
          onCancel={() => setRelevanceArticleModal({ visible: false })}
          onOk={() => {
            setRelevanceArticleModal({ visible: false });
            getData();
          }}
        ></RelevanceArticle>

        {/* 用户详情弹窗 */}
        <Modal
          visible={userDetailModal.visible}
          key={userDetailModal.key}
          title="用户详情"
          width={800}
          onCancel={() => setUserDetailModal({ visible: false })}
          onOk={() => setUserDetailModal({ visible: false })}
        >
          {/*{user.visible && getUserDetail(user.detail)}*/}
          {userDetailModal.visible && <UserDetail detail={userDetailModal.detail} />}
        </Modal>

        <PicConfigFormDrawer
          {...picConfig}
          onClose={() => setPicConfig({ visible: false })}
          onEnd={() => setPicConfig({ visible: false })}
        ></PicConfigFormDrawer>
      </div>
    </>
  );
}
