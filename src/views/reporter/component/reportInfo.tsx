import React, { forwardRef, useEffect, useImperativeHandle, useRef, useState } from 'react';
import '../styles/reportDrawer.scss';
import { Col, Row, Tag, Timeline } from 'antd';
import moment from 'moment';
import EllipsisText from './EllipsisText';
import { PhotoSlider } from 'react-photo-view';

const ReportInfo = (props: any, ref: any) => {
  const {
    detail: {
      created_at = '',
      number = '',
      nick_name = '',
      contact_name = '',
      contact_phone = '',
      status = 0,
      remark = '',
      journalist_user_id = '',
      journalist_user_name = '',
      journalist_user_type = 0,
    } = {},
    reply_list = [],
  } = props;
  console.log(props.detail,'props16545645634654654')
  const [imagePreview, setImagePreview] = useState<any>({
    visible: false,
    imgs: [],
    index: 0,
  });

  useImperativeHandle(
    ref,
    () => ({
      ...props.form,
    }),
    []
  );
  return (
    <div className={`${props.className} report_info_wrapper`}>
      {/* 联系信息 */}
      <div>
        <div className="report_info_header">
          <span>联系信息</span>
        </div>

        <div className="contact_info">
          <Row key={0}>
            <Col span={7}>报料时间：</Col>
            <Col span={17}>
              {moment(created_at).format('YYYY-MM-DD HH:mm:ss')}&nbsp;
              <a onClick={props.changeTime}>修改</a>
            </Col>
          </Row>
          <Row key={1}>
            <Col span={7}>报料编号：</Col>
            <Col span={17}>{number}</Col>
          </Row>
          <Row key={2}>
            <Col span={7}>用户昵称：</Col>
            <Col span={17}>
              <a onClick={props.showUserDetailModal}>{nick_name}</a>
            </Col>
          </Row>
          <Row key={3}>
            <Col span={7}>联系人：</Col>
            <Col span={17}>{contact_name}</Col>
          </Row>
          <Row key={4}>
            <Col span={7}>手机号码：</Col>
            <Col span={17}>{contact_phone}</Col>
          </Row>
        </div>
      </div>

      {/* 备注 */}
      {!!remark && (
        <div>
          <div className="report_info_header">
            <span>备注信息</span>
            <a
              onClick={() => {
                props.showRemarkModal && props.showRemarkModal();
              }}
            >
              修改
            </a>
          </div>
          <EllipsisText key={remark} text={remark} maxLine={5} lineHeight={21}></EllipsisText>
        </div>
      )}

      {/* 回复 */}
      {Boolean(props.type == 1) && (
        <div>
          <div className="report_info_header">
            <span>回复</span>
            <a onClick={() => props.showCommentDrawer && props.showCommentDrawer()}>管理</a>
          </div>

          {props.reply_list?.map((v: any, i: number) => {
            return (
              <div className="comment_wrapper" key={`${v.id}-${i}`}>
                <div className="comment_header">
                  <img src={v.head_img} alt="用户头像"></img>
                  <div className="comment_user">
                    <span>{v.name}</span>
                    <span>{moment(v.created_at).format('YYYY-MM-DD HH:mm:ss')}</span>
                  </div>
                </div>
                <EllipsisText
                  key={v.content}
                  text={v.content}
                  maxLine={5}
                  lineHeight={21}
                ></EllipsisText>
                {v.pic_urls?.length > 0 && (
                  <div className="table_comment_imgs">
                    {v.pic_urls?.split(',')?.map((image: any, i: number) => {
                      return (
                        <img
                          onClick={() => {
                            setImagePreview({
                              visible: true,
                              imgs: v.pic_urls?.split(','),
                              index: i,
                            });
                          }}
                          src={image}
                          alt="回复图片"
                          key={image}
                        ></img>
                      );
                    })}
                  </div>
                )}
              </div>
            );
          })}
        </div>
      )}

      <PhotoSlider
        maskOpacity={0.5}
        images={imagePreview.imgs?.map((v: any) => ({ src: v, key: v })) || []}
        visible={imagePreview.visible}
        onClose={() => setImagePreview({ visible: false })}
        index={imagePreview.index}
        onIndexChange={(index) => setImagePreview({ ...imagePreview, index })}
      />
    </div>
  );
};

export default forwardRef<any, any>(ReportInfo);
