import { Button, Modal, Timeline, message } from 'antd';
import React, { forwardRef, useEffect, useState } from 'react';

import { reportApi, opApi, userApi } from '@app/api';
import '@components/business/styles/business.scss';
import { UserDetail } from '@app/utils/utils';
import { useDispatch } from 'react-redux';
import { setConfig } from '@app/action/config';
import { Drawer } from '@app/components/common';
import ReportFormDrawer from './reportFormDrawer';
import '../styles/reportDrawer.scss';
import ReportDetail from './reportDetail';
import OriginalReportDetailDrawer from './originalReportDetailDrawer';
import ReportInfo from './reportInfo';
import moment from 'moment';
import uuid from 'uuid';
import { PermButton } from '@app/components/permItems';
import AddRemarkModal from './addRemarkModal';
import ChangeReportTimeModal from './changeReportTimeModal';
import ReportCommentDrawer from './reportCommentDrawer';

declare const BUILD_ENV: 'dev' | 'test' | 'prev' | 'prod' | 'testb';

const ReportDetailDrawer = (props: any, ref: any) => {
  const dispatch = useDispatch();
  const [detail, setDetail] = useState<any>({});
  const [processList, setProcessList] = useState<any>([]);
  const [replyList, setReplyList] = useState<any>([]);
  const [reportFormDrawer, setReportFormDrawer] = useState<any>({
    visible: false,
    key: uuid(),
    formContent: null,
  });
  const [operateLog, setOperateLog] = useState<any>({
    visible: false,
    logs: null,
  });
  const [userDetail, setUserDetail] = useState<any>({
    visible: false,
    detail: null,
  });
  const [changeTimeModal, setChangeTimeModal] = useState<any>({
    visible: false,
    type: 0,
    formContent: null,
  });
  const [remarkModal, setRemarkModal] = useState<any>({
    visible: false,
    formContent: null,
  });
  const [originalReporterDetail, setOriginalReporterDetail] = useState<any>({
    visible: false,
    formContent: null,
  });
  const [reportCommentDrawer, setReportCommentDrawer] = useState<any>({
    visible: false,
    formContent: null,
  });
  useEffect(() => {
    if (props.visible) {
      getDetail();
    }
  }, [props.visible]);

  const getDetail = () => {
    let arr: any = [
      reportApi.getReportDetail({ id: props.record.id, biz_type: props.record.biz_type }),
    ];
    if (props.type == 1) {
      arr = arr.concat([
        reportApi.reportReplyList({
          report_id: props.record.id,
          biz_type: props.record.biz_type,
        }),
      ]);
    }

    dispatch(setConfig({ mLoading: true }));
    Promise.allSettled(arr)
      .then((values: any) => {
        dispatch(setConfig({ mLoading: false }));
        if (!!values?.[0]) {
          const obj = values?.[0];
          if (obj?.status == 'fulfilled') {
            setDetail(obj?.value?.data?.detail || {});
          } else if (obj?.reason?.code == 30201) {
            // 详情报错
            props.onReload && props.onReload();
            props.onClose && props.onClose();
            return;
          }
        }

        if (!!values?.[1]) {
          const obj = values?.[1];
          if (obj?.status == 'fulfilled') {
            setReplyList(obj?.value?.data?.list || []);
          }
        }
      })
      .catch((e: any) => {
        dispatch(setConfig({ mLoading: false }));
      });
  };

  const handleDelete = () => {
    Modal.confirm({
      title: '确定删除该报料？',
      onOk: () => {
        reportApi
          .changeAuditStatus({ id: props.record.id, status: 10, biz_type: props.record.biz_type })
          .then(() => {
            message.success('操作成功');
            getDetail();
            props.onReload && props.onReload();
            props.onClose && props.onClose();
          })
          .catch(() => {});
      },
    });
  };

  const handlePass = () => {
    Modal.confirm({
      title: '确定通过该报料？',
      onOk: () => {
        reportApi
          .changeAuditStatus({ id: props.record.id, status: 1, biz_type: props.record.biz_type })
          .then(() => {
            message.success('操作成功');
            getDetail();
            props.onReload && props.onReload();
            props.onClose && props.onClose();
          })
          .catch(() => {});
      },
    });
  };

  const handleNoPass = () => {
    Modal.confirm({
      title: '确定不通过该报料？',
      onOk: () => {
        reportApi
          .changeAuditStatus({ id: props.record.id, status: 2, biz_type: props.record.biz_type })
          .then(() => {
            message.success('操作成功');
            getDetail();
            props.onReload && props.onReload();
            props.onClose && props.onClose();
          })
          .catch(() => {});
      },
    });
  };

  const handleReview = () => {
    Modal.confirm({
      title: '确定将该报料重新设为【待审核】状态？',
      onOk: () => {
        reportApi
          .changeAuditStatus({ id: props.record.id, status: 0, biz_type: props.record.biz_type })
          .then(() => {
            message.success('操作成功');
            getDetail();
            props.onReload && props.onReload();
            props.onClose && props.onClose();
          })
          .catch(() => {});
      },
    });
  };

  const showOriginalDetail = () => {
    setOriginalReporterDetail({
      visible: true,
      formContent: props.record,
    });
  };

  const editReport = () => {
    setReportFormDrawer({
      visible: true,
      key: uuid(),
      formContent: detail,
    });
  };
  const showCommentDrawer = () => {
    setReportCommentDrawer({
      visible: true,
      // skey: uuid(),
      formContent: props.record,
    });
  };
  // 操作日志
  const getOperateLog = () => {
    opApi
      .getOperateLog({ target_id: props.record.id, type: 162 })
      .then((r: any) => {
        setOperateLog({
          visible: true,
          logs: r.data.admin_log_list,
          key: uuid(),
        });
      })
      .catch();
  };

  const showRemarkModal = () => {
    setRemarkModal({
      visible: true,
      formContent: detail,
    });
  };

  // 显示用户详情
  const showUserDetailModal = () => {
    dispatch(setConfig({ mLoading: true }));
    userApi
      .getUserDetail({ accountId: detail.account_id })
      .then((r: any) => {
        dispatch(setConfig({ mLoading: false }));
        setUserDetail({
          key: uuid(),
          visible: true,
          detail: r.data.account,
        });
      })
      .catch(() => dispatch(setConfig({ mLoading: false })));
  };

  const changeTime = () => {
    setChangeTimeModal({
      visible: true,
      key: uuid(),
      formContent: detail,
    });
  };

  return (
    <Drawer
      title={
        <div style={{ display: 'flex', alignItems: 'center' }}>
          <div style={{ marginRight: 8 }}>报料详情</div>
          {detail?.has_copy && (
            <Button style={{ width: 100, marginRight: 8 }} onClick={showOriginalDetail}>
              原始报料
            </Button>
          )}
          <Button style={{ width: 100, marginRight: 8 }} onClick={getOperateLog}>
            操作日志
          </Button>
        </div>
      }
      width={1000}
      maskClosable={true}
      visible={props.visible}
      skey={props.skey}
      onClose={props.onClose}
      footer={
        <div
          style={{
            width: '100%',
            display: 'flex',
            justifyContent: 'space-between',
          }}
        >
          {Boolean(
            (props.type == 0 && [0, 1].includes(detail?.audit_status ?? -1)) || props.type == 1
          ) && (
            <PermButton
              perm={'report:update'}
              style={{ width: 100 }}
              onClick={editReport}
            >
              编辑报料
            </PermButton>
          )}

          <div></div>

          {/* 审核报料 */}
          {props.type == 0 && (
            <div>
              {[0, 1, 2].includes(detail?.audit_status ?? -1) && (
                <PermButton
                  perm="report:doc:change_audit_status"
                  style={{ marginRight: 8, color: 'red' }}
                  onClick={handleDelete}
                >
                  删除
                </PermButton>
              )}
              {[11, 12].includes(detail?.audit_status ?? -1) && (
                <PermButton
                  perm="report:doc:change_audit_status"
                  style={{ marginRight: 8, width: 100, color: 'red' }}
                  onClick={handleDelete}
                >
                  彻底删除
                </PermButton>
              )}
              {[1, 2].includes(detail?.audit_status ?? -1) && (
                <PermButton
                  perm="report:doc:change_audit_status"
                  style={{ width: 100, marginRight: 8 }}
                  onClick={() => handleReview()}
                >
                  重新审核
                </PermButton>
              )}
              {[0, 1].includes(detail?.audit_status ?? -1) && (
                <PermButton
                  perm="report:doc:change_audit_status"
                  style={{ width: 100, marginRight: 8 }}
                  onClick={handleNoPass}
                >
                  不通过
                </PermButton>
              )}
              {[0, 2].includes(detail?.audit_status ?? -1) && (
                <PermButton
                  perm="report:doc:change_audit_status"
                  style={{ width: 100 }}
                  type="primary"
                  onClick={handlePass}
                >
                  通过
                </PermButton>
              )}
            </div>
          )}
        </div>
      }
    >
      <div className="drawer_detail_content">
        <ReportDetail className="report_content" detail={detail}></ReportDetail>
        <ReportInfo
          className="report_info"
          type={props.type}
          detail={detail}
          reply_list={replyList}
          showUserDetailModal={showUserDetailModal}
          showRemarkModal={showRemarkModal}
          changeTime={changeTime}
          showCommentDrawer={showCommentDrawer}
        ></ReportInfo>
      </div>
      {/* 编辑爆料 */}
      <ReportFormDrawer
        {...reportFormDrawer}
        onClose={() => setReportFormDrawer({ visible: false })}
        onEnd={() => {
          setReportFormDrawer({ visible: false });
          getDetail();
          props.onReload && props.onReload();
        }}
      ></ReportFormDrawer>

      <AddRemarkModal
        {...remarkModal}
        onCancel={() => setRemarkModal({ visible: false })}
        onOk={() => {
          setRemarkModal({ visible: false });
          getDetail();
          props.onReload && props.onReload();
        }}
      ></AddRemarkModal>

      {/* 原始报料 */}
      <OriginalReportDetailDrawer
        {...originalReporterDetail}
        onClose={() => setOriginalReporterDetail({ visible: false })}
      ></OriginalReportDetailDrawer>
      {/* 回复管理 */}
      <ReportCommentDrawer
        {...reportCommentDrawer}
        onClose={() => setReportCommentDrawer({ visible: false })}
        onReload={getDetail}
      ></ReportCommentDrawer>
      {/* 操作日志 */}
      <Modal
        visible={operateLog.visible}
        title="操作日志"
        key={operateLog.key}
        cancelText={null}
        onCancel={() => setOperateLog({ visible: false })}
        onOk={() => setOperateLog({ visible: false })}
      >
        <div>
          <Timeline>
            {operateLog.logs?.map((v: any, i: number) => [
              <Timeline.Item className="timeline-dot-big" data-show={v.date} key={`time${i}`}>
                &nbsp;
              </Timeline.Item>,
              v.log_list?.map((action: any, index: number) => (
                <Timeline.Item
                  className="timeline-dot"
                  data-show={moment(action.created_at).format('HH:mm:ss')}
                  key={`time${i}-action${index}`}
                >
                  {action.admin_name}&emsp;{action.remark}
                </Timeline.Item>
              )),
            ])}
          </Timeline>
        </div>
      </Modal>

      <ChangeReportTimeModal
        {...changeTimeModal}
        onCancel={() => {
          setChangeTimeModal({ visible: false });
        }}
        onOk={() => {
          setChangeTimeModal({ visible: false });
          getDetail();
          props.onReload && props.onReload();
        }}
      ></ChangeReportTimeModal>

      {/* 用户详情弹窗 */}
      <Modal
        visible={userDetail.visible}
        key={userDetail.key}
        title="用户详情"
        width={800}
        onCancel={() => setUserDetail({ visible: false })}
        onOk={() => setUserDetail({ visible: false })}
      >
        {userDetail.visible && <UserDetail detail={userDetail.detail} />}
      </Modal>
    </Drawer>
  );
};

export default forwardRef<any, any>(ReportDetailDrawer);

// 爆料类型，1-投诉举报，2-咨询求助，3-建言献策
// 业务类型，1-问政，2-帮办，3-帮帮团：
// "id":'1234',
//             	                "biz_type":1,
//             	                "report_type":2,
// 				"number":"1343", //编号
//                                 "title":"xxxx",
//                                 "url":"xxxx",//跳转链接
//                                 "account_id":1234,
//                                 "nick_name":'xxx',//爆料人
//                                 "operator":"bob",
//                                 "reporter_id":1234, //跟进记者id
//                                 "reporter_name":'', //记者名称
//                                 "expert_id":1234, //跟进专家id
//                                 "expert_name":'', //专家名称
//                                 "finish_time":************,//审核时间
//                                 "created_at":**************, //爆料时间
//                                 "medias":[{
//                                         "id":"1234",
//                                         "type":0,
//                                         "url":"https://xxx.jpg"
//                                  }
//                                 ]
