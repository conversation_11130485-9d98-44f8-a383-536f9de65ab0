import { Button, Form, Icon, Input, InputNumber, Modal, Radio, Spin, Tooltip, message } from 'antd'
import React, { forwardRef, useImperativeHandle, useState } from 'react'

import { reportApi } from '@app/api';
import { Drawer, ImageUploader } from '@app/components/common';
import { useDispatch } from 'react-redux';
import { setConfig } from '@app/action/config';
import ImgUploadList from '@app/components/common/imgUploadList';

const AddCommentModal = (props: any, ref: any) => {
  const dispatch = useDispatch();

  const { getFieldDecorator } = props.form;
  useImperativeHandle(
    ref,
    () => ({
      ...props.form,
    }),
    []
  );

  const formLayout = {
    labelCol: { span: 4 },
    wrapperCol: { span: 18 },
  };

  const handleSubmit = () => {
    // e.preventDefault();
    props.form.validateFieldsAndScroll((err: any, values: any) => {
      if (!err) {
        dispatch(setConfig({ mLoading: true }))
        let promise = null
        const parmas = {
          ...props.formContent,
          name: values.name.trim(),
          content: values.content.trim(),
          head_img: values.head_img,
          pic_urls: values.pic_urls?.join(',')
        }
        if (!props.record) {
          // 新增
          promise = reportApi.addReportComment(parmas)
        } else {
          promise = reportApi.updateReportComment(parmas)
        }
        promise.then((res: any) => {
          message.success(!props.record ? '新增成功' : '更新成功');
          dispatch(setConfig({ mLoading: false }))
          props.onEnd && props.onEnd()
        }).catch(() => {
          // message.error('添加失败');
          dispatch(setConfig({ mLoading: false }))
        })
      } else {
        message.error('请检查表单内容');
        dispatch(setConfig({ mLoading: false }))
      }
    });
  }

  return <Drawer
    title={!props.record ? "添加回复" : "编辑回复"}
    visible={props.visible}
    skey={props.skey}
    onClose={props.onClose}
    maskClosable={false}
    // width={500}
    onOk={handleSubmit}
    okText='保存'
  >
    <Form {...formLayout}>
      <Form.Item label="回复人名称">
        {getFieldDecorator('name', {
          initialValue: props.record?.name,
          rules: [
            {
              required: true,
              message: '请输入回复人名称',
              whitespace: true
            },
            {
              max: 16,
              message: '最多16字'
            }
          ],
        })(<Input placeholder='最多16字'></Input>)}
      </Form.Item>

      <Form.Item label="回复人头像" extra="支持jpg,jpeg,png图片格式，比例为1:1">
        {getFieldDecorator('head_img', {
          initialValue: props.record?.head_img || "https://image.jiaxingren.com/assets/20250609/1749461178683_6846a8ba0baf6145a301eae3.png",
          rules: [
            {
              required: true,
              message: '请上传图片',
            },
          ],
        })(<ImageUploader ratio={1 / 1} accept={['image/jpeg', 'image/png', 'image/jpg', 'image/gif']} />)}
      </Form.Item>

      <Form.Item label="回复内容">
        {getFieldDecorator('content', {
          initialValue: props.record?.content,
          rules: [
            {
              required: true,
              message: '请输入回复内容',
              whitespace: true
            },
            {
              // max: 500,
              // message: '最多500字'
              validator: (rule: any, value: any, callback: any) => {
                if (value?.replace(/\r\n/g, '\n')?.length > 5000) {
                  callback('最多5000字')
                  return
                }
                callback()
              }
            }
          ],
        })(<Input.TextArea placeholder='最多5000字' rows={5}></Input.TextArea>)}
      </Form.Item>

      <Form.Item label="图片">
        {getFieldDecorator('pic_urls', {
          initialValue: !!props.record?.pic_urls ? props.record?.pic_urls?.split(',') || [] : [],
          // rules: [
          //   {
          //     required: true,
          //     message: '请上传预览图',
          //   },
          // ],
        })(
          <ImgUploadList
            max={9}
            isCutting={true}
            accept={['image/jpeg', 'image/png', 'image/jpg']}
            // tips={'用于让用户预览主题样式，可上传1～10张图片'}
            extra={"最多可添加9张图，单张图片10MB以内"}
            // onChange={(v: any) => setImgs(v)}
            // disable={imgs.length == 9 ? '只能添加9个附件（图片/视频）' : ''}
            customOp={true}
          />
        )}
      </Form.Item>
    </Form>
  </Drawer>

}

export default Form.create<any>({ name: 'AddCommentModal' })(forwardRef<any, any>(AddCommentModal));
