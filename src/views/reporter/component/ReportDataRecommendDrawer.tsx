import { Form, Input, InputNumber, Radio, Switch, message } from 'antd';
import React, { forwardRef, useEffect, useImperativeHandle, useState } from 'react';

import { reportApi, communityApi } from '@app/api';
import '@components/business/styles/business.scss';
import { useDispatch } from 'react-redux';
import { setConfig } from '@app/action/config';
import { Drawer, ImageUploader, VideoUploader } from '@app/components/common';
import _, { set } from 'lodash';
import '../styles/reportDrawer.scss';
import RecommendCommon from '@app/components/business/recommend/RecommendCommon';
import ArLinkInput, { arLinkValidator, isArLink } from '@app/components/common/arLinkInput';

const ReportDataRecommendDrawer = (props: any, ref: any) => {
  const dispatch = useDispatch();

  const {
    jump_url = '',
    jump_new_page = false,
    width,
    height,
    position = 1,
    url,
  } = props.formContent || {};

  const { getFieldDecorator, getFieldValue } = props.form;
  useImperativeHandle(
    ref,
    () => ({
      ...props.form,
      doSubmit,
    }),
    []
  );

  const doSubmit = () => {
    props.form.validateFieldsAndScroll((err: any, values: any) => {
      if (!err) {
        const body: any = { ...values };
        body.title_show = values.title_style >= 0;
        body.title_pic = values.title_style >= 1 ? values.title_pic : '';
        if (values.jump_enabled) {
          body.jump_model_url = values.jump_url;
        }
        delete body.jump_url;
        delete body.arUrl;
        delete body.linkType;

        if (!!props.formContent) {
          body.id = props.formContent.id;
        }

        dispatch(setConfig({ mLoading: true }));
        (!!props.formContent
          ? reportApi.editReportRecommendData
          : reportApi.createReportRecommendData)(body)
          .then(() => {
            dispatch(setConfig({ mLoading: false }));
            message.success('操作成功');
            props.onEnd();
          })
          .catch(() => {
            dispatch(setConfig({ mLoading: false }));
          });
      } else {
        message.error('请检查表单内容');
      }
    });
  };

  const formLayout = {
    labelCol: { span: 3 },
    wrapperCol: { span: 21 },
  };

  const jumpURL = jump_url;

  return (
    <Drawer
      title={`${!!props.formContent ? '编辑' : '创建'}H5推荐位`}
      visible={props.visible}
      skey={props.skey}
      onClose={props.onClose}
      onOk={doSubmit}
      okText="保存"
      // footer={((<><Button style={{ width: 100, marginRight: 8 }} onClick={props.onClose}>取消</Button>
      //   <Dropdown trigger={['hover']} overlay={menu} placement="topCenter">
      //     <PermButton style={{ width: 100 }} type="primary" perm='' onClick={props.onOk}>
      //       保存<Icon type="up" />
      //     </PermButton>
      //   </Dropdown>

      // </>))}
    >
      <Form {...formLayout}>
        <RecommendCommon
          form={props.form}
          // hiddenTitleType={'读报推荐位'}
          editData={props.formContent || {}}
          isEdit={true}
        ></RecommendCommon>

        <Form.Item label="URL地址">
          {getFieldDecorator('url', {
            initialValue: url,
            rules: [
              {
                required: true,
                message: '请填写URL地址',
              },
              {
                pattern: /^https?:\/\//,
                message: '请填写正确的URL地址',
              },
            ],
          })(<Input placeholder="请输入URL地址" />)}
        </Form.Item>

        <Form.Item label="显示位置">
          {getFieldDecorator('position', {
            initialValue: position,
            rules: [
              {
                required: true,
                message: '请选择',
              },
            ],
          })(
            <Radio.Group
            // onChange={(e: any) => console.log('')}
            >
              <Radio key={1} value={1} style={{ lineHeight: '40px' }}>
                最新报道前面
              </Radio>
              <Radio key={2} value={2}>
                问政模块前面
              </Radio>
              <Radio key={3} value={3}>
                帮办模块前面
              </Radio>
              <Radio key={4} value={4}>
                帮帮团模块前面
              </Radio>
              <Radio key={5} value={5}>
                小店帮模块前面
              </Radio>
              {/* <Radio key={7} value={7}>
                联动帮模块前面
              </Radio> */}
              <Radio key={6} value={6}>
                联盟模块前面
              </Radio>
            </Radio.Group>
          )}
        </Form.Item>

        <Form.Item label="宽度">
          {getFieldDecorator('width', {
            initialValue: width,
            rules: [
              {
                required: true,
                message: '请输入宽度',
              },
            ],
          })(
            <InputNumber
              style={{ width: '100%' }}
              placeholder="请输入具体像素数值"
              min={0}
              precision={0}
              // max={10000}
            />
          )}
        </Form.Item>

        <Form.Item label="高度">
          {getFieldDecorator('height', {
            initialValue: height,
            rules: [
              {
                required: true,
                message: '请输入高度',
              },
            ],
          })(
            <InputNumber
              style={{ width: '100%' }}
              placeholder="请输入具体像素数值"
              min={0}
              precision={0}
              // max={10000}
            />
          )}
        </Form.Item>

        <Form.Item label="跳转新页面">
          {getFieldDecorator('jump_enabled', {
            initialValue: jump_new_page,
            valuePropName: 'checked',
          })(<Switch></Switch>)}
        </Form.Item>

        {getFieldValue('jump_enabled') && (
          <Form.Item label="跳转地址">
            {getFieldDecorator('jump_url', {
              initialValue: jumpURL,
              // preserve: true,
              rules: [
                {
                  required: true,
                  message: '请填写跳转地址',
                },
                {
                  pattern: /^https?:\/\//,
                  message: '请正确填写跳转地址',
                },
              ],
            })(<Input placeholder="请输入跳转地址" />)}
          </Form.Item>
        )}
      </Form>
    </Drawer>
  );
};

export default Form.create<any>({ name: 'ReportDataRecommendDrawer' })(
  forwardRef<any, any>(ReportDataRecommendDrawer)
);
