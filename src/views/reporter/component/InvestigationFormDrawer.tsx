import { But<PERSON>, Drawer, Form, Select, Spin, Switch, Table, message, Radio } from 'antd';
import React, { forwardRef, useEffect, useImperativeHandle, useState } from 'react';

import { reportApi, recommendApi } from '@app/api';
import { useDispatch } from 'react-redux';
import { setConfig } from '@app/action/config';
import _ from 'lodash';

const InvestigationFormDrawer = (props: any, ref: any) => {
  const dispatch = useDispatch();
  const { getFieldDecorator, getFieldValue, setFieldsValue } = props.form;

  const [loading, setLoading] = useState(false);
  const [optionData, setOptionData] = useState<any>([]);
  const [selectedArticle, setSelectedArticle] = useState<any>(null);

  const { status = 0, ref_ids = '', article_info = null } = props.formContent || {};

  // 表单布局
  const formLayout = {
    labelCol: { span: 4 },
    wrapperCol: { span: 18 },
  };

  useImperativeHandle(
    ref,
    () => ({
      ...props.form,
      doSubmit,
    }),
    []
  );

  // 初始化
  useEffect(() => {
    if (props.article) {
      setSelectedArticle(props.article);
      setOptionData([props.article]);
    } else {
      setSelectedArticle(null);
    }
  }, [props.formContent]);

  // 提交表单
  const doSubmit = () => {
    props.form.validateFieldsAndScroll((err: any, values: any) => {
      if (!err) {
        setLoading(true);
        dispatch(setConfig({ loading: true }));

        reportApi
          .saveFirstTierModuleDetail(values)
          .then(() => {
            message.success('操作成功');
            setLoading(false);
            dispatch(setConfig({ loading: false }));
            props.onEnd && props.onEnd();
          })
          .catch(() => {
            setLoading(false);
            dispatch(setConfig({ loading: false }));
          });
      } else {
        message.error('请检查表单内容');
      }
    });
  };

  // 搜索文章
  const handleArticleSearch = _.debounce((val: any) => {
    if (!val) {
      setOptionData([]);
      return;
    }

    let data = { keyword: val.toString(), doc_types: '5' };
    recommendApi
      .searchManuscript(data)
      .then((res: any) => {
        let article_list: any = [...res.data.article_list];
        setOptionData(article_list);
      })
      .catch(() => {
        setOptionData([]);
      });
  }, 500);

  // 选择文章
  const handleArticleChange = (val: any, option: any) => {
    const selectedOption = optionData.find((item: any) => item.id === val);
    setFieldsValue({ ref_ids: val });
    setSelectedArticle(selectedOption);
  };

  // 删除已选择的文章
  const handleRemoveArticle = () => {
    setFieldsValue({ ref_ids: undefined });
    setSelectedArticle(null);
  };

  // 定义表格列
  const columns = [
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id',
      width: 80,
    },
    {
      title: '新闻频道',
      dataIndex: 'channel_name',
      key: 'channel_name',
    },
    {
      title: '新闻标题',
      dataIndex: 'list_title',
      key: 'list_title',
      ellipsis: true,
    },
    {
      title: '操作',
      key: 'action',
      width: 80,
      render: () => (
        <Button 
          type="link" 
          size="small" 
          onClick={handleRemoveArticle}
        >
          删除
        </Button>
      ),
    },
  ];

  return (
    <Drawer
      title="管理一线调查模块"
      visible={props.visible}
      onClose={props.onClose}
      width={600}
      destroyOnClose
      bodyStyle={{ paddingBottom: 80 }}
    >
      <Spin spinning={loading}>
        <Form {...formLayout}>
          <Form.Item label="开启显示">
            {getFieldDecorator('status', {
              initialValue: status,
              rules: [
                {
                  required: true,
                  message: '请设置是否开启显示',
                },
              ],
            })(
              <Radio.Group>
                <Radio value={1}>开启</Radio>
                <Radio value={0}>关闭</Radio>
              </Radio.Group>
            )}
          </Form.Item>
          <Form.Item label="关联稿件">
            {getFieldDecorator('ref_ids', {
              initialValue: ref_ids,
              rules: [
                {
                  required: true,
                  message: '请选择关联稿件',
                },
              ],
            })(
              <Select
                placeholder="输入标题或ID关联稿件"
                onSearch={handleArticleSearch}
                showSearch
                onChange={handleArticleChange}
                filterOption={false}
                allowClear
              >
                {optionData.map((d: any) => (
                  <Select.Option key={d.id} value={d.id}>
                    {`${d.id} - 【${d.channel_name}】- ${d.list_title}`}
                  </Select.Option>
                ))}
              </Select>
            )}
          </Form.Item>
          
          {/* 已选择的稿件信息 */}
          {selectedArticle && (
            <div style={{ marginBottom: 24 }}>
              <div style={{ fontWeight: 'bold', marginBottom: 8 }}>已选择稿件：</div>
              <Table 
                dataSource={[selectedArticle]} 
                columns={columns}
                pagination={false}
                rowKey="id"
                locale={{ emptyText: '暂无数据' }}
              />
            </div>
          )}
        </Form>
      </Spin>

      {/* 底部按钮 */}
      <div
        style={{
          position: 'absolute',
          right: 0,
          bottom: 0,
          width: '100%',
          borderTop: '1px solid #e9e9e9',
          padding: '10px 16px',
          background: '#fff',
          textAlign: 'right',
        }}
      >
        <Button onClick={props.onClose} style={{ marginRight: 8 }}>
          取消
        </Button>
        <Button onClick={doSubmit} type="primary" loading={loading}>
          保存
        </Button>
      </div>
    </Drawer>
  );
};

export default Form.create<any>({ name: 'InvestigationForm' })(
  forwardRef<any, any>(InvestigationFormDrawer)
);
