import {
  <PERSON><PERSON>,
  Col,
  Drawer,
  Form,
  Icon,
  Input,
  Modal,
  Radio,
  Row,
  Select,
  Spin,
  Table,
  message,
} from 'antd';
import React, { forwardRef, useEffect, useImperativeHandle, useState } from 'react';

import { reportApi, recommendApi } from '@app/api';
import { ImageUploader } from '@app/components/common';
import { useDispatch } from 'react-redux';
import { setConfig } from '@app/action/config';
import _ from 'lodash';

const FirstTierFormDrawer = (props: any, ref: any) => {
  const dispatch = useDispatch();
  const { getFieldDecorator, getFieldValue, setFieldsValue } = props.form;
  
  const [loading, setLoading] = useState(false);
  const [optionData, setOptionData] = useState<any>([]);
  const [selectedArticle, setSelectedArticle] = useState<any>(null);

  const {
    id,
    pic_url = '',
    title = '',
    jump_type = 1, // 1 或 2
    url = '',
    ref_ids = '',
  } = props.formContent || {}; 

  // 表单布局
  const formLayout = {
    labelCol: { span: 4 },
    wrapperCol: { span: 18 },
  };

  useImperativeHandle(
    ref,
    () => ({
      ...props.form,
      doSubmit,
    }),
    []
  );

  // 初始化
  useEffect(() => {
    if (props.formContent && props.article) {
      
      setSelectedArticle(props.article);
    } else {
      setSelectedArticle(null);
    }
  }, [props.formContent]);

  // 提交表单
  const doSubmit = () => {
    props.form.validateFieldsAndScroll((err: any, values: any) => {
      if (!err) {
        const body: any = { ...values };
        
        // 去掉配图文字首尾空格
        if (body.title) {
          body.title = body.title.trim();
        }
        
        // 如果选择的是链接跳转，清空文章ID
        if (body.jump_type === 1) {
          body.ref_ids = '';
        } else {
          // 如果选择的是稿件跳转，清空跳转链接
          body.url = '';
        }
        delete body.jump_type;

        setLoading(true);
        dispatch(setConfig({ loading: true }));

        // 根据是否有ID调用不同的API
        const apiMethod = id 
          ? reportApi.editFirstTierList({ ...body, id }) 
          : reportApi.addFirstTierList(body);

        apiMethod
          .then(() => {
            message.success('操作成功');
            props.onEnd && props.onEnd();
          })
          .catch(error => {
            message.error(error.message || '操作失败');
          })
          .finally(() => {
            setLoading(false);
            dispatch(setConfig({ loading: false }));
          });
      } else {
        message.error('请检查表单内容');
      }
    });
  };

  // 搜索文章
  const handleArticleSearch = _.debounce((val: any) => {
    if (!val) {
      setOptionData([]);
      return;
    }

    let data = { keyword: val.toString(), doc_types: '2,3,4,5,8,9' };
    recommendApi.searchManuscript(data)
      .then((res: any) => {
        let article_list: any = [...res.data.article_list];
        setOptionData(article_list);
      })
      .catch(() => {
        setOptionData([]);
      });
  }, 500);

  // 选择文章
  const handleArticleChange = (val: any, option: any) => {
    const selectedOption = optionData.find((item: any) => item.id === val);
    setFieldsValue({ ref_ids: val });
    setSelectedArticle(selectedOption);
  };

  // 删除已选择的文章
  const handleRemoveArticle = () => {
    setFieldsValue({ ref_ids: undefined });
    setSelectedArticle(null);
  };

  // 表格列定义
  const columns = [
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id',
      width: 80,
    },
    {
      title: '新闻频道',
      dataIndex: 'channel_name',
      key: 'channel_name',
    },
    {
      title: '新闻标题',
      dataIndex: 'list_title',
      key: 'list_title',
      ellipsis: true,
    },
    {
      title: '操作',
      key: 'action',
      width: 80,
      render: () => (
        <Button 
          type="link" 
          size="small" 
          onClick={handleRemoveArticle}
        >
          删除
        </Button>
      ),
    },
  ];

  return (
    <Drawer
      title={id ? '编辑一线关注' : '添加一线关注'}
      visible={props.visible}
      onClose={props.onClose}
      width={600}
      destroyOnClose
    >
      <div 
        className="drawer-container" 
        style={{ 
          display: 'flex', 
          flexDirection: 'column', 
          height: 'calc(100% - 55px)' 
        }}
      >
        <Spin spinning={loading}>
          <div style={{ 
            flex: 1, 
            overflow: 'auto', 
            paddingBottom: '60px' 
          }}>
            <Form {...formLayout}>
              <Form.Item label="上传图片" extra="支持上传jpg、jpeg、png、gif图片格式">
                {getFieldDecorator('pic_url', {
                  initialValue: pic_url,
                  rules: [
                    {
                      required: true,
                      message: '请上传图片',
                    },
                  ],
                })(<ImageUploader ratio={3 / 2} />)}
              </Form.Item>

              <Form.Item label="配图文字">
                {getFieldDecorator('title', {
                  initialValue: title,
                  rules: [
                    {
                      max: 30,
                      message: '最多30字',
                    },
                  ],
                })(<Input placeholder="请输入配图文字，最多30字" maxLength={30} />)}
              </Form.Item>

              <Form.Item label="跳转">
                {getFieldDecorator('jump_type', {
                  initialValue: jump_type || 1,
                  rules: [
                    {
                      required: true,
                      message: '请选择跳转类型',
                    },
                  ],
                })(
                  <Radio.Group>
                    <Radio value={1}>链接</Radio>
                    <Radio value={2}>稿件</Radio>
                  </Radio.Group>
                )}
              </Form.Item>

              {getFieldValue('jump_type') === 1 && (
                <Form.Item label="跳转链接">
                  {getFieldDecorator('url', {
                    initialValue: url,
                    rules: [
                      {
                        required: getFieldValue('jump_type') === 1,
                        message: '请输入跳转链接',
                      },
                      {
                        pattern: /^https?:\/\//,
                        message: '请填写正确的URL地址', 
                      },
                    ],
                  })(<Input placeholder="请输入跳转链接" />)}
                </Form.Item>
              )}
              
              {getFieldValue('jump_type') === 2 && (
                <Form.Item label="关联稿件">
                  {getFieldDecorator('ref_ids', {
                    initialValue: ref_ids,
                    rules: [
                      {
                        required: getFieldValue('jump_type') === 2,
                        message: '请选择关联稿件',
                      },
                    ],
                  })(
                    <Select
                      placeholder="输入标题或ID关联稿件"
                      onSearch={handleArticleSearch}
                      showSearch
                      onChange={handleArticleChange}
                      filterOption={false}
                      allowClear
                    >
                      {optionData.map((d: any) => (
                        <Select.Option key={d.id} value={d.id}>
                          {`${d.id} - 【${d.channel_name}】- ${d.list_title}`}
                        </Select.Option>
                      ))}
                    </Select>
                  )}
                </Form.Item>
              )}

              {/* 已选择的稿件列表 */}
              {getFieldValue('jump_type') === 2 && (
                <div style={{ marginBottom: 24 }}>
                  <div style={{ fontWeight: 'bold', marginBottom: 8 }}>已选择稿件：</div>
                  <Table 
                    columns={columns}
                    dataSource={selectedArticle ? [selectedArticle] : []} 
                    pagination={false}
                    rowKey="id"
                    locale={{ emptyText: '暂无数据' }}
                  />
                </div>
              )}
            </Form>
          </div>
        </Spin>
      </div>
      
      <div style={{ 
        position: 'absolute', 
        bottom: 0, 
        width: '100%', 
        borderTop: '1px solid #e8e8e8', 
        padding: '10px 16px', 
        textAlign: 'right', 
        left: 0, 
        backgroundColor: '#fff',
        boxShadow: '0 -2px 8px rgba(0, 0, 0, 0.06)'
      }}>
        <Button onClick={props.onClose} style={{ marginRight: 8 }}>
          取消
        </Button>
        <Button onClick={doSubmit} type="primary" loading={loading}>
          保存
        </Button>
      </div>
    </Drawer>
  );
};

export default Form.create<any>({ name: 'FirstTierForm' })(
  forwardRef<any, any>(FirstTierFormDrawer)
); 