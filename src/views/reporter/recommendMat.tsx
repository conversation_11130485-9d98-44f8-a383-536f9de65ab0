import {
  Col,
  Icon,
  Input,
  Modal,
  Row,
  message,
} from 'antd';
import {
  UserDetail,
  getCrumb,
  reportTypeMap,
  searchToObject,
} from '@app/utils/utils';
import ReactClipboard from 'react-clipboardjs-copy';
import { useHistory } from 'react-router';
import { useSelector, useDispatch, useStore } from 'react-redux';
import { getTableList } from '@app/action/tableList';
import { Table, OrderColumn } from '@components/common';
import React, { useEffect, useState } from 'react';
import { reportApi as api, userApi } from '@app/api';
import { PermA, PermButton } from '@app/components/permItems';
import { setConfig } from '@app/action/config';
import { useLocation } from 'react-router-dom';
import AddRecommendReport from './component/addRecommendReport';
import ReportDetailDrawer from './component/reportDetailDrawer';
import uuid from 'uuid';

export default function recommendMat(props: any) {
  const history = useHistory();
  const location = useLocation();
  const dispatch = useDispatch();
  const { total, current, size, records = [] } = useSelector((state: any) => state.tableList);
  const { TextArea } = Input;
  const { session } = useStore().getState();
  const biz_type = searchToObject()?.type
  const [filter, setFilter] = useState({ biz_type });

  const [addRecommendReport, setAddRecommendReport] = useState<any>({
    visible: false,
    formContent: null,
  })

  const [userDetailModal, setUserDetailModal] = useState<any>({
    visible: false,
    key: uuid(),
    detail: null,
  })

  // 详情
  const [reportDetailDrawer, setReportDetailDrawer] = useState<any>({
    visible: false,
    record: null,
    skey: Date.now()
  })

  const getSeq = (i: number) => (current - 1) * size + i + 1;

  const columns: any = [
    {
      title: '排序',
      key: 'order',
      render: (text: any, record: any, i: number) => {
        const pos = getSeq(i);
        return (
          <OrderColumn
            pos={pos}
            start={1}
            end={total}
            perm=""
            // disableUp={!record.enabled || (i > 0 && !records[i - 1].enabled)}
            // disableDown={!record.enabled || (i < records.length - 1 && !records[i + 1].enabled)}
            onUp={() => exchangeOrder(record.id, getSeq(i), 0)}
            onDown={() => exchangeOrder(record.id, getSeq(i), 1)}
          />
        );
      },
      width: 70,
    },
    {
      title: '序号',
      key: 'id',
      render: (a: any, b: any, index: number) => <span>{getSeq(index)}</span>,
      width: 50,
    },
    {
      title: '编号/ID',
      dataIndex: 'number',
      width: 250,
      render: (text: any, record: any) => (<div>
        <div>编号：{record.number}</div>
        <div>ID：{record.id}</div>
      </div>)
    },
    {
      title: '报料类型',
      dataIndex: 'report_type',
      render: (report_type: any) => reportTypeMap(report_type),
      width: 100,
    },
    {
      title: '报料标题',
      key: 'title',
      dataIndex: 'title',
      render: (title: any, record: any) => (
        <a onClick={() => showDetailModal(record)}> {title}</a>
      ),
    },
    {
      title: '报料人',
      key: 'nick_name',
      dataIndex: 'nick_name',
      width: 160,
      render: (nick_name: any, record: any) => (
        <a onClick={() => showUserDetailModal(record)}>{nick_name}</a>
      ),
    },
    {
      title: '操作',
      key: 'op',
      render: (text: any, record: any) => (
        <span>
          <PermA perm={`report:${biz_type}:recommend:mgr`} onClick={() => cancelRecommend(record)}>
            取消推荐
          </PermA>
        </span>
      ),
      width: 180,
    },
  ];
  // 排序
  const exchangeOrder = (id: number, current: number, offset: number) => {
    dispatch(setConfig({ loading: true }));
    api
      .sortRecommendMatList({ id, sort_flag: offset, biz_type })
      .then(() => {
        message.success('操作成功');
        getData();
        dispatch(setConfig({ loading: false }));
      })
      .catch(() => {
        dispatch(setConfig({ loading: false }));
      });
  };
  // 取消推荐
  const cancelRecommend = (val: any) => {
    Modal.confirm({
      title: '确定取消推荐该报料？',
      onOk: () => {
        dispatch(setConfig({ loading: true }));
        api
          .cancelRecommendReport({ id: val.id, biz_type })
          .then(() => {
            message.success('操作成功');
            dispatch(setConfig({ loading: false }));
            getData();
          })
          .catch(() => {
            dispatch(setConfig({ loading: false }));
          });
      },
    });
  };
  const changeFilter = (key: string, val: any, goToFirstPage = true) => {
    let newFilter = {
      ...filter,
      [key]: val,
    };
    setFilter(newFilter);
    getData(goToFirstPage, newFilter);
  };

  const handleKey = (e: { which: number }) => {
    if (e.which === 13) {
      const newFilter = {
        ...filter,
      };
      setFilter(newFilter);
      getData(true, newFilter);
    }
  };

  // 报料标题点击
  const showDetailModal = (record: any) => {
    setReportDetailDrawer({
      record,
      visible: true,
      skey: Date.now()
    })
  };

  // 爆料人/审核人点击
  const showUserDetailModal = (record: any) => {
    dispatch(setConfig({ loading: true }))
    userApi
      .getUserDetail({ accountId: record.account_id })
      .then((r: any) => {
        dispatch(setConfig({ loading: false }))
        setUserDetailModal({
          key: uuid(),
          visible: true,
          detail: r.data.account,
        })
      })
      .catch(() => dispatch(setConfig({ loading: false })));
  };
  // 获取table
  const getData = (goToFirstPage = false, newFilter = filter) => {
    let cur = goToFirstPage ? 1 : current;
    dispatch(getTableList('getrecommendMatList', 'list', { current: cur, size, ...newFilter }));
  };
  // 返回
  const back = () => {
    history.goBack()
  };
  // 添加
  const add = () => {
    if (total >= 3) {
      message.error('最多可设置3条推荐')
      return
    }

    setAddRecommendReport({
      visible: true,
      formContent: {
        biz_type
      }
    })
  };

  useEffect(() => {
    const params = new URLSearchParams(location.search); // 获取地址栏参数
    const paramValue = params.get('type')
    const { selectKeys, openKeys } = props;
    // dispatch(setConfig({ selectKeys, openKeys }));
    changeFilter('biz_type', paramValue)
    // getData(true);
  }, []);

  return (
    <>
      <Row className="layout-infobar">
        <Col span={12}>
          <PermButton
            perm=""
            onClick={() => {
              back();
            }}
          >
            <Icon type="left-circle-o" />
            返回
          </PermButton>
          <PermButton
            perm={`report:${biz_type}:recommend:mgr`}
            onClick={() => {
              add();
            }}
            style={{ marginLeft: 8 }}
          >
            添加
          </PermButton>
        </Col>
        <Col span={12} className="layout-breadcrumb">
          {getCrumb(['一线管理', `${['', '问政管理', '帮办管理', '帮帮团管理', '应急求助管理', '小店帮'][biz_type]}`, '推荐报料'])}
        </Col>
      </Row>
      <div className="component-content">
        <Table func="getrecommendMatList" index="list" pagination={false} rowKey="id" columns={columns} />
      </div>

      {/* 添加推荐稿件 */}
      <AddRecommendReport
        {...addRecommendReport}
        onCancel={() => setAddRecommendReport({ visible: false })}
        onOk={() => {
          setAddRecommendReport({ visible: false })
          getData()
        }}
      ></AddRecommendReport>

      {/* 详情 */}
      <ReportDetailDrawer
        record={reportDetailDrawer.record}
        key={reportDetailDrawer.skey}
        visible={reportDetailDrawer.visible}
        onClose={() => setReportDetailDrawer({ visible: false })}
        type={1}
        onReload={() => {
          // getData(false)
        }}
      >
      </ReportDetailDrawer>

      {/* 用户详情弹窗 */}
      <Modal
        visible={userDetailModal.visible}
        key={userDetailModal.key}
        title="用户详情"
        width={800}
        onCancel={() => setUserDetailModal({ visible: false })}
        onOk={() => setUserDetailModal({ visible: false })}
      >
        {/*{user.visible && getUserDetail(user.detail)}*/}
        {userDetailModal.visible && <UserDetail detail={userDetailModal.detail} />}
      </Modal>

    </>
  );
}
