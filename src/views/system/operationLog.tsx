/* eslint-disable no-return-assign */
/* eslint-disable no-nested-ternary */
import React, { useState, useEffect, useRef } from 'react';
import { A, Table, Drawer } from '@components/common';
import { CommonObject } from '@app/types';
import { setConfig } from '@app/action/config';
import { getCrumb, requirePerm } from '@app/utils/utils';
import { Row, Col, message, Icon, Input, Button, DatePicker } from 'antd';
import { getTableList } from '@app/action/tableList';
import { sysApi as api } from '@app/api';
import { PermA, PermButton } from '@app/components/permItems';
import { withRouter } from 'react-router';
import connect from '@utils/connectTable';

import moment from 'moment';
@(withRouter as any)
@connect
class operationLog extends React.Component<any, any> {
  formRef: any;
  constructor(props: any) {
    super(props);
    this.state = {
      loading: false,
      filter: {
        remark: '',
        begin_time: '',
        end_time: '',
      },
    };
  }
  componentDidMount() {
    this.getData();
    this.props.dispatch(
      setConfig({
        selectKeys: this.props.selectKeys,
        openKeys: this.props.openKeys,
      })
    );
  }

  getData = (overlap: CommonObject = {}, filter: CommonObject = this.getFilter()) => {
    this.props.dispatch(
      getTableList('getOperationLogList', 'admin_log_list', { ...filter, ...overlap })
    );
  };
  // 获取搜索条件
  getFilter = () => {
    const { current, size } = this.props.tableList;
    const { filter } = this.state;
    const { remark, begin_time, end_time } = this.state.filter;
    const result: CommonObject = { current, size, remark, begin_time, end_time };
    Object.keys(filter).forEach((k: string) => {
      if (k === 'begin_time' || k === 'end_time') {
        if (filter[k]) {
          result[k] = filter[k].format('YYYY-MM-DD');
        }
      } else if (filter[k]) {
        result[k] = filter[k];
      }
    });
    return result;
  };
  // 输入
  inputChange = (value: any) => {
    this.setState({ filter: { ...this.state.filter, remark: value } });
  };
  handleKey = (e: any) => {
    if (e.which === 13) {
      this.getData({ current: 1 });
    }
  };
  timeChange = (dates: any) => {
    if (dates.length === 0) {
      this.setState(
        {
          filter: { ...this.state.filter, begin_time: '', end_time: '' },
        },
        () => this.getData({ current: 1 })
      );
    } else {
      this.setState(
        {
          filter: { ...this.state.filter, begin_time: dates[0], end_time: dates[1] },
        },
        () => this.getData({ current: 1 })
      );
    }
  };

  // 序号
  getColumns = () => {
    const { current, size } = this.props.tableList;
    const getSeq = (i: number) => (current - 1) * size + i + 1;
    return [
      {
        title: '序号',
        key: 'seq',
        render: (a: any, b: any, c: number) => getSeq(c),
        width: 70,
      },
      {
        title: '操作项',
        dataIndex: 'remark',
      },

      {
        title: '操作人',
        dataIndex: 'user_name',
        width: 100,
      },
      {
        title: '操作时间',
        dataIndex: 'created_at_str',
        width: 100,
      },
      // {
      //   title: '模板状态',
      //   key: 'status',
      //   dataIndex: 'status',
      //   render: (text: any) => <span>{text ? '上线' : '下线'}</span>,
      //   width: 80,
      // },
    ];
  };
  closeDrawer = () => {
    this.setState({
      form: { ...this.state.form, visible: false },
    });
  };
  submitSuccess = () => {
    this.closeDrawer();
    this.getData();
  };
  render() {
    const { filter } = this.state;
    return (
      <>
        <Row className="layout-infobar">
          <Col span={12}>
          </Col>
          <Col span={12} className="layout-breadcrumb">
            {getCrumb(this.props.breadCrumb)}
          </Col>
        </Row>
        <div className="component-content">
          <Row style={{ marginBottom: 16 }}>
            <Col span={12}>
              <DatePicker.RangePicker
                value={[filter.begin_time, filter.end_time]}
                format="YYYY-MM-DD"
                onChange={this.timeChange}
              />
            </Col>
            <Col span={12} style={{ textAlign: 'right' }}>
              <Input
                placeholder="输入操作事件关键词"
                style={{ width: 150 }}
                value={filter.remark}
                onChange={(e: any) => this.inputChange(e.target.value)}
                onKeyPress={this.handleKey}
              />
              <Button
                style={{ marginLeft: 8, verticalAlign: 'top' }}
                onClick={() => this.handleKey({ which: 13 })}
              >
                <Icon type="search" />
                搜索
              </Button>
            </Col>
          </Row>
          <Table
            columns={this.getColumns()}
            filter={this.getFilter()}
            rowKey="id"
            func="getOperationLogList"
            index="admin_log_list"
            pagination={true}
          />
        </div>
      </>
    );
  }
}
export default operationLog;
