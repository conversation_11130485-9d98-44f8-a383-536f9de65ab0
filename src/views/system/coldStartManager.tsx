/* eslint-disable no-return-assign */
/* eslint-disable no-nested-ternary */
import React, { useState, useEffect, useRef } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { A, Table, Drawer } from '@components/common';
import Form from '@components/business/ColdStartForm';
import { getCrumb, setMenuHook } from '@app/utils/utils';
import { Row, Col, message, Icon, Modal, Divider } from 'antd';
import { getTableList } from '@app/action/tableList';
import { sysApi as api } from '@app/api';
import useXHR from '@utils/useXhr';
import { PermA, PermButton } from '@app/components/permItems';
import moment from 'moment';

export default function ColdStartManager(props: any) {
  const [form, setForm] = useState(
    () =>
      ({
        visible: false,
        key: Date.now(),
        list: [],
      } as any)
  );
  const dispatch = useDispatch();

  const { loading, run } = useXHR();

  const formRef = useRef({} as any);

  const fullRecords = useSelector((state: any) => state.tableList);

  const getList = () => {
    dispatch(
      getTableList('getProposalWordList', 'proposal_word_list', {
        size: fullRecords.size,
        current: fullRecords.current,
      })
    );
  };

  const editRecord = (record: any = {}) => {
    if (record.id) {
      // run(api.getArSceneDetail, { ar_id: record.ar_id }).then((res: any) => {
      setForm({
        list: [],
        visible: true,
        key: Date.now(),
        isEdit: true,
        id: record.id,
        detail: record,
      });
      // });
    } else {
      setForm({
        list: [],
        visible: true,
        key: Date.now(),
        isEdit: false,
        detail: {},
      });
    }
  };

  const deleteRecord = (record: any) => {
    Modal.confirm({
      title: '确定删除吗',
      onOk: () => {
        run(api.deleteProposalWord, { id: record.id }, true).then(() => {
          message.success('操作成功');
          getList();
        });
      },
    });
  };

  const updateStatus = (record: any) => {
    run(api.updateStatusProposalWord, { id: record.id, status: [1, 0][record.status] }, true).then(
      () => {
        message.success('操作成功');
        getList();
      }
    );
  };

  const updateStatusDefault = (record: any) => {
    run(
      api.setDefaultProposalWord,
      { id: record.id, set_default: [1, 0][record.set_default] },
      true
    ).then(() => {
      message.success('操作成功');
      getList();
    });
  };

  const exchangeOrder = (text: number, offset: number) => {
    run(api.updateSortProposalWord, { id: text, sort_flag: offset }, true).then(() => {
      message.success('操作成功');
      getList();
    });
  };

  const getSeq = (i: number) => (fullRecords.current - 1) * fullRecords.size + i + 1;

  const columns = [
    {
      title: '排序',
      key: 'order',
      dataIndex: 'id',
      render: (text: any, record: any, i: number) => (
        <span>
          <PermA
            perm="proposal_word:update_sort"
            disabled={getSeq(i) === 1 || !record.status}
            className="sort-up"
            onClick={() => exchangeOrder(text, 0)}
          >
            <Icon type="up-circle" theme="filled" />
          </PermA>{' '}
          <PermA
            perm="proposal_word:update_sort"
            disabled={getSeq(i) === fullRecords.allData.on_show_count || !record.status}
            className="sort-down"
            onClick={() => exchangeOrder(text, 1)}
          >
            <Icon type="down-circle" theme="filled" />
          </PermA>
        </span>
      ),
      width: 70,
    },
    {
      title: '偏好内容',
      dataIndex: 'content',
    },
    {
      title: '图标',
      dataIndex: 'pic_url',
      render: (text: any) => <img src={text} className="list-pic" />,
      width: 120,
    },
    {
      title: '关联频道',
      dataIndex: 'ref_nav_name',
    },
    {
      title: '关联地方号',
      dataIndex: 'ref_column_name',
    },
    {
      title: '状态',
      key: 'status',
      dataIndex: 'status',
      render: (text: any) => <span>{text ? '上线' : '下线'}</span>,
      width: 80,
    },
    {
      title: '创建人',
      dataIndex: 'created_by',
    },
    {
      title: '创建时间',
      dataIndex: 'created_at',
      render: (text: any) => moment(text).format('YYYY-MM-DD HH:mm:ss'),
      width: 170,
    },
    {
      title: '操作',
      key: 'op',
      render: (record: any) => (
        <span>
          <PermA perm="proposal_word:set_default" onClick={() => updateStatusDefault(record)}>
            {['设为默认', '取消默认'][record.set_default]}
          </PermA>

          <Divider type="vertical" />
          <PermA perm="proposal_word:update" onClick={() => editRecord(record)}>
            编辑
          </PermA>
          <Divider type="vertical" />
          <PermA perm="proposal_word:update_status" onClick={() => updateStatus(record)}>
            {['上架', '下架'][record.status]}
          </PermA>
          <Divider type="vertical" />
          <PermA perm="proposal_word:delete" onClick={() => deleteRecord(record)}>
            删除
          </PermA>
        </span>
      ),
      width: 160,
    },
  ];

  useEffect(() => {
    getList();
    setMenuHook(dispatch, props);
    console.log(dispatch);
  }, []);

  const filterSize = () => {
    return {
      current: 1,
      size: 10,
    };
  };

  return (
    <>
      <Row className="layout-infobar">
        <Col span={12}>
          <PermButton perm="proposal_word:create" onClick={() => editRecord()}>
            <Icon type="plus-circle" /> 添加
          </PermButton>
        </Col>
        {/* <div>{props}</div> */}
        <Col span={12} className="layout-breadcrumb">
          {getCrumb(props.breadCrumb)}
        </Col>
      </Row>
      <div className="component-content">
        <Table
          filter={filterSize}
          columns={columns}
          rowKey="id"
          func="getProposalWordList"
          index="proposal_word_list"
          pagination={true}
        />
        <Drawer
          visible={form.visible}
          skey={form.key}
          title={form.isEdit ? '编辑偏好内容' : '添加偏好内容'}
          onClose={() => setForm((s: any) => ({ ...s, visible: false }))}
          onOk={() => formRef.current.doSubmit()}
        >
          <Form
            formContent={form}
            allIds={form.arIds}
            wrappedComponentRef={(instance: any) => (formRef.current = instance)}
            onEnd={() => {
              setForm((s: any) => ({ ...s, visible: false }));
              getList();
            }}
          />
        </Drawer>
      </div>
    </>
  );
}
