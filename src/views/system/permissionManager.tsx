/* eslint-disable array-callback-return */
import { setConfig } from '@action/config';
import { sysApi as api } from '@app/api';
import { BaseComponent } from '@components/common';
import { connectSession as connect } from '@utils/connect';
import { getCrumb, requirePerm, searchToObject } from '@utils/utils.tsx';
import { Button, Checkbox, Col, Collapse, Input, Layout, Menu, message, Radio, Row } from 'antd';
import cloneDeep from 'lodash/cloneDeep';
import debounce from 'lodash/debounce';
import React from 'react';
import { withRouter } from 'react-router';

import './styles/permissionManager.scss';
import {
  CommonObject,
  IBaseProps,
  CommonResponse,
  TSysPermissionGroupData,
  TSysPermissionGroupRecord,
  TSysPermissionElementRecord,
  TSysPermissionElementData,
} from '@app/types';
import { SelectParam } from 'antd/es/menu';
import { RadioChangeEvent } from 'antd/es/radio';
import { CheckboxChangeEvent } from 'antd/es/checkbox';
import { debug } from 'webpack';

const { SubMenu } = Menu;
interface PermissionListRC extends TSysPermissionElementRecord {
  indeterminate?: boolean;
  hasSubMenu?: boolean;
  children: PermissionListRC[];
}

type TState = {
  loading: boolean;
  groupList: TSysPermissionGroupRecord[];
  filteredGroupList: TSysPermissionGroupRecord[];
  permissionList: PermissionListRC[];
  type: string;
  categoryId: string;
  roleId: string;
  select1: number;
  select2: number;
  select3: number;
  preserveIds: number[];
  centerId: number | false;
  level: number;
  childIndex: any;
};

type Props = IBaseProps;

class PermissionManager extends BaseComponent<{}, TState> {
  constructor(props: Props) {
    super(props);
    const params = searchToObject();
    this.state = {
      loading: false,
      groupList: [],
      filteredGroupList: [],
      permissionList: [],
      type: '2',
      categoryId: '2',
      roleId: params.roleId || '',
      select1: -1,
      select2: -1,
      preserveIds: [],
      centerId: false,
      select3: -1,
      level: 1,
      childIndex: null,
    };
    this.doSearchGroup = debounce(this.doSearchGroup, 500);
  }

  componentDidMount() {
    this.getGroupList();
    if (this.state.roleId) {
      this.showPriv();
    }
    this.setMenu();
  }

  getGroupList = () => {
    this.props.dispatch(setConfig({ loading: true }));
    api
      .getAllGroupList()
      .then((r: CommonResponse<TSysPermissionGroupData>) => {
        message.success('权限组获取成功');
        this.props.dispatch(setConfig({ loading: false }));
        this.setState({ groupList: r.data.role_list, filteredGroupList: r.data.role_list });
        setTimeout(() => {
          this.props.dispatch(setConfig({ loading: false }));
        }, 1000);
      })
      .catch(() => this.props.dispatch(setConfig({ loading: false })));
  };

  doSearchGroup = (value: string) => {
    if (value === '') {
      this.setState({ filteredGroupList: this.state.groupList });
      return;
    }
    const filteredList: TSysPermissionGroupRecord[] = [];
    this.state.groupList.map((v: TSysPermissionGroupRecord) => {
      if (v.name.indexOf(value) > -1) {
        filteredList.push(v);
      }
    });
    this.setState({
      filteredGroupList: filteredList,
      roleId: '',
      permissionList: [],
      select1: -1,
      select2: -1,
    });
  };

  searchGroup = (e: React.ChangeEvent<HTMLInputElement>) => {
    e.persist();
    this.doSearchGroup(e.target.value);
  };

  showPriv(roleId = this.state.roleId, type = this.state.type) {
    const category = type.toString() === '3' ? '2' : type.toString();
    this.setState({
      roleId: roleId.toString(),
      type: type.toString(),
      categoryId: category,
    });
    if (roleId === '') {
      message.error('请先选择权限组');
      return;
    }
    this.props.dispatch(setConfig({ loading: true }));
    api
      .getPermissionList({ category, role_id: roleId })
      .then((r: CommonResponse<TSysPermissionElementData>) => {
        message.success('权限列表获取成功');
        this.props.dispatch(setConfig({ loading: false }));
        const list: PermissionListRC[] = cloneDeep(r.data.elements);
        let nList: PermissionListRC[] = [];
        let preserveIds: number[] = [];
        let centerId: number | false = false;
        if (category === '1') {
          list.map((v: PermissionListRC, i: number) => {
            v.indeterminate =
              v.children.filter((vv: PermissionListRC) => {
                return vv.checked;
              }).length < v.children.length && v.checked;
            nList.push(v);
          });
        }
        if (category === '2') {
          const centeredList: PermissionListRC[] = [];
          const contentList: PermissionListRC[] = [];
          let centeredIds: number[] = [];
          let contentIds: number[] = [];
          list.map((v: PermissionListRC, i: number) => {
            if (v.name === '中心频道') {
              centerId = v.id;
              centeredIds = this.getChildrenIds(v.children);
              v.children.map((vv: PermissionListRC, ii: number) => {
                vv.indeterminate =
                  vv.children.filter((vvv: PermissionListRC) => {
                    return vvv.checked;
                  }).length < vv.children.length && vv.checked;
                centeredList.push(vv);
              });
            } else {
              v.indeterminate =
                v.children.filter((vv: PermissionListRC) => {
                  return vv.checked;
                }).length < v.children.length && v.checked;
              contentList.push(v);
              contentIds = contentIds.concat(this.getChildrenIds(v.children));
              if (v.checked) {
                contentIds = contentIds.concat([v.id]);
              }
            }
          });
          if (type === '2') {
            nList = contentList;
            preserveIds =
              centeredIds.length > 0 ? centeredIds.concat([centerId as unknown as number]) : [];
          } else {
            nList = centeredList;
            preserveIds = contentIds;
          }
        }
        if (category === '0') {
          list.map((v: PermissionListRC, i: number) => {
            v.children.map((vv: PermissionListRC, ii: number) => {
              list[i].children[ii].indeterminate =
                vv.children.filter((vvv: PermissionListRC) => {
                  return vvv.checked;
                }).length < vv.children.length && vv.checked;
            });
          });
          nList = list;
        }
        this.setState({
          centerId,
          preserveIds,
          permissionList: nList,
          select1: -1,
          select2: -1,
          select3: -1,
          level: 1,
          childIndex: null,
        });
      })
      .catch(() => this.props.dispatch(setConfig({ loading: false })));
  }

  getChildrenIds = (list: PermissionListRC[]) => {
    let ids: number[] = [];
    list.map((v: PermissionListRC) => {
      if (v.checked) {
        ids.push(v.id);
      }
      if (v.children.length > 0) {
        ids = ids.concat(this.getChildrenIds(v.children));
      }
    });
    return ids;
  };

  handleSubmit = () => {
    const selectedIds = this.getChildrenIds(this.state.permissionList);
    let pids = selectedIds.concat(this.state.preserveIds);
    if (this.state.type === '3' && this.state.centerId && selectedIds.length > 0) {
      pids = pids.concat([this.state.centerId]);
    }
    this.props.dispatch(setConfig({ loading: true }));
    api
      .updatePermissions({
        category: this.state.categoryId,
        role_id: this.state.roleId,
        permission_ids: pids.join(','),
      })
      .then(() => {
        message.success('操作成功');
        this.showPriv();
        this.setState({
          select1: 0,
          select2: 0,
          select3: 0,
          level: 1,
        });
      })
      .catch(() => this.props.dispatch(setConfig({ loading: false })));
  };

  selectAll = (select2 = this.state.select2, e: CheckboxChangeEvent) => {
    const list = cloneDeep(this.state.permissionList);
    const { select1, type } = this.state;
    const { checked } = e.target;
    
    // 递归设置所有子节点的勾选状态
    const setAllChildrenChecked = (node: any, isChecked: boolean) => {
      node.checked = isChecked;
      node.indeterminate = false;
      if (node.children && node.children.length > 0) {
        node.children.forEach((child: any) => {
          setAllChildrenChecked(child, isChecked);
        });
      }
    };
    
    if (type === '0') {
      // 综合权限：三层结构
      setAllChildrenChecked(list[select1].children[select2], checked);
      
      // 更新父节点状态
      const checkedChildren = list[select1].children.filter((v: any) => v.checked);
      list[select1].checked = checkedChildren.length > 0;
      list[select1].indeterminate = checkedChildren.length > 0 && checkedChildren.length < list[select1].children.length;
    } else {
      // 其他类型：两层或多层结构
      if (this.state.level === 2 && this.state.childIndex !== null) {
        // 选中的是二级菜单
        setAllChildrenChecked(list[select1].children[this.state.childIndex], checked);
        
        // 更新一级菜单状态
        const checkedChildren = list[select1].children.filter((v: any) => v.checked);
        list[select1].checked = checkedChildren.length > 0;
        list[select1].indeterminate = checkedChildren.length > 0 && checkedChildren.length < list[select1].children.length;
      } else {
        // 选中的是一级菜单
        setAllChildrenChecked(list[select1], checked);
      }
    }
    
    this.setState({ permissionList: list });
  };

  selectSingle = (i: number, select2 = this.state.select2, e: CheckboxChangeEvent) => {
    const list = cloneDeep(this.state.permissionList);
    const { select1, type } = this.state;
    const { checked } = e.target;
    // if (type === '1') {
    //   list[select1].children[i].checked = checked;
    //   const length = list[select1].children.filter((v: any) => {
    //     return v.checked;
    //   }).length;
    //   list[select1].checked = length > 0;
    //   list[select1].indeterminate = length > 0 && length < list[select1].children.length;
    // }
    if (type === '0') {
      list[select1].children[select2].children[i].checked = checked;
      const { length } = list[select1].children[select2].children.filter((v: any) => {
        return v.checked;
      });
      list[select1].children[select2].checked = length > 0;
      list[select1].children[select2].indeterminate =
        length > 0 && length < list[select1].children[select2].children.length;
      list[select1].checked =
        list[select1].children.filter((v: any) => {
          return v.checked;
        }).length > 0;
    } else {
      // 当单选选择二级菜单下权限
      if (this.state.level === 2) {
        list[select1].children[this.state.childIndex].children[i].checked = checked;
        const { length } = list[select1].children[this.state.childIndex].children.filter(
          (v: any) => {
            return v.checked;
          }
        );
        list[select1].children[this.state.childIndex].checked = length > 0;
        list[select1].children[this.state.childIndex].indeterminate =
          length > 0 && length < list[select1].children[this.state.childIndex].children.length;
        list[select1].checked = length > 0;
        list[select1].indeterminate =
          length > 0 && length < list[select1].children[this.state.childIndex].children.length;
      } else {
        list[select1].children[i].checked = checked;
        const { length } = list[select1].children.filter((v: any) => {
          return v.checked;
        });
        list[select1].checked = length > 0;
        list[select1].indeterminate = length > 0 && length < list[select1].children.length;
      }
    }
    this.setState({ permissionList: list });
  };

  // 递归选择单个权限项
  selectSingleRecursive = (path: number[], checked: boolean) => {
    const list = cloneDeep(this.state.permissionList);
    const { select1, type, level, childIndex } = this.state;
    
    // 递归更新节点状态
    const updateNodeByPath = (nodes: any[], currentPath: number[]): boolean => {
      if (currentPath.length === 0) return false;
      
      const index = currentPath[0];
      const node = nodes[index];
      
      if (currentPath.length === 1) {
        // 到达目标节点
        node.checked = checked;
        
        // 递归设置所有子节点
        if (node.children && node.children.length > 0) {
          const setAllChildrenChecked = (n: any, isChecked: boolean) => {
            n.checked = isChecked;
            n.indeterminate = false;
            if (n.children && n.children.length > 0) {
              n.children.forEach((child: any) => setAllChildrenChecked(child, isChecked));
            }
          };
          setAllChildrenChecked(node, checked);
        }
        return true;
      } else {
        // 继续向下查找
        if (node.children && node.children.length > 0) {
          const updated = updateNodeByPath(node.children, currentPath.slice(1));
          if (updated) {
            // 更新当前节点的状态
            const checkedChildren = node.children.filter((c: any) => c.checked);
            const totalChildren = node.children.length;
            node.checked = checkedChildren.length > 0;
            node.indeterminate = checkedChildren.length > 0 && checkedChildren.length < totalChildren;
          }
          return updated;
        }
      }
      return false;
    };
    
    // 根据不同的类型处理路径
    if (type === '0') {
      // 综合权限：需要考虑三层结构
      updateNodeByPath(list, path);
    } else {
      // 其他类型
      if (level === 2 && childIndex !== null) {
        // 从二级菜单开始的路径
        const adjustedPath = [select1, childIndex, ...path.slice(2)];
        updateNodeByPath(list, adjustedPath);
      } else {
        // 从一级菜单开始的路径
        updateNodeByPath(list, path);
      }
    }
    
    this.setState({ permissionList: list });
  };

  // 递归渲染权限项
  renderPermissionItems = (items: any[], parentPath: number[] = [], colLayout: any) => {
    return items.map((item: any, index: number) => {
      const currentPath = [...parentPath, index];
      
      // 如果有子菜单，递归渲染
      if (item.children && item.children.length > 0) {
        const depth = parentPath.length;
        const isTopLevel = depth === 0 || (this.state.type === '0' && depth === 1);
        
        return (
          <React.Fragment key={index}>
            <Col span={24} style={{ marginTop: depth > 0 ? 12 : 0, marginBottom: 8 }}>
              <Checkbox
                checked={item.checked}
                indeterminate={item.indeterminate}
                onChange={(e) => this.selectSingleRecursive(currentPath, e.target.checked)}
                style={{ 
                  fontWeight: isTopLevel ? 'bold' : 'normal',
                  marginLeft: depth * 20,
                  color: isTopLevel ? '#1890ff' : '#595959'
                }}
              >
                {item.name}{isTopLevel && '（全部）'}
              </Checkbox>
            </Col>
            <Col span={24}>
              <Row gutter={8} style={{ marginLeft: depth * 20 }}>
                {this.renderPermissionItems(item.children, currentPath, colLayout)}
              </Row>
            </Col>
          </React.Fragment>
        );
      }
      
      // 如果没有子菜单，渲染单个复选框
      const depth = parentPath.length;
      return (
        <Col {...colLayout} key={index} style={{ marginLeft: depth > 1 ? (depth - 1) * 20 : 0 }}>
          <Checkbox
            checked={item.checked}
            onChange={(e) => this.selectSingleRecursive(currentPath, e.target.checked)}
          >
            {item.name}
          </Checkbox>
        </Col>
      );
    });
  };

  onRoleSelect = (item: SelectParam) => {
    this.showPriv(item.key);
  };

  onTypeChange = (e: RadioChangeEvent) => {
    this.showPriv(this.state.roleId, e.target.value);
  };

  cancelEdit = () => {
    this.showPriv();
  };

  onSelectFirstKey = (item: any) => {
    // if (item.item.props['data-index']) {
    //   this.setState({
    //     select1: parseInt(item.item.props['data-index'], 10),
    //     select2: parseInt(item.key, 10),
    //   });
    // } else {
    // 层级
    const level = item.item.props.level;
    const parentIndex = item.item.props['data-parent'];
    const childIndex = item.item.props['data-index'];
    if (level == 2) {
      this.setState({
        select1: parseInt(parentIndex, 10),
        select3: parseInt(item.key, 10),
        childIndex: parseInt(childIndex, 10),
        level: level,
      });
    } else {
      this.setState({
        select1: parseInt(item.key, 10),
        select3: 0,
        childIndex: null,
        level: 1,
      });
    }

    // }
  };
  render() {
    const list = this.state.permissionList;
    const s1 = this.state.select1;
    const s2 = this.state.select2;
    const s3 = this.state.select3;
    const colLayout = {
      xs: { span: 12 },
      sm: { span: 12 },
      lg: { span: 8 },
      xl: { span: 6 },
    };

    return (
      <>
        <Row className="layout-infobar">
          <Col span={24} className="layout-breadcrumb">
            {getCrumb(this.props.breadCrumb)}
          </Col>
        </Row>
        <div className="component-content permission-manager">
          <Layout className="outside">
            <Layout.Sider className="group-selector">
              <Row style={{ marginBottom: 8, marginTop: 4 }}>
                <Input
                  onChange={this.searchGroup}
                  style={{ width: '90%' }}
                  placeholder="请输入搜索内容"
                />
              </Row>
              <Menu
                theme="light"
                selectedKeys={[this.state.roleId]}
                style={{ marginRight: 8, height: '100%', borderRight: 'none' }}
                onSelect={this.onRoleSelect}
              >
                {this.state.filteredGroupList.map((v: any) => (
                  <Menu.Item key={v.id}>{v.name}</Menu.Item>
                ))}
              </Menu>
            </Layout.Sider>
            <Layout className="permission-content">
              <Row style={{ textAlign: 'center', marginBottom: 8 }}>
                <Radio.Group value={this.state.type} onChange={this.onTypeChange}>
                  {requirePerm(
                    this,
                    'permission:update_content_permissions'
                  )(<Radio.Button value="2">分配内容频道</Radio.Button>)}
                  {/* {requirePerm(
                    this,
                    'permission:update_content_permissions'
                  )(<Radio.Button value="3">分配中心频道</Radio.Button>)}
                  {requirePerm(
                    this,
                    'permission:update_area_permissions'
                  )(<Radio.Button value="1">分配城市频道</Radio.Button>)} */}
                  {requirePerm(
                    this,
                    'permission:update_permissions'
                  )(<Radio.Button value="0">分配综合权限</Radio.Button>)}
                </Radio.Group>
              </Row>
              <Row className="title-bar">
                {Boolean(this.state.roleId) && (
                  <>
                    <Col span={12} style={{ fontSize: 16, textIndent: '2em' }}>
                      <span style={{ marginBottom: -4 }}>
                        分配
                        {
                          ['综合', '城市频道', '内容频道', '中心频道'][
                            parseInt(this.state.type, 10)
                          ]
                        }
                        权限
                      </span>
                    </Col>
                    <Col span={12} style={{ fontSize: 16, textAlign: 'right', paddingRight: 16 }}>
                      <Button style={{ marginRight: 8, marginBottom: 4 }} onClick={this.cancelEdit}>
                        取消
                      </Button>
                      {requirePerm(
                        this,
                        `permission:update${
                          ['_', '_area_', '_content_', '_content_'][parseInt(this.state.type, 10)]
                        }permissions`
                      )(
                        <Button
                          type="primary"
                          style={{ marginBottom: 4 }}
                          onClick={this.handleSubmit}
                        >
                          保存
                        </Button>
                      )}
                    </Col>
                  </>
                )}
              </Row>
              {['1', '2', '3'].indexOf(this.state.type) > -1 && (
                <Row gutter={8} className="permission-list">
                  <Col span={4} className="left">
                    <Menu
                      theme="light"
                      selectedKeys={[this.state.level === 1 ? s1.toString() : s3.toString()]}
                      style={{ margin: 8, padding: 8, border: 'none' }}
                      onSelect={this.onSelectFirstKey}
                      mode="inline"
                    >
                      {list.map((v: any, i: number) =>
                        v.is_last_level_menu ? (
                          <Menu.Item
                            key={i.toString()}
                            className={v.checked ? 'left-selected' : ''}
                          >
                            {v.name}
                          </Menu.Item>
                        ) : (
                          <SubMenu
                            key={i.toString()}
                            title={v.name}
                            className={v.checked ? 'left-selected' : ''}
                          >
                            {v.children.map((k: any, j: number) => (
                              <Menu.Item
                                key={k.id.toString()}
                                data-parent={i}
                                data-index={j}
                                className={k.checked ? 'left-selected' : ''}
                              >
                                {k.name}
                              </Menu.Item>
                            ))}
                          </SubMenu>
                        )
                      )}
                    </Menu>
                  </Col>
                  <Col span={20} className="right">
                    {s1 > -1 &&
                      (this.state.level > 1 ? (
                        <>
                          <Row className="title">
                            <Checkbox
                              checked={list[s1].children[this.state.childIndex].checked}
                              indeterminate={list[s1].children[this.state.childIndex].indeterminate}
                              onChange={this.selectAll.bind(this, this.state.select2)}
                            >
                              签发平台（全部）
                            </Checkbox>
                          </Row>
                          <Row className="content" gutter={8}>
                            {this.renderPermissionItems(list[s1].children[this.state.childIndex].children, [s1, this.state.childIndex], colLayout)}
                          </Row>
                        </>
                      ) : (
                        <>
                          <Row className="title">
                            <Checkbox
                              checked={list[s1].checked}
                              indeterminate={list[s1].indeterminate}
                              onChange={this.selectAll.bind(this, this.state.select2)}
                            >
                              签发平台（全部）
                            </Checkbox>
                          </Row>
                          <Row className="content" gutter={8}>
                            {this.renderPermissionItems(list[s1].children, [s1], colLayout)}
                          </Row>
                        </>
                      ))}
                  </Col>
                </Row>
              )}
              {/* {this.state.type === '2' && (
                <Row gutter={8} className="permission-list">
                  <Col span={4} className="left type-1">
                    <Collapse bordered={false}>
                      {list.map((v: any, i: number) => (
                        <Collapse.Panel
                          key={v.id}
                          header={
                            <span className={`panel ${v.checked ? 'left-selected' : ''}`}>
                              {v.name}
                            </span>
                          }
                        >
                          {v.children.map((vv: any, ii: number) => (
                            <Row
                              key={vv.id}
                              className={`row ${vv.checked ? 'left-selected' : ''} ${
                                i.toString() + ii === s1.toString() + s2 ? 'current' : ''
                              }`}
                              onClick={() => this.setState({ select1: i, select2: ii })}
                            >
                              {vv.name}
                            </Row>
                          ))}
                        </Collapse.Panel>
                      ))}
                    </Collapse>
                  </Col>
                  <Col span={20} className="right">
                    {s2 > -1 && (
                      <React.Fragment>
                        <Row className="title">
                          <Checkbox
                            checked={list[s1].children[s2].checked}
                            indeterminate={list[s1].children[s2].indeterminate}
                            onChange={(e: any) => this.selectAll(e.target.checked)}
                          >
                            签发平台（全部）
                          </Checkbox>
                        </Row>
                        <Row className="content" gutter={8}>
                          {list[s1].children[s2].children.map((v: any, i: number) => (
                            <Col {...colLayout} key={i}>
                              <Checkbox
                                checked={v.checked}
                                onChange={(e: any) => this.selectSingle(e.target.checked, i)}
                              >
                                {v.name}
                              </Checkbox>
                            </Col>
                          ))}
                        </Row>
                      </React.Fragment>
                    )}
                  </Col>
                </Row>
              )} */}
              {this.state.type === '0' && (
                <Row gutter={8} className="permission-list">
                  <Col span={4} className="left">
                    <Menu
                      theme="light"
                      selectedKeys={[s1.toString()]}
                      style={{ margin: 8, padding: 8, border: 'none' }}
                      onSelect={this.onSelectFirstKey}
                    >
                      {list.map((v: any, i: number) => (
                        <Menu.Item key={i.toString()} className={v.checked ? 'left-selected' : ''}>
                          {v.name}
                        </Menu.Item>
                      ))}
                    </Menu>
                  </Col>
                  <Col span={20} className="right">
                    {s1 > -1 && (
                      <>
                        {/* 使用递归渲染处理多层权限结构 */}
                        <div style={{ padding: '0 16px' }}>
                          {this.renderPermissionItems(list[s1].children, [s1], colLayout)}
                        </div>
                      </>
                    )}
                  </Col>
                </Row>
              )}
            </Layout>
          </Layout>
        </div>
      </>
    );
  }
}

export default withRouter(connect(PermissionManager));
