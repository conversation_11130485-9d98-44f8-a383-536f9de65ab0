/* eslint-disable react/no-string-refs */
/* eslint-disable max-classes-per-file */
import { systemApi } from '@app/api';
import { Button, Col, Icon, message, Modal, Popconfirm, Row, Radio } from 'antd';
import Cropper from 'cropperjs';
import React from 'react';

import 'cropperjs/dist/cropper.css';
import './styles/imageUploader.scss';

function ratioFromRatioType(ratioType: string) {
  switch (ratioType) {
    case '4:1':
      return 4 / 1;
    case '16:9':
      return 16 / 9;
    case '4:3':
      return 4 / 3;
    case '1:1':
      return 1 / 1;
    case '3:1':
      return 3 / 1;
  }
  return 4 / 1;
}

export class ImageCropper extends React.Component<any, any> {
  cropper: any;

  constructor(props: any) {
    super(props);
    this.state = {
      imageData: {
        naturalWidth: 0,
        naturalHeight: 0,
        width: 0,
        height: 0,
      },
      loading: false,
      ratioType: props.ratioType,
    };
  }

  componentDidMount() {
    setTimeout(() => this.registerCropper(), 500);
  }

  componentWillUnmount() {
    if (this.cropper) {
      this.cropper.destroy();
    }
  }

  handleCrop = (e: any) => {
    const imageData = this.cropper.getImageData();
    const { detail } = e;
    this.setState({
      imageData: {
        naturalHeight: imageData.naturalHeight,
        naturalWidth: imageData.naturalWidth,
        width: detail.width.toFixed(0),
        height: detail.height.toFixed(0),
      },
    });
  };

  handleOk = () => {
    if (this.state.loading) {
      return;
    }

    // 设置裁剪宽高
    this.cropper.getCroppedCanvas(this.props.fixedSize || {}).toBlob(
      (blob: any) => {
        console.log(blob);
        console.log(blob.size / 1024);
        console.log(this.props.imgSize);
        console.log(blob.size / 1024, this.props.imgSize);
        if (blob.size / 1024 >= this.props.imgSize) {
          message.error(
            `图片上传过大，请上传小于${parseFloat((this.props.imgSize / 1024).toFixed(2))}M的图片！`
          );
        } else {
          this.setState({ loading: true });
          const file = new File([blob], `cropped-image.${this.props.type?.split('/')?.[1] || ''}`, {
            type: this.props.type, // 或 blob.type
            lastModified: Date.now(),
          });

          const params: any = { file };
          if (this.props.needMz) {
            params.media_type = 0;
          }
          systemApi
            .uploadImg(params)
            .then((r: any) => {
              message.success('上传成功');
              this.props.onComplete({
                url: r.data.url,
                ratioType: this.state.ratioType,
              });
            })
            .catch(() => {
              this.setState({ loading: false });
            });
        }
      },
      this.props.type,
      0.7
    );
  };

  handleZoom = (zoom: any) => {
    this.cropper.zoom(zoom);
  };

  handleRotate = (degree: any) => {
    this.cropper.rotate(degree);
  };

  handleReset = () => {
    this.cropper.reset();
  };

  handleCancel = () => {
    this.props.onComplete(false);
  };

  registerCropper() {
    if (this.refs.imageCropper) {
      const cropper = new Cropper(this.refs.imageCropper as HTMLImageElement, {
        aspectRatio:
          this.props.ratio ||
          (!!this.props.ratioType ? ratioFromRatioType(this.props.ratioType) : undefined),
        preview: '.rox-image-cropper-preview',
        autoCropArea: 1,
        toggleDragModeOnDblclick: true,
        viewMode: 2,
        crop: this.handleCrop.bind(this),
      });
      cropper.replace(this.props.blobUrl);
      this.cropper = cropper;
    } else {
      setTimeout(() => this.registerCropper(), 500);
    }
  }

  handleRatioChange(e: any) {
    const ratioType = e.target.value;
    this.setState({ ratioType });
    this.cropper.setAspectRatio(ratioFromRatioType(ratioType));
  }

  render() {
    return (
      <Modal
        visible={true}
        title="裁剪图片"
        width={960}
        style={{ top: 20 }}
        onCancel={this.handleCancel}
        footer={
          <Button
            type="primary"
            disabled={this.state.loading}
            loading={this.state.loading}
            onClick={this.handleOk}
          >
            确定
          </Button>
        }
      >
        <div className="rox-image-cropper-container">
          <div className="rox-image-cropper-left">
            <img ref="imageCropper" />
          </div>
          <div className="rox-image-cropper-right">
            {this.props.showRatioTypeChange && (
              <div className="rox-image-cropper-operation">
                <p>
                  <b>图片比例</b>
                </p>
                <div>
                  <Radio.Group
                    value={this.state.ratioType}
                    onChange={(e) => {
                      this.handleRatioChange(e);
                    }}
                  >
                    <Radio value={'4:1'}>4:1</Radio>
                    <Radio value={'16:9'}>16:9</Radio>
                    <br />
                    <Radio value={'4:3'}>4:3</Radio>
                    <Radio value={'1:1'}>1:1</Radio>
                  </Radio.Group>
                </div>
              </div>
            )}
            <div className="rox-image-cropper-operation">
              <p>
                <b>操作</b>
              </p>
              <div>
                {!this.props.fixedSize ? (
                  <Button size="small" onClick={this.handleReset}>
                    <Icon type="reload" /> 重置
                  </Button>
                ) : null}
              </div>
              <div>
                {!this.props.fixedSize ? (
                  <Button.Group size="small">
                    <Button onClick={() => this.handleZoom(-0.1)}>缩小</Button>
                    <Button onClick={() => this.handleZoom(+0.1)}>放大</Button>
                    <Button onClick={() => this.handleRotate(-90)}>左转</Button>
                    <Button onClick={() => this.handleRotate(+90)}>右转</Button>
                  </Button.Group>
                ) : null}
              </div>
            </div>
            <div className="rox-image-cropper-operation">
              <p>
                <b>预览</b>
              </p>
              <div className="rox-image-cropper-preview" />
            </div>
            <div className="rox-image-cropper-operation">
              <p>
                <b>图片参数</b>
              </p>
              <p>
                原图尺寸：{this.state.imageData.naturalWidth}&nbsp;x&nbsp;
                {this.state.imageData.naturalHeight}
              </p>
              <p>
                裁剪尺寸：
                {this.props.fixedSize ? this.props.fixedSize.width : this.state.imageData.width}
                &nbsp;x&nbsp;
                {this.props.fixedSize ? this.props.fixedSize.height : this.state.imageData.height}
              </p>
            </div>
          </div>
        </div>
      </Modal>
    );
  }
}

class ImageUploader extends React.Component<any, any> {
  static getDerivedStateFromProps(nextProps: any) {
    if ('value' in nextProps) {
      return { currentUrl: nextProps.value || '', ratio: nextProps.ratio };
    }
    return null;
  }

  constructor(props: any) {
    super(props);
    this.state = {
      isCutting: props.isCutting,
      showCropper: false,
      blob: '',
      type: '',
      accept: props.accept || ['image/jpeg', 'image/png', 'image/jpg', 'image/gif'],
      currentUrl: props.value || '',
      ratio: props.ratio || (!!props.ratioType ? ratioFromRatioType(props.ratioType) : undefined),
      imgSize: props.imgsize || 10240, // 不传默认限制10M
      imgMaxWidth: props.imgMaxWidth || 400,
      imgMaxHeight: props.imgMaxHeight || 200,
    };
  }

  handleFileInputChange = (e: any) => {
    const file = e.target.files[0];
    if (file) {
      // console.log(file)
      // if (file.size / 1024 > this.state.imgSize) {
      //   message.error(`图片上传过大，请上传小于${parseFloat((this.state.imgSize / 1024).toFixed(2))}M的图片！`);
      //   return;
      // }
      const blob = window.URL.createObjectURL(file);
      const imgType = file.type;
      e.target.value = null;
      if (this.state.accept.indexOf(imgType) === -1) {
        message.error('图片选择格式不正确');
        return;
      }
      if (this.state.isCutting) {
        this.doUpload(file);
        return;
      }

      if (imgType === 'image/gif') {
        this.doUpload(file);
        return;
      }
      this.setState({
        blob,
        showCropper: true,
        type: imgType,
      });
    }
  };

  cropperEnd = (data: any) => {
    console.log(data);
    if (data) {
      this.triggerChange(data.url, data.ratioType);
      this.triggerEffectRatioType(data.ratioType);
    }
    this.setState({ showCropper: false, blob: '', type: '' });
  };

  triggerEffectRatioType(ratioType: string) {
    const { effectRatioType } = this.props;
    if (effectRatioType) {
      effectRatioType(ratioType);
    }
  }

  triggerChange = (url: string, ratioType?: string) => {
    const { onChange } = this.props;
    this.setState({
      currentUrl: url,
    });
    if (onChange) {
      onChange(url, ratioType);
    }
  };

  clickUpload = () => {
    if (this.state.loading) {
      return;
    }
    if (!!this.props.disable) {
      message.error(this.props.disable);
      return;
    }
    (this.refs.imageUpload as HTMLDivElement).click();
  };

  doUpload = (blob: any) => {
    if (blob.size / 1024 >= this.state.imgSize) {
      message.error(
        `图片上传过大，请上传小于${parseFloat((this.state.imgSize / 1024).toFixed(2))}M的图片！`
      );
    } else {
      this.setState({ loading: true });
      const params: any = { file: blob };
      if (this.props.needMz) {
        params.media_type = 0;
      }
      systemApi
        .uploadImg(params)
        .then((r: any) => {
          message.success('上传成功');
          this.triggerChange(r.data.url);
          this.setState({ loading: false });
        })
        .catch(() => {
          this.setState({ loading: false });
        });
    }
  };

  render() {
    return (
      <Row className="image-uploader">
        <input
          ref="imageUpload"
          type="file"
          accept={this.state.accept.join(',')}
          onChange={this.handleFileInputChange}
          style={{ display: 'none' }}
        />
        {Boolean(this.state.currentUrl) && (
          <Row style={{ marginBottom: 8 }}>
            <img
              src={
                this.state.currentUrl?.includes(',')
                  ? this.state.currentUrl.split(',')[1]
                  : this.state.currentUrl
              }
              style={{
                maxWidth: `min(${`${this.state.imgMaxWidth}px`}, 100%)`,
                maxHeight: this.state.imgMaxHeight,
              }}
            />
          </Row>
        )}
        <Row>
          <Button
            style={{ marginRight: 8 }}
            type="primary"
            loading={this.state.loading}
            onClick={() => this.clickUpload()}
            disabled={this.props.disabled}
          >
            <Icon type="upload" />
            选择图片
          </Button>
          {Boolean(this.state.currentUrl) && (
            <Popconfirm
              title="确认删除图片?"
              okText="确定"
              cancelText="取消"
              onConfirm={() => {
                this.triggerChange('');
                // this.triggerEffectRatioType('4:1')
              }}
            >
              <Button disabled={this.props.disabled}>删除图片</Button>
            </Popconfirm>
          )}
        </Row>
        {this.state.showCropper && (
          <ImageCropper
            ratio={this.state.ratio}
            ratioType={this.props.ratioType}
            showRatioTypeChange={this.props.showRatioTypeChange}
            type={this.state.type}
            imgSize={this.state.imgSize}
            blobUrl={this.state.blob}
            onComplete={this.cropperEnd}
            fixedSize={this.props.fixedSize}
            needMz={this.props.needMz}
          />
        )}
      </Row>
    );
  }
}

export default ImageUploader;
