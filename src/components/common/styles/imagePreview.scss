.image_preview {
  position: fixed;
  z-index: 9999;
  left: 0;
  top: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);

  &_mask {
    position: fixed;
    width: 100vw;
    height: 100vh;
    z-index: 98;
    left: 0;
    top: 0;
  }

  .image_preview_content {
    position: absolute;
    left: 50%;
    top: 50%;
    height: 70%;
    transform: translate(-50%, -50%);
    z-index: 99;

    img,
    .image_preview_content_img {
      position: relative;
      left: 50%;
      top: 50%;
      transform: translate(-50%, -50%);
      height: 100%;
      user-select: none;
    }

    .close_btn {
      position: absolute;
      top: -17px;
      right: -17px;
      color: #fff;
      font-size: 34px;
      cursor: pointer;
    }
  }
}
