.layout-container {
  height: 100%;

  .collapse-trigger {
    width: 25px;
    height: 50px;
    position: absolute;
    top: 50%;
    transform: translateY(-50%) translateX(-13px);
    background: #466385;
    z-index: 19;
    left: 200px;
    border-radius: 0 100px 100px 0;
    line-height: 50px;
    text-align: right;
    color: rgba(255, 255, 255, 0.75);
    transition: all ease 0.2s;
    font-size: 12px;
  }

  .collapse-trigger:hover {
    cursor: pointer;
  }

  .collapsed {
    left: 80px;
  }

  .layout-header {
    background-color: #fff;
    padding: 0;

    .name-bar {
      margin-left: 30px;
      margin-right: 16px;

      i {
        display: inline-block;
        width: 20px;
        margin-right: 20px;
        font-size: 20px;
        font-style: normal;
        cursor: pointer;
      }
    }

    .ranking {
      width: 30px;
      height: 30px;
      // background-color: green;
      position: relative;
      background: url('/assets/layout_ranking_icon.png') no-repeat center / 100%;

      .new {
        position: absolute;
        right: -20px;
        top: -5px;  
        height: 18px;
        width: 30px;
        background-color: red;
        line-height: 18px;
        color: white;
        text-align: center;
        border-radius: 10px;
        font-size: 12px;
      }
    }

    .notice {
      position: relative;
      width: 30%;
      margin: 3px 8px;
      padding: 4px 8px;
      font-size: 14px !important;
      color: rgba(0, 0, 0, 0.75) !important;
      border-radius: 4px;
      background-color: #e6f7ff;
      border: 1px solid #91d5ff;

      .row {
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        height: 16px !important;
        line-height: 16px !important;
      }

      .content {
        * {
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
          word-break: break-all;
        }
      }
    }
  }

  .layout-sub-container {
    overflow-y: hidden;
  }

  .layout-sidebar {
    z-index: 20;
    overflow-y: auto;

    &::-webkit-scrollbar {
      width: 0px !important;
    }

    scrollbar-width: none;
  }

  .layout-content {
    margin: 0 16px 12px 16px;
    overflow-y: hidden;
    overflow-x: hidden;
    flex: 1;
  }

  .layout-infobar {
    margin-top: 12px;
  }

  .layout-content>div {
    height: 100%;
  }

  .layout-breadcrumb {
    text-align: right;
  }

  .spin {
    width: 100%;
    height: 100%;
    flex: 1;
  }

  .ant-spin-container {
    height: 100%;
    display: flex;
    flex-direction: column;
  }
}

.layout-notice {
  font-size: 13px;

  div {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    margin-bottom: 5px;

    span {
      display: flex;
      align-items: center;
    }
  }

  .notice-news-title {
    display: block;
    color: #1890ff;
  }
}

.hot-news-notice.ant-notification-notice {
  position: absolute;
  bottom: 0;
  width: 395px;
}

.hot-news-list_yi {
  height: fit-content;
  margin-left: 4px;
  padding: 0 6px;
  color: #fff;
  background-color: #f00;
  border-radius: 8px;
  text-align: center;
  font-size: 12px;
  transform-origin: left center;
  transform: scale(0.8);
  font-style: normal;
}

.hot-news_copy {
  display: inline-block;
  min-width: 14px;
  width: 14px;
  height: 14px;
  margin-bottom: 3px;
  background: url('/assets/icon_copy.png') no-repeat center / contain;
  cursor: pointer;
}