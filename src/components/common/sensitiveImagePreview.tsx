import React from 'react';
import ReactDOM from 'react-dom';
import { Icon } from 'antd';
import './styles/imagePreview.scss';
import SensitiveImage from '@app/views/operates/SensitiveImage';

export default function SensitiveImagePreview(props: any) {
  const { visible, data, onClose, closeByMask } = props;
  return (
    visible &&
    ReactDOM.createPortal(
      <div className="image_preview">
        <div
          className="image_preview_mask"
          onClick={() => {
            console.log(closeByMask, 'closeByMask');
            if (closeByMask) {
              onClose();
            }
          }}
        />
        <div className="image_preview_content">
          <SensitiveImage
            className="image_preview_content_img"
            src={data.imgUrl}
            evidence={data.evidence}
          ></SensitiveImage>
          <Icon className="close_btn" type="close-circle" onClick={onClose} />
        </div>
      </div>,
      document.body
    )
  );
}
