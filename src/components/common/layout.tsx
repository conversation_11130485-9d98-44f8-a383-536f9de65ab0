import { clearSession } from '@app/action/session';
import { systemApi as api, opApi } from '@app/api';
import { connectAll as connect } from '@utils/connect';
import { ERROR_CODES } from '@utils/constants';
import { Button, Col, Icon, Layout, Modal, Popover, Row, Spin, message, notification } from 'antd';
import React from 'react';
import { withRouter, RouteComponentProps } from 'react-router';
import SideMenu from './sideMenu';
import HotNewsListDrawer from './hotNewsListDrawer';
import './styles/layout.scss';
import { IComponentProps, IAllProps } from '@app/types';
import ReactClipboard from 'react-clipboardjs-copy';
import moment from 'moment';

interface LayoutProps {
  [key: string]: any;
}

interface LayoutState {
  collapsed: boolean;
  sNotice: boolean;
  notice: any;
  hotNewsDrawerVisible: boolean;
  notificationIds: any;
  rankingVisible: boolean;
  rankingMessage: any;
  rankingMaxTime: any;
}

class MainLayout extends React.Component<
  RouteComponentProps & IComponentProps & IAllProps,
  LayoutState
> {
  hb: any;
  constructor(props: any) {
    super(props);
    this.state = {
      collapsed: false,
      sNotice: false,
      notice: {},
      hotNewsDrawerVisible: false,
      notificationIds: [],
      rankingVisible: false,
      rankingMessage: [],
      rankingMaxTime: 0
    };
  }

  componentDidMount() {
    this.doHeartBeat();
    this.getNotice();
  }

  componentWillUnmount() {
    clearInterval(this.hb);
  }

  copySuccess = () => {
    message.success('稿件ID已复制');
  };

  copyFail = () => {
    message.error('复制失败');
  };

  doHeartBeat = () => {
    const INTERVAL_TIME = 30000;
    this.hb = setInterval(() => {
      api
        .rolling()
        .then()
        .catch((e: any) => {
          if (e.code && e.code === ERROR_CODES.LOG_TIME_OUT) {
            clearInterval(this.hb);
            Modal.error({
              title: '登录超时',
              content: '请重新登录',
              onOk: () => {
                this.props.dispatch(clearSession());
                this.props.history.push('/logout');
              },
            });
          }
        });
      this.getNotice();
    }, INTERVAL_TIME);
  }

  getNotice = () => {
    api
      .getNotice()
      .then((r: any) => {
        this.setState({
          notice: r.data.app_notice,
        });
      })
      .catch(() => this.setState({ notice: {} }));
  }

  onCollapse = (collapsed: boolean) => {
    this.setState({ collapsed });
  }

  collapse = () => {
    this.setState({
      collapsed: !this.state.collapsed,
    });
  }

  closeNotice = () => {
    this.setState({
      sNotice: false,
    });
  }

  logout = () => {
    api.logout().then(() => {
      this.props.dispatch(clearSession());
      this.props.history.push('/logout');
    });
  }

  showDetail = () => {
    this.setState({ sNotice: true });
  }
  render() {
    const content = this.state.notice.content || '';
    const hasHotNewsPermission = this.props.session.permissions.includes('article_metadata:hot_list')

    const maxTime = this.state.rankingMessage?.sort((a: any, b: any) => b.sync_time - a.sync_time)?.[0]?.sync_time || 0;

    let publishList = []
    let unpublishList = []
    for (const item of this.state.rankingMessage) {
      const name = item.type == 0 ? '24h热榜' : '市县热闻榜'
      if (item.need_publish) {
        publishList.push(name)
      } else {
        unpublishList.push(name)
      }
    }

    return (
      <Layout className="layout-container">
        <Layout.Header className="layout-header">
          <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', width: '100%', overflow: 'hidden' }}>
            <div>
              <img
                src="/assets/header.png?t=tmxw1"
                height="40"
                className="header-img"
                style={{ marginLeft: 32 }}
              />
            </div>
            <div style={{ display: 'flex', justifyContent: 'flex-end', alignItems: 'center', flex: 1, overflow: 'hidden' }}>
              {this.state.notice.status && (
                <div className="notice">
                  <div className="row">{this.state.notice.title}</div>
                  <div className="row content" style={{ width: '100%', overflow: 'hidden', whiteSpace: 'nowrap', textOverflow: 'ellipsis' }} dangerouslySetInnerHTML={{ __html: content }} />
                  <div className="row">
                    值班电话：{this.state.notice.tel}
                    <span style={{ float: 'right' }}>
                      <a onClick={this.showDetail}>查看详情>></a>
                    </span>
                  </div>
                </div>
              )}
              <div className="name-bar">
                欢迎您，{this.props.session.admin.name}！[<a onClick={this.logout}>登出</a>]
              </div>
            </div>
          </div>
        </Layout.Header>
        <Layout className="layout-sub-container">
          <Layout.Sider
            className="layout-sidebar"
            collapsible={true}
            collapsed={this.state.collapsed}
            trigger={null}
          // onCollapse={this.onCollapse}
          // collapsedWidth={0}
          >
            <SideMenu />
          </Layout.Sider>
          <Layout>
            <Layout.Content className="layout-content">
              <Spin
                tip="正在加载..."
                spinning={Boolean(this.props.config.loading || this.props.tableList.loading)}
                wrapperClassName="spin"
                className="spin"
              >
                {this.props.children}
              </Spin>
            </Layout.Content>
          </Layout>
        </Layout>

        <Modal
          title="系统公告"
          visible={this.state.sNotice}
          footer={
            <Button type="primary" onClick={this.closeNotice}>
              关闭
            </Button>
          }
          onOk={this.closeNotice}
          onCancel={this.closeNotice}
        >
          <h3>{this.state.notice.title}</h3>
          <div dangerouslySetInnerHTML={{ __html: content }} />
          <p>值班电话：{this.state.notice.tel}</p>
        </Modal>
        <div
          className={`collapse-trigger ${this.state.collapsed ? 'collapsed' : ''}`}
          onClick={this.collapse}
        >
          <Icon type={this.state.collapsed ? 'right' : 'left'} style={{ marginRight: 1 }} />
        </div>
      </Layout>
    );
  }
}

export default withRouter(connect()(MainLayout));
