import { systemApi } from '@app/api';
import { <PERSON><PERSON>, Col, I<PERSON>, message, Modal, Popconfirm, Row } from 'antd';
import React from 'react';
import './styles/videoUploader.scss';

class VideoUploader extends React.Component<any, any> {

  static getDerivedStateFromProps(nextProps: any) {
    if ('value' in nextProps) {
      return { currentUrl: nextProps.value || '' };
    }
    return null;
  }

  constructor(props: any) {
    super(props);
    this.state = {
      currentUrl: props.value || '',
      size: props.size || 0,
    };
  }

  handleFileInputChange = (e: any) => {
    const file = e.target.files[0];
    if (file) {
      const imgType = file.type;
      const size = file.size;
      if (size > this.state.size * 1024 * 1024 && this.state.size > 0) {
        message.error(`选择的视频不能大于${this.state.size}M`);
        return;
      }
      e.target.value = null;
      if (imgType.indexOf('video') === -1) {
        message.error('视频选择格式不正确');
        return;
      }
      this.doUpload(file);
    }
  }

  cropperEnd = (data: any) => {
    console.log(data);
    if (data) {
      this.triggerChange(data.url);
    }
    this.setState({ showCropper: false, blob: '', type: '' });
  }

  triggerChange = (url: string) => {
    console.log(url);
    const onChange = this.props.onChange;
    this.setState({
      currentUrl: url,
    });
    if (onChange) {
      onChange(url);
    }
  }

  clickUpload = () => {
    if (this.state.loading) { return; }
    if (!!this.props.disable) {
      message.error(this.props.disable)
      return
    }

    (this.refs.videoUpload as HTMLDivElement).click();
  }

  doUpload = (blob: any) => {
    this.setState({ loading: true });
    const params: any = { file: blob }
    if (this.props.needMz) {
      params.media_type = 1
    }
    systemApi.uploadImg(params).then((r: any) => {
      message.success('上传成功');
      this.triggerChange(r.data.url);
      this.setState({ loading: false });
    }).catch(() => {
      this.setState({ loading: false });
    });
  }

  render() {
    let videoURL = this.state.currentUrl
    if (this.props.needMz) {
      videoURL = `http${videoURL?.split(',http')?.[1]}`;
    }
    return (
      <Row className="video-uploader">
        <input
          ref="videoUpload"
          type="file"
          accept="video/*"
          onChange={this.handleFileInputChange}
          style={{ display: 'none' }}
        />
        {Boolean(this.state.currentUrl) &&
          <Row style={{ marginBottom: 8 }}>
            <video src={videoURL} className="rox-video-uploader-thumb" controls={true} />
          </Row>
        }
        <Row>
          <Button
            style={{ marginRight: 8 }}
            type="primary"
            loading={this.state.loading}
            onClick={() => this.clickUpload()}
            disabled={this.props.disabled}
          >
            <Icon type="upload" />选择视频
          </Button>
          {Boolean(this.state.currentUrl) &&
            <Popconfirm title="确认删除视频?" onConfirm={() => this.triggerChange('')}>
              <Button disabled={this.props.disabled}>删除视频</Button>
            </Popconfirm>
          }
        </Row>
      </Row>
    );
  }
}

export default VideoUploader;
