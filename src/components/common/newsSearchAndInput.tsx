/* eslint-disable react/no-array-index-key */
/* eslint-disable array-callback-return */
/* eslint-disable no-nested-ternary */
import { searchApi } from '@app/api';
import A from '@components/common/a';
import NewsPicker from '@components/common/newsPicker';
import { Button, Icon, message, Row, Select, Spin, Table } from 'antd';
import { ColumnProps, PaginationConfig } from 'antd/es/table';
import clonedeep from 'lodash/cloneDeep';
import debounce from 'lodash/debounce';
import React from 'react';
import { CommonObject } from '@app/types';

interface TProps {
  func: keyof typeof searchApi;
  body?: CommonObject;
  max: number;
  columns: ColumnProps<CommonObject>[];
  triggerInitialValueChange?: (values: CommonObject) => void;
  order?: boolean;
  onChange?: (values: CommonObject) => void;
  picker?: boolean;
  afix?: React.ReactElement | React.ReactNode;
  pagination?: PaginationConfig;
  pickerTitle?: string;
  pickerFunc?: keyof typeof searchApi;
  displayChannel?: string;
  placeholder?: string;
  initialValues?: { list: any };
  disabled?: boolean;
  selectOptionDisplay?: (record: any) => string;
  funcIndex?: string;
  excludeIds?: any[];
  idField?: string;
  addOnTop?: boolean;
  addOnTopIndex?: number;
  searchKey?: string;
  apiWithPagination?: boolean;
  onAddItem?: (id: any) => void;
  onRemoveItem?: (id: any) => void;
  detailMode?: boolean;
  onSelected?: (value: any) => string | undefined;
}

interface TState {
  showPicker: boolean;
  searched: boolean;
  fetching: boolean;
  searchResults: CommonObject[];
  channelArticleIds: number[];
  channelArticles: CommonObject[];
}

class SearchAndInput extends React.Component<TProps, TState> {
  static getDerivedStateFromProps(nextProps: any) {
    if ('value' in nextProps) {
      console.log('aaaaaaa', nextProps.value);
      return nextProps.value || '';
    }
    return null;
  }

  constructor(props: any) {
    super(props);
    const ids: number[] = [];
    if (props.initialValues && props.initialValues.list) {
      props.initialValues.list.map((v: any) => {
        ids.push(v.uuid || v.id);
      });
    }
    this.state = {
      searchResults: [],
      channelArticles: (props.initialValues && props.initialValues.list) || [],
      channelArticleIds: ids,
      fetching: false,
      searched: false,
      showPicker: false,
    };
    this.fetchNews = debounce(this.fetchNews, 500);
  }

  fetchNews = (value: string) => {
    if (value === '') {
      this.setState({
        searchResults: [],
      });
      return;
    }
    this.setState({ fetching: true });
    const basicBody = this.props.body || {};
    searchApi[this.props.func]({
      ...basicBody,
      [this.props.searchKey || 'keyword']: value,
    })
      .then((r: any) => {
        this.setState({ fetching: false, searched: true });
        this.setState({
          searchResults: this.props.apiWithPagination
            ? r.data[this.props.funcIndex || 'article_list'].records
            : r.data[this.props.funcIndex || 'article_list'],
        });
      })
      .catch(() => {
        this.setState({ fetching: false });
      });
  };

  handleChange = (svalue: any) => {
    const value = JSON.parse(svalue[0]);
    if (this.state.channelArticleIds.length >= this.props.max) {
      message.error(`最多可关联${this.props.max}条数据`);
      return;
    }
    const id = this.props.idField ? value[this.props.idField] : value.uuid || value.id;
    if (this.state.channelArticleIds.indexOf(id) > -1) {
      message.error('数据已在列表中，请重新选择');
      return;
    }
    if (this.props.excludeIds && this.props.excludeIds.indexOf(id) > -1) {
      message.error('数据已在总列表中，请重新选择');
      return;
    }
    if (this.props.onSelected) {
      const result = this.props.onSelected(value);
      if (result) {
        message.error(result);
        return;
      }
    }
    let state;
    if (this.props.addOnTop) {
      const index = this.props.addOnTopIndex || 0;
      const channelArticleIds = [...this.state.channelArticleIds];
      channelArticleIds.splice(index, 0, id);
      const channelArticles = [...this.state.channelArticles];
      channelArticles.splice(index, 0, value);
      state = {
        channelArticleIds,
        channelArticles,
        searchResults: [],
        searched: false,
      };
    } else {
      state = {
        channelArticleIds: [...this.state.channelArticleIds, id],
        channelArticles: [...this.state.channelArticles, value],
        searchResults: [],
        searched: false,
      };
    }
    this.setState({ ...state });
    this.triggerChange(state.channelArticleIds, state.channelArticles);
    if (this.props.triggerInitialValueChange) {
      this.props.triggerInitialValueChange({
        channelArticleIds: state.channelArticleIds,
        channelArticles: state.channelArticles,
      });
    }
    if (this.props.onAddItem) {
      this.props.onAddItem(id);
    }
  };

  handleDelete = (record: any) => {
    const articleList = clonedeep(this.state.channelArticles);
    let index = -1;
    articleList.map((v: any, i: number) => {
      if (v.id === record.id) {
        index = i;
      }
    });
    if (index === -1) {
      message.error('查找删除条目异常');
      return;
    }
    articleList.splice(index, 1);
    const ids: number[] = [];
    articleList.map((v: any) => {
      const id = this.props.idField ? v[this.props.idField] : v.uuid || v.id;
      ids.push(id);
    });
    this.setState({
      channelArticleIds: ids,
      channelArticles: articleList,
    });
    this.triggerChange(ids, articleList);
    if (this.props.triggerInitialValueChange) {
      this.props.triggerInitialValueChange({
        channelArticleIds: ids,
        channelArticles: articleList,
      });
    }
    if (this.props.onRemoveItem) {
      const id = this.props.idField ? record[this.props.idField] : record.uuid || record.id;
      this.props.onRemoveItem(id);
    }
  };

  exchangeOrder = (index: number, offset: number) => {
    const articleList = clonedeep(this.state.channelArticles);
    const temp = clonedeep(articleList[index]);
    articleList[index] = clonedeep(articleList[index - offset]);
    articleList[index - offset] = temp;
    const ids: number[] = [];
    articleList.map((v: any) => {
      const id = v.uuid || v.id;
      ids.push(id);
    });
    this.setState({
      channelArticleIds: ids,
      channelArticles: articleList,
    });
    this.triggerChange(ids, articleList);
    if (this.props.triggerInitialValueChange) {
      this.props.triggerInitialValueChange({
        channelArticleIds: ids,
        channelArticles: articleList,
      });
    }
  };

  triggerChange = (value: any, channelArticles: any) => {
    const { onChange, detailMode } = this.props;
    if (onChange) {
      onChange(detailMode ? channelArticles : value);
    }
  };

  getColumns = () => {
    let { columns } = this.props;
    const orderColumns: ColumnProps<CommonObject>[] = [
      {
        title: '排序',
        key: 'order',
        render: (text: any, record: any, i: number) => (
          <span>
            <A
              disabled={i === 0 || this.props.disabled}
              className="sort-up"
              onClick={this.exchangeOrder.bind(this, i, 1)}
            >
              <Icon type="up-circle" theme="filled" />
            </A>{' '}
            <A
              disabled={i === this.state.channelArticles.length - 1 || this.props.disabled}
              className="sort-down"
              onClick={this.exchangeOrder.bind(this, i, -1)}
            >
              <Icon type="down-circle" theme="filled" />
            </A>
          </span>
        ),
        width: 70,
      },
    ];
    if (this.props.order) {
      columns = orderColumns.concat(columns);
    }
    return columns.concat([
      {
        title: '操作',
        key: 'op',
        render: (text: any, record: any) => (
          <A disabled={this.props.disabled} onClick={this.handleDelete.bind(this, record)}>
            删除
          </A>
        ),
        width: 70,
      },
    ]);
  };

  cancelPick = () => {
    this.setState({ showPicker: false });
  };

  confirmPick = (list: any) => {
    if (list.length === 0) {
      message.info('未选择数据');
      this.cancelPick();
      return;
    }
    const newRows: any = [];
    const newIds: any = [];
    const repeatRows: any = [];
    list.map((v: any) => {
      const id = v.uuid || v.id;
      if (this.state.channelArticleIds.indexOf(id) > -1) {
        repeatRows.push(v);
      } else {
        newRows.push(v);
        newIds.push(id);
      }
    });
    if (newRows.length + this.state.channelArticleIds.length > this.props.max) {
      message.error(
        `最多可关联${this.props.max}条数据，已关联：${this.state.channelArticleIds.length}条`
      );
      return;
    }
    const state = {
      channelArticleIds: [...this.state.channelArticleIds, ...newIds],
      channelArticles: [...this.state.channelArticles, ...newRows],
    };
    this.setState({ ...state });
    this.triggerChange(state.channelArticleIds, state.channelArticles);
    if (repeatRows.length > 0) {
      message.info(`有${repeatRows.length}条数据在列表，已添加${newRows.length}条数据`);
    } else {
      message.info(`已添加${newRows.length}条数据`);
    }
    this.cancelPick();
  };

  showPicker = () => {
    this.setState({
      showPicker: true,
    });
  };

  onSelectBlur = () => {
    this.setState({
      searchResults: [],
      searched: false,
    });
  };

  render() {
    const displayChannel = this.props.displayChannel || 'channel_name';
    const displayOption =
      this.props.selectOptionDisplay ||
      ((d: any) => `${d.id} - 【${d[displayChannel]}】  ${d.list_title}`);
    return (
      <>
        <Row>
          <Select
            mode="multiple"
            disabled={this.props.disabled}
            value={[]}
            placeholder={this.props.placeholder || '输入新闻（专题）ID或标题关联'}
            notFoundContent={
              this.state.fetching ? (
                <Spin size="small" style={{ margin: 'auto' }} />
              ) : this.state.searched ? (
                '无结果'
              ) : null
            }
            filterOption={false}
            onSearch={this.fetchNews}
            onChange={this.handleChange}
            onBlur={this.onSelectBlur}
            style={{ width: 350 }}
          >
            {this.state.searchResults.map((d: any, i: number) => (
              <Select.Option key={`${i}`} value={JSON.stringify(d)}>
                {displayOption(d)}
              </Select.Option>
            ))}
          </Select>
          {Boolean(this.props.picker) && (
            <Button
              style={{ marginLeft: 8 }}
              onClick={this.showPicker}
              disabled={this.props.disabled}
            >
              {this.props.picker}
            </Button>
          )}
          {this.props.afix && <>&nbsp;&nbsp;&nbsp;&nbsp;</>}
          {this.props.afix || null}
        </Row>
        <Row style={{ marginTop: 8 }}>
          <Table
            columns={this.getColumns()}
            rowKey="id"
            dataSource={this.state.channelArticles}
            pagination={this.props.pagination ? this.props.pagination : false}
          />
        </Row>
        {this.state.showPicker && (
          <NewsPicker
            title={this.props.pickerTitle}
            func={this.props.pickerFunc}
            onCancel={this.cancelPick}
            onOk={this.confirmPick}
          />
        )}
      </>
    );
  }
}

export default SearchAndInput;
