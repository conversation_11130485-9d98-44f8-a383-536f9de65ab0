/* eslint-disable jsx-a11y/media-has-caption */
import { Row } from 'antd';
import React from 'react';
import moment from 'moment';
import Drawer from './drawer';

interface IRoxPreviewMCNProps {
  visible: boolean;
  skey: string | number;
  onClose: () => void;
  url: string;
}

function RoxDrawer(props: IRoxPreviewMCNProps) {
  const { visible, skey, url } = props;
  const { onClose } = props;

  return (
    <Drawer visible={visible} skey={skey} title="预览帖子" closeText="关闭" onClose={onClose}>
      <iframe src={url} width="414" height="736" title="预览帖子" />
    </Drawer>
  );
}

export default RoxDrawer;
