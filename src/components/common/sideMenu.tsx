/* eslint-disable @typescript-eslint/no-unused-expressions */
/* eslint-disable no-param-reassign */
import { releaseListApi as api } from '@app/api';
import { MenuItem, Session, IAllProps, CommonObject, IComponentProps } from '@app/types';
import { connectAll as connect } from '@utils/connect';
import { Icon, Menu, message, Input } from 'antd';
import React from 'react';
import { withRouter, RouteComponentProps } from 'react-router';
import { setTableCache } from "@action/tableCache";
import { clearTableList, setTableList } from "@action/tableList";


const { Search } = Input;

interface MenuProps {
  session?: Session;
  history?: any;
  selectKeys: string[];
}

class SideMenu extends React.Component<IAllProps & RouteComponentProps & IComponentProps, {}> {
  constructor(props: any) {
    super(props);

    const { session } = this.props;
    // 过滤掉PV菜单
    const isYunYing = session.menus.filter((v) => v.name === '运营管理').length > 0;
    if (isYunYing) {
      session.menus.forEach((v) => {
        if (v.children.length) {
          v.children = v.children.filter((v) => v.url !== '/pvShowOperate');
        }
      });
    }
    this.state = {
      menus: session.menus,
      currentMenus: session.menus,
    };
  }

  clickMenu = (menu: MenuItem, event: any) => {
    if (menu.url != "/commentList" && (!this.props.location.pathname.endsWith(menu.url?.split('?')[0]))) {
      this.props.dispatch && this.props.dispatch(clearTableList())
    }

    this.props.dispatch(
      setTableCache({
        beforeRoute: this.props.match?.path,
        ...this.props.tableList
      })
    )
    if (['1', 1].indexOf(menu.location_type) > -1) {
      const w = window.open();
      api
        .getCommentSystemUrl()
        .then((r: any) => {
          if (w) {
            w.location = r.data.url;
          } else {
            message.error('浏览器可能拦截了新页面');
          }
        })
        .catch(() => {
          w && w.close();
        });
    } else if (['2', 2].indexOf(menu.location_type) > -1) {
      const w = window.open(this.getMenuUrl(menu));
    } else {
      this.props.history.push(`/view${this.getMenuUrl(menu)}`);
    }
  };

  getMenuUrl = (menu: MenuItem) => {
    return `${menu.url}${menu.url.indexOf('?') > -1 ? '&' : '?'}is_tou_tiao=${menu.is_tou_tiao ? 1 : 0
      }`;
  };

  getMenu = (menu: MenuItem) => {
    if (menu.name === '潮客内容管理') {
      menu.url = menu.url.replace('releaselist', 'chaoke');
    }
    if (menu.name === '直播') {
      menu.url = menu.url.replace('releaselist', 'live');
    }
    if (menu.name === '潮鸣号内容管理') {
      menu.url = menu.url.replace('releaselist', 'tmh');
    }
    if (Boolean(menu.children) && menu.children.length > 0) {
      return (
        <Menu.SubMenu key={`/view${menu.url}`} title={menu.name}>
          {menu.children.map((m: any) => this.getMenu(m))}
        </Menu.SubMenu>
      );
    }
    return (
      <Menu.Item key={`/view${menu.url}`} onClick={this.clickMenu.bind(this, menu)}>
        {menu.name}
        {menu.is_tou_tiao ? '（头条）' : ''}
      </Menu.Item>
    );
  };

  getIcon = (index: string) => {
    const icons: CommonObject = {
      稿件管理: <Icon type="book" key="0" />,
      服务管理: <Icon type="customer-service" key="1" />,
      运营管理: <Icon type="gift" key="2" />,
      订阅管理: <Icon type="profile" key="3" />,
      用户中心: <Icon type="team" key="4" />,
      APP功能管理: <Icon type="mobile" key="5" />,
      系统管理: <Icon type="database" key="6" />,
      广告管理: <Icon type="shopping" key="7" />,
      运维管理: <Icon type="safety" key="8" />,
      推荐页管理: <Icon type="container" key="9" />,
      嘉友圈管理: <Icon type="cluster" key="10" />,
      创作者平台管理: <Icon type="profile" key="11" />,
      读报管理: <Icon type="file" key="12" />,
      一线管理: <Icon type="audit" key="13" />,
      数据管理: <Icon type="container" key="14" />,
    };
    return icons[index];
  };

  searchMenu(value: string) {
    console.log(value);
    console.log(this.state.menus);
    const defaultMenus = JSON.parse(JSON.stringify(this.state.menus));
    if (!value) {
      this.setState({
        ...this.state,
        currentMenus: defaultMenus,
      });
      return;
    }

    const searchInNode = (val: any, keyword: any): any => {
      if (val.name.includes(keyword.toUpperCase())) {
        return val
      }

      if (val.children) {
        const newChildren = []
        for (const item of val.children) {
          const result = searchInNode(item, keyword)
          if (result) {
            newChildren.push(result)
          }
        }

        if (newChildren.length > 0) {
          return {
            ...val,
            children: newChildren
          }
        }
      }

      return null
    }


    const newMenus = []
    for (const menu of defaultMenus) {
      const result = searchInNode(menu, value)
      if (result) {
        newMenus.push(result)
      }
    }

    // // 获取所有一级和二级包含关键词的菜单
    // const newMenus = defaultMenus.filter((item) => {
    //   if (item.name.includes(value)) {
    //     return true;
    //   }
    //   if (item.children) {
    //     const newChildren = item.children.filter((child) => {
    //       return child.name.includes(value);
    //     });
    //     if (newChildren.length) {
    //       return true;
    //     }
    //   } else {
    //     return false;
    //   }
    // });
    // // 剔除二级中不包含关键词的菜单
    // newMenus.forEach((item) => {
    //   if (!item.name.includes(value)) {
    //     item.children = item.children.filter((child) => {
    //       return child.name.includes(value);
    //     });
    //   }
    // });
    this.setState({
      ...this.state,
      currentMenus: newMenus,
    });
  }
  
  
  render() {
    const { config } = this.props;
    return (
      <Menu
        theme="dark"
        mode="inline"
        selectedKeys={config.selectKeys}
        defaultOpenKeys={config.openKeys}
      >
        <div
          style={{
            height: '50px',
            display: 'flex',
            alignItems: 'center',
            paddingLeft: '24px',
          }}
        >
          <Search
            style={{ width: '160px' }}
            placeholder="搜索菜单名称"
            allowClear
            onSearch={(event) => this.searchMenu(event)}
          />
        </div>
        {this.state.currentMenus.map((menu: any, i: number) => (
          <Menu.SubMenu
            key={`/view${menu.url}`}
            title={
              <span>
                {this.getIcon(menu.name)}
                <span>{menu.name}</span>
              </span>
            }
          >
            {menu.children.map((m: any) => this.getMenu(m))}
          </Menu.SubMenu>
        ))}
      </Menu>
    );
  }
}

export default withRouter(connect()(SideMenu));
