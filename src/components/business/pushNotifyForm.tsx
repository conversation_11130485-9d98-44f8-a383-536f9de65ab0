import { opApi as api } from '@app/api';
import SearchAndInput from '@components/common/newsSearchAndInput';
import { Form, Icon, Input, message, Radio, Tooltip, TreeSelect } from 'antd';
import React from 'react';
import _debounce from 'lodash/throttle';
import { setMLoading } from '@utils/utils';
import connectSession from '@utils/connectSession';
import { ImageUploader, PhoneFileUploader } from '@components/common';
import PushTagComponent from './pushTagComponent';

@connectSession
@(Form.create({ name: 'pushNotifyForm' }) as any)
class PushNotifyForm extends React.Component<any, any> {
  doSubmit: any;

  constructor(props: any) {
    super(props);
    this.fileUploaderRef = React.createRef();
    const form = {
      title: '',
      content: '',
      pic_url: '',
      channel_article_ids: [],
      channelArticles: [],
      receiver_type: 0,
      user_url: '',
      url: '',
      circle_tags: {},
    };

    const circle_tags = !!props.formContent.circle_tags
      ? JSON.parse(props.formContent.circle_tags)
      : {};

    this.state = {
      ...form,
      ...props.formContent,
      circle_tags: circle_tags,
      areaList: [],
    };
  }

  componentDidMount() {
    this.getAreaList();
    this.doSubmit = _debounce(this.debDoSubmit, 8000, {
      trailing: false,
    });
  }

  getAreaList = () => {
    api.getPushAreaList().then((res: any) => {
      const areaList = res.data?.area_tree?.map((v: any) => {
        return {
          title: v.name,
          value: v.name,
          key: v.id,
          children: v.children.map((vv: any) => {
            return { title: vv.name, value: vv.name, key: vv.id };
          }),
        };
      });
      this.setState({
        areaList,
      });
    });
  };

  handleSubmit = (e: any) => {
    e.preventDefault();
    this.doSubmit();
  };

  debDoSubmit = () => {
    this.props.form.validateFieldsAndScroll((err: any, values: any) => {
      if (!err) {
        if (values.title.length > 30) {
          return message.error('标题最大为30字，请修改后再提交');
        }
        if (values.receiver_type === 1) {
          const fileUploaderState = this.fileUploaderRef.current.state;
          if (!fileUploaderState.resultStatus) return message.error('请检测手机号是否正确');
        } else {
          values.user_url = '';
        }
        setMLoading(this, true);
        const body = { ...values, type: '1' };
        if (body.relevance == 0) {
          // 稿件
          body.channel_article_ids = values.channel_article_ids?.join(',');
          body.url = '';
        } else {
          // url
          body.channel_article_ids = '';
        }
        let func = 'createPushNotify';
        if (this.state.id) {
          func = 'updatePushNotify';
          body.push_notify_id = this.state.id;
        }
        if (values.receiver_type === 2) {
          const tags: any = {
            ...values.circle_tags,
          };
          body.circle_tags = JSON.stringify(tags);

          if (
            !values.circle_tags.article_read_preference &&
            !values.circle_tags.gov_emp_type &&
            !values.circle_tags.time_preference &&
            values.circle_tags.location.length == 0
          ) {
            message.error('至少选择一个标签维度');
            setMLoading(this, false);
            return;
          }
        } else {
          delete body.circle_tags;
        }

        console.log('555555', body);
        api[func as keyof typeof api](body)
          .then(() => {
            message.success('操作成功');
            setMLoading(this, false);
            this.props.onEnd();
          })
          .catch(() => setMLoading(this, false));
      } else {
        console.log('111111', values);
        message.error('请检查表单内容');
      }
    });
  };

  getColumn = () => {
    return [
      {
        title: '新闻频道',
        key: 'channel',
        dataIndex: 'channel_name',
        width: 95,
      },
      {
        title: '新闻标题',
        key: 'list_title',
        dataIndex: 'list_title',
      },
    ];
  };

  articlesChange = (data: any) => {
    this.setState({ ...data });
  };

  render() {
    const { getFieldDecorator, getFieldValue } = this.props.form;
    const formLayout = {
      labelCol: { span: 4 },
      wrapperCol: { span: 18 },
    };

    const values = this.props.form.getFieldsValue();

    return (
      <Form {...formLayout} onSubmit={this.handleSubmit}>
        <Form.Item
          label="标题"
          extra={
            <div>
              {`已输入${values.title ? values.title.length : 0}个字`}
              &nbsp;&nbsp;最多输入30字
            </div>
          }
        >
          {getFieldDecorator('title', {
            initialValue: this.state.title,
            rules: [
              {
                required: false,
                message: '请填写推送标题',
              },
            ],
          })(<Input maxLength={30} placeholder="填写推送标题" />)}
        </Form.Item>
        <Form.Item
          label="推送内容"
          extra={
            <div>
              {`已输入${values.content ? values.content.length : 0}个字`}
              &nbsp;&nbsp;最多输入100字
            </div>
          }
        >
          {getFieldDecorator('content', {
            initialValue: this.state.content,
            rules: [
              {
                required: true,
                message: '请填写推送内容',
              },
            ],
          })(<Input.TextArea rows={3} maxLength={100} placeholder="输入推送内容" />)}
        </Form.Item>
        <Form.Item
          label={
            <span style={{ position: 'relative' }}>
              <Tooltip title={<img src="/assets/sketch_map.png" width={237} height={70} />}>
                <Icon type="question-circle" style={{ position: 'absolute', left: -20, top: 3 }} />
              </Tooltip>
              上传图片
            </span>
          }
          extra="支持扩展名：.jpg .png .jpeg 等格式图片，尺寸为120*120px，文件小于200kb"
        >
          {getFieldDecorator('pic_url', {
            initialValue: this.state.pic_url || '',
            rules: [{ message: '请上传图片' }],
          })(<ImageUploader ratio={1} imgsize={200} fixedSize={{ width: 120, height: 120 }} />)}
        </Form.Item>
        <Form.Item
          label="推送范围"
          style={{ marginBottom: 0 }}
          // extra={getFieldValue('receiver_type') == undefined ? '未选择任何范围时，默认为全局' : ''}
        >
          {getFieldDecorator('receiver_type', {
            initialValue: this.state.receiver_type,
          })(
            <Radio.Group>
              <Radio value={0}>全部</Radio>
              <Radio value={1}>指定手机号</Radio>
            </Radio.Group>
          )}
        </Form.Item>
        {this.props.form.getFieldsValue().receiver_type === 1 && (
          <Form.Item
            wrapperCol={{ offset: 4 }}
            extra={
              <div>
                excel文件填写说明：第一列填写手机号即可
                <Tooltip
                  overlayStyle={{ maxWidth: 300 }}
                  title={<img width={280} src="/assets/excel_prompt.png" />}
                  placement="top"
                >
                  <Icon type="question-circle" />
                </Tooltip>
                <br />, 支持上传xlsx.xls格式文件，文件小于200kb
              </div>
            }
          >
            {getFieldDecorator('user_url', {
              initialValue: this.state.user_url,
              rules: [{ required: true, message: '指定手机号需上传文件' }],
            })(<PhoneFileUploader accept=".xls,.xlsx" ref={this.fileUploaderRef} maxSize={200} />)}
          </Form.Item>
        )}
        <Form.Item label="关联内容">
          {getFieldDecorator('relevance', {
            initialValue: !!this.state.url ? 1 : 0,
            rules: [
              {
                required: true,
                message: '请选择关联内容',
              },
            ],
          })(
            <Radio.Group>
              <Radio value={0}>稿件</Radio>
              <Radio value={1}>链接</Radio>
            </Radio.Group>
          )}
        </Form.Item>
        {getFieldValue('relevance') == 0 && (
          <Form.Item label="" style={{ paddingLeft: 134, marginTop: -20 }}>
            {getFieldDecorator('channel_article_ids', {
              initialValue: this.state.channel_article_ids,
              // preserve: true,
              rules: [
                {
                  required: true,
                  message: '请关联新闻',
                  type: 'array',
                },
                {
                  max: 10,
                  message: '最多关联10条新闻',
                  type: 'array',
                },
              ],
            })(
              <SearchAndInput
                max={10}
                func="pushSearch"
                columns={this.getColumn()}
                placeholder="输入新闻（专题）ID或标题关联"
                initialValues={{ list: this.state.channelArticles }}
                triggerInitialValueChange={this.articlesChange}
              />
            )}
          </Form.Item>
        )}

        {getFieldValue('relevance') == 1 && (
          <Form.Item label="跳转链接">
            {getFieldDecorator('url', {
              initialValue: this.state.url,
              preserve: true,
              rules: [
                {
                  required: true,
                  message: '请输入跳转链接',
                },
                {
                  pattern: /^https?:\/\//,
                  message: '请输入正确的链接格式',
                },
              ],
            })(<Input style={{ width: 400 }} placeholder="请输入跳转链接" maxLength={200} />)}
          </Form.Item>
        )}
      </Form>
    );
  }
}

export default PushNotifyForm;
