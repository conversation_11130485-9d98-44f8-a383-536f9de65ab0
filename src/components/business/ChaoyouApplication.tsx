import React, { useState, useEffect } from 'react';
import {
  Form,
  Input,
  Button,
  Icon,
  Checkbox,
  Select,
  Modal,
  Row,
  Col,
  Table,
  message,
  Slider,
  InputNumber,
  Tooltip,
} from 'antd';
import connectSession from '@utils/connectSession';
import { A } from '@components/common';
import SearchAndInput from '@components/common/newNewsSearchAndInput';
import { sysApi as api } from '@app/api';
import { cloneDeep, reject, xor } from 'lodash';
import { setMLoading } from '@utils/utils';
import 'emoji-mart/css/emoji-mart.css';
import { Picker } from 'emoji-mart';
import { resolve } from 'path';

const optionsData: any = [];
let filterState: any = [];

// const { Title } = Typography;
const CheckboxGroup = Checkbox.Group;
const { TextArea } = Input;

const rates = [
  {
    id: 1,
    name: '1天1次'
  },
  {
    id: 2,
    name: '2天1次'
  },
  {
    id: 3,
    name: '3天1次'
  },
  {
    id: 4,
    name: '4天1次'
  },
  {
    id: 5,
    name: '5天1次'
  },
  {
    id: 6,
    name: '6天1次'
  },
  {
    id: 7,
    name: '7天1次'
  },
  {
    id: 8,
    name: '8天1次'
  },
  {
    id: 9,
    name: '9天1次'
  },
  {
    id: 10,
    name: '10天1次'
  },
]
@connectSession
@(Form.create({ name: 'ChaoyouApplication' }) as any)
class ChaoyouApplication extends React.Component<any, any> {
  constructor(props: any) {
    super(props);
    const { formContent } = props;
    this.state = {
      ...props,
      form: {
        scene_name: '',
        scene_code: [],
        designatedUser: '',
        dimension_choice: '',
        frequency: null
      },
      options2: [],
      dataList: null,
      textAreaState: false,
    };

    api.getRecommendsceneDimensionlistAll({}).then((res: any) => {
      res.data.dimension_list.forEach((value: any) => {
        console.log(value.id !== 10);
        // if (value.id !== 10 && value.id !== 13) {
        this.state.options2.push({
          label: value.name,
          value: `${value.id}`,
          status: value.status,
          proportion: 10,
        });
        // }
      });
      this.setState({});
      console.log(this.state.options2);
    });

    if (formContent.id) {
      const dimensionChoice = JSON.parse(formContent.dimension_choice);
      formContent.disabled = true;
      if (
        dimensionChoice.choice.find((x: any) => Number(x) === 13) &&
        dimensionChoice.users.length > 0
      ) {
        this.state.form.designatedUser = dimensionChoice.users.join('\n');
        this.state.textAreaState = true;
      }
      api.getRecommendsceneDetails({ id: formContent.id }).then((res: any) => {
        console.log(res.data.recommend_scene);
        const resData = res.data.recommend_scene;
        console.log(JSON.parse(resData.dimension_sort).filter((x: any) => Number(x.value) !== 13));
        this.setState({
          form: {
            ...this.state.form,
            scene_name: resData.scene_name,
            frequency: parseInt(resData.frequency) || 1,
            scene_code: resData.scene_code,
            recommend_type: JSON.parse(resData.recommend_type),
            dimension_choice: JSON.parse(resData.dimension_choice).choice.map(
              (str: string) => `${str}`
            ),
            designatedUser: JSON.parse(resData.dimension_choice).users.join('\n'),
          },
          dataList: JSON.parse(resData.dimension_sort).filter((x: any) => Number(x.value) !== 13),
        });
      });
    }
  }

  doSubmit = () => {
    const { formContent } = this.state;
    // if (formContent.disabled) return this.props.onClose();
    this.props.form.validateFieldsAndScroll((err: any, values: any) => {
      if (!err) {
        const data: any = {};
        const dimensionSort = [];
        this.state.dataList.forEach((obj: any) => {
          dimensionSort.push({
            label: obj.label,
            value: obj.value,
            proportion: obj.proportion,
          });
        });

        console.log(values);
        if (values.dimension_choice.find((x: any) => Number(x) === 13)) {
          dimensionSort.push({
            label: '指定用户',
            value: '13',
            proportion: 0,
          });
        }

        data.scene_name = values.scene_name;
        data.scene_code = values.scene_code;
        data.recommend_type = JSON.stringify(values.recommend_type);
        data.dimension_choice = JSON.stringify({
          choice: values.dimension_choice,
          users: values.dimension_choice.find((x: any) => Number(x) === 13)
            ? this.state.form.designatedUser.split('\n')
            : [],
        });
        data.dimension_sort = JSON.stringify(dimensionSort);
        data.frequency = parseInt(values.frequency)
        if (formContent.id) {
          data.id = formContent.id;
          api
            .updateRecommendscene(data)
            .then((r) => {
              message.success('修改成功');
              setMLoading(this, false);
              this.props.onEnd();
            })
            .catch(() => {
              setMLoading(this, false);
            });
        } else {
          api
            .createRecommendscene(data)
            .then((r) => {
              message.success('新建成功');
              setMLoading(this, false);
              this.props.onEnd();
            })
            .catch(() => {
              setMLoading(this, false);
            });
        }
      } else {
        message.error('请检查表单内容');
      }
    });
    return true;
  };

  exchangeOrder = (i: any, n: string) => {
    const data = cloneDeep(this.state.dataList);
    if (n === '0') {
      data.splice(i, 1, ...data.splice(i - 1, 1, data[i]));
    } else {
      data.splice(i, 1, ...data.splice(i + 1, 1, data[i]));
    }
    this.setState({
      dataList: data,
    });
  };

  sliderOnChange = (value: any, record: any) => {
    this.setState({
      dataList: this.state.dataList.map((x: any) => {
        if (value.value === x.value) {
          x.proportion = record || 10;
        }
        return x;
      }),
    });
  };

  OnChange = (value: any) => {
    console.log(value.target.value);
    this.setState({
      form: {
        ...this.state.form,
        designatedUser: value.target.value,
      },
    });
  };

  handleSubmit = (e: any) => {
    e.preventDefault();
    this.doSubmit();
  };

  render() {
    const formLayout = {
      labelCol: { span: 4 },
      wrapperCol: { span: 20 },
    };
    const { getFieldDecorator } = this.props.form;
    const { form, formContent } = this.state;
    const options = [
      { label: '潮客', value: '1' },
      { label: '潮鸣号', value: '2' },
      { label: '地方号工作室', value: '3' },
    ];

    const onChange = (checkedValues: any) => {
      console.log('checked = ', checkedValues);
    };

    const onChange2 = (checkedValues: any) => {
      const dataList = this.state.options2
        .filter((vl: any) => {
          return !!checkedValues.find(
            (str: any) => Number(str) === Number(vl.value) && Number(str) !== 13
          );
        })
        .map((obj: any) => {
          const dataListFind =
            this.state.dataList &&
            this.state.dataList.find((x: any) => Number(x.value) === Number(obj.value));
          if (dataListFind) {
            return dataListFind;
          }
          return obj;
        });

      if (dataList.length >= 10) {
        const optionsList: any = [];
        this.state.options2.forEach((objckt: any) => {
          if (checkedValues.find((vl: any) => Number(objckt.value) === Number(vl))) {
            optionsList.push(objckt);
          } else {
            // eslint-disable-next-line no-param-reassign
            if (objckt.status) {
              // eslint-disable-next-line no-param-reassign
              objckt.status = 0;
              console.log(objckt);
              filterState.push(objckt);
            }
            optionsList.push(objckt);
          }
        });

        this.setState({
          options2: optionsList,
          dataList,
          textAreaState: checkedValues.find((str: any) => Number(str) === 13),
        });
        return;
      }

      if (filterState.length > 0) {
        const optionsList: any = [];
        this.state.options2.forEach((objckt: any) => {
          if (filterState.find((vl: any) => Number(objckt.value) === Number(vl.value))) {
            if (!objckt.status) {
              // eslint-disable-next-line no-param-reassign
              objckt.status = 1;
            }
            optionsList.push(objckt);
          } else {
            optionsList.push(objckt);
          }
        });
        filterState = [];
        this.setState({
          options2: optionsList,
        });
      }
      console.log(checkedValues);
      this.setState({
        dataList,
        textAreaState: checkedValues.find((str: any) => Number(str) === 13),
      });
    };

    const columns = [
      {
        title: '排序',
        dataIndex: 'name',
        key: 'name',
        render: (text: any, record: any, i: number) => (
          <span>
            <A
              disabled={i === 0}
              className="sort-up"
              onClick={this.exchangeOrder.bind(this, i, '0')}
            >
              <Icon type="up-circle" theme="filled" />
            </A>
            &nbsp;&nbsp;
            <A
              disabled={i + 1 >= this.state.dataList.length}
              className="sort-down"
              onClick={this.exchangeOrder.bind(this, i, '1')}
            >
              <Icon type="down-circle" theme="filled" />
            </A>
          </span>
        ),
      },
      {
        title: '维度',
        dataIndex: 'label',
        key: 'label',
      },
      {
        title: '推荐数量占比',
        dataIndex: 'proportion',
        render: (text: any, record: any) => (
          <Row>
            <Col span={12}>
              <Slider
                style={{ width: '200px' }}
                range={false}
                step={10}
                defaultValue={text || 10}
                onChange={this.sliderOnChange.bind(this, record)}
              />
            </Col>
            <Col span={12}> {text || 10}% </Col>
          </Row>
        ),
      },
    ];

    return (
      <>
        <Form {...formLayout} onSubmit={this.handleSubmit} style={{ position: 'relative' }}>
          <Form.Item label="应用场景">
            <Col span={14}>
              {getFieldDecorator('scene_name', {
                initialValue: form.scene_name,
                rules: [
                  {
                    required: true,
                    message: '应用场景不能为空',
                  },
                  {
                    max: 10,
                    message: '最多10个字',
                  },
                ],
              })(<Input placeholder="请输入应用场景名称，不超过10个汉字" />)}
            </Col>
          </Form.Item>
          <Form.Item label="场景编码">
            <Col span={14}>
              {getFieldDecorator('scene_code', {
                initialValue: form.scene_code,
                rules: [
                  {
                    required: true,
                    message: '场景编码不能为空',
                  },
                  {
                    max: 20,
                    message: '最多20个字符',
                  },
                ],
              })(
                <Input
                  disabled={formContent.disabled}
                  placeholder="请输入场景编码，由字母和符号_组成，不超过20个字符"
                />
              )}
            </Col>
          </Form.Item>
          <Form.Item label="推荐频次">
            <Col span={10}>
              {getFieldDecorator(`frequency`, {
                initialValue: form.frequency,
                rules: [
                  {
                    required: true,
                    message: '推荐频次不能为空',
                  },
                ],
              })(
                <Select placeholder="请选择推荐频次">
                  {rates.map((vv: any) => (
                    <Select.Option value={vv.id} key={vv.id}>
                      {vv.name}
                    </Select.Option>
                  ))}
                </Select>
              )}
            </Col>
          </Form.Item>
          <Form.Item label="推荐类型">
            <Col span={14}>
              {getFieldDecorator('recommend_type', {
                initialValue: form.recommend_type,
                rules: [
                  {
                    required: true,
                    message: '推荐类型不能为空',
                  },
                ],
              })(<CheckboxGroup options={options} onChange={onChange} />)}
            </Col>
          </Form.Item>
          <Form.Item label="维度选择">
            <Row>
              {this.state.options2.length > 0 &&
                getFieldDecorator('dimension_choice', {
                  initialValue: form.dimension_choice,
                  rules: [
                    {
                      required: true,
                      message: '维度选择不能为空',
                    },
                  ],
                })(
                  <CheckboxGroup onChange={onChange2}>
                    <Row>
                      {this.state.options2.map((temp: any, i: number) => {
                        return (
                          <Col key={temp.value} span={8}>
                            <Checkbox disabled={!temp.status} value={temp.value}>
                              {temp.label != '编辑推荐关注' ? temp.label : (
                                <span>
                                  {temp.label}&nbsp;
                                  <Tooltip title="该维度包括用户中心-推荐关注用户+潮客社区-用户推荐位+近30天发布过【被设为推荐的ugc内容】的用户">
                                    <Icon type="question-circle" />
                                  </Tooltip>
                                </span>
                              )}
                            </Checkbox>
                          </Col>
                        );
                      })}
                    </Row>
                  </CheckboxGroup>
                )}
            </Row>
            <Row>
              <Input.TextArea
                disabled={!this.state.textAreaState}
                defaultValue={String(this.state.form.designatedUser)}
                placeholder="请输入用户长ID，一行一个，最多5行"
                onChange={this.OnChange.bind(this)}
              />
            </Row>
            <Row>
              <Table
                columns={columns}
                pagination={false}
                rowKey={(x) => x.value}
                dataSource={this.state.dataList}
              />
            </Row>
          </Form.Item>
        </Form>
      </>
    );
  }
}

export default ChaoyouApplication;
