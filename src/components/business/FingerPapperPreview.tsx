declare const BUILD_ENV: 'dev' | 'test' | 'prev' | 'prod' | 'testb';

import React, { forwardRef, useEffect } from 'react';
import './styles/FingerPaperPreview.scss';

const FingerPapperPreview = (props: any, ref: any) => {
  // https://test.tianmunews.com
  const host =
    {
      dev: 'https://test-web.jiaxingren.com',
      test: 'https://test-web.jiaxingren.com',
      prev: 'https://tmprev.tidenews.com.cn',
      prod: 'https://app.jiaxingren.com',
      testb: 'https://test-web.jiaxingren.com',
    }[BUILD_ENV] || 'https://test-web.jiaxingren.com';

  useEffect(() => {
    let iframe = document.getElementById('iframe');
    const record = !!props.path
      ? {
          ...props.record,
        }
      : {
          ...props.record,
          content_list: JSON.parse(props.record?.content || '{}'),
        };
    iframe?.addEventListener(
      'load',
      function () {
        const iframeWindow = iframe?.contentWindow; // 利用iframe.contentWindow 也就是4.html 的window对象.
        setTimeout(() => {
          iframeWindow?.postMessage(JSON.stringify(record), host);
        }, 1000);
      },
      false
    );
  }, []);

  return (
    <div className="wrapper">
      <iframe
        id="iframe"
        src={`${host}/${props.path || 'finger_paper.html'}?inner=tidenews`}
      ></iframe>
    </div>
  );
};

export default forwardRef<any, any>(FingerPapperPreview);
