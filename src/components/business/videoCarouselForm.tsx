import { releaseListApi } from '@app/api';
import ImageUploader from '@components/common/imageUploader';
import A from '@components/common/a';
import SearchAndInput from '@components/common/newNewsSearchAndInput';
import { Form, Icon, Input, InputNumber, message, Modal, Tooltip, Switch } from 'antd';
import React from 'react';
import { setMLoading } from '@utils/utils';
import connectSession from '@utils/connectSession';

@connectSession
@(Form.create({ name: 'videoCarouselForm' }) as any)
class VideoCarouselForm extends React.Component<any, any> {
  constructor(props: any) {
    super(props);
    this.state = { ...props.formContent };
  }

  handleSubmit = (e: any) => {
    e.preventDefault();
    this.doSubmit();
  };

  doSubmit = () => {
    this.props.form.validateFieldsAndScroll((err: any, values: any) => {
      if (!err) {
        if (values.channelArticles.filter((v: any) => !v.picUrl).length > 0) {
          message.error('请上传全部稿件的图片');
          return;
        }
        setMLoading(this, true);
        let api: keyof typeof releaseListApi = 'createVideoCarouselRecommend';
        if (this.state.id) {
          api = 'updateVideoCarouselRecommend';
        }
        try {
          releaseListApi[api]({
            ...values,
            [this.state.id ? 'id' : 'channel_id']: this.state.id || this.props.channelId,
            channel_id: this.props.channelId,
            ref_ids: values.channelArticles.map((v: any) => v.uuid || v.id),
            pic_urls: values.channelArticles.map((v: any) => v.picUrl),
            display_pic: values.display_pic ? 1 : 0,
          })
            .then(() => {
              setMLoading(this, false);
              message.success('操作成功');
              this.props.onEnd();
            })
            .catch(() => {
              setMLoading(this, false);
            });
        } catch (errr) {
          console.error(errr);
        }
      } else {
        message.error('请检查表单内容');
      }
    });
  };

  editPic = (url: string, index: number) => {
    let pic: string = url;
    let modal: any;
    const picChange = (u: string) => {
      pic = u;
      modal.update({
        content: (
          <>
            <ImageUploader value={pic} onChange={picChange} ratio={3 / 4} />
            <p>比例3:4</p>
          </>
        ),
      });
    };
    modal = Modal.confirm({
      title: '编辑图片',
      content: (
        <>
          <ImageUploader value={pic} onChange={picChange} ratio={3 / 4} />
          <p>比例3:4</p>
        </>
      ),
      onOk: (destroy: Function) => {
        if (!pic) {
          message.error('请上传图片');
          return;
        }
        const articles = [...this.props.form.getFieldsValue().channelArticles];
        articles[index].picUrl = pic;
        this.props.form.setFieldsValue({ channelArticles: articles });
        destroy();
      },
    });
  };

  getColumn = () => {
    return [
      {
        title: 'ID',
        dataIndex: 'id',
        width: 80,
        render: (_: any, v: any) => v.uuid || v.id,
      },
      {
        title: '新闻频道',
        key: 'type',
        dataIndex: 'channel_name',
        width: 90,
      },
      {
        title: '新闻标题',
        key: 'list_title',
        dataIndex: 'list_title',
      },
      {
        title: '列表图',
        dataIndex: 'picUrl',
        width: 90,
        render: (text: string, _: any, index: number) => (
          <>
            <A onClick={() => this.editPic(text, index)}>{text ? '修改' : '上传'}</A>
          </>
        ),
      },
    ];
  };

  articlesChange = (data: any) => {
    this.setState({ ...data });
  };

  render() {
    const { getFieldDecorator } = this.props.form;
    const formLayout = {
      labelCol: { span: 4 },
      wrapperCol: { span: 18 },
    };

    const text = (
      <span>
        <p>输入说明：</p>
        <p>
          直接输入阿拉伯数字代表位置序号，例如要在头条频道信息流第8个位置插入该推荐位，则直接输入“8”即可。
        </p>
        <p>最大输入数字为500</p>
        <br />
        <p>注：</p>
        <p>直接在输入的指定位置上插入推荐位显示，不参与稿件的排序，且优先于媒立方稿件展示</p>
      </span>
    );

    const text2 = (
      <p>最多关联15条，只能关联视频稿，且每条稿件都必须上传推荐位使用的列表图，才能创建成功。</p>
    );
    return (
      <Form {...formLayout} onSubmit={this.handleSubmit}>
        <Form.Item label="名称">
          {getFieldDecorator('recommend_name', {
            initialValue: this.state.recommend_name,
            rules: [
              {
                required: true,
                message: '请填写名称',
              },
              {
                max: 30,
                message: '名称最长不能超过30个字',
              },
            ],
          })(<Input placeholder="请输入名称" />)}
        </Form.Item>
        <Form.Item label="插入位置">
          {getFieldDecorator('position', {
            initialValue: this.state.position,
            rules: [
              {
                required: true,
                message: '请输入位置序号',
                type: 'number',
              },
              {
                max: 500,
                message: '最大不能超过500',
                type: 'number',
              },
            ],
          })(<InputNumber max={500} style={{ width: 200 }} placeholder="请输入位置序号" min={1} />)}
          &emsp;
          <Tooltip title={text}>
            <Icon type="question-circle" />
          </Tooltip>
        </Form.Item>
        <Form.Item label="关联稿件">
          {getFieldDecorator('channelArticles', {
            initialValue: this.state.channelArticles,
            preserve: true,
            rules: [
              {
                required: true,
                message: '请关联新闻',
                type: 'array',
              },
              {
                max: 15,
                message: '最多关联15条新闻',
                type: 'array',
              },
              {
                min: 2,
                message: '为保证客户端显示效果，关联新闻数不能少于2条！',
                type: 'array',
              },
            ],
          })(
            <SearchAndInput
              max={15}
              func="listArticleRecommendSearch"
              columns={this.getColumn()}
              placeholder="输入ID或标题关联稿件"
              body={{ doc_types: '9,10' }}
              order={true}
              addOnTop={true}
              afix={
                <Tooltip title={text2}>
                  <Icon type="question-circle" />
                </Tooltip>
              }
            />
          )}
        </Form.Item>
      </Form>
    );
  }
}

export default VideoCarouselForm;
