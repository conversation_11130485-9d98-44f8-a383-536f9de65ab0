import { sysApi as api } from '@app/api';
import { Form, Input, message, Radio, Slider, Tooltip, Icon, Row, Switch } from 'antd';
import React from 'react';
import { setMLoading } from '@utils/utils';
import connectSession from '@utils/connectSession';
import { ImageUploader } from '../common';

@connectSession
@(Form.create({ name: 'NavTopBgForm' }) as any)
class NavTopBgForm extends React.Component<any, any> {
  constructor(props: any) {
    super(props);
    this.state = {
      ...props.formContent?.record,
      status: props.formContent?.status ?? 0,
    };
  }

  handleSubmit = (e: any) => {
    e.preventDefault();
    this.doSubmit();
  };

  sliderOnChange = (value: any) => {
    console.log('onChange: ', value);
  };

  onAfterChange = (value: any) => {
    console.log('onAfterChange: ', value);
  };

  doSubmit = () => {
    this.props.form.validateFieldsAndScroll((err: any, values: any) => {
      if (!err) {
        setMLoading(this, true);
        const extension = { ...values };
        delete extension.status;
        const body = {
          status: values.status ? 1 : 0,
          type: 60,
          ref_extensions: JSON.stringify(extension),
        };
        api
          .saveNavTopBg(body)
          .then(() => {
            setMLoading(this, false);
            message.success('操作成功');
            this.props.onEnd();
          })
          .catch(() => {
            setMLoading(this, false);
          });
      } else {
        message.error('请检查表单内容');
      }
    });
  };

  render() {
    const { getFieldDecorator, getFieldsValue } = this.props.form;
    const formLayout = {
      labelCol: { span: 4 },
      wrapperCol: { span: 18 },
    };
    const values = { ...this.props.formContent, ...getFieldsValue() };
    return (
      <Form {...formLayout} onSubmit={this.handleSubmit}>
        <Form.Item label="开关">
          {getFieldDecorator('status', {
            initialValue: this.state.status || 0,
            valuePropName: 'checked',
          })(<Switch></Switch>)}
        </Form.Item>
        <Form.Item label="logo颜色" extra=" 输入色值 例: #000000">
          {getFieldDecorator('logo_color', {
            initialValue: this.state.logo_color || '',
            rules: [
              { required: true, message: '请输入色值' },
              { pattern: /^#[0-9ABCDEFGabcdefg]{6}$/, message: '输入格式不正确' },
            ],
          })(<Input placeholder="输入色值" />)}
        </Form.Item>

        <Form.Item label="搜索图标颜色" extra=" 输入色值 例: #000000">
          {getFieldDecorator('search_icon_color', {
            initialValue: this.state.search_icon_color || '',
            rules: [
              { required: true, message: '请输入色值' },
              { pattern: /^#[0-9ABCDEFGabcdefg]{6}$/, message: '输入格式不正确' },
            ],
          })(<Input placeholder="输入色值" />)}
        </Form.Item>
        <Form.Item
          label="搜索框底色"
          extra={
            <span>
              输入色值 例: #00000000, 前两位为透明度
              <Tooltip title="透明度的对应色值如下：100% : FF，95% : F2，90% : E6，85% : D9，80% : CC，75% : BF，70% : B3，65% : A6，60% : 99，55% : 8C，50% : 80，45% : 73，40% : 66，35% : 59，30% : 4D，25% : 40，20% : 33，15% : 26，10% : 1A，5% : 0D，0% : 00">
                <Icon type="question-circle" />
              </Tooltip>
            </span>
          }
        >
          {getFieldDecorator('search_kuang_color', {
            initialValue: this.state.search_kuang_color || '',
            rules: [
              { required: true, message: '请输入色值' },
              { pattern: /^#[0-9ABCDEFGabcdefg]{8}$/, message: '输入格式不正确' },
            ],
          })(<Input placeholder="输入色值" />)}
        </Form.Item>

        <Form.Item
          label="背景图"
          extra="支持.jpg .jpeg .png等格式, 比例未限制，建议尺寸 1125px * 393px"
        >
          {getFieldDecorator('bg_pic_url', {
            initialValue: this.state.bg_pic_url || '',
            rules: [{ required: true, message: '请上传图片' }],
          })(<ImageUploader />)}
        </Form.Item>
      </Form>
    );
  }
}

export default NavTopBgForm;
