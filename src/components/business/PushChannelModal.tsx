import React from 'react';
import { listApi, releaseListApi, systemApi } from '@app/api';
import { searchToObject } from '@app/utils/utils';
import { Col, message, Modal, Row, Spin, TreeSelect } from 'antd';
import { useEffect, useState } from 'react';

export default function PushChannelModal(props: any) {
  const [loading, setLoading] = useState(false);
  const [originalChannel, setOriginalChannel] = useState<any>(null);
  const [checkedKeys, setCheckedKeys] = useState<any[]>([]);
  const [channelList, setChannelList] = useState<any[]>([]);
  const [disabledCheckedKeys, setDisabledCheckedKeys] = useState<any[]>([]);

  useEffect(() => {}, []);

  useEffect(() => {
    if (props.visible) {
      getChannelList();
    } else {
      setCheckedKeys([]);
      setOriginalChannel(null);
      setChannelList([]);
    }
  }, [props.visible]);

  const handleOkClick = () => {
    if (!props.record.id) {
      return;
    }
    const canSaveKeys = checkedKeys.filter((key: any) => !disabledCheckedKeys.includes(key));
    // if (canSaveKeys.length === 0) {
    //   message.error('请选择频道');
    //   return;
    // }

    setLoading(true);
    releaseListApi
      .pushMultiChannel({
        id: props.record.id,
        channel_ids: canSaveKeys.join(','),
      })
      .then((r: any) => {
        setLoading(false);
        props.onOk();
        message.success('操作成功');
      })
      .catch(() => {
        setLoading(false);
      });
  };

  // const toChannelList = () => {
  //   listApi
  //     .getToChannelList({ article_id: props.record.id })
  //     .then((r: any) => {
  //       // 这里返回的频道列表都是推过的，不能二次推
  //       if (article.id === props.record.id) {
  //         const olist: any[] = [];
  //         list.forEach((item: any) => {
  //           if (item.type === 3) {
  //             olist.push(item);
  //           }
  //         });
  //         const oChannel = olist.map((item: any) => item.to_channel_name).join(',');
  //         setOriginalChannel(oChannel);
  //         setDisabledCheckedKeys([...olist.map((item: any) => item.to_channel_id)]);
  //         setCheckedKeys(list.map((item: any) => item.to_channel_id));
  //       }
  //     })
  //     .catch(() => {});
  // };

  const getChannelList = async () => {
    setLoading(true);
    try {
      const allChannel: any = await systemApi.getAllChannelsTree();
      const toChannel: any = await listApi.getToChannelList({ article_id: props.record.id });

      const allChannelList = allChannel.data.channel_list || [];
      const toChannelList = toChannel.data.list || [];
      const oChannel = toChannelList.filter((item: any) => item.type == 3)?.[0] || {};

      setCheckedKeys(
        toChannelList.filter((item: any) => item.type != 3 && item.type != 1).map((item: any) => item.to_channel_id)
      );

      setOriginalChannel(oChannel);

      setChannelList(allChannelList);

      setLoading(false);
    } catch (error) {
      setLoading(false);
    }
  };

  const renderTreeNodeDisabled = (node: any) => {
    return false;
    // // 自己不能选，潮客，潮鸣号不能选
    // const channel_id = searchToObject().channel_id;
    // return (
    //   // node.name === '潮客' ||
    //   // node.name === '潮鸣号' ||
    //   disabledCheckedKeys.includes(node.id)
    //   //  ||
    //   // (this.state.checkedKeys.length >= 3 && !this.state.checkedKeys.includes(node.id))
    // );
  };

  const renderTreeNode = (node: any) => {
    if (node.id == originalChannel?.to_channel_id) {
      return null;
    }

    return node.children && node.children.length > 0 ? (
      <TreeSelect.TreeNode title={node.name} key={node.id} value={node.id}>
        {node.children.map((snode: any) => renderTreeNode(snode))}
      </TreeSelect.TreeNode>
    ) : (
      <TreeSelect.TreeNode
        title={node.name}
        key={node.id}
        value={node.id}
        disabled={renderTreeNodeDisabled(node)}
      />
    );
  };
  return (
    <Modal
      title="多频道展示"
      onCancel={props.onClose}
      visible={props.visible}
      key={props.skey}
      onOk={handleOkClick}
    >
      <Spin tip="正在加载..." spinning={loading}>
        <Row>
          <Col span={6}>ID</Col>
          <Col span={18}>{props.record?.id}</Col>
        </Row>
        <Row>
          <Col span={6}>天目蓝云ID</Col>
          <Col span={18}>{props.record?.metadata_id}</Col>
        </Row>
        <Row>
          <Col span={6}>标题</Col>
          <Col span={18}>{props.record?.list_title}</Col>
        </Row>
        <Row>
          <Col span={6}>原始频道</Col>
          <Col span={18}>{originalChannel?.to_channel_name || ''}</Col>
        </Row>
        {/* <Row>
          <Col span={6}>已关联频道</Col>
          <Col span={18}>{relatedChannel}</Col>
        </Row> */}
        <Row>
          <Col span={6}>多签频道</Col>
          <Col span={18}>
            <TreeSelect
              dropdownStyle={{ maxHeight: 400, overflow: 'auto' }}
              placeholder="请选择频道"
              treeDefaultExpandAll={false}
              allowClear
              treeCheckable
              style={{ width: '100%' }}
              multiple
              onChange={(value: any) => setCheckedKeys(value)}
              value={checkedKeys}
              treeNodeFilterProp="title"
            >
              {channelList.map((node: any) => renderTreeNode(node))}
            </TreeSelect>
          </Col>
        </Row>

        {/* <div style={{ display: 'flex', marginBottom: '8px' }}>
        <span style={{ width: '70px' }}>推至频道</span>
        <div onClick={this.handleTreeClick} >
          <Tree selectable={false} checkedKeys={this.state.checkedKeys} onCheck={this.handleCheckChannel} checkable>
            {this.state.channelList.map((node: any) => this.renderTreeNode(node))}
          </Tree>
        </div>
      </div> */}
      </Spin>
    </Modal>
  );
}
