import { sysApi as api } from '@app/api';
import { ImageUploader } from '@components/common';
import { Divider, Form, Input, message, Radio, Select, Switch, TreeSelect } from 'antd';
import React from 'react';
import ArLinkInput, { arLinkValidator } from '@components/common/arLinkInput';
import { setMLoading } from '@utils/utils';
import connectSession from '@utils/connectSession';

type Api = 'updateAppService' | 'createAppService';

@connectSession
@(Form.create({ name: 'appServiceForm' }) as any)
class AppServiceForm extends React.Component<any, any> {
  constructor(props: any) {
    super(props);
    const form = {
      id: '',
    };
    this.state = {
      ...form,
      ...props.formContent,
      status: false,
      form1: {
        feature: 'custom_operate_switch',
        url: '',
        custom_icon_url: '',
      },
    };
  }

  componentDidMount() {
    this.switchDetail();
  }

  switchDetail = () => {
    const { form1 } = this.state;

    api
      .getSwitchDetail({ feature: 'custom_operate_switch' })
      .then((res: any) => {
        const data = res.data;
        this.setState({
          status: data?.switch,
          form1: {
            ...form1,
            custom_icon_url: data?.web_feature ? data?.web_feature.custom_icon_url || '' : '',
            url: data?.web_feature ? data?.web_feature.url || '' : '',
          },
        });
      })
      .catch((err) => {
        console.error(err);
      });
  };

  handleSubmit = (e: any) => {
    e.preventDefault();
    this.doSubmit();
  };

  doSubmit = () => {
    this.props.form.validateFieldsAndScroll((err: any, values: any) => {
      if (!err) {
        api
          .updateSwitchWithVer({
            feature: 'custom_operate_switch',
            status: this.state.status,
            url: values.url,
            custom_icon_url: values.custom_icon_url,
          })
          .then(() => {
            message.success('操作成功');
            setMLoading(this, false);
            this.props.onEnd();
          })
          .catch(() => setMLoading(this, false));
      } else {
        message.error('请检查表单内容');
      }
    });
  };

  statusChange = (checked: boolean) => {
    this.setState({
      status: checked,
    });
  };

  urlValidator = (rule: any, value: any, callback: any) => {
    const regex = /^https?:\/\//;
    if (value === '') {
      callback('请填写链接');
      return;
    }
    if (!regex.test(value)) {
      callback('请正确填写链接格式');
      return;
    }

    callback();
  };

  render() {
    const { getFieldDecorator } = this.props.form;
    const formLayout = {
      labelCol: { span: 4 },
      wrapperCol: { span: 18 },
    };

    return (
      <Form onSubmit={this.handleSubmit} {...formLayout}>
        <Form.Item label="开关">
          <Switch
            key={this.state.status}
            defaultChecked={this.state.status}
            onChange={this.statusChange}
          />
        </Form.Item>

        <Form.Item required={true} label="入口图标">
          {getFieldDecorator('custom_icon_url', {
            initialValue: this.state.form1.custom_icon_url,
            rules: [
              {
                required: true,
                message: '图片不能为空',
              },
            ],
          })(
            <ImageUploader
              ratio={1 / 1}
              imgsize={100}
              onChange={(v: any) => this.setState({ custom_icon_url: v })}
            />
          )}
        </Form.Item>

        <Form.Item required={true} label="链接">
          {getFieldDecorator('url', {
            initialValue: this.state.form1.url,
            rules: [
              {
                required: true,
                message: '请填写推链接',
              },
              {
                validator: this.urlValidator,
              },
            ],
          })(<Input placeholder="请输入链接" />)}
        </Form.Item>
      </Form>
    );
  }
}

export default AppServiceForm;
