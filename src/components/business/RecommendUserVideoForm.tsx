import { releaseList<PERSON>pi as api } from '@app/api';
import { Form, Input, message, Radio, Select, Icon, Switch, Tooltip } from 'antd';
import React from 'react';
import { setMLoading, searchToObject } from '@utils/utils';
import connectSession from '@utils/connectSession';
import SearchAndInput from '@components/common/newNewsSearchAndInput';

type Api = 'createCenterNav' | 'updateCenterNav';

@connectSession
@(Form.create({ name: 'RecommendUserVideoFom' }) as any)
class RecommendUserVideoFom extends React.Component<any, any> {
  constructor(props: any) {
    super(props);
    this.state = {
      ...props.formContent,
    };
  }

  handleSubmit = (e: any) => {
    e.preventDefault();
    this.doSubmit();
  };

  getColumn = () => {
    return [
      {
        title: '序号',
        dataIndex: 'eq',
        width: 70,
        render: (_: any, v: any, i: number) => i + 1,
      },
      {
        title: '新闻频道',
        key: 'type',
        dataIndex: 'channel_name',
        width: 90,
      },
      {
        title: '新闻标题',
        key: 'list_title',
        dataIndex: 'list_title',
      },
    ];
  };

  doSubmit = () => {
    this.props.form.validateFieldsAndScroll((err: any, values: any) => {
      if (!err) {
        let body: any;
        if (values.enabled) {
          body = {
            enabled: values.enabled ? 1 : 0,
            video_type: values.video_type,
          };
          if (values.channelArticles) {
            body.video_id = values.channelArticles[0].id;
            body.video_title = values.channelArticles[0].list_title;
          }
        } else {
          body = {
            enabled: values.enabled ? 1 : 0,
          };
        }

        setMLoading(this, true);
        api
          .saveUgcVideoRecommend(body)
          .then(() => {
            message.success('操作成功');
            setMLoading(this, false);
            this.props.onEnd();
          })
          .catch(() => setMLoading(this, false));
      } else {
        message.error('请检查表单内容');
      }
    });
  };

  onChange = (e: any) => {
    console.log(this.props.form.getFieldValue('enabled'));
    this.props.form.setFieldsValue({
      enabled: e,
    });
  };

  render() {
    const { getFieldDecorator, formContent } = this.props.form;
    const formLayout = {
      labelCol: { span: 4 },
      wrapperCol: { span: 18 },
    };

    // 固定获取潮客id----
    const channel_id = JSON.parse(window.localStorage.getItem('session'))
      .menus.find((menu: any) => {
        return menu.name === '稿件管理';
      })
      .children.find((me: any) => {
        return me.name === '潮客';
      })
      .url.split('?')[1]
      .split('=')[1];

    return (
      <Form {...formLayout} onSubmit={this.handleSubmit}>
        <Form.Item label="开关">
          {getFieldDecorator('enabled', {
            initialValue: this.state.enabled,
            valuePropName: 'checked',
          })(<Switch onChange={this.onChange} />)}
          <span>开启即启用潮客视频推荐</span>
        </Form.Item>
        {this.props.form.getFieldValue('enabled') && (
          <Form.Item label="视频类型">
            {getFieldDecorator('video_type', {
              initialValue: this.state.video_type ? this.state.video_type : 1,
              rules: [
                {
                  required: true,
                  message: '请选择类型',
                },
              ],
            })(
              <Radio.Group>
                <Radio value={1}>系统推荐</Radio>
                <Radio value={2}>自定义</Radio>
              </Radio.Group>
            )}
          </Form.Item>
        )}
        {this.props.form.getFieldValue('video_type') === 2 &&
          this.props.form.getFieldValue('enabled') && (
            <Form.Item>
              {getFieldDecorator('channelArticles', {
                initialValue: this.state.channelArticles,
                preserve: false,
                rules: [
                  {
                    required: true,
                    message: '请关联新闻',
                    type: 'array',
                  },
                  {
                    max: 15,
                    message: '最多关联15条新闻',
                    type: 'array',
                  },
                  {
                    min: 1,
                    message: '为保证客户端显示效果，关联新闻数不能少于1条！',
                    type: 'array',
                  },
                ],
              })(
                <SearchAndInput
                  max={1}
                  func="conmentSearch"
                  columns={this.getColumn()}
                  placeholder="请输入潮客视频ID或标题关键词"
                  body={{ doc_types: '10' }}
                  order={false}
                  addOnTop={true}
                  filterData={(x: any) => {
                    return x.channel_id === channel_id;
                  }}
                  afix={
                    <Tooltip title={<p>最多关联1条。</p>}>
                      <Icon type="question-circle" />
                    </Tooltip>
                  }
                />
              )}
            </Form.Item>
          )}
      </Form>
    );
  }
}

export default RecommendUserVideoFom;
