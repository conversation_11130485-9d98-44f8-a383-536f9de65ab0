import { userApi as api } from '@app/api';
import { Form, Input, message, Radio, DatePicker } from 'antd';
import React from 'react';
import { setMLoading } from '@utils/utils';
import connectSession from '@utils/connectSession';
import moment from 'moment';
import { releaseListApi } from '@app/api';
const { RangePicker } = DatePicker;

@connectSession
@(Form.create({ name: 'HideForm' }) as any)
class HideForm extends React.Component<any, any> {
  constructor(props: any) {
    super(props);
    this.state = {
      ...props.HideForm,
    };
  }
  componentDidMount() {
    console.log(this.props, 'state==========');
    this.setState({
      ...this.state.first,
      start_show_time: moment(this.state.start_show_time),
      end_show_time: moment(this.state.end_show_time),
    });
  }
  handleSubmit = (e: any) => {
    e.preventDefault();
    this.doSubmit();
  };

  doSubmit = () => {
    this.props.form.validateFieldsAndScroll((err: any, values: any) => {
      console.log(values, 'values===========');
      if (!err) {
        const data: any = {
          id: this.state.id,
          is_timing: false,
          visible: values.visible,
        };
        if (values.visible) {
          data.is_timing = values.visible_type;
          if (values.visible_type) {
            data.start_show_time = values.date[0].format('YYYY-MM-DD HH:mm:ss');
            data.end_show_time = values.date[1].format('YYYY-MM-DD HH:mm:ss');
            data.visible = false;
          }
        }

        setMLoading(this, true);
        releaseListApi
          .releaseChangeVisible(data)
          .then((res) => {
            setMLoading(this, false);
            this.props.onEnd();
            message.success('操作成功');
          })
          .catch(() => {
            setMLoading(this, false);
          });
      } else {
        message.error('请检查表单内容');
      }
    });
  };
  changeRadio = (e: any) => {
    this.setState({
      ...this.state,
      visible: e.target.value,
    });
  };
  render() {
    const { getFieldDecorator } = this.props.form;
    const formLayout = {
      labelCol: { span: 4 },
      wrapperCol: { span: 18 },
    };
    return (
      <Form {...formLayout} onSubmit={this.handleSubmit} ref="form">
        <Form.Item label="是否展示">
          {getFieldDecorator('visible', {
            initialValue: !!this.props.HideForm.is_timing ? true : this.state.visible,
            rules: [
              {
                required: true,
                message: '请选择是否展示',
              },
            ],
          })(
            <Radio.Group onChange={this.changeRadio}>
              <Radio value={false}>隐藏</Radio>
              <Radio value={true}>显示</Radio>
            </Radio.Group>
          )}
        </Form.Item>
        {this.props.form.getFieldValue('visible') && (
          <Form.Item label="显示方式">
            {getFieldDecorator('visible_type', {
              initialValue: !!this.props.HideForm.is_timing,
              rules: [
                {
                  required: true,
                  message: '请填写问题答复',
                },
              ],
            })(
              <Radio.Group>
                <Radio value={false}>一直显示</Radio>
                <Radio value={true}>定期显示</Radio>
              </Radio.Group>
            )}
          </Form.Item>
        )}
        {this.props.form.getFieldValue('visible') && this.props.form.getFieldValue('visible_type') && (
          <Form.Item label={`显示时间`}>
            {getFieldDecorator('date', {
              initialValue: !!this.props.HideForm.is_timing
                ? [moment(this.state.start_show_time), moment(this.state.end_show_time)]
                : [],
              rules: [
                {
                  required: true,
                  message: '结束时间必须大于开始时间',
                },
              ],
            })(<RangePicker showTime format="YYYY-MM-DD HH:mm:ss" />)}
          </Form.Item>
        )}
      </Form>
    );
  }
}

export default HideForm;
