import { sysApi as api } from '@app/api';
import { Checkbox, Form, Icon, Input, InputNumber, message, Radio, Select, Tooltip } from 'antd';
import React from 'react';
import { compareVersions, setMLoading } from '@utils/utils';
import connectSession from '@utils/connectSession';
import { ImageUploader } from '../common';

type Api = 'createAppNav' | 'updateAppNav';

@connectSession
@(Form.create({ name: 'appNavForm' }) as any)
class AppNavForm extends React.Component<any, any> {
  constructor(props: any) {
    super(props);
    this.state = {
      ...props.formContent,
      privGroup: props.privGroup || [],
    };
  }

  handleSubmit = (e: any) => {
    e.preventDefault();
    this.doSubmit();
  };

  doSubmit = () => {
    this.props.form.validateFieldsAndScroll((err: any, values: any) => {
      if (!err) {
        const body: any = { ...values };
        body.nav_target_type = values.nav_target_type || '';
        body.selected = true;
        let func = 'create';
        if (this.state.id) {
          func = 'update';
          body.id = this.state.id;
        }
        if (body.require_permission == 0) {
          body.require_permission_id = 0;
        } else {
          body.require_permission_id = body.require_permission_id.join(',');
        }
        delete body.require_permission;

        setMLoading(this, true);
        api[`${func}AppNav` as Api](body)
          .then(() => {
            message.success('操作成功');
            setMLoading(this, false);
            this.props.onEnd();
          })
          .catch(() => setMLoading(this, false));
      } else {
        message.error('请检查表单内容');
      }
    });
  };

  validateVersion = (rule: any, value: any, callback: any) => {
    // TODO 大小比较
    const { form } = this.props;
    const { min_version_str, max_version_str } = form.getFieldsValue([
      'min_version_str',
      'max_version_str',
    ]);
    if (value?.split('.')?.length != 3) {
      callback('请输入正确的版本号');
      return;
    }

    if (compareVersions(min_version_str, max_version_str) == -1) {
      if (rule.field === 'min_version_str') {
        callback('最低版本号不能高于最高版本号');
      } else {
        callback('最高版本号不能低于最低版本号');
      }
      return;
    }
    if (min_version_str !== '' && max_version_str !== '') {
      form.setFields({
        min_version_str: { value: min_version_str },
        max_version_str: { value: max_version_str },
      });
    }
    callback();
  };

  render() {
    const { getFieldDecorator, getFieldValue } = this.props.form;
    const formLayout = {
      labelCol: { span: 4 },
      wrapperCol: { span: 18 },
    };
    return (
      <Form {...formLayout} onSubmit={this.handleSubmit}>
        <Form.Item label="名称">
          {getFieldDecorator('name', {
            initialValue: this.state.name,
            rules: [
              {
                required: true,
                message: '请填写名称',
              },
              // {
              //   pattern: /^[a-zA-Z0-9\u4e00-\u9fa5]+$/,
              //   message: '名称不能包含特殊字符',
              // },
            ],
          })(<Input placeholder="请输入名称" />)}
        </Form.Item>
        <Form.Item label="简介">
          {getFieldDecorator('information', {
            initialValue: this.state.information,
            rules: [
              {
                max: 20,
                message: '简介最多20个字',
              },
            ],
          })(<Input placeholder="请输入简介" />)}
        </Form.Item>
        <Form.Item label="页面类型">
          {getFieldDecorator('nav_kind', {
            initialValue: this.state.nav_kind ?? 0,
          })(
            <Radio.Group>
              <Radio value={0}>原生</Radio>
              <Radio value={1}>H5</Radio>
            </Radio.Group>
          )}
        </Form.Item>
        <Form.Item label="频道地址">
          {getFieldDecorator('uri_scheme', {
            initialValue: this.state.uri_scheme,
            rules: [
              {
                required: true,
                message: '请输入频道地址',
              },
            ],
          })(<Input placeholder="请输入频道地址" />)}
        </Form.Item>
        <Form.Item label="属性">
          {getFieldDecorator('collapsed', {
            initialValue: this.state.collapsed,
          })(
            <Radio.Group>
              <Radio value={true}>固定导航</Radio>
              <Radio value={false}>可移动导航</Radio>
            </Radio.Group>
          )}
        </Form.Item>
        <Form.Item label="状态">
          {getFieldDecorator('enabled', {
            initialValue: this.state.enabled,
          })(
            <Radio.Group>
              <Radio value={true}>上线</Radio>
              <Radio value={false}>下线</Radio>
            </Radio.Group>
          )}
        </Form.Item>
        {/* <Form.Item label="导航设置">
          {getFieldDecorator('selected', {
            initialValue: this.state.selected,
          })(
            <Radio.Group>
              <Radio value={true}>已选区</Radio>
              <Radio value={false}>待选区</Radio>
            </Radio.Group>
          )}
        </Form.Item> */}
        <Form.Item label="默认频道设置">
          {getFieldDecorator('account_set_default_status', {
            initialValue: this.state.account_set_default_status,
          })(
            <Radio.Group>
              <Radio value={0}>不可选</Radio>
              <Radio value={1}>可选</Radio>
            </Radio.Group>
          )}
        </Form.Item>

        {!this.state.uri_scheme.startsWith('followed') &&
          !this.state.uri_scheme.startsWith('proposal') &&
          !this.state.uri_scheme.startsWith('ai') && (
            <Form.Item label="添加到桌面">
              {getFieldDecorator('nav_share_flag', {
                initialValue: this.state.nav_share_flag ?? false,
              })(
                <Radio.Group>
                  <Radio value={false}>关闭</Radio>
                  <Radio value={true}>开启</Radio>
                </Radio.Group>
              )}
            </Form.Item>
          )}

        {getFieldValue('nav_share_flag') && (
          <>
            <Form.Item label="名称">
              {getFieldDecorator('nav_share_name', {
                initialValue: this.state.nav_share_name,
              })(<Input placeholder="最多8字，不填写时默认为频道名称" maxLength={8} />)}
            </Form.Item>

            <Form.Item label="iOS图标" extra={'支持.jpg .jpeg .png 图片格式, 比例1:1'}>
              {getFieldDecorator('ios_icon', {
                initialValue: this.state.ios_icon,
                rules: [
                  {
                    required: true,
                    message: '请上传iOS图标',
                  },
                ],
              })(<ImageUploader ratio={1}></ImageUploader>)}
            </Form.Item>

            <Form.Item label="安卓图标" extra={'支持.jpg .jpeg .png 图片格式, 比例1:1'}>
              {getFieldDecorator('android_icon', {
                initialValue: this.state.android_icon,
                rules: [
                  {
                    required: true,
                    message: '请上传安卓图标',
                  },
                ],
              })(<ImageUploader ratio={1}></ImageUploader>)}
            </Form.Item>

            <Form.Item label="分享页预览图" extra={'图片比例 245:445，支持扩展名：.jpg .jpeg .png'}>
              {getFieldDecorator('share_preview_image', {
                initialValue: this.state.share_preview_image,
                rules: [
                  {
                    required: true,
                    message: '请上传分享页预览图',
                  },
                ],
              })(<ImageUploader ratio={245 / 445}></ImageUploader>)}
            </Form.Item>

            <Form.Item label="分享地址">
              {getFieldDecorator('nav_share_url', {
                initialValue: this.state.nav_share_url,
                rules: [
                  {
                    required: true,
                    message: '请输入分享地址',
                  },
                ],
              })(<Input placeholder="请输入分享地址" />)}
            </Form.Item>
          </>
        )}

        <Form.Item label="最低版本" extra="包含所输版本！示例：5.0.0">
          {getFieldDecorator('min_version_str', {
            initialValue: this.state.min_version_str,
            normalize: (value: any) => {
              return value?.replaceAll(' ', '');
            },
            rules: [
              {
                required: true,
                message: '请输入最小版本号',
              },
              {
                validator: this.validateVersion,
              },
            ],
          })(<Input placeholder="请输入最低版本" />)}
        </Form.Item>
        <Form.Item label="最高版本" extra="包含所输版本！示例：10.0.0">
          {getFieldDecorator('max_version_str', {
            initialValue: this.state.max_version_str,
            normalize: (value: any) => {
              return value?.replaceAll(' ', '');
            },
            rules: [
              {
                required: true,
                message: '请输入最高版本号',
              },
              {
                validator: this.validateVersion,
              },
            ],
          })(<Input placeholder="请输入最高版本" />)}
        </Form.Item>
        <Form.Item label="特权组要求">
          {getFieldDecorator('require_permission', {
            initialValue:
              this.state.require_permission_id == 0 || !this.state.require_permission_id ? 0 : 1,
          })(
            <Radio.Group>
              <Radio value={0}>所有用户可见</Radio>
              <Radio value={1}>仅特权组可见</Radio>
            </Radio.Group>
          )}
        </Form.Item>
        {getFieldValue('require_permission') == 1 && (
          <Form.Item label=" " colon={false} style={{ marginTop: -20 }}>
            {getFieldDecorator('require_permission_id', {
              initialValue:
                this.state.require_permission_id == 0 || !this.state.require_permission_id
                  ? []
                  : this.state.require_permission_id.toString().split(','),
              rules: [
                {
                  validator: (rule: any, value: any, callback: any) => {
                    if (value?.length > 0) {
                      callback();
                    } else {
                      callback('请至少选择1个特权组');
                    }
                  },
                },
              ],
            })(
              <Checkbox.Group>
                {this.state.privGroup.map((v: any) => (
                  <Checkbox key={v.id} value={v.id.toString()}>
                    {v.group_name}
                  </Checkbox>
                ))}
              </Checkbox.Group>
            )}
          </Form.Item>
        )}
      </Form>
    );
  }
}

export default AppNavForm;
