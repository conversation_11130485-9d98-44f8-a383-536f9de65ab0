import React, { forwardRef, useRef, useState, useEffect } from 'react';
import {
  Form,
  DatePicker,
  Input,
  Icon,
  Tooltip,
  Radio,
  Col,
  Button,
  Modal,
  InputNumber,
  Popover,
  message,
  Popconfirm,
} from 'antd';

import ImageUploader from '@components/common/imageUploader';
import SearchAndInput from '@components/common/newNewsSearchAndInput';
import uuid from 'uuid';
import { A } from '@components/common';
import { opApi as api } from '@app/api';
import moment from 'moment';
import clonedeep from 'lodash/cloneDeep';

interface FormRef {
  validateFields: (param: (...arg: any) => void) => void;
  getFieldsValue?: () => any;
  setFieldsValue?: () => void;
}

interface ProportionObject {
  divisor: number;
  dividend: number;
}

export interface FingerPaperFormRef {
  submit: () => void;
}

const FingerPaperForm = forwardRef<
  FormRef,
  {
    form: any;
    getData: () => void;
    onEnd: () => void;
    save: (val: number) => void;
    btnStatus: any;
    formContent: any;
    drawer: any;
  }
>(({ form, getData, onEnd, formContent, drawer }, ref) => {
  let head_article_list = undefined;
  if (!!formContent && formContent.paper_page_style == 1) {
    for (let index = 0; index < formContent.page_list.length; index++) {
      const page = formContent.page_list[index];
      if (page.page_type == -1) {
        head_article_list = page.article_list.map((article: any) => {
          return {
            ...article,
            page_type: -1,
          };
        });
      }
    }
  }

  // 初始化页面数据
  const intPageList = () => {
    let arr = [];
    if (formContent?.page_list && formContent?.paper_page_style != 1) {
      arr = formContent.page_list.map(
        (
          item: {
            page_type: number;
            article_list: [];
            id: string;
          },
          index: number
        ) => {
          item.id = uuid();
          item.article_list.map(
            (cItem: { pid: number; page_type: number; cItem: { pid: number } }) => {
              cItem.page_type = item.page_type;
              cItem.pid = index;
            }
          );
          return item;
        }
      );
    }
    return arr;
  };

  const intFlatPageList = () => {
    let arr = [];
    if (formContent?.page_list && formContent?.paper_page_style == 1) {
      arr = formContent.page_list
        .filter((item: any) => item.page_type != -1)
        .map(
          (
            item: {
              article_list: [];
              id: string;
            },
            index: number
          ) => {
            item.id = uuid();
            item.article_list.map((article: any) => {
              article.page_type = -2;
              article.pid = index;
            });
            return item;
          }
        );
    }
    return arr;
  };
  const [page_list, setPageList] = useState([]);
  const [flat_page_list, setFlatPageList] = useState([]);
  useEffect(() => {
    if (!page_list || page_list.length == 0) {
      setPageList(intPageList());
    }
    if (!flat_page_list || flat_page_list.length == 0) {
      setFlatPageList(intFlatPageList());
    }
  }, []);

  const [recordInfo, setRecordInfo] = useState({
    visible: false,
    titleName: '',
    key: '',
    pid: '',
    index: '',
    value: '',
    sizeMax: 40,
    page_type: undefined,
  });
  const proportionHandle = (
    record: {
      page_type: number;
      [key: string]: any;
    },
    index: number
  ): ProportionObject => {
    let proportionObject: ProportionObject;
    switch (record.page_type) {
      case 5:
        proportionObject = { divisor: 375, dividend: 812 };
        break;
      case 4:
        proportionObject = { divisor: 16, dividend: 9 };
        break;
      case 1:
        if (index === 0) {
          proportionObject = { divisor: 15, dividend: 18 };
        } else {
          proportionObject = { divisor: 3, dividend: 4 };
        }
        break;
      case 3:
        proportionObject = { divisor: 3, dividend: 4 };
        break;
      case -1:
        // 头图稿件
        proportionObject = { divisor: 3, dividend: 4 };
        break;
      case -2:
        // 平铺稿件
        proportionObject = { divisor: 16, dividend: 9 };
        break;
      default:
        console.log('${record.page_type}', record.page_type);
        throw new Error(`Unsupported template type: ${record.page_type}`);
    }
    return proportionObject;
  };
  // 获取图片比例
  const getImageRatio = async (imageUrl) => {
    return new Promise((resolve, reject) => {
      const img = new Image();

      img.onload = () => {
        const width = img.width;
        const height = img.height;
        const ratio = width / height;
        resolve(ratio);
      };

      img.onerror = () => {
        resolve(2);
      };

      img.src = imageUrl;
    });
  };
  const editPic = async (url: string, record: object, index: number) => {
    let pic: string = url;
    let modal: any;
    const { divisor: w, dividend: h } = proportionHandle(record, index);
    let proportion = (w / h).toFixed(2);
    let consistentProportions = true;
    if (url) {
      const afterCalculation = await getImageRatio(url);
      if (Math.abs(afterCalculation - proportion) <= 0.05) {
        consistentProportions = false;
      } else {
        consistentProportions = true;
      }
    }

    const picChange = (u: string) => {
      pic = u;
      modal.update({
        content: (
          <>
            <ImageUploader
              value={pic}
              onChange={picChange}
              ratio={
                proportionHandle(record, index).divisor / proportionHandle(record, index).dividend
              }
            />
            <p>
              {' '}
              比例{proportionHandle(record, index).divisor}:
              {proportionHandle(record, index).dividend}
            </p>
          </>
        ),
      });
    };
    modal = Modal.confirm({
      title: '编辑图片',
      width: 500,
      content: (
        <>
          <ImageUploader
            value={pic}
            onChange={picChange}
            imgMaxWidth={400}
            isCutting={proportionHandle(record, index).divisor === 375}
            ratio={w / h}
          />
          {consistentProportions && record.page_type !== 5 && url ? (
            <span style={{ color: 'red' }}>
              该图片比例非{w}:{h}，图片将自动居中截取，建议重新上传。
            </span>
          ) : (
            <p>
              {record.page_type === 5 && '建议'}比例{w}:{h}
            </p>
          )}
        </>
      ),
      onOk: async (destroy: Function) => {
        if (record.page_type != -2 && !pic) {
          message.error('请上传图片');
          return;
        }
        if (record.page_type == -1) {
          // 平铺头图
          const article_list = getFieldsValue().head_article_list;
          article_list[0].article_pic = pic;
          setFieldsValue({ head_article_list: article_list });
          destroy();
          return;
        }

        if (record.page_type == -2) {
          const pageList = getFieldsValue().flat_page_list;
          pageList[record.pid].article_list[index].article_pic = pic;
          setFieldsValue({ flat_page_list: pageList });
          destroy();
          return;
        }

        const pageList = getFieldsValue().page_list;
        pageList[record.pid].article_list[index].article_pic = pic;
        setFieldsValue({ page_list: pageList });
        destroy();
      },
    });
  };
  // 编辑
  const handleOperate = (key, value, pid, index) => {
    setRecordInfo({
      visible: true,
      titleName: key === 'doc_title' ? '自定义标题' : '摘要',
      sizeMax: key === 'doc_title' ? 40 : 60,
      pid,
      index,
      key,
      value,
      page_type: undefined,
    });
  };

  const handleOperateFlat = (key, value, pid, index, page_type) => {
    setRecordInfo({
      visible: true,
      titleName: key === 'doc_title' ? '自定义标题' : '摘要',
      sizeMax: key === 'doc_title' ? 40 : 100,
      pid,
      index,
      key,
      value,
      page_type,
    });
  };

  const infoSubmit = () => {
    if (recordInfo.page_type == -1) {
      const article_list = getFieldsValue().head_article_list;
      const { pid, index, key, value } = recordInfo;
      article_list[0][key] = value;
      setFieldsValue({ head_article_list: article_list });
      setRecordInfo({
        visible: false,
      });
      return;
    }
    if (recordInfo.page_type == -2) {
      const pageList = getFieldsValue().flat_page_list;
      const { pid, index, key, value } = recordInfo;
      pageList[pid].article_list[index][key] = value;
      setFieldsValue({ flat_page_list: pageList });
      setRecordInfo({
        visible: false,
      });
      return;
    }

    if (!recordInfo.value) return message.error('请填写内容');
    const pageList = getFieldsValue().page_list;
    const { pid, index, key, value } = recordInfo;
    pageList[pid].article_list[index][key] = value;
    setFieldsValue({ page_list: pageList });
    setRecordInfo({
      visible: false,
    });
  };
  const columns = [
    { title: 'ID', dataIndex: 'id', key: 'id', width: 80 },
    { title: '新闻频道', dataIndex: 'channel_name', key: 'channel_name', width: 80 },
    {
      title: '新闻标题',
      dataIndex: 'list_title',
      key: 'list_title',
      width: 100,
      render: (text: string, record: any, index: number) => (
        <div style={{ display: 'flex', flexDirection: 'row', alignItems: 'center' }}>
          <span
            style={{
              display: '-webkit-box',
              overflow: 'hidden',
              textOverflow: 'ellipsis',
              WebkitLineClamp: 5,
              WebkitBoxOrient: 'vertical',
              maxWidth: '80px',
            }}
            title={text}
          >
            {text}
          </span>
        </div>
      ),
    },
    {
      title: '自定义标题',
      dataIndex: 'doc_title',
      key: 'doc_title',
      width: 120,
      render: (text: string, record: any, index: number) => (
        <div
          style={{
            display: 'flex',
            flexDirection: 'row',
            alignItems: 'center',
            justifyContent: 'space-between',
          }}
        >
          <span
            style={{
              display: '-webkit-box',
              overflow: 'hidden',
              textOverflow: 'ellipsis',
              WebkitLineClamp: 5,
              WebkitBoxOrient: 'vertical',
              maxWidth: '76px',
            }}
            title={text}
          >
            {text}
          </span>
          <A onClick={() => handleOperate('doc_title', text, record.pid, index)}>编辑</A>
        </div>
      ),
    },
    {
      title: '图片比例',
      dataIndex: 'page_type',
      key: 'page_type',
      render: (text: string, record: any, index: number) => (
        <>
          {proportionHandle(record, index).divisor} : {proportionHandle(record, index).dividend}
        </>
      ),
      width: 80,
    },
    {
      title: '列表图',
      dataIndex: 'article_pic',
      key: 'article_pic',
      width: 114,
      render: (text: string, record: any, index: number) => (
        <div style={{ display: 'flex', justifyContent: 'space-around', alignItems: 'center' }}>
          {text && <img src={text} style={{ height: 40, maxWidth: 70 }} />}
          <A onClick={() => editPic(text, record, index)}>{text ? '修改' : '上传'}</A>
        </div>
      ),
    },
    {
      title: '摘要',
      dataIndex: 'summary',
      key: 'summary',
      width: 60,
      render: (text: string, record: any, index: number) =>
        record.page_type != 5 && (record.page_type !== 1 || index !== 0) ? (
          <A onClick={() => handleOperate('summary', text, record.pid, index)}>
            {text ? '编辑' : '填写'}
          </A>
        ) : null,
    },
  ];
  const getFlatColumns = (scale: string) => {
    const flatColumns = [
      { title: 'ID', dataIndex: 'id', key: 'id', width: 80 },
      { title: '新闻频道', dataIndex: 'channel_name', key: 'channel_name', width: 80 },
      {
        title: '新闻标题',
        dataIndex: 'list_title',
        key: 'list_title',
        width: 100,
        render: (text: string, record: any, index: number) => (
          <div style={{ display: 'flex', flexDirection: 'row', alignItems: 'center' }}>
            <span
              style={{
                display: '-webkit-box',
                overflow: 'hidden',
                textOverflow: 'ellipsis',
                WebkitLineClamp: 5,
                WebkitBoxOrient: 'vertical',
                maxWidth: '80px',
              }}
              title={text}
            >
              {text}
            </span>
          </div>
        ),
      },
      {
        title: '自定义标题',
        dataIndex: 'doc_title',
        key: 'doc_title',
        width: 120,
        render: (text: string, record: any, index: number) => (
          <div
            style={{
              display: 'flex',
              flexDirection: 'row',
              alignItems: 'center',
              justifyContent: 'space-between',
            }}
          >
            <span
              style={{
                display: '-webkit-box',
                overflow: 'hidden',
                textOverflow: 'ellipsis',
                WebkitLineClamp: 5,
                WebkitBoxOrient: 'vertical',
                maxWidth: '76px',
              }}
              title={text}
            >
              {text}
            </span>
            <A
              onClick={() =>
                handleOperateFlat('doc_title', text, record.pid, index, record.page_type)
              }
            >
              编辑
            </A>
          </div>
        ),
      },
      {
        title: (
          <>
            <Tooltip title={`图片比例 ${scale}`}>列表图</Tooltip>
          </>
        ),
        dataIndex: 'article_pic',
        key: 'article_pic',
        align: 'center',
        width: 114,
        render: (text: string, record: any, index: number) => (
          <div style={{ display: 'flex', justifyContent: 'space-around', alignItems: 'center' }}>
            {text && <img src={text} style={{ height: 40, maxWidth: 70 }} />}
            <A onClick={() => editPic(text, record, index)}>{text ? '修改' : '上传'}</A>
          </div>
        ),
      },
      {
        title: '摘要',
        dataIndex: 'summary',
        key: 'summary',
        width: 60,
        render: (text: string, record: any, index: number) =>
          record.page_type != 5 && (record.page_type !== 1 || index !== 0) ? (
            <A
              onClick={() =>
                handleOperateFlat('summary', text, record.pid, index, record.page_type)
              }
            >
              {text ? '编辑' : '填写'}
            </A>
          ) : null,
      },
    ];
    return flatColumns;
  };

  const orderChange = (articleList: any) => {
    form.setFieldsValue({
      article_list: articleList.map((article: any, index: number) => {
        if (index > 0 || !article.picScale) {
          console.log('清空');
          article.picUrl = '';
          article.picScale = '16:9';
        }
        return article;
      }),
    });
  };
  useEffect(() => {
    const { page_list: arr } = getFieldsValue();

    if (arr) {
      setTimeout(() => {
        setFieldsValue({ page_list: page_list });
      }, 0);
    }
  }, [page_list]);

  useEffect(() => {
    const { flat_page_list: arr } = getFieldsValue();

    if (arr) {
      setTimeout(() => {
        setFieldsValue({ flat_page_list: flat_page_list });
      }, 0);
    }
  }, [flat_page_list]);

  const handleMove = (data: {}, index: number, type: number = 0) => {
    let position = index + 1;
    Modal.confirm({
      width: 250,
      content: (
        <>
          位置:{' '}
          <InputNumber
            min={1}
            max={type == 0 ? page_list.length : flat_page_list.length}
            defaultValue={position}
            onChange={(e: any) => {
              position = e;
            }}
          />
        </>
      ),
      icon: null,
      onOk() {
        if (type == 0) {
          const { page_list: arr } = getFieldsValue();
          let item = arr[index];
          let newArray = [...arr];
          newArray.splice(index, 1);
          newArray.splice(position - 1, 0, item);
          setPageList(newArray);
        } else {
          const { flat_page_list: arr } = getFieldsValue();
          let item = arr[index];
          let newArray = [...arr];
          newArray.splice(index, 1);
          newArray.splice(position - 1, 0, item);
          setFlatPageList(newArray);
        }
      },
      onCancel() {},
    });
  };
  const delectList = (id: number) => {
    const { page_list: arr } = getFieldsValue();
    const arrayConment = arr.filter((item: { id: number }) => {
      return item.id !== id;
    });
    setTimeout(() => {
      console.log(arrayConment);
      setFieldsValue({ page_list: arrayConment });
      console.log(getFieldsValue());
    }, 0);
    setPageList(arrayConment);
  };

  const delectFlatList = (id: number) => {
    const { flat_page_list: arr } = getFieldsValue();
    const arrayConment = arr.filter((item: { id: number }) => {
      return item.id !== id;
    });
    setFlatPageList(arrayConment);
  };

  const addItem = (type: number) => {
    const { page_list: articleList = [] } = getFieldsValue();
    const result = [
      ...articleList,
      {
        id: uuid(),
        list_title: '',
        page_type: type,
      },
    ];
    setPageList(result);
  };

  const addFlatItem = () => {
    const { flat_page_list: articleList = [] } = getFieldsValue();
    const result = [
      ...articleList,
      {
        id: uuid(),
        list_title: '',
        page_type: -2,
      },
    ];
    setFlatPageList(result);
  };

  const doSubmit = () => {
    form.validateFieldsAndScroll((err: any, values: any) => {
      if (!err) {
        const params = { ...values };
        if (params.paper_page_style == 0) {
          if (page_list && page_list.length === 0) return message.error('请添加页面');
        } else {
          if (flat_page_list && flat_page_list.length === 0) return message.error('请添加页面');
        }

        params.pub_date = params.pub_date.format('YYYYMMDD');
        if (params.paper_page_style == 0) {
          params.page_list = params.page_list.map((item) => {
            delete item.id;
            const oldArr = JSON.parse(JSON.stringify(item.article_list));
            item.article_list = oldArr.map(
              ({ id, list_title, article_pic, summary, page_type, doc_title }) => ({
                id,
                list_title,
                article_pic,
                summary,
                page_type,
                doc_title,
              })
            );
            return item;
          });
        } else {
          params.page_list = params.flat_page_list.map((item) => {
            delete item.id;
            delete item.page_type;
            const oldArr = JSON.parse(JSON.stringify(item.article_list));
            item.article_list = oldArr.map(
              ({ id, list_title, article_pic, summary, doc_title }) => ({
                id,
                list_title,
                article_pic,
                summary,
                doc_title,
              })
            );
            return item;
          });

          params.page_list.unshift({
            page_type: '-1',
            title: '',
            article_list: params.head_article_list?.map((article) => {
              return {
                id: article.id,
                list_title: article.list_title,
                article_pic: article.article_pic,
                summary: article.summary,
                doc_title: article.doc_title,
              };
            }),
          });

          delete params.head_article_list;
          delete params.flat_page_list;
        }
        if (params.paper_list_type != 1) {
          params.cover_image = '';
        }
        let func;
        if (drawer.type === 'edit') {
          func = 'updateReadPaper';
          params.auto_pk = formContent.auto_pk;
        } else {
          func = 'createReadPaper';
        }
        api[func as keyof typeof api](params).then(() => {
          message.success('操作成功');
          getData();
          onEnd();
        });
      }
    });
  };
  React.useImperativeHandle(ref, () => {
    return {
      doSubmit,
    };
  });
  const { getFieldDecorator, getFieldsValue, setFieldsValue } = form;
  const formRef = useRef<any>(null);
  return (
    <>
      <Form ref={formRef} labelCol={{ span: 3 }} wrapperCol={{ span: 19 }}>
        <Form.Item label="日期">
          {getFieldDecorator('pub_date', {
            initialValue: formContent ? moment(`${formContent.pub_date}`, 'YYYYMMDD') : '',
            rules: [
              {
                required: true,
                message: '请选择日期',
              },
            ],
          })(<DatePicker style={{ width: '100%' }} disabled={drawer.type === 'edit'} />)}
        </Form.Item>
        <Form.Item label="类型">
          {getFieldDecorator('time_type', {
            initialValue: formContent?.time_type,
            rules: [
              {
                required: true,
                message: '请选择类型',
              },
            ],
          })(
            <Radio.Group onChange={() => {}}>
              <Radio value={1}>早报</Radio>
              <Radio value={2}>晚报</Radio>
            </Radio.Group>
          )}
        </Form.Item>
        <Form.Item
          label={
            <>
              <Tooltip title={'标题显示在推荐位上'} placement="topLeft">
                <Icon type="question-circle" style={{ position: 'absolute', left: -20, top: 3 }} />
              </Tooltip>
              <span>列表标题</span>
            </>
          }
        >
          {getFieldDecorator('title', {
            initialValue: formContent?.title,
            rules: [
              {
                required: true,
                message: '请填写标题',
              },
            ],
          })(<Input maxLength={40} placeholder="最多输入40字" />)}
        </Form.Item>

        <Form.Item label="列表样式">
          {getFieldDecorator('paper_list_type', {
            initialValue: formContent?.paper_list_type || 1,
            rules: [
              {
                required: true,
                message: '请选择类型',
              },
            ],
          })(
            <Radio.Group onChange={() => {}}>
              <Radio value={1}>大图</Radio>
              <Radio value={2}>
                文字&nbsp;
                <Tooltip title={'选择文字样式时，将自动获取早晚报中稿件的自定义标题'}>
                  <Icon type="question-circle" />
                </Tooltip>
              </Radio>
              <Radio value={3}>
                图文&nbsp;
                <Tooltip title={'无需上传图片，系统根据日期和早/晚报自动展示图片'}>
                  <Icon type="question-circle" />
                </Tooltip>
              </Radio>
            </Radio.Group>
          )}
        </Form.Item>

        {form.getFieldValue('paper_list_type') == 1 && (
          <Form.Item
            label={
              <span style={{ position: 'relative' }}>
                <Tooltip title={'封面图显示在推荐位上'} placement="topLeft">
                  <Icon
                    type="question-circle"
                    style={{ position: 'absolute', left: -30, top: 3 }}
                  />
                </Tooltip>
                列表图
              </span>
            }
            extra="支持扩展名：.jpg .jpeg .png，比例 16:9"
          >
            {getFieldDecorator('cover_image', {
              initialValue: formContent?.cover_image || '',
              rules: [{ required: true, message: '请上传封面图' }],
            })(
              <ImageUploader
                ratio={16 / 9}
                imgsize={2048}
                accept={['image/png', 'image/jpeg', 'image/jpg']}
              />
            )}
          </Form.Item>
        )}

        <Form.Item label="页面样式">
          {getFieldDecorator('paper_page_style', {
            initialValue: formContent?.paper_page_style || 0,
            rules: [
              {
                required: true,
                message: '请选择类型',
              },
            ],
          })(
            <Radio.Group onChange={() => {}}>
              <Radio value={0}>折叠翻页</Radio>
              <Radio value={1}>平铺展示</Radio>
            </Radio.Group>
          )}
        </Form.Item>
        {form.getFieldValue('paper_page_style') == 0 && (
          <>
            {page_list.map(
              (item: { list_title: string; id: string; page_type: number }, index: number) => {
                return (
                  <div key={item.id + `${index}`}>
                    <Form.Item label="id" style={{ display: 'none' }}>
                      {getFieldDecorator(`page_list[${index}].id`, {
                        initialValue: item.id,
                      })(<Input disabled />)}
                    </Form.Item>
                    <Form.Item label="page_type," style={{ display: 'none' }}>
                      {getFieldDecorator(`page_list[${index}].page_type`, {
                        initialValue: item.page_type,
                      })(<Input disabled />)}
                    </Form.Item>
                    <Form.Item>
                      <h3 style={{ marginLeft: 50 }}>
                        页面{index + 1}
                        <img style={{ width: 50 }} src={`/assets/template${item.page_type}.png`} />
                      </h3>
                    </Form.Item>
                    <Form.Item label="页面标题">
                      {getFieldDecorator(`page_list[${index}].title`, {
                        initialValue: item.title,
                        validateTrigger: 'onChange',
                        rules: [
                          {
                            required: false,
                            message: '页面标题不能为空',
                          },
                        ],
                      })(<Input placeholder="最多输入4字" maxLength={4} />)}
                      {page_list.length > 1 && (
                        <div
                          style={{
                            display: 'flex',
                            position: 'absolute',
                            width: 60,
                            top: '-12px',
                            right: '-60px',
                          }}
                        >
                          <div
                            style={{
                              height: '40px',
                              lineHeight: '40px',
                              width: '40px',
                              textAlign: 'center',
                              cursor: 'pointer',
                            }}
                          >
                            <Icon
                              type="swap"
                              style={{ transform: 'rotate(90deg)' }}
                              onClick={() => {
                                handleMove(item, index);
                              }}
                            />
                          </div>

                          <div
                            onClick={() => {}}
                            style={{
                              height: '40px',
                              lineHeight: '40px',
                              width: '40px',
                              textAlign: 'center',
                              cursor: 'pointer',
                            }}
                          >
                            <Popconfirm
                              placement="top"
                              title="确定要删除吗？"
                              okText="确定"
                              cancelText="取消"
                              icon={
                                <Icon
                                  type="exclamation-circle"
                                  theme="twoTone"
                                  twoToneColor="red"
                                />
                              }
                              onConfirm={() => {
                                delectList(item.id);
                              }}
                            >
                              <Icon type="delete" />
                            </Popconfirm>
                          </div>
                        </div>
                      )}
                    </Form.Item>
                    <Form.Item
                      label={
                        <>
                          <Tooltip placement="left" title={'仅支持关联媒立方稿件'}>
                            <Icon
                              type="question-circle"
                              style={{ position: 'absolute', left: -20, top: 3 }}
                            />
                          </Tooltip>
                          关联稿件
                        </>
                      }
                    >
                      {getFieldDecorator(`page_list[${index}].article_list`, {
                        initialValue: item.article_list,
                        validateTrigger: 'onChange',
                        rules: [
                          {
                            required: true,
                            message: '请添加关联稿件',
                            type: 'array',
                          },
                          {
                            max: [0, 3, 0, 4, 2, 1][item.page_type],
                            message: `最多关联${[0, 3, 0, 4, 2, 1][item.page_type]}条新闻`,
                            type: 'array',
                          },
                          {
                            min: [0, 3, 0, 4, 2, 1][item.page_type],
                            message: `当前模板必须关联${[0, 3, 0, 4, 2, 1][item.page_type]}条稿件`,
                            type: 'array',
                          },
                          {
                            validator: (rule, val, callback) => {
                              let summaryStatus;
                              if (val) {
                                // val里的page_type 为1或者5 时 index为1 summary没有值返回true 有值返回true,
                                // val的page_type不为1或者5时summary 没有值返回false 有值返回true
                                console.log(val[0].page_type);
                                if (val[0].page_type == 1 || val[0].page_type == 5) {
                                  console.log('进了');
                                  if (
                                    val.filter((v, index) => !v.summary && index !== 0).length > 0
                                  ) {
                                    summaryStatus = false;
                                  } else {
                                    summaryStatus = true;
                                  }
                                } else {
                                  console.log(val.filter((v, index) => !v.summary));
                                  if (val.filter((v, index) => !v.summary).length > 0) {
                                    summaryStatus = false;
                                  } else {
                                    summaryStatus = true;
                                  }
                                }
                              }
                              if (!val) {
                                return callback('');
                              } else if (val.filter((v) => !v.doc_title).length > 0) {
                                return callback('请填写自定义标题');
                              } else if (!summaryStatus) {
                                return callback('请编辑并填写摘要');
                              } else if (val.filter((v) => !v.article_pic).length > 0) {
                                return callback('请上传列表图');
                              } else {
                                return callback();
                              }
                            },
                          },
                        ],
                      })(
                        <SearchAndInput
                          max={[0, 3, 0, 4, 2, 1][item.page_type]}
                          func="listArticleRecommendSearch"
                          columns={columns}
                          customOp={true}
                          placeholder="输入ID或标题关联稿件"
                          orderChange={orderChange}
                          body={{ doc_types: '2,3,4,5,8,9' }}
                          order={true}
                          isMedia={true}
                          map={(article) => {
                            const list_pics = article.list_pics.split(',')[0] || null;
                            // 使用list_pics后将其删除
                            delete article.list_pics;
                            return {
                              ...article,
                              article_pic: list_pics || null,
                              page_type: item.page_type,
                              doc_title: article.doc_title ? article.doc_title : article.list_title,
                              pid: index,
                            };
                          }}
                        />
                      )}
                    </Form.Item>
                  </div>
                );
              }
            )}
            {!formContent?.disabled && page_list.length < 10 && (
              <>
                <Form.Item>
                  <Col span={24}>
                    <Popover
                      content={
                        <div>
                          {/*addItem()*/}
                          <Radio.Group
                            value={null}
                            onChange={(e) => {
                              addItem(e.target.value);
                            }}
                            style={{ display: 'flex' }}
                          >
                            <Radio value={5} style={{ display: 'flex' }}>
                              <div style={{ marginTop: -2 }}>单条模板</div>
                              <img style={{ width: 100 }} src={'/assets/template5.png'} />
                            </Radio>
                            <Radio value={4} style={{ display: 'flex' }}>
                              <div style={{ marginTop: -2 }}>2条模板</div>
                              <img style={{ width: 100 }} src={'/assets/template4.png'} />
                            </Radio>
                            <Radio value={1} style={{ display: 'flex' }}>
                              <div style={{ marginTop: -2 }}>3条模板</div>
                              <img style={{ width: 100 }} src={'/assets/template1.png'} />
                            </Radio>
                            <Radio value={3} style={{ display: 'flex' }}>
                              <div style={{ marginTop: -2 }}>4条模板</div>
                              <img style={{ width: 100 }} src={'/assets/template3.png'} />
                            </Radio>
                          </Radio.Group>
                        </div>
                      }
                      placement="bottom"
                    >
                      <Button style={{ width: '100%', marginLeft: 100 }}>
                        {' '}
                        <Icon type="plus-circle-o" />
                        新建页面
                      </Button>
                    </Popover>
                  </Col>
                  <Col span={9} />
                </Form.Item>
              </>
            )}
          </>
        )}
        {form.getFieldValue('paper_page_style') == 1 && (
          <>
            <Form.Item>
              <h3 style={{ marginLeft: 50 }}>头图</h3>
            </Form.Item>
            <Form.Item
              label={
                <>
                  <Tooltip placement="left" title={'仅支持关联媒立方稿件'}>
                    <Icon
                      type="question-circle"
                      style={{ position: 'absolute', left: -20, top: 3 }}
                    />
                  </Tooltip>
                  关联稿件
                </>
              }
            >
              {getFieldDecorator(`head_article_list`, {
                initialValue: head_article_list,
                validateTrigger: 'onChange',
                rules: [
                  {
                    required: true,
                    message: '请添加关联稿件',
                    type: 'array',
                  },
                  {
                    max: 1,
                    message: `最多关联1条新闻`,
                    type: 'array',
                  },
                  {
                    min: 1,
                    message: `必须关联1条稿件`,
                    type: 'array',
                  },
                  {
                    validator: (rule, val, callback) => {
                      if (!val) {
                        return callback('');
                        //   } else if (val.filter((v) => !v.doc_title).length > 0) {
                        //     return callback('请填写自定义标题');
                      } else if (val.filter((v) => !v.summary).length > 0) {
                        return callback('请编辑并填写摘要');
                      } else if (val.filter((v) => !v.article_pic).length > 0) {
                        return callback('请上传列表图');
                      } else {
                        return callback();
                      }
                    },
                  },
                ],
              })(
                <SearchAndInput
                  max={1}
                  func="listArticleRecommendSearch"
                  columns={getFlatColumns('3:4')}
                  customOp={true}
                  placeholder="输入ID或标题关联稿件"
                  orderChange={orderChange}
                  body={{ doc_types: '2,3,4,5,8,9' }}
                  order={true}
                  isMedia={true}
                  map={(article) => {
                    const list_pics = article.list_pics.split(',')[0] || null;
                    // 使用list_pics后将其删除
                    delete article.list_pics;
                    return {
                      ...article,
                      article_pic: list_pics || null,
                      page_type: -1,
                      doc_title: article.doc_title ? article.doc_title : article.list_title,
                    };
                  }}
                />
              )}
            </Form.Item>

            {flat_page_list.map(
              (item: { list_title: string; id: string; page_type: number }, index: number) => {
                return (
                  <div key={item.id + `${index}`}>
                    <Form.Item label="id" style={{ display: 'none' }}>
                      {getFieldDecorator(`flat_page_list[${index}].id`, {
                        initialValue: item.id,
                      })(<Input disabled />)}
                    </Form.Item>
                    <Form.Item label="page_type," style={{ display: 'none' }}>
                      {getFieldDecorator(`flat_page_list[${index}].page_type`, {
                        initialValue: item.page_type,
                      })(<Input disabled />)}
                    </Form.Item>
                    <Form.Item>
                      <h3 style={{ marginLeft: 50 }}>分组{index + 1}</h3>
                    </Form.Item>
                    <Form.Item label="分组标题">
                      {getFieldDecorator(`flat_page_list[${index}].title`, {
                        initialValue: item.title,
                        validateTrigger: 'onChange',
                        rules: [
                          {
                            required: true,
                            message: '分组标题不能为空',
                          },
                        ],
                      })(<Input placeholder="最多输入4字" maxLength={4} />)}
                      {flat_page_list.length > 1 && (
                        <div
                          style={{
                            display: 'flex',
                            position: 'absolute',
                            width: 60,
                            top: '-12px',
                            right: '-60px',
                          }}
                        >
                          <div
                            style={{
                              height: '40px',
                              lineHeight: '40px',
                              width: '40px',
                              textAlign: 'center',
                              cursor: 'pointer',
                            }}
                          >
                            <Icon
                              type="swap"
                              style={{ transform: 'rotate(90deg)' }}
                              onClick={() => {
                                handleMove(item, index, 1);
                              }}
                            />
                          </div>

                          <div
                            onClick={() => {}}
                            style={{
                              height: '40px',
                              lineHeight: '40px',
                              width: '40px',
                              textAlign: 'center',
                              cursor: 'pointer',
                            }}
                          >
                            <Popconfirm
                              placement="top"
                              title="确定要删除吗？"
                              okText="确定"
                              cancelText="取消"
                              icon={
                                <Icon
                                  type="exclamation-circle"
                                  theme="twoTone"
                                  twoToneColor="red"
                                />
                              }
                              onConfirm={() => {
                                delectFlatList(item.id);
                              }}
                            >
                              <Icon type="delete" />
                            </Popconfirm>
                          </div>
                        </div>
                      )}
                    </Form.Item>
                    <Form.Item
                      label={
                        <>
                          <Tooltip placement="left" title={'仅支持关联媒立方稿件'}>
                            <Icon
                              type="question-circle"
                              style={{ position: 'absolute', left: -20, top: 3 }}
                            />
                          </Tooltip>
                          关联稿件
                        </>
                      }
                    >
                      {getFieldDecorator(`flat_page_list[${index}].article_list`, {
                        initialValue: item.article_list,
                        validateTrigger: 'onChange',
                        rules: [
                          {
                            required: true,
                            message: '请添加关联稿件',
                            type: 'array',
                          },
                          {
                            max: 4,
                            message: `最多关联4条新闻`,
                            type: 'array',
                          },
                          {
                            min: 1,
                            message: `最少关联1条稿件`,
                            type: 'array',
                          },
                          {
                            validator: (rule, val, callback) => {
                              if (!val) {
                                return callback('');
                                //   } else if (val.filter((v) => !v.doc_title).length > 0) {
                                //     return callback('请填写自定义标题');
                              } else if (val.filter((v) => !v.summary).length > 0) {
                                return callback('请编辑并填写摘要');
                                //   } else if (val.filter((v) => !v.article_pic).length > 0) {
                                //     return callback('请上传列表图');
                              } else {
                                return callback();
                              }
                            },
                          },
                        ],
                      })(
                        <SearchAndInput
                          max={4}
                          func="listArticleRecommendSearch"
                          columns={getFlatColumns('16:9')}
                          customOp={true}
                          placeholder="输入ID或标题关联稿件"
                          orderChange={orderChange}
                          body={{ doc_types: '2,3,4,5,8,9' }}
                          order={true}
                          isMedia={true}
                          map={(article) => {
                            const list_pics = article.list_pics.split(',')[0] || null;
                            // 使用list_pics后将其删除
                            delete article.list_pics;
                            return {
                              ...article,
                              article_pic: list_pics || null,
                              page_type: -2,
                              doc_title: article.doc_title ? article.doc_title : article.list_title,
                              pid: index,
                            };
                          }}
                        />
                      )}
                    </Form.Item>
                  </div>
                );
              }
            )}

            {!formContent?.disabled && flat_page_list.length < 5 && (
              <>
                <Form.Item>
                  <Col span={24}>
                    <Button
                      style={{ width: '100%', marginLeft: 100 }}
                      onClick={() => addFlatItem()}
                    >
                      {' '}
                      <Icon type="plus-circle-o" />
                      新建分组
                    </Button>
                  </Col>
                  <Col span={9} />
                </Form.Item>
              </>
            )}
          </>
        )}

        <p style={{ height: '40px', lineHeight: '40px', fontWeight: 600 }}>分享信息</p>
        <Form.Item label="分享标题">
          {getFieldDecorator('share_title', {
            initialValue: formContent?.share_title,
            rules: [
              {
                required: false,
                message: '请填写分享标题',
              },
            ],
          })(<Input placeholder="最多24字，如不填写，默认显示第一条内容标题" maxLength={24} />)}
        </Form.Item>
        <Form.Item label="分享副标题">
          {getFieldDecorator('share_subtitle', {
            initialValue: formContent?.share_subtitle,
            rules: [
              {
                required: false,
                message: '请填写分享副标题',
              },
            ],
          })(
            <Input placeholder="最多30字，如不填写，默认显示「来自读嘉客户端」" maxLength={30} />
          )}
        </Form.Item>
        <Form.Item
          label={
            <span style={{ position: 'relative' }}>
              <Tooltip
                placement="left"
                title={<img src="/assets/shareCard.png" style={{ width: 100 }} />}
              >
                <Icon type="question-circle" style={{ position: 'absolute', left: -30, top: 3 }} />
              </Tooltip>
              分享卡片
            </span>
          }
          extra="支持jpg、png等格式，比例为 259:404"
        >
          {getFieldDecorator('share_image', {
            initialValue: formContent?.share_image,
            rules: [
              {
                required: true,
                message: '请上传分享卡片',
              },
            ],
          })(
            <ImageUploader
              imgsize={2048}
              isCutting={false}
              ratio={259 / 404}
              accept={['image/jpeg', 'image/png', 'image/jpg']}
            />
          )}
        </Form.Item>
      </Form>
      <Modal
        visible={recordInfo.visible}
        onCancel={() => {
          setRecordInfo({ visible: false });
        }}
        onOk={() => {
          infoSubmit();
        }}
        width={650}
      >
        <p>
          <span style={{ color: 'red' }}>*</span> &nbsp;{recordInfo.titleName}
        </p>
        <Input.TextArea
          rows={4}
          value={recordInfo.value}
          maxLength={recordInfo.sizeMax}
          placeholder={`最多输入${recordInfo.sizeMax}字`}
          onChange={(e) => {
            setRecordInfo({
              ...recordInfo,
              value: e.target.value,
            });
          }}
        />
        <div style={{ textAlign: 'right' }}>{recordInfo.value?.length ?? 0}/{recordInfo.sizeMax}</div>
      </Modal>
    </>
  );
});

export default Form.create({})(FingerPaperForm);
