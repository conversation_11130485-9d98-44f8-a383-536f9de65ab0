import { opApi as api } from '@app/api';
import SearchAndInput from '@components/common/newsSearchAndInput';
import { Form, Input, message, InputNumber } from 'antd';
import React from 'react';
import _debounce from 'lodash/throttle';
import { setMLoading } from '@utils/utils';
import connectSession from '@utils/connectSession';

@connectSession
@(Form.create({ name: 'pushNotifyForm' }) as any)
class PushNotifyForm extends React.Component<any, any> {
  constructor(props: any) {
    super(props);
    const form = {
      name: '',
      duration: '',
      describe: '',
    };
    this.state = {
      ...form,
      ...props.formContent,
      areaList: [],
    };
  }

  handleSubmit = (e: any) => {
    e.preventDefault();
    this.doSubmit();
  };

  doSubmit = () => {
    this.props.form.validateFieldsAndScroll((err: any, values: any) => {
      if (!err) {
        setMLoading(this, true);
        const body = { ...values };
        console.log(body);
        let func = 'addLongVideo';
        api[func as keyof typeof api](body)
          .then(() => {
            message.success('操作成功');
            setMLoading(this, false);
            this.props.onEnd();
          })
          .catch(() => setMLoading(this, false));
      } else {
        console.log('111111', values);
        message.error('请检查表单内容');
      }
    });
  };
  limitNumber = () => {};

  render() {
    const { getFieldDecorator } = this.props.form;
    const formLayout = {
      labelCol: { span: 4 },
      wrapperCol: { span: 18 },
    };

    const values = this.props.form.getFieldsValue();

    return (
      <Form {...formLayout} onSubmit={this.handleSubmit}>
        <Form.Item label="权限组名称">
          {getFieldDecorator('name', {
            initialValue: this.state.name,
            rules: [
              {
                required: true,
                message: '请填写权限组名称',
              },
            ],
          })(<Input placeholder="填写权限组名称" maxLength={30} />)}
        </Form.Item>
        <Form.Item label="视频时长" extra="单位（秒）需要自行换算分钟">
          {getFieldDecorator('duration', {
            initialValue: this.state.duration,
            rules: [
              {
                required: true,
                message: '请填写视频时长',
              },
            ],
          })(<InputNumber max={600} min={181} />)}
        </Form.Item>
        <Form.Item label="权限组描述">
          {getFieldDecorator('describe', {
            initialValue: this.state.describe,
          })(<Input.TextArea rows={3} maxLength={100} placeholder="输入权限组描述" />)}
        </Form.Item>
      </Form>
    );
  }
}

export default PushNotifyForm;
