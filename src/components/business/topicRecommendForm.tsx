import { releaseListApi } from '@app/api';
import ImageUploader from '@components/common/imageUploader';
import SearchAndInput from '@components/common/newsSearchAndInput';
import {
  Form,
  Icon,
  Input,
  InputNumber,
  message,
  Radio,
  Row,
  Tooltip,
  Checkbox,
  Switch,
  Select,
  Spin,
  Table,
  Popover,
} from 'antd';
import React from 'react';
import { setMLoading } from '@utils/utils';
import connectSession from '@utils/connectSession';
import moment from 'moment';

@connectSession
@(Form.create({ name: 'topicRecommendForm' }) as any)
class TopicRecommendForm extends React.Component<any, any> {
  constructor(props: any) {
    super(props);
    console.log(props.formContent);
    this.state = { ...props.formContent };
  }

  handleSubmit = (e: any) => {
    e.preventDefault();
    this.doSubmit();
  };

  doSubmit = () => {
    this.props.form.validateFieldsAndScroll((err: any, values: any) => {
      if (!err) {
        setMLoading(this, true);
        let api: keyof typeof releaseListApi = 'createTopicRecommend';
        if (this.state.id) {
          api = 'updateTopicRecommend';
        }
        try {
          releaseListApi[api]({
            ...values,
            [this.state.id ? 'id' : 'channel_id']: this.state.id || this.props.channelId,
            channel_id: this.props.channelId,
            ref_ids: values.channel_article_ids.join(','),
            topic_type:'0'
          })
            .then(() => {
              setMLoading(this, false);
              message.success('操作成功');
              this.props.onEnd();
            })
            .catch(() => {
              setMLoading(this, false);
            });
        } catch (errr) {
          console.error(errr);
        }
      } else {
        message.error('请检查表单内容');
      }
    });
  };

  getColumns = () => {
    return [
      { title: '地方号名称', dataIndex: 'name' },
      { title: '创建人', dataIndex: 'created_user_name' },
      {
        title: '创建时间',
        dataIndex: 'created_at',
        render: (text: number) => moment(text).format('YYYY-MM-DD'),
      },
    ];
  };

  articlesChange = (data: any) => {
    this.setState({ ...data });
  };

  render() {
    const { getFieldDecorator } = this.props.form;
    const formLayout = {
      labelCol: { span: 4 },
      wrapperCol: { span: 18 },
    };
    const img = [
      <img
        src="https://model-tianmunews.oss-cn-hangzhou.aliyuncs.com/model/tmAdmin/1.png"
        alt=""
        height="100"
      />,
      <img
        src="https://model-tianmunews.oss-cn-hangzhou.aliyuncs.com/model/tmAdmin/2.png"
        alt=""
      />,
      <img
        src="https://model-tianmunews.oss-cn-hangzhou.aliyuncs.com/model/tmAdmin/3.png"
        alt=""
      />,
    ];
    const text = (
      <span>
        <p>输入说明：</p>
        <p>
          直接输入阿拉伯数字代表位置序号，例如要在头条频道信息流第8个位置插入该推荐位，则直接输入“8”即可。
        </p>
        <p>最大输入数字为500</p>
        <br />
        <p>注：</p>
        <p>直接在输入的指定位置上插入推荐位显示，不参与稿件的排序，且优先于媒立方稿件展示</p>
      </span>
    );
    return (
      <Form {...formLayout} onSubmit={this.handleSubmit}>
        <Form.Item label="名称">
          {getFieldDecorator('recommend_name', {
            initialValue: this.state.recommend_name,
            rules: [
              {
                required: true,
                message: '请填写名称',
              },
              {
                max: 30,
                message: '名称最长不能超过30个字',
              },
            ],
          })(<Input placeholder="仅用于后台区分" />)}
        </Form.Item>
        <Form.Item label="插入位置">
          {getFieldDecorator('position', {
            initialValue: this.state.position,
            rules: [
              {
                required: true,
                message: '请输入位置序号',
                type: 'number',
              },
              {
                max: 500,
                message: '最大不能超过500',
                type: 'number',
              },
            ],
          })(<InputNumber max={500} style={{ width: 200 }} placeholder="请输入位置序号" min={1} />)}
          &emsp;
          <Tooltip title={text}>
            <Icon type="question-circle" />
          </Tooltip>
        </Form.Item>
        <Form.Item label="循环轮播">
          {getFieldDecorator('cycle_carousel', {
            initialValue: this.state.cycle_carousel,
            valuePropName: 'checked',
          })(<Switch />)}
        </Form.Item>
        <Form.Item label="样式选择">
          {getFieldDecorator('column_style', {
            initialValue: this.state.column_style,
            rules: [
              {
                required: true,
                message: '请输选择样式',
                type: 'number',
              },
            ],
          })(
            <Radio.Group>
              <Radio value={1}>
                样式一
                <Popover content={img[0]} placement="top">
                  <Icon type="question-circle" />
                </Popover>
              </Radio>
              <Radio value={2}>
                样式二
                <Popover content={img[1]} placement="top">
                  <Icon type="question-circle" />
                </Popover>
              </Radio>
              <Radio value={3}>
                样式三
                <Popover content={img[2]} placement="top">
                  <Icon type="question-circle" />
                </Popover>
              </Radio>
            </Radio.Group>
          )}
        </Form.Item>

        <Form.Item label="关联地方号" required={true}>
          {getFieldDecorator('channel_article_ids', {
            initialValue: this.state.channel_article_ids,
            rules: [
              {
                required: true,
                message: '请关联地方号',
                type: 'array',
              },
              {
                max: 30,
                message: '最多关联30条话题',
                type: 'array',
              },
            ],
          })(
            <SearchAndInput
              max={30}
              func="searchUGCTopic"
              columns={this.getColumns()}
              placeholder="输入名称搜索地方号"
              initialValues={{ list: this.state.channelArticles }}
              triggerInitialValueChange={this.articlesChange}
              body={{ type: 0 }}
              order={true}
              addOnTop={true}
              searchKey="name"
              funcIndex="list"
              apiWithPagination={true}
              selectOptionDisplay={(record: any) => `#${record.name}#`}
            />
          )}
        </Form.Item>
      </Form>
    );
  }
}

export default TopicRecommendForm;
