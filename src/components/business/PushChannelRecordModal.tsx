import React from "react";
import { listApi, releaseListApi } from '@app/api';
import moment from 'moment';
import {
  Button,
  Modal,
  Table as ATable,
  Spin,
  message
} from 'antd';
import connect from '@utils/connectSession';
import { requirePerm, searchToObject } from '@utils/utils';

@connect
class PushChannelRecordModal extends React.Component<any, any> {
  constructor(props: any) {
    super(props)
    this.state = {
      loading: false,
      list: [],
    }
  }

  componentDidUpdate(prevProps: Readonly<any>, prevState: Readonly<any>, snapshot?: any): void {
    if (!prevProps.visible && this.props.visible) {
      this.setState({ list: [] })
    }
    if (prevProps.record !== this.props.record && this.props.record) {
      // 切换了稿件，请求稿件推频道信息
      const { id: article_id } = this.props.record
      if (!article_id) {
        return
      }
      this.loadList(article_id)
    }
  }

  loadList(article_id: any) {
    this.setState({ loading: true });
    listApi.getToChannelList({ article_id }).then((r: any) => {
      // 这里返回的频道列表都是推过的，不能二次推
      const { data: { article, list = [] } } = r
      if (article.id === article_id) {
        this.setState({ loading: false, list });
      } else {
        this.setState({ loading: false });
      }
    }).catch(() => {
      this.setState({ loading: false });
    })
  }

  handleCancelPub(record: any) {
    releaseListApi
      .revokeNews({ id: record.id, to_channel_id: record.channel_id })
      .then(() => {
        message.success('操作成功');
        const { id: article_id } = this.props.record
        this.loadList(article_id)
      })
      .catch(() => { });
  }

  handleCancelAllPub() {
    const { id } = this.props.record
    releaseListApi
      .revokeAllNews({ id })
      .then(() => {
        message.success('操作成功');
        const { id: article_id } = this.props.record
        this.loadList(article_id)
      })
      .catch(() => { });
  }

  render() {
    const { visible, onCancel, skey, record } = this.props
    const channel_id = searchToObject().channel_id
    const that = this
    const columns: any = [
      {
        title: '稿件标题',
        key: 'list_title',
        dataIndex: 'list_title',
      },
      {
        title: '天目蓝云ID',
        key: 'metadata_id',
        dataIndex: 'metadata_id',
        width: 100,
        align: 'center',
      },
      {
        title: 'ID',
        key: 'id',
        dataIndex: 'id',
        width: 80,
        align: 'center',
      },
      {
        title: '展示频道',
        key: 'channel_name',
        dataIndex: 'channel_name',
        width: 100,
        align: 'center',
      },
      {
        title: '状态',
        key: 'pushed',
        dataIndex: 'pushed',
        render(pushed: number) {
          return pushed === 2 ? '已使用' : '未使用'
        },
        width: 80,
        align: 'center',
      },
      {
        title: '是否隐藏',
        key: 'visible',
        dataIndex: 'visible',
        render(visible: boolean | undefined) {
          if (typeof visible === 'undefined') {
            return ''
          }
          return visible ? '否' : '是'
        },
        width: 80,
        align: 'center',
      },
      {
        title: '签发时间',
        key: 'published_at',
        dataIndex: 'published_at',
        render: (text: any, record: any) => (
          text && <span>{moment(text).format('YYYY-MM-DD HH:mm:ss')}</span>
        ),
        width: 95,
        align: 'center',
      },
      {
        title: '操作',
        key: 'op',
        width: 110,
        align: 'center',
        render(text: any, record: any) {
          return record.pushed === 2 ?
            requirePerm(that, `channel_article:${channel_id}:cancel_release`)(<Button type="link" onClick={() => that.handleCancelPub(record)}>取消签发</Button>) :
            null
        }
      }
    ]
    const isAllUnused = !this.state.list.some((item: any) => item.pushed === 2)
    return <Modal
      visible={visible}
      title="推至频道签发记录"
      width="870px"
      footer={isAllUnused ? null : [requirePerm(that, `channel_article:${channel_id}:cancel_release`)(<Button key="submit" type="primary" onClick={() => this.handleCancelAllPub()}>全部取消签发</Button>)]}
      onCancel={onCancel}
      key={skey}>
      <Spin
        tip="正在加载..."
        spinning={this.state.loading}
      >
        <div style={{ marginBottom: '15px' }}>源稿件ID<span style={{ marginLeft: '8px' }}>{record.id}</span></div>
        <ATable dataSource={this.state.list} pagination={false} columns={columns} rowKey="id" />
      </Spin>
    </Modal>
  }
}


export default PushChannelRecordModal