import React, { useState, useEffect } from 'react';
import { Form, Input, Button, Icon, Select, Modal, Row, Col, message, Tooltip } from 'antd';
import connectSession from '@utils/connectSession';
import SearchAndInput from '@components/common/newNewsSearchAndInput';
import { userApi as api } from '@app/api';
import { cloneDeep } from 'lodash';
import { setMLoading } from '@utils/utils';
import 'emoji-mart/css/emoji-mart.css';
import { Picker } from 'emoji-mart';

// const { Title } = Typography;
const count = 0;
const { confirm } = Modal;
@connectSession
@(Form.create({ name: 'CommentTipsForm' }) as any)
class CommentTipsForm extends React.Component<any, any> {
  constructor(props: any) {
    super(props);
    const { formContent } = props;
    this.state = {
      ...props,
      pickerConfig: {
        Show: false,
        top: '0px',
        right: '300px',
      },
      form: {
        id: undefined,
        channelArticles: [],
        content: '',
      },
    };
    if (formContent.id) {
      this.getInfo();
    }
  }

  componentDidMount() {
    const { pickerConfig } = this.state;
    // document.onclick = (e) => {
      // this.setState({
      //   pickerConfig: {
      //     ...pickerConfig,
      //     Show: false,
      //   },
      // });
    // };
  }

  getInfo = () => {
    const { formContent } = this.state;
    api.getInfoCommentTips({ id: formContent.id }).then((res: any) => {
      const ary: any = [];
      const channelArticles = res.data.comment_tips_infos;
      channelArticles.forEach((x: any) => {
        ary.push({
          tipId: x.id,
          id: x.channel_article_id,
          channel_name: x.channel_name,
          list_title: x.channel_article_title,
        });
      });

      this.setState({
        form: {
          plan_name: formContent.plan_name,
          channelArticles: ary,
          content: formContent.content,
        },
      });
    });
  };

  doSubmit = () => {
    const { formContent } = this.state;
    if (formContent.disabled) return this.props.onClose();
    this.props.form.validateFieldsAndScroll((err: any, values: any) => {
      if (!err) {
        const data: any = {};
        if (!formContent.types) {
          data.draft_info = [];
          values.channelArticles.forEach((x: any) => {
            data.draft_info.push({
              channelArticleId: x.id,
              channelArticleTitle: x.list_title,
            });
          });
          data.draft_info = JSON.stringify(data.draft_info);
        }
        data.plan_name = values.plan_name;
        data.content = values.content;
        setMLoading(this, true);
        if (formContent.comment_tips_id) {
          data.comment_tips_id = formContent.comment_tips_id;
          api
            .updateCommentTips(data)
            .then((r) => {
              message.success('修改成功');
              setMLoading(this, false);
              this.props.onEnd();
            })
            .catch(() => {
              setMLoading(this, false);
            });
        } else {
          api
            .createCommentTips(data)
            .then((r) => {
              message.success('创建成功');
              setMLoading(this, false);
              this.props.onEnd();
            })
            .catch(() => {
              setMLoading(this, false);
            });
        }
      } else {
        message.error('请检查表单内容');
      }
    });
    return true;
  };

  handleSubmit = (e: any) => {
    e.preventDefault();
    this.doSubmit();
  };

  getColumn = () => {
    return [
      {
        title: '序号',
        dataIndex: 'eq',
        width: 70,
        render: (_: any, v: any, i: number) => i + 1,
      },
      {
        title: '稿件ID',
        dataIndex: 'id',
        width: 80,
      },
      {
        title: '新闻频道',
        key: 'type',
        dataIndex: 'channel_name',
        width: 90,
      },
      {
        title: '新闻标题',
        key: 'list_title',
        dataIndex: 'list_title',
      },
    ];
  };

  PickerEmoji = (e: any) => {
    e.nativeEvent.stopImmediatePropagation();
    const { top } = e.target.getBoundingClientRect();
    const { pickerConfig } = this.state;
    this.setState({
      pickerConfig: { ...pickerConfig, top: top - 50, show: !pickerConfig.show },
    });
  };

  addEmoji = (emoji: any) => {
    const { form } = this.state;
    const text = form.content + emoji.native;
    this.setState({
      form: { ...form, content: text },
    });

    this.props.form.setFieldsValue({
      content: text,
    });
  };

  inputChange = (e: any) => {
    const { form } = this.state;
    this.setState({
      form: { ...form, content: e.target.value },
    });
  };

  // 删除关联稿件
  searchAndInputOnChange = (e: any, deleteObj: any) => {
    if (deleteObj && Object.keys(deleteObj).length > 0) {
      if (deleteObj.tipId) {
        const that = this;
        console.log(deleteObj.tipId);
        confirm({
          title: '确认删除吗？',
          onOk() {
            setMLoading(that, true);
            api
              .deleteOneCommentTips({ id: deleteObj.tipId })
              .then((res) => {
                if (e.length <= 0) {
                  that.props.onEnd();
                }
                message.success('删除成功');
                deleteObj.callBack();
                setMLoading(that, false);
              })
              .catch(() => {
                setMLoading(that, false);
              });
          },
          onCancel() {},
        });
      } else {
        deleteObj.callBack();
      }
    }
  };

  render() {
    const that = this;
    const formLayout = {
      labelCol: { span: 4 },
      wrapperCol: { span: 18 },
    };
    const { getFieldDecorator } = this.props.form;
    const { form, formContent, pickerConfig } = this.state;
    const text2 = <p>最多关联15条。</p>;

    // 验证 稿件是否被关联过
    const validatorFn = async (val: any) => {
      const res: any = await api.getChannelArticleIdExistsCommentTips({
        channel_article_id: val.id,
      });
      if (res.data.channel_article_id_exists) {
        return '此稿件已配置提示语，请移除后继续配置';
      }
      return false;
    };

    return (
      <>
        <Form {...formLayout} onSubmit={this.handleSubmit} style={{ position: 'relative' }}>
          <Form.Item label="方案名">
            <Col span={14}>
              {getFieldDecorator('plan_name', {
                initialValue: form.plan_name,
                rules: [
                  {
                    required: true,
                    message: '方案名不能为空',
                  },
                  {
                    max: 15,
                    message: '最多15个字',
                  },
                ],
              })(
                <Input
                  disabled={formContent.disabled}
                  placeholder="请输入提示语方案名 至多15个汉字"
                />
              )}
            </Col>
          </Form.Item>
          {!Number(formContent.types) && (
            <Form.Item label="关联稿件">
              {getFieldDecorator('channelArticles', {
                initialValue: form.channelArticles,
                preserve: false,
                rules: [
                  {
                    required: true,
                    message: '请关联新闻',
                    type: 'array',
                  },
                  {
                    max: 15,
                    message: '最多关联15条新闻',
                    type: 'array',
                  },
                  {
                    min: 1,
                    message: '为保证客户端显示效果，关联新闻数不能少于1条！',
                    type: 'array',
                  },
                ],
              })(
                <SearchAndInput
                  max={15}
                  disabled={formContent.disabled}
                  func="conmentSearch"
                  columns={this.getColumn()}
                  placeholder="输入ID或标题关联稿件"
                  body={{ doc_types: '8,9' }}
                  order={false}
                  addOnTop={true}
                  validator={validatorFn}
                  onChange={this.searchAndInputOnChange}
                  onChangeCallback={true}
                  afix={
                    <Tooltip title={text2}>
                      <Icon type="question-circle" />
                    </Tooltip>
                  }
                />
              )}
            </Form.Item>
          )}
          <Form.Item label="提示语">
            <Col span={14}>
              {getFieldDecorator(`content`, {
                initialValue: form.content,
                rules: [
                  {
                    required: true,
                    message: '名称不能为空',
                  },
                  {
                    max: 100,
                    message: '最多100个字',
                  },
                ],
              })(
                <Input
                  disabled={formContent.disabled}
                  placeholder="请输入提示语 至多100个汉字"
                  onChange={(e) => this.inputChange(e)}
                  addonAfter={
                    <Icon
                      onClick={(e) => {
                        if (!formContent.disabled) {
                          this.PickerEmoji(e);
                        }
                      }}
                      type="smile"
                    />
                  }
                />
              )}
            </Col>
          </Form.Item>

          {pickerConfig.show && (
            <Picker
              onSelect={this.addEmoji}
              title="表情包"
              native={false}
              showPreview={false}
              showSkinTones={false}
              emojiTooltip={true}
              style={{ position: 'absolute', top: pickerConfig.top, right: pickerConfig.right }}
              i18n={{
                search: '搜索表情符号',
                categories: {
                  search: '搜索结果',
                  recent: '常用',
                  people: '标签符号与人物',
                  nature: '动物与自然',
                  foods: '食物与饮料',
                  activity: '活动',
                  places: '旅行与地点',
                  objects: '物体',
                  symbols: '符号',
                  flags: '旗帜',
                  custom: '习俗',
                },
              }}
            />
          )}
        </Form>
      </>
    );
  }
}

export default CommentTipsForm;
