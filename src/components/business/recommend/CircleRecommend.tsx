import React from 'react';
import AddForm from '@components/business/addForm';
import { releaseListApi } from '@app/api';
import { searchToObject } from '@app/utils/utils';

export default class CircleRecommend extends React.Component<any> {
  state = {
    bannerData: [
      { reason: '', name: '' },
      { reason: '', name: '' },
    ],
    bannerObj: { title: '', position: '', round_carousel: true, style: 0 },
    id: undefined,
  };

  constructor(props: any) {
    super(props);
    this.props.wrappedComponentRef.current = this;
  }

  setFormItem = () => {
    return {
      name: '',
      reason: '',
    };
  };

  getUpdateDate = (values: any) => {
    const data: any = {
      title: values.title,
      article_category: 1,
      ref_type: 30,
      channel_id: searchToObject().channel_id,
      ref_extensions: JSON.stringify(
        values.json.map((item: any) => ({
          circle_id: item.id,
        }))
      ),
      id: this.state.id,
      round_carousel: values.round_carousel,
      style: values.style,
    };
    if (values.position) {
      data.position = values.position;
    }
    if (!data.id) {
      delete data.id;
    }
    return data;
  };

  setEditData = (data: any) => {
    if (data) {
      const { recommend, circle_list } = data;
      this.setState(
        {
          id: recommend.id,
          bannerData: circle_list.map((item: any) => ({
            name: item.name,
            reason: item.reason,
            id: item.id,
          })),
          bannerObj: {
            position: recommend.position,
            title: recommend.title,
            round_carousel: recommend.cycle_carousel,
            style: recommend.style,
          },
        },
        () => {
          this.props.wrappedComponentRef.current.init();
        }
      );
    } else {
      this.setState({
        editData: null,
      });
    }
  };

  render() {
    const { isEdit, maxPosition } = this.props;
    return !isEdit || this.state.id ? (
      <AddForm
        isEdit={isEdit}
        maxPosition={maxPosition}
        setEditData={this.setEditData}
        bannerData={this.state.bannerData} //复现数据
        bannerObj={this.state.bannerObj} //复现数据
        changeVisible={this.props.onEnd}
        showLoading={this.props.showLoading}
        wrappedComponentRef={(instance: any) => (this.props.wrappedComponentRef.current = instance)}
        getUpdateDate={this.getUpdateDate} //获取要提交的数据
        setFormItem={this.setFormItem()} //设置表单item
        joggle={
          isEdit ? releaseListApi.updateContentRecommend : releaseListApi.createContentRecommend
        } // 接口
        componentNames={'圈子推荐位'} //判断使用的组件
        addLabel={'添加一个圈子'}
        headName={'圈子'}
        listLength={20}
        disabled={2}
      />
    ) : null;
  }
}
