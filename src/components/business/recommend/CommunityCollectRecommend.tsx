import React from "react";
import { Form, Icon, Input, Tooltip, Switch, Select, Modal, message } from 'antd';
import RecommendCommon, { RecommendFormLayout } from "./RecommendCommon";
import Radio from "antd/es/radio";
import SearchAndInput from '@components/common/newNewsSearchAndInput';
import { communityApi as api } from "@app/api";
import _ from "lodash";
import { A, ImageUploader } from "@app/components/common";

// 帖子汇总推荐位
@(Form.create({ name: 'CommunityCollectRecommend' }) as any)
export default class CommunityCollectRecommend extends React.Component<any> {

  state: any = {
    editData: null,
    hasJump: false,
    circleOptions: []
  }

  specialValidateFields = () => {
    const { form: { getFieldsValue } } = this.props
    const { ref_ids_type } = getFieldsValue();
    const fields = ['title', 'title_style', 'title_pic', 'position', 'style', 'round_carousel', 'ref_ids_type']
    if (this.state.hasJump) {
      fields.push('jump_model_url')
    }
    fields.push(ref_ids_type === 0 ? 'channelArticles' : 'circle_info')
    return fields
  }

  customValidator = (values: any) => {
    const { style, channelArticles, ref_ids_type } = values
    if (style === 42 && ref_ids_type === 0 && channelArticles.filter((v: any) => !v.picUrl).length > 0) {
      message.error('请上传全部稿件的图片');
      return false;
    }
    return true
  }

  saveDataTransfer = (values: any) => {
    // console.log(values, this.state.hasJump)
    const { title, title_style, title_pic, position, channelArticles, ref_ids_type, style, round_carousel, jump_model_url, circle_info } = values
    const title_show = title_style >= 0
    const data: any = {
      article_category: this.props.isCommunity ? 1 : 0,
      ref_type: 27,
      jump_enabled: this.state.hasJump,
      title,
      title_show,
      title_pic: title_style >= 1 ? title_pic : '',
      ref_ids_type,
      style,
      round_carousel
    }
    if (position) {
      data.position = position
    }
    if (this.state.hasJump) {
      data.jump_model_url = jump_model_url
    }
    if (title_show) {
      data.title_style = title_style
    }
    if (ref_ids_type === 0) {
      data.ref_ids = channelArticles.map((item: any) => item.uuid || item.id).join(',')
      if (style === 42) {
        data.ref_extensions = JSON.stringify(channelArticles.map((item: any) => ({ article_id: item.uuid || item.id, list_pic: item.picUrl })))
      }
    } else {
      const circleId = circle_info.split('-')[1]
      data.ref_ids = circleId || this.state.editData.circle?.id || ''
    }
    return data
  }

  setEditData = (data: any) => {
    if (data) {
      const {
        circle = {},
        article_list = [],
        recommend:
        { title,
          show_title,
          title_style = 0,
          title_pic = '',
          position,
          style,
          jump_new_page,
          jump_url,
          cycle_carousel,
          nav_type,
          ref_ids_type = 0,
          ref_extensions,
        } } = data
      const extensions = ref_extensions ? JSON.parse(ref_extensions) : []
      this.setState({
        hasJump: jump_new_page,
        editData: {
          title,
          title_style: show_title ? title_style : -1,
          title_pic,
          position,
          style,
          nav_type,
          jump_model_url: jump_url,
          ref_ids_type,
          circle_info: ref_ids_type === 0 ? undefined : circle.name,
          circle,
          round_carousel: cycle_carousel,
          channelArticles: article_list.map((v: any) => {
            const id = v.uuid || v.id
            const data: any = {
              circle_name: v.articles.circle_name,
              list_title: v.list_title,
              id,
              uuid: v.uuid,
            }
            const picArticle = extensions.find((item: any) => item.article_id == id)
            if (picArticle) {
              data.picUrl = picArticle.list_pic
            }
            return data
          })
        },
      })
    } else {
      this.setState({
        editData: null
      })
    }
  }

  handleCircleSearch = _.debounce((circleName: any) => {
    const { form: { getFieldsValue, setFieldsValue } } = this.props
    const { circle_info } = getFieldsValue()
    let selectedCircleName = ''
    if (circle_info) {
      selectedCircleName = circle_info.split('-')[0]
    }
    if (selectedCircleName.indexOf(circleName) < 0) {
      setFieldsValue({ circle_info: undefined })
      this.setState({ circleOptions: [] })
    }
    if (!circleName) {
      return
    }
    api.getCircleList({ current: 1, size: 100, enabled: true, keyword: circleName })
      .then((res) => {
        const { data: { list: { records } } } = res as any
        this.setState({ circleOptions: records })
      }).catch(() => { })
  }, 500)

  editPic = (url: string, index: number) => {
    let pic: string = url;
    let modal: any;
    const picChange = (u: string) => {
      pic = u;
      modal.update({
        content: (
          <>
            <ImageUploader value={pic} onChange={picChange} ratio={1} />
            <p style={{ marginTop: 5, color: '#aaa' }}>推荐位列表图实际展示比例为1:1，非正方形图片将自动居中截取，如效果不佳可重新上传</p>
          </>
        ),
      });
    };
    modal = Modal.confirm({
      title: '编辑图片',
      content: (
        <>
          <ImageUploader value={pic} onChange={picChange} ratio={1} />
          <p style={{ marginTop: 5, color: '#aaa' }}>推荐位列表图实际展示比例为1:1，非正方形图片将自动居中截取，如效果不佳可重新上传</p>
        </>
      ),
      onOk: (destroy: Function) => {
        if (!pic) {
          message.error('请上传图片');
          return;
        }
        const articles = [...this.props.form.getFieldsValue().channelArticles];
        articles[index].picUrl = pic;
        this.props.form.setFieldsValue({ channelArticles: articles });
        destroy();
      },
    });
  };

  onAddItem = (item: any) => {
    /*
      视频，取视频封面图
      短图文，取第一张图
      长图文，取封面图
    */
    item.picUrl = item.list_pics
  }

  handleStyleChange = (style: number) => {
    const { getFieldError, getFieldValue, setFields } = this.props.form
    const [err] = getFieldError('channelArticles') || ['']
    const value = getFieldValue('channelArticles') || []
    if (value.length >= 3 || err.startsWith('为保证客户端显示效果')) {
      let errors
      if ((style === 42 && value.length >= 4) || (style === 41 && value.length >= 3)) {
        errors = null
      } else {
        errors = [new Error(`为保证客户端显示效果，关联新闻数不能少于${style === 42 ? 4 : 3}条！`)]
      }
      setFields({ channelArticles: { value, errors } })
    }
  }

  render() {
    const { form: { getFieldDecorator, getFieldsValue }, isEdit } = this.props
    const columns: any = [
      {
        title: '帖子ID',
        dataIndex: 'uuid',
        width: 80,
      },
      {
        title: '关联圈子',
        key: 'circle_name',
        dataIndex: 'circle_name',
        width: 90,
      },
      {
        title: '内容标题',
        key: 'list_title',
        dataIndex: 'list_title',
      },
    ];

    const styleTip = (style: number) => <img src={`/assets/${style === 0 ? 'communityBigCard' : 'communitySmallCard'}.png`} width={150} height={style === 0 ? 113 : 66} />
    const editData: any = this.state.editData || { style: isEdit ? undefined : 41, ref_ids_type: isEdit ? undefined : 0, round_carousel: false }
    const values = getFieldsValue();
    const { style = editData.style, ref_ids_type = editData.ref_ids_type } = values
    const { hasJump } = this.state
    let minLength = 3

    if (style === 42) {
      minLength = 4
      columns.push({
        title: '列表图',
        dataIndex: 'picUrl',
        width: 110,
        render: (text: string, _: any, index: number) => (
          <>
            {text && <img src={text} style={{ width: 60, height: 60, marginRight: 5, objectFit: 'cover' }} />}
            <A onClick={() => this.editPic(text, index)}>{text ? '修改' : '上传'}</A>
          </>
        ),
      })
    }

    return (
      <Form {...RecommendFormLayout}>
        <RecommendCommon {...this.props} editData={editData} />
        <Form.Item label="显示样式">
          {getFieldDecorator('style', {
            initialValue: editData.style,
            rules: [{
              required: true,
              message: '请选择样式',
            }],
          })(
            <Radio.Group onChange={(e) => this.handleStyleChange(e.target.value)}>
              <Radio value={41}>样式一&emsp;
                <Tooltip title={styleTip(1)}>
                  <Icon type="question-circle" />
                </Tooltip>
              </Radio>
              <Radio value={42}>样式二&emsp;
                <Tooltip title={styleTip(0)}>
                  <Icon type="question-circle" />
                </Tooltip>
              </Radio>
            </Radio.Group>
          )}
        </Form.Item>
        <Form.Item className="ccr_more_jump" label="更多跳转" required>
          <Radio.Group value={hasJump} onChange={(e) => this.setState({ hasJump: e.target.value })}>
            <Radio value={false}>无跳转</Radio>
            <Radio value={true}>
              有跳转
              {hasJump && getFieldDecorator('jump_model_url', {
                initialValue: editData.jump_model_url,
                rules: [{
                  required: true,
                  message: '请输入要跳转的链接',
                }, {
                  pattern: /^https?:\/\//,
                  message: '请填写正确的URL地址',
                }],
              })(<Input style={{ marginLeft: 8 }} placeholder="请输入要跳转的链接" />)}
            </Radio>
          </Radio.Group>
        </Form.Item>
        <Form.Item label="循环轮播">
          {getFieldDecorator('round_carousel', {
            rules: [{
              required: true,
              message: '请设置是否轮播',
            }],
            initialValue: editData.round_carousel,
            valuePropName: 'checked',
          })(<Switch />)}
        </Form.Item>
        <Form.Item label="关联内容">
          {getFieldDecorator('ref_ids_type', {
            initialValue: editData.ref_ids_type,
            rules: [{
              required: true,
              message: '请选择样式',
            }],
          })(
            <Radio.Group>
              <Radio value={0}>手动添加内容</Radio>
              <Radio value={1}>自动读取圈子精选&emsp;
                <Tooltip title="系统自动读取指定圈子下，最近发布的前15篇精选内容（图文帖/视频贴）；如选择样式二，无图的图文帖内容将被自动过滤；符合条件的内容不足4篇时，模块整个不显示">
                  <Icon type="question-circle" />
                </Tooltip>
              </Radio>
            </Radio.Group>
          )}
        </Form.Item>
        <Form.Item style={{ paddingLeft: 135, display: ref_ids_type === 0 ? 'block' : 'none' }} >
          {getFieldDecorator('channelArticles', {
            initialValue: editData.channelArticles,
            preserve: true,
            rules: [
              {
                required: true,
                message: '请关联内容',
                type: 'array',
              },
              {
                max: 15,
                message: '最多关联15条内容',
                type: 'array',
              },
              {
                min: minLength,
                message: `为保证客户端显示效果，关联内容数不能少于${minLength}条！`,
                type: 'array',
              },
            ]
          })(
            <SearchAndInput
              max={15}
              func="listArticleRecommendSearch"
              columns={columns}
              placeholder="输入ID或标题关联内容"
              body={{ doc_types: '10,12' }}
              order={true}
              addOnTop={true}
              categoryTip="内容"
              onAddItem={this.onAddItem}
            />
          )}
        </Form.Item>
        <Form.Item style={{ marginTop: -20, paddingLeft: 255, display: ref_ids_type === 1 ? 'block' : 'none' }}>
          {getFieldDecorator('circle_info', {
            initialValue: editData.circle_info,
            rules: [{
              required: true,
              message: '请输入圈子名称',
            }],
          })(
            <Select
              style={{ width: 180 }}
              placeholder="请输入圈子名称"
              onSearch={(val) => this.handleCircleSearch(val)}
              showSearch
              allowClear>
              {this.state.circleOptions.map((item: any) => <Select.Option key={item.id} value={`${item.name}-${item.id}`}>{item.name}</Select.Option>)}
            </Select>
          )}
        </Form.Item>
      </Form>
    )
  }
}