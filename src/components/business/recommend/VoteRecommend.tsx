import React from 'react';
import { Form, Icon, Input, Tooltip, Switch, Select, Modal, message } from 'antd';
import RecommendCommon, { RecommendFormLayout } from './RecommendCommon';
import Radio from 'antd/es/radio';
import SearchAndInput from '@components/common/newNewsSearchAndInput';
import { communityApi as api, listApi, searchApi } from '@app/api';
import _ from 'lodash';
import { A, ImageUploader } from '@app/components/common';
import { searchToObject } from '@app/utils/utils';

// 投票推荐位
@(Form.create({ name: 'VoteRecommend' }) as any)
export default class VoteRecommend extends React.Component<any> {
  state: any = {
    editData: null,
    circleOptions: [],
    recommendList: [],
  };

  specialValidateFields = () => {
    const {
      form: { getFieldsValue },
    } = this.props;
    // const { ref_ids_type } = getFieldsValue();
    const fields = [
      'title',
      'title_style',
      'title_pic',
      'position',
      'pic_url',
      'ref_ids2',
      'channelArticles',
    ];
    return fields;
  };

  // customValidator = (values: any) => {
  //   const { style, channelArticles, ref_ids_type } = values
  //   if (style === 42 && ref_ids_type === 0 && channelArticles.filter((v: any) => !v.picUrl).length > 0) {
  //     message.error('请上传全部稿件的图片');
  //     return false;
  //   }
  //   return true
  // }

  handleCheckArticle = (article: any) => {
    searchApi
      .searchArticleVote({ article_id: article.uuid || article.id })
      .then((res: any) => {
        const vote_url = res?.data?.detail?.vote_url;
        if (!!vote_url) {
          // const values = this.props.form.getFieldsValue();
          // const channelArticles = values.channelArticles;
          // const currentArticle = channelArticles?.[0];
          // if ((article.uuid || article.id) == (currentArticle.uuid || currentArticle.id)) {
          //   currentArticle.vote_title = vote_name;
          //   this.props.form.setFieldsValue({ channelArticles: [currentArticle] });
          // }
        } else {
          message.error('请关联有投票组件的稿件');
          this.props.form.setFieldsValue({ channelArticles: [] });
        }
      })
      .catch(() => {});
  };

  saveDataTransfer = (values: any) => {
    // console.log(values, this.state.hasJump)
    const { title, title_style, title_pic, position, pic_url, ref_ids2, channelArticles } = values;
    const title_show = title_style >= 0;
    const data: any = {
      article_category: 1,
      ref_type: 38,
      title,
      title_show,
      title_pic: title_style >= 1 ? title_pic : '',
      pic_url,
      ref_ids2,
      ref_ids: channelArticles.map((item: any) => item.uuid || item.id).join(','),
      // ref_extensions: JSON.stringify(
      //   channelArticles.map((item: any) => ({
      //     article_id: item.uuid || item.id,
      //     // vote_title: item.vote_title,
      //     article_title: item.list_title,
      //   }))
      // ),
    };
    if (position) {
      data.position = position;
    }
    if (title_show) {
      data.title_style = title_style;
    }
    return data;
  };

  setEditData = (data: any) => {
    if (data) {
      const {
        article_list = [],
        recommend: {
          article_id,
          title,
          show_title,
          title_style = 0,
          title_pic = '',
          position,
          // ref_extensions,
          pic_url,
          ref_ids2,
        },
      } = data;
      // const extensions = ref_extensions ? JSON.parse(ref_extensions) : [];
      this.setState({
        article_id,
        editData: {
          title,
          title_style: show_title ? title_style : -1,
          title_pic,
          position,
          pic_url,
          ref_ids2,
          channelArticles: article_list,
        },
      });
    } else {
      this.setState({
        editData: null,
      });
    }
  };

  render() {
    const {
      form: { getFieldDecorator, getFieldsValue, getFieldValue, setFieldsValue },
      isEdit,
    } = this.props;
    const columns = [
      {
        title: 'ID',
        dataIndex: 'id',
        width: 80,
        render: (_: any, v: any) => v.id || v.uuid,
      },
      {
        title: '稿件标题',
        key: 'list_title',
        dataIndex: 'list_title',
        width: 150,
        render: (text: any) => text?.slice(0, 30),
      },
      // {
      //   title: '投票标题',
      //   key: 'vote_title',
      //   dataIndex: 'vote_title',
      //   width: 150,
      //   render: (text: any) => text?.slice(0, 30),
      // },
    ];
    const editData: any = this.state.editData || { pic_url: '', ref_ids2: '' };
    const values = getFieldsValue();
    const { ref_ids_type = editData.ref_ids_type } = values;
    const { hasJump } = this.state;
    let minLength = 3;

    return (
      <Form {...RecommendFormLayout}>
        <RecommendCommon {...this.props} editData={editData} />

        <Form.Item label="投票描述">
          {getFieldDecorator('ref_ids2', {
            rules: [
              {
                max: 50,
                message: '最多输入50个字符',
                whitespace: true,
              },
            ],
            initialValue: editData.ref_ids2 || '',
          })(<Input placeholder="请简单描述投票主题，不超过50字，选填"></Input>)}
        </Form.Item>

        <Form.Item label="投票配图" extra="支持jpg,jpeg,png图片格式,比例345:90">
          {getFieldDecorator('pic_url', {
            initialValue: editData.pic_url,
          })(<ImageUploader ratio={345 / 90} />)}
        </Form.Item>

        <Form.Item label="关联稿件">
          {getFieldDecorator('channelArticles', {
            initialValue: editData.channelArticles,
            preserve: true,
            rules: [
              {
                required: true,
                message: '请关联稿件',
                type: 'array',
              },
              {
                max: 1,
                message: '最多关联1条稿件',
                type: 'array',
              },
            ],
          })(
            <SearchAndInput
              // key={style}
              max={1}
              func="listArticleRecommendSearch"
              columns={columns}
              placeholder="输入ID或标题，请先在媒立方或UGC稿件的评论里添加投票组件，再关联"
              body={{ doc_types: '2,3,4,5,8,9,10,11,12,13' }}
              order={false}
              addOnTop={true}
              onChange={(val: any) => {
                if (val?.length > 0) {
                  const article = val[0];
                  this.handleCheckArticle(article);
                }
              }}
            />
          )}
        </Form.Item>
      </Form>
    );
  }
}
