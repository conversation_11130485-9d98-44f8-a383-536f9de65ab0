import { Form, Icon, Modal, Radio, Tooltip, message } from 'antd';
import SearchAndInput from '@components/common/newNewsSearchAndInput';
import RecommendCommon, { RecommendFormLayout } from './RecommendCommon';
import React from 'react';
import { A, ImageUploader } from '@app/components/common';
import moment from 'moment';

// 直播汇总推荐位
@(Form.create({ name: 'LiveCollectRecommend' }) as any)
export default class LiveCollectRecommend extends React.Component<any> {
  state = {
    editData: null,
  };

  specialValidateFields = () => {
    const fields = ['title', 'title_style', 'title_pic', 'position', 'style', 'channelArticles'];
    return fields;
  };

  customValidator = (values: any) => {
    const { style } = values;
    if (style == 101) {
      if (
        values.channelArticles.filter((v: any) => {
          return !v.picUrl;
        }).length > 0
      ) {
        message.error('请上传全部稿件的列表图');
        return false;
      }
    }
    return true;
  };

  saveDataTransfer = (values: any) => {
    const {
      title,
      title_style,
      title_pic,
      position,
      channelArticles,
      style,
    } = values;
    const title_show = title_style >= 0;
    const data: any = {
      article_category: 0,
      title,
      title_show,
      title_pic: title_style >= 1 ? title_pic : '',
      position,
      ref_type: 14,
      style,
    };
    if (style == 101) {
      data.ref_extensions = JSON.stringify(
        channelArticles.map((item: any) => ({
          article_id: item.uuid || item.id,
          list_pic: item.picUrl,
          list_title: item.list_title,
        }))
      );
    } else {
      data.ref_ids_type = 0;
    }
    data.ref_ids = channelArticles.map((item: any) => item.uuid || item.id).join(',');

    if (position) {
      data.position = position;
    }
    if (title_show) {
      data.title_style = title_style;
    }
    return data;
  };

  setEditData = (data: any) => {
    if (data) {
      const {
        article_list,
        recommend: {
          title,
          show_title,
          title_style = 0,
          title_pic = '',
          position,
          style = 0,
          ref_extensions,
        },
      } = data;
      const extensions = ref_extensions ? JSON.parse(ref_extensions) : [];
      this.setState({
        editData: {
          title,
          title_style: show_title ? title_style : -1,
          title_pic,
          position,
          style,
          channelArticles:
            article_list?.map((v: any) => {
              const id = v.uuid || v.id;
              const picArticle = extensions.find((item: any) => item.article_id == id);

              return {
                channel_name: v.channel_name,
                doc_type_name: v.doc_type_name,
                list_title: v.list_title,
                id: v.id,
                uuid: v.uuid,
                picUrl: picArticle?.list_pic,
              };
            }) || [],
        },
      });
    } else {
      this.setState({
        editData: null,
      });
    }
  };

  styleTip(style: number) {
    return (
      <img
        src={`/assets/${style === 0 ? 'h_live_tips.png' : 'v_live_tips.png'}`}
        width={200}
        height={style === 0 ? 106 : 149}
      />
    );
  }

  handleStyleChange = (style: number) => {
    this.setState({
      ...this.state,
      editData: {
        ...(this.state.editData || {}),
        style: style,
      },
    });
    // const { getFieldError, getFieldValue, setFields } = this.props.form
    // const [err] = getFieldError('channelArticles') || ['']
    // const value = getFieldValue('channelArticles') || []
    // const oldStyle = getFieldValue('style') || 0

    // if (style == 82 || oldStyle == 82) {
    //   for (const item of value) {
    //     item.picUrl = ''
    //   }
    // }

    // console.log(style, oldStyle)
    // if (value.length >= 3 || err.startsWith('为保证客户端显示效果')) {
    //   let errors
    //   if ((style === 81 && value.length >= 5) || (style === 0 && value.length >= 3)) {
    //     errors = null
    //   } else {
    //     errors = [new Error(`为保证客户端显示效果，关联新闻数不能少于${style === 81 ? 5 : 3}条！`)]
    //   }
    //   setFields({ channelArticles: { value, errors } })
    // }
  };

  editPic = (url: string, index: number) => {
    const style = this.props.form.getFieldsValue().style;
    let pic: string = url;
    let modal: any;
    const picChange = (u: string) => {
      pic = u;
      modal.update({
        content: (
          <>
            <ImageUploader value={pic} onChange={picChange} ratio={3 / 4} />
            <p>比例3:4</p>
          </>
        ),
      });
    };
    modal = Modal.confirm({
      title: '编辑图片',
      content: (
        <>
          <ImageUploader value={pic} onChange={picChange} ratio={3 / 4} />
          <p>比例3:4</p>
        </>
      ),
      onOk: (destroy: Function) => {
        if (!pic) {
          message.error('请上传图片');
          return;
        }
        const articles = [...this.props.form.getFieldsValue().channelArticles];
        articles[index].picUrl = pic;
        this.props.form.setFieldsValue({ channelArticles: articles });
        destroy();
      },
    });
  };

  render() {
    const {
      form: { getFieldDecorator, getFieldValue },
    } = this.props;
    const relatedLiveTip = (
      <div>
        <p>若本频道的稿件被关联成功，将不再显示在本频道的已签发信息流列表中。</p>
        <p>
          反过来，本频道的稿件从推荐位上被取消关联后，稿件将重新按照原有序号回到本频道的已签发列表中。
        </p>
        {/* <p>能关联的稿件类型有:新闻，链接，图集，活动，视频，直播</p> */}
        <p>为保证客户端显示效果，最少关联3条，最多关联30条。</p>
      </div>
    );
    const columns: any = [
      {
        title: '频道',
        key: 'channel',
        dataIndex: 'channel_name',
        width: 95,
      },
      {
        title: 'ID',
        dataIndex: 'id',
        width: 90,
      },
      {
        title: '新闻类型',
        key: 'type',
        dataIndex: 'doc_type_name',
        width: 90,
      },
      {
        title: '新闻标题',
        key: 'list_title',
        dataIndex: 'list_title',
      },
    ];
    const topicColumns = [
      { title: '地方号名称', dataIndex: 'name' },
      {
        title: '创建人',
        dataIndex: 'created_user_name',
        render: (text: string, record: any) => record.created_user_name || record.created_by,
      },
      {
        title: '创建时间',
        dataIndex: 'created_at',
        render: (text: number) => moment(text).format('YYYY-MM-DD'),
      },
    ];
    const editData: any = this.state.editData || {};

    if (editData.style == 101) {
      columns.push({
        width: 100,
        title: '列表图',
        key: 'picUrl',
        dataIndex: 'picUrl',
        render: (text: string, record: any, index: number) => {
          return (
            <>
              <A onClick={() => this.editPic(text, index)}>{text ? '修改' : '上传'}</A>
            </>
          );
        },
      });
    }
    return (
      <Form {...RecommendFormLayout}>
        <RecommendCommon {...this.props} editData={editData} />
        <Form.Item label="显示样式">
          {getFieldDecorator('style', {
            initialValue: editData.style ?? 0,
            rules: [
              {
                required: true,
                message: '请选择样式',
              },
            ],
          })(
            <Radio.Group onChange={(e) => this.handleStyleChange(e.target.value)}>
              <Radio value={0}>
                横直播&emsp;
                <Tooltip title={this.styleTip(0)}>
                  <Icon type="question-circle" />
                </Tooltip>
              </Radio>
              <Radio value={101}>
                竖直播&emsp;
                <Tooltip title={this.styleTip(1)}>
                  <Icon type="question-circle" />
                </Tooltip>
              </Radio>
            </Radio.Group>
          )}
        </Form.Item>
        {/* {getFieldValue('style') == 0 && (
          <Form.Item label="稿件来源">
            {getFieldDecorator('ref_ids_type', {
              initialValue: editData.ref_ids_type ?? 0,
              rules: [
                {
                  required: true,
                  message: '请选择稿件来源',
                },
              ],
            })(
              <Radio.Group>
                <Radio value={0}>手动添加</Radio>
                <Radio value={1}>自动取地方号下的直播稿</Radio>
              </Radio.Group>
            )}
          </Form.Item>
        )} */}
        <Form.Item label="关联直播">
          {getFieldDecorator('channelArticles', {
            initialValue: editData.channelArticles,
            preserve: true,
            rules: [
              {
                required: true,
                message: '请关联新闻',
                type: 'array',
              },
              {
                max: 30,
                message: '最多关联30条新闻',
                type: 'array',
              },
              {
                min: 3,
                message: '为保证客户端显示效果，关联新闻数不能少于3条！',
                type: 'array',
              },
            ],
          })(
            <SearchAndInput
              max={30}
              func="listArticleRecommendSearch"
              columns={columns}
              placeholder="输入ID或标题关联稿件"
              body={{ doc_types: '8' }}
              order={true}
              addOnTop={true}
              afix={
                <Tooltip title={relatedLiveTip}>
                  <Icon type="question-circle" />
                </Tooltip>
              }
            />
          )}
        </Form.Item>
      </Form>
    );
  }
}
