import React from "react";
import { A, ImageUploader } from '@components/common';
import SearchAndInput from '@components/common/newNewsSearchAndInput';
import { Form, Icon, Input, InputNumber, Modal, Tooltip, message } from "antd";
import RecommendCommon, { RecommendFormLayout } from "./RecommendCommon";
import Radio from "antd/es/radio";

// 热点新闻推荐位
@(Form.create({ name: 'HotNewsRecommend' }) as any)
export default class HotNewsRecommend extends React.Component<any> {
  state = {
    editData: null
  }
  saveDataTransfer = (values: any) => {
    const { title, title_style, title_pic, position, channelArticles } = values
    const title_show = title_style >= 0
    const data = {
      ...values,
      article_category: 0,
      title,
      title_show,
      title_pic: title_style >= 1 ? title_pic : '',
      ref_type: 13,
      ref_ids: channelArticles.map((item: any) => item.uuid || item.id).join(','),
      ref_extensions: JSON.stringify(channelArticles.map((item: any) => ({ article_id: item.uuid || item.id, list_pic: item.picUrl, list_title: item.custom_title || item.list_title }))),
      style: 21,
    }
    const pic_custom_scale = channelArticles[0].picScale
    if (pic_custom_scale) {
      data.pic_custom_scale = pic_custom_scale
    }
    if (title_show) {
      data.title_style = title_style
    }
    if (!position) {
      delete data.position;
    }
    delete data.channelArticles
    return data
  }


  setEditData = (data: any) => {
    if (data) {
      const { article_list, recommend: { title, show_title, title_style = 0, title_pic = '', position, show_article_count, ref_extensions, list_pic_custom_scale } } = data
      const extensions = JSON.parse(ref_extensions)
      this.setState({
        editData: {
          title,
          title_style: show_title ? title_style : -1,
          title_pic,
          position,
          show_article_count,
          channelArticles: article_list.map((v: any) => {
            const id = v.uuid || v.id
            const data: any = {
              channel_name: v.channel_name,
              list_title: v.list_title,
              id,
              uuid: v.uuid,
              picScale: list_pic_custom_scale
            }
            const picArticle = extensions.find((item: any) => item.article_id == id)
            if (picArticle) {
              data.picUrl = picArticle.list_pic
              data.custom_title = picArticle.list_title
            }
            return data
          })
        }
      })
    } else {
      this.setState({
        editData: null
      })
    }
  }

  editTitle = (value: string, index: number) => {
    let title: string = value || ''
    let modal: any;
    const titleChange = (e: any) => {
      title = e.target.value
      modal.update({
        content: (
          <>
            <Input.TextArea placeholder="最多输入40字" value={title} maxLength={40} onPressEnter={(e) => e.preventDefault()} onChange={titleChange}></Input.TextArea>
            <div style={{ textAlign: "right" }}>{title?.length}/40</div>
          </>
        ),
      });
    };
    modal = Modal.confirm({
      title: '自定义标题',
      content: (
        <>
          <Input.TextArea placeholder="最多输入40字" value={title} maxLength={40} onPressEnter={(e) => e.preventDefault()} onChange={titleChange}></Input.TextArea>
          <div style={{ textAlign: "right" }}>{title?.length}/40</div>
        </>
      ),
      onOk: (destroy: Function) => {
        if (!title?.trim()) {
          message.error('请输入自定义标题')
          return
        }
        const articles = [...this.props.form.getFieldsValue().channelArticles];
        articles[index].custom_title = title?.trim();
        this.props.form.setFieldsValue({ channelArticles: articles });
        destroy();
      },
    });
  }

  editPic = (url: string, index: number, picScale: string) => {
    let pic: string = url;
    let scale: string = picScale || '16:9';
    let modal: any;
    const picChange = (u: string, picScale: string) => {
      pic = u;
      scale = picScale || '16:9';
      modal.update({
        content: (
          <>
            <ImageUploader value={pic} imgMaxWidth={300} onChange={picChange} ratioType={scale} showRatioTypeChange />
            <p>比例{scale}</p>
          </>
        ),
      });
    };
    modal = Modal.confirm({
      title: '编辑图片',
      content: (
        <>
          <ImageUploader value={pic} imgMaxWidth={300} onChange={picChange} ratioType={scale} showRatioTypeChange />
          <p>比例{scale}</p>
        </>
      ),
      onOk: (destroy: Function) => {
        const articles = [...this.props.form.getFieldsValue().channelArticles];
        articles[index].picUrl = pic;
        articles[index].picScale = scale;
        this.props.form.setFieldsValue({ channelArticles: articles });
        destroy();
      },
    });
  };

  orderChange = (channelArticles: any) => {
    this.props.form.setFieldsValue({
      channelArticles: channelArticles.map((article: any, index: number) => {
        if (index > 0 || !article.picScale) {
          console.log('清空')
          article.picUrl = ''
          article.picScale = '16:9'
        }
        return article
      })
    });
  };

  render() {
    const { form: { getFieldDecorator, getFieldsValue } } = this.props
    const { channelArticles = [] } = getFieldsValue();
    const styleTip = (style: number) => <img src={`/assets/${style === 0 ? 'hotRecommendWord' : 'hotRecommendPic'}.png`} width={150} height={style === 0 ? 212 : 290} />
    const columns = [
      {
        title: '稿件ID',
        dataIndex: 'id',
        width: 80,
      },
      {
        title: '新闻频道',
        key: 'type',
        dataIndex: 'channel_name',
        width: 90,
      },
      {
        title: '新闻标题',
        key: 'list_title',
        dataIndex: 'list_title',
        width: 150
      },
      {
        // width: 90,
        title: '自定义标题',
        key: 'custom_title',
        dataIndex: 'custom_title',
        render: (text: string, record: any, index: number) => {
          return <div style={{
            display: 'flex',
            alignItems: 'center',
          }}>
            <div>{record.custom_title || record.list_title}</div>
            <a style={{ flex: 'none', marginLeft: '5px' }} onClick={() => this.editTitle(record.custom_title || record.list_title, index)}>修改</a>
          </div>
        }
      },
      {
        title: '列表图',
        dataIndex: 'picUrl',
        width: 90,
        align: 'center' as any,
        render: (text: string, record: any, index: number) => (
          <>
            {index === 0 ? <A onClick={() => this.editPic(text, index, record.picScale)}>{text ? '修改' : '上传'}</A> : '/'}
          </>
        ),
      },
    ];
    const editData: any = this.state.editData || { style: 21 }
    return (
      <Form {...RecommendFormLayout}>
        <RecommendCommon {...this.props} editData={editData} />
        {/* <Form.Item label="显示样式">
          {getFieldDecorator('style', {
            initialValue: editData.style,
            rules: [{
              required: true,
              message: '请选择样式',
            }],
          })(
            <Radio.Group>
              <Radio value={21}>默认+纯文&emsp;
                <Tooltip title={styleTip(0)}>
                  <Icon type="question-circle" />
                </Tooltip></Radio>
              <Radio value={22}>默认+左图右文&emsp;
                <Tooltip title={styleTip(1)}>
                  <Icon type="question-circle" />
                </Tooltip>
              </Radio>
            </Radio.Group>
          )}
        </Form.Item> */}
        <Form.Item label="显示稿件数" extra="列表显示稿件数，必须≥1，≤10的正整数">
          {getFieldDecorator('show_article_count', {
            initialValue: editData.show_article_count,
            rules: [
              {
                required: true,
                message: '请输入列表显示稿件数',
                type: 'number',
              },
              {
                max: 10,
                message: '最大不能超过10',
                type: 'number',
              },
            ],
          })(<InputNumber style={{ width: 400 }} placeholder="请输入列表显示稿件数" min={1} max={10} />)}
        </Form.Item>

        <Form.Item label="关联稿件">
          {getFieldDecorator('channelArticles', {
            initialValue: editData.channelArticles,
            preserve: true,
            rules: [
              {
                required: true,
                message: '请关联新闻',
                type: 'array',
              },
              {
                max: 30,
                message: '最多关联30条新闻',
                type: 'array',
              },
              {
                min: 3,
                message: '为保证客户端显示效果，关联新闻数不能少于3条！',
                type: 'array',
              },
            ],
          })(
            <SearchAndInput
              max={30}
              func="listArticleRecommendSearch"
              columns={columns}
              placeholder="输入ID或标题关联稿件"
              orderChange={this.orderChange}
              body={{ doc_types: '2,3,4,5,8,9' }}
              order={true}
              addOnTop={true}
              addOnTopIndex={channelArticles.length > 0 && channelArticles[0].picUrl ? 1 : 0}
            />
          )}
        </Form.Item>
      </Form>
    )
  }
}
