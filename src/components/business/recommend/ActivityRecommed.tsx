import React from 'react';
import Recommend<PERSON><PERSON><PERSON>, { RecommendFormLayout } from './RecommendCommon';
import {
  Form,
  Icon,
  Input,
  InputNumber,
  message,
  Modal,
  Radio,
  Select,
  Switch,
  Tooltip,
} from 'antd';
import { A, ImageUploader } from '@components/common';
import RangeInput from '@app/components/common/RangeInput';
import { releaseListApi } from '@app/api';
import NewNewsSearchAndInput from '@app/components/common/newNewsSearchAndInput';

// 读报推荐位
@(Form.create({ name: 'ActivityRecommed' }) as any)
export default class ActivityRecommed extends React.Component<any> {
  state = {
    editData: null,
  };

  saveDataTransfer = (values: any) => {
    const { title, position, ref_extensions, round_carousel, title_style, title_pic } = values;
    console.log('123123', values);
    const title_show = title_style >= 0;
    const data: any = {
      title,
      title_show,
      title_pic: title_style >= 1 ? title_pic : '',
      ref_type: 41,
      round_carousel,
      ref_ids: ref_extensions.map((v: any) => v.id),
      ref_extensions: JSON.stringify(
        ref_extensions.map((v: any) => {
          return {
            id: v.id,
            pic: v.pic,
          };
        })
      ),
    };

    if (title_show) {
      data.title_style = title_style;
    }
    if (position) {
      data.position = position;
    }
    return data;
  };

  setEditData = (data: any) => {
    if (data) {
      const {
        activity_list = [],
        recommend: {
          title,
          position,
          ref_extensions,
          ref_type,
          cycle_carousel = true,
          show_title,
          title_style = 0,
          title_pic = '',
        },
      } = data;

      const extensions = ref_extensions ? JSON.parse(ref_extensions) : [];
      this.setState({
        editData: {
          title,
          title_style: show_title ? title_style : -1,
          title_pic,
          position,
          round_carousel: cycle_carousel,
          isEdit: true,
          activity_list: activity_list,
        },
      });
    } else {
      this.setState({
        editData: null,
      });
    }
  };

  editPic = async (url: string, record: object, index: number) => {
    let pic: string = url;
    let modal: any;

    const picChange = (u: string) => {
      pic = u;
      modal.update({
        content: (
          <>
            <ImageUploader value={pic} onChange={picChange} ratio={16 / 9} />
            <p> 比例16: 9</p>
          </>
        ),
      });
    };
    modal = Modal.confirm({
      title: '编辑图片',
      width: 500,
      content: (
        <>
          <ImageUploader value={pic} onChange={picChange} imgMaxWidth={400} ratio={16 / 9} />
          <p>
            建议比例{16}:{9}
          </p>
        </>
      ),
      onOk: async (destroy: Function) => {
        if (!pic) {
          message.error('请上传图片');
          return;
        }

        const ref_extensions = this.props.form.getFieldsValue().ref_extensions;
        console.log(ref_extensions);
        ref_extensions[index].pic = pic;
        this.props.form.setFieldsValue({ ref_extensions: ref_extensions });
        destroy();
      },
    });
  };
  columns = [
    {
      title: '活动标题',
      dataIndex: 'title',
    },
    {
      title: '状态',
      dataIndex: 'status',
      width: 80,
      render: (text: any) => {
        return <span>{['未开始', '进行中', '已结束'][text]}</span>;
      },
    },
    {
      title: '活动图',
      align: 'center',
      key: 'pic',
      dataIndex: 'pic',
      width: 120,
      render: (text: string, record: any, index: number) => (
        <div style={{ display: 'flex', justifyContent: 'space-around', alignItems: 'center' }}>
          {text && <img src={text} style={{ height: 40, maxWidth: 70 }} />}
          <A onClick={() => this.editPic(text, record, index)}>{text ? '编辑' : '上传'}</A>
        </div>
      ),
    },
  ];

  render() {
    const editData: any = this.state.editData || {
      round_carousel: true,
    };
    const {
      form: { getFieldDecorator, getFieldValue, setFieldsValue, getFieldsValue },
    } = this.props;
    return (
      <Form {...RecommendFormLayout}>
        <RecommendCommon {...this.props} editData={editData}></RecommendCommon>

        <Form.Item label="循环轮播">
          {getFieldDecorator('round_carousel', {
            rules: [
              {
                required: true,
                message: '请设置是否轮播',
              },
            ],
            initialValue: editData.round_carousel,
            valuePropName: 'checked',
          })(<Switch />)}
        </Form.Item>

        <Form.Item label="关联活动">
          {getFieldDecorator('ref_extensions', {
            initialValue: editData.activity_list,
            rules: [
              {
                required: true,
                message: '请设置关联活动',
              },
              {
                max: 20,
                message: '最多关联20个活动',
                type: 'array',
              },
              {
                min: 3,
                message: '为保证客户端显示效果，请至少关联3个',
                type: 'array',
              },
              {
                validator: (rule: any, val: any, callback: any) => {
                  if (!val) {
                    return callback('');
                  } else if (val.filter((v: any) => !v.pic).length > 0) {
                    return callback('请上传活动图');
                  } else {
                    return callback();
                  }
                },
              },
            ],
          })(
            <NewNewsSearchAndInput
              max={20}
              func="getActivityList"
              indexKey="list"
              apiWithPagination={true}
              columns={this.columns}
              placeholder="请选择需要关联的活动"
              categoryTip="活动"
              order={true}
              body={{ enabled: true }}
              searchKey="title"
              selectMap={(d: any) => d.title}
              afix={
                <Tooltip title="最多关联20个活动">
                  <Icon type="question-circle" />
                </Tooltip>
              }
            />
          )}
        </Form.Item>
      </Form>
    );
  }
}
