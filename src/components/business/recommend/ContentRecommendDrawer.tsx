import { Drawer } from '@components/common';
import { Spin, message } from 'antd';
import Radio from 'antd/es/radio';
import React, { useEffect, useRef, useState } from "react"
import VideoCarouselRecommend from './VideoCarouselRecommend';
import NewsCollectRecommend from './NewsCollectRecommend';
import HotNewsRecommend from './HotNewsRecommend';
import LiveCollectRecommend from './LiveCollectRecommend';
import CommentRecommend from './CommentRecommend';
import ColumnRecommend from './ColumnRecommend';
import UserRecommend from './UserRecommend';
import AdvertisementRecommend from './AdvertisementRecommend';
import DataRecommend from './DataRecommend';
import TopicRecommend from './TopicRecommend';
import AggregationRecommend from './AggregationRecommend'
import CommunityCollectRecommend from './CommunityCollectRecommend'
import CommunitySingleRecommend from './CommunitySingleRecommend'
import PicRecommend from './PicRecommend'
import CircleRecommend from './CircleRecommend'
import CompetitionRecommend from './CompetitionRecommend'
import ReadPaperRecommend from './ReadPaperRecommend';
import GenerateRecommend from './GenerateRecommend';
import { releaseListApi } from '@app/api';
import { searchToObject, RecommendTypeNames } from '@app/utils/utils';
import { setConfig } from '@action/config';
import { useDispatch } from 'react-redux';
import RankingRecommend from './RankingRecommed';
import ReportRecommend from './ReportRecommend';
import VoteRecommend from './VoteRecommend';
import AudioRecommend from './AudioRecommend';
import LiveNoticeRecommend from './LiveNoticeRecommend';
import ActivityRecommed from './ActivityRecommed';

export default function ContentRecommendDrawer(props: any) {
  const dispatch = useDispatch()
  const { visible, skey, maxPosition, onClose, onEnd, record, supportTypes, isCommunity } = props
  const currentFormRef = useRef(null)
  const [type, setType] = useState(0)
  const [subtype, setSubtype] = useState(-1)
  const [loading, setLoading] = useState(false)

  useEffect(() => {
    if (visible) {
      // 编辑
      if (record) {
        if (record._createRecommendCode === 'all') {
          setType(0)
          setSubtype(supportTypes[0].subtypes[0].code)
        } else if (record._createRecommendCode) {
          setSubtype(record._createRecommendCode)
        } else {
          setSubtype(record.ref_type)
          setLoading(true)
          releaseListApi.getContentRecommend({ id: record.recommend_id })
            .then(({ data }) => {
              setLoading(false)
              setEditData(data)
            }).catch(() => {
              setLoading(false)
              setEditData(null)
            })
        }
      } else {
        setType(0)
        setSubtype(supportTypes[0].subtypes[0].code)
      }
    } else {
      // 关闭时，清空当前loading等状态
      dispatch(setConfig({ mLoading: false }));
      setLoading(false)
      setType(0)
      setSubtype(-1)
      setEditData(null)
    }
  }, [visible])

  const setEditData = (data: any) => {
    const currentForm: any = currentFormRef.current
    if (currentForm) {
      const { setEditData } = currentForm
      if (setEditData) {
        setEditData(data)
      }
    }
  }

  const handleTypeChange = (type: number) => {
    setType(type)
    handleSubtypeChange(supportTypes[type].subtypes[0].code)
  }

  const handleSubtypeChange = (subtype: number) => {
    setSubtype(subtype)
  }

  const onOk = () => {
    const currentForm: any = currentFormRef.current
    if (currentForm) {
      if (currentForm.handleSubmit) {
        currentForm.handleSubmit()
      } else {
        const { props: { form: { validateFieldsAndScroll } }, specialValidateFields, customValidator, saveDataTransfer } = currentForm
        const validateFieldsParams = []
        if (specialValidateFields) {
          validateFieldsParams.push(specialValidateFields())
        }
        validateFieldsParams.push((err: any, values: any) => {
          if (!err) {
            if (!customValidator || customValidator(values)) {
              dispatch(setConfig({ mLoading: true }));
              const submitData = (saveDataTransfer && saveDataTransfer(values)) || values
              submitData['channel_id'] = searchToObject().channel_id
              const isEdit = record && record.recommend_id
              if (isEdit) {
                submitData['id'] = record.recommend_id
              }
              releaseListApi[isEdit ? 'updateContentRecommend' : 'createContentRecommend'](submitData)
                .then(() => {
                  message.success('操作成功');
                  onEnd()
                })
                .catch(() => {
                  dispatch(setConfig({ mLoading: false }));
                })
            }
          } else {
            message.error('请检查表单内容');
          }
        })
        validateFieldsAndScroll(...validateFieldsParams)
      }

    }
  }

  const isEdit = record && record.recommend_id
  const recommendProps = {
    wrappedComponentRef: currentFormRef,
    isEdit,
    isCommunity,
    maxPosition
  }
  let title = '创建推荐位'
  if (record) {
    const name = RecommendTypeNames[subtype]
    if (record.recommend_id) {
      if (name) {
        title = name
      }
    } else if (record._createRecommendCode !== 'all') {
      if (name) {
        title = `创建${name}`
      }
    }
  }
  return (
    <Drawer title={title}
      visible={visible}
      skey={skey}
      onClose={onClose}
      onOk={onOk}
      width={1000}>
      <Spin
        tip="正在加载..."
        spinning={loading}
      >
        {
          (!record || record._createRecommendCode === 'all') && (<>
            <div style={{ textAlign: 'center', marginBottom: '20px' }}>
              <Radio.Group buttonStyle="solid" value={type} onChange={(e) => handleTypeChange(e.target.value)}>
                {supportTypes.map((types: any, index: any) => types.hideMenu ? null : <Radio.Button key={index} value={index}>{types.name}</Radio.Button>)}
              </Radio.Group>
            </div>
            <div style={{ marginBottom: '16px' }}>
              <div className='ant-form-item-label' style={{ width: '16.66667%' }}>
                <label className="ant-form-item-required">推荐位类型</label>
              </div>
              <Radio.Group value={subtype} onChange={(e) => handleSubtypeChange(e.target.value)}>
                {supportTypes[type].subtypes.map((type: any, index: any) => <Radio key={index} value={type.code}>{type.name}</Radio>)}
              </Radio.Group>
            </div>
          </>)
        }
        {subtype === 11 && <VideoCarouselRecommend {...recommendProps} />}
        {subtype === 12 && <NewsCollectRecommend {...recommendProps} />}
        {subtype === 13 && <HotNewsRecommend {...recommendProps} />}
        {subtype === 14 && <LiveCollectRecommend {...recommendProps} />}
        {subtype === 21 && <CommentRecommend {...recommendProps} />}
        {subtype === 22 && <ColumnRecommend {...recommendProps} />}
        {subtype === 23 && <UserRecommend {...recommendProps} showLoading={(show: boolean) => dispatch(setConfig({ mLoading: show }))} onEnd={onEnd} />}
        {subtype === 24 && <AdvertisementRecommend {...recommendProps} />}
        {subtype === 25 && <DataRecommend {...recommendProps} />}
        {subtype === 26 && <TopicRecommend {...recommendProps} />}
        {subtype === 27 && <CommunityCollectRecommend {...recommendProps} />}
        {subtype === 28 && <CommunitySingleRecommend {...recommendProps} />}
        {subtype === 29 && <PicRecommend {...recommendProps} showLoading={(show: boolean) => dispatch(setConfig({ mLoading: show }))} onEnd={onEnd} />}
        {subtype === 30 && <CircleRecommend {...recommendProps} showLoading={(show: boolean) => dispatch(setConfig({ mLoading: show }))} onEnd={onEnd} />}
        {subtype === 31 && <CompetitionRecommend {...recommendProps} showLoading={(show: boolean) => dispatch(setConfig({ mLoading: show }))} onEnd={onEnd} />}
        {/* 读报、早晚报推荐位 */}
        {Boolean(subtype === 32 || subtype === 34) && <ReadPaperRecommend {...recommendProps} />}
        {/* 通用推荐位 */}
        {subtype === 33 && <GenerateRecommend {...recommendProps} showLoading={(show: boolean) => dispatch(setConfig({ mLoading: show }))} onEnd={onEnd} />}
        {/* 聚合推荐位 */}
        {subtype === 35 && <AggregationRecommend {...recommendProps} showLoading={(show: boolean) => dispatch(setConfig({ mLoading: show }))} onEnd={onEnd} />}
        {/* 榜单推荐位 */}
        {subtype === 36 && <RankingRecommend {...recommendProps}></RankingRecommend>}
        {/* 报料推荐位 */}
        {subtype == 37 && <ReportRecommend {...recommendProps}></ReportRecommend>}
        {/* 投票推荐位 */}
        {subtype == 38 && <VoteRecommend {...recommendProps}></VoteRecommend>}
        {/* 活动推荐位 */}
        {subtype === 41 &&<ActivityRecommed {...recommendProps}></ActivityRecommed>}
      </Spin>
    </Drawer>
  )
}