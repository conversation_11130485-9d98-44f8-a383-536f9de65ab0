import React from "react";
import AddForm from '@components/business/addForm';
import { releaseListApi } from "@app/api";
import { searchToObject } from "@app/utils/utils";

export default class UserRecommend extends React.Component<any> {

  setFormItem: any
  state: any

  constructor(props: any) {
    super(props)
    this.props.wrappedComponentRef.current = this
    this.setFormItem = () => {
      return {
        nick_name: '',
        chao_id: '',
        reason: '',
        image_url: '',
        colorInfo: {
          color_type: 1,
          auto_color: null,
          bottom_color: null
        }
      }
    }
    this.state = {
      bannerData: [this.setFormItem(), this.setFormItem()],
      bannerObj: { title: '', position: '', style: 0, },
      id: undefined
    }
  }

  getUpdateDate = (values: any) => {
    const { title, title_style, title_pic, position, json } = values
    const title_show = title_style >= 0
    const data: any = {
      title,
      title_show,
      title_pic: title_style >= 1 ? title_pic : '',
      article_category: this.props.isCommunity ? 1 : 0,
      ref_type: 23,
      channel_id: searchToObject().channel_id,
      style: 0,
      ref_extensions: JSON.stringify(json.map((item: any) => {
        const data: any = {
          nick_name: item.nick_name,
          chao_id: item.chao_id,
          reason: item.reason,
          account_id: item.id,
        }
        return data
      })),
      id: this.state.id
    }
    if (position) {
      data.position = position
    }
    if (title_show) {
      data.title_style = title_style
    }
    if (!data.id) {
      delete data.id
    }
    return data
  }


  setEditData = (data: any) => {
    if (data) {
      const { recommend: { id, title, show_title, title_style = 0, title_pic = '', position, style = 0 }, user_list } = data
      this.setState({
        id,
        bannerData: user_list.map((item: any) => {
          const color_type = item.color_type === 0 ? 0 : 1
          const bottom_color = item.bg_color || null
          return {
            nick_name: item.nick_name,
            chao_id: item.chao_id,
            reason: item.reason,
            id: item.account_id,
            image_url: item.image_url,
            colorInfo: {
              color_type,
              auto_color: color_type === 1 ? bottom_color : null,
              bottom_color
            }
          }
        }),
        bannerObj: { title, title_style: show_title ? title_style : -1, title_pic, position, style }
      }, () => {
        this.props.wrappedComponentRef.current.init()
      })
    } else {
      this.setState({
        editData: null
      })
    }
  }

  render() {
    const { isEdit, maxPosition } = this.props
    return !isEdit || this.state.id ? <AddForm
      isEdit={isEdit}
      maxPosition={maxPosition}
      setEditData={this.setEditData}
      bannerData={this.state.bannerData} //复现数据
      bannerObj={this.state.bannerObj} //复现数据
      changeVisible={this.props.onEnd}
      showLoading={this.props.showLoading}
      wrappedComponentRef={(instance: any) => (this.props.wrappedComponentRef.current = instance)}
      getUpdateDate={this.getUpdateDate} //获取要提交的数据
      setFormItem={this.setFormItem()} //设置表单item
      joggle={isEdit ? releaseListApi.updateContentRecommend : releaseListApi.createContentRecommend} // 接口
      componentNames='用户管理位' //判断使用的组件
      addLabel='添加一个用户'
      headName='用户'
      listLengthTip='最多添加20个用户'
      listLength={20}
      disabled={2}
    /> : null
  }
}
