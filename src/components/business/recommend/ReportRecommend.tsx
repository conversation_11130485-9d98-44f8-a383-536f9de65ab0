import React from 'react';
import {
  Form,
  Icon,
  Input,
  Tooltip,
  Switch,
  Select,
  Modal,
  message,
  Divider,
  Row,
  Button,
} from 'antd';
import RecommendCommon, { RecommendFormLayout } from './RecommendCommon';
import Radio from 'antd/es/radio';
import SearchAndInput from '@components/common/newNewsSearchAndInput';
import { communityApi as api, listApi, searchApi } from '@app/api';
import _ from 'lodash';
import { A, ImageUploader } from '@app/components/common';
import { bizTypeMap, searchToObject } from '@app/utils/utils';
import TMFormList, { FormListTitle } from '@app/components/common/TMFormList';

// 报料推荐位
@(Form.create({ name: 'ReportRecommend' }) as any)
export default class ReportRecommend extends React.Component<any> {
  formListRef = React.createRef<any>();

  state: any = {
    editData: null,
    circleOptions: [],
    recommendList: [],
  };

  specialValidateFields = () => {
    const {
      form: { getFieldsValue, setFieldsValue },
    } = this.props;
    const { ref_ids_type } = getFieldsValue();
    setFieldsValue({ length: this.formListRef.current?.total ?? 0 });
    const fields = [
      'title',
      'title_style',
      'title_pic',
      'position',
      'style',
      'round_carousel',
      'ref_ids_type',
      'title_slogan',
      'pic_url',
      'list',
      'ad_corner_show',
      'length',
    ];
    if (ref_ids_type === 0) {
      fields.push('report_list');
    } else if (ref_ids_type == 2) {
      fields.push('ref_ids2');
    }
    return fields;
  };

  // customValidator = (values: any) => {
  //   const length = this.formListRef.current?.total ?? 0;
  //   if (length > 0 && length < 3) {
  //     this.props.form.setFields({
  //       length: {
  //         errors: ['为保证客户端显示效果，请至少添加3张图片，或全部删除'],
  //       },
  //     });
  //     return false;
  //   }
  //   return true;
  // };

  saveDataTransfer = (values: any) => {
    // console.log(values, this.state.hasJump)
    const {
      title,
      title_style,
      title_pic,
      position,
      report_list,
      ref_ids_type,
      style,
      round_carousel,
      title_slogan,
      ref_ids2,
      pic_url,
      list,
      ad_corner_show,
    } = values;
    const title_show = title_style >= 0;
    const data: any = {
      ref_type: 37,
      title,
      title_show,
      title_pic: title_style >= 1 ? title_pic : '',
      ref_ids_type,
      // style,
      round_carousel,
      title_slogan,
      pic_url,
      ad_corner_show: ad_corner_show == undefined ? true : ad_corner_show,
      ref_extensions: JSON.stringify(list || []),
    };
    if (position) {
      data.position = position;
    }
    if (title_show) {
      data.title_style = title_style;
    }
    if (ref_ids_type === 0) {
      // data.ref_ids = report_list.map((item: any) => item.id).join(',')
      data.ref_ids = report_list?.map((item: any) => item.id).join(',');
      // data.ref_extensions = JSON.stringify(report_list)
    }
    if (ref_ids_type == 2) {
      data.ref_ids2 = ref_ids2;
    }
    return data;
  };

  setEditData = (data: any) => {
    if (data) {
      const {
        circle = {},
        report_list = [],
        recommend: {
          article_id,
          title,
          show_title,
          title_style = 0,
          title_pic = '',
          position,
          style,
          jump_new_page,
          jump_url,
          cycle_carousel = true,
          nav_type,
          ref_ids_type = 0,
          ref_extensions,
          title_slogan,
          ref_ids2,
          pic_url,
          corner_show,
        },
      } = data;
      const extensions = ref_extensions ? JSON.parse(ref_extensions) : [];
      this.setState(
        {
          article_id,
          hasJump: jump_new_page,
          editData: {
            title,
            title_style: show_title ? title_style : -1,
            title_pic,
            pic_url,
            position,
            title_slogan,
            // style,
            // nav_type,
            // jump_model_url: jump_url,
            ref_ids_type,
            // circle_info: ref_ids_type === 0 ? undefined : circle.name,
            // circle,
            round_carousel: cycle_carousel,
            report_list: report_list,
            ref_ids2,
            list: extensions || [],
            corner_show,
          },
        },
        () => {
          if (!!ref_ids2) {
            this.handleRecommedSearch(ref_ids2);
          }
        }
      );
    } else {
      this.setState({
        editData: null,
      });
    }
  };

  handleCircleSearch = _.debounce((circleName: any) => {
    const {
      form: { getFieldsValue, setFieldsValue },
    } = this.props;
    const { circle_info } = getFieldsValue();
    let selectedCircleName = '';
    if (circle_info) {
      selectedCircleName = circle_info.split('-')[0];
    }
    if (selectedCircleName.indexOf(circleName) < 0) {
      setFieldsValue({ circle_info: undefined });
      this.setState({ circleOptions: [] });
    }
    if (!circleName) {
      return;
    }
    api
      .getCircleList({ current: 1, size: 100, enabled: true, keyword: circleName })
      .then((res) => {
        const {
          data: {
            list: { records },
          },
        } = res as any;
        this.setState({ circleOptions: records });
      })
      .catch(() => {});
  }, 500);

  editPic = (url: string, index: number) => {
    let pic: string = url;
    let modal: any;
    const picChange = (u: string) => {
      pic = u;
      modal.update({
        content: (
          <>
            <ImageUploader value={pic} onChange={picChange} ratio={1} />
            <p style={{ marginTop: 5, color: '#aaa' }}>
              推荐位列表图实际展示比例为1:1，非正方形图片将自动居中截取，如效果不佳可重新上传
            </p>
          </>
        ),
      });
    };
    modal = Modal.confirm({
      title: '编辑图片',
      content: (
        <>
          <ImageUploader value={pic} onChange={picChange} ratio={1} />
          <p style={{ marginTop: 5, color: '#aaa' }}>
            推荐位列表图实际展示比例为1:1，非正方形图片将自动居中截取，如效果不佳可重新上传
          </p>
        </>
      ),
      onOk: (destroy: Function) => {
        if (!pic) {
          message.error('请上传图片');
          return;
        }
        const articles = [...this.props.form.getFieldsValue().channelArticles];
        articles[index].picUrl = pic;
        this.props.form.setFieldsValue({ channelArticles: articles });
        destroy();
      },
    });
  };

  onAddItem = (item: any) => {
    /*
      视频，取视频封面图
      短图文，取第一张图
      长图文，取封面图
    */
    // item.picUrl = item.list_pics
  };

  handleStyleChange = (style: number) => {
    const { getFieldError, getFieldValue, setFields } = this.props.form;
    const [err] = getFieldError('channelArticles') || [''];
    const value = getFieldValue('channelArticles') || [];
    if (value.length >= 3 || err.startsWith('为保证客户端显示效果')) {
      let errors;
      if ((style === 42 && value.length >= 4) || (style === 41 && value.length >= 3)) {
        errors = null;
      } else {
        errors = [new Error(`为保证客户端显示效果，关联新闻数不能少于${style === 42 ? 4 : 3}条！`)];
      }
      setFields({ channelArticles: { value, errors } });
    }
  };

  handleRecommedSearch = _.debounce((val: any) => {
    // 'channel_id': searchToObject().channel_id,
    searchApi
      .listArticleRecommendSearch({ doc_types: '-1', keyword: val })
      .then((result: any) => {
        const list =
          result?.data?.article_list?.filter((article: any) => {
            return article.ref_type == 37 && article.id != this.state.article_id;
          }) || [];
        this.setState({ ...this.state, recommendList: list });
      })
      .catch((err) => {});
  }, 500);
  render() {
    const {
      form: { getFieldDecorator, getFieldsValue, getFieldValue },
      isEdit,
    } = this.props;
    const columns: any = [
      {
        title: '报料编号',
        dataIndex: 'number',
        width: 120,
      },
      {
        title: '业务类型',
        key: 'biz_type',
        dataIndex: 'biz_type',
        width: 90,
        render: (text: any) => {
          return <span>{bizTypeMap(text) || ''}</span>;
        },
      },
      {
        title: '报料标题',
        key: 'title',
        dataIndex: 'title',
      },
    ];

    const styleTip = (style: number) => (
      <img
        src={`/assets/${style === 0 ? 'communityBigCard' : 'communitySmallCard'}.png`}
        width={150}
        height={style === 0 ? 113 : 66}
      />
    );
    const editData: any = this.state.editData || {
      ref_ids_type: isEdit ? undefined : 0,
      round_carousel: true,
      list: [],
      corner_show: true,
    };
    const values = getFieldsValue();
    const { ref_ids_type = editData.ref_ids_type } = values;
    const { hasJump } = this.state;
    let minLength = 3;

    return (
      <Form {...RecommendFormLayout}>
        <RecommendCommon {...this.props} editData={editData} isReport={true} />

        <Form.Item label="一句话口号">
          {getFieldDecorator('title_slogan', {
            rules: [
              {
                max: 20,
                message: '最多输入20个字符',
                whitespace: true,
              },
            ],
            initialValue: editData.title_slogan || '',
          })(<Input placeholder="请输入口号，最多20字"></Input>)}
        </Form.Item>

        {/* <Form.Item
          label="推荐位背景图"
          extra="支持上传jpg,jpeg,png图片格式, 比例未限制,前台显示的宽度固定为345px"
        >
          {getFieldDecorator('pic_url', {
            rules: [
              {
                required: true,
                message: '请上传推荐位背景图',
              },
            ],
            initialValue: editData.pic_url || '',
          })(<ImageUploader></ImageUploader>)}
        </Form.Item> */}
        <Divider type="horizontal"></Divider>

        <h3 style={{ marginLeft: 40 }}>
          图片推荐区
          <span
            style={{ fontSize: 14, color: 'rgba(0, 0, 0, 0.65)', fontWeight: 400, marginLeft: 10 }}
          >
            该区域仅在7.0及以后版本显示；选填，最多可添加20张图片
          </span>
        </h3>

        {this.formListRef.current?.total > 0 && (
          <Form.Item label="循环轮播">
            {getFieldDecorator('ad_corner_show', {
              rules: [
                {
                  required: true,
                  message: '请设置是否轮播',
                },
              ],
              initialValue: editData.corner_show,
              valuePropName: 'checked',
            })(<Switch />)}
          </Form.Item>
        )}

        <TMFormList
          ref={this.formListRef}
          dataList={editData.list}
          form={this.props.form}
          fromItem={() => {
            return {
              pic_url: '',
              link_url: '',
            };
          }}
          filed="list"
        >
          {(item: any, i: any, total: number) => (
            <Row key={i}>
              <FormListTitle
                allowClear={true}
                total={total}
                i={i}
                title="图片"
                upMove={() => this.formListRef.current?.upMove(i)}
                downMove={() => this.formListRef.current?.downMove(i)}
                removeItem={() => this.formListRef.current?.removeItem(i)}
              ></FormListTitle>
              <Row>
                <Form.Item label="上传图片" extra="支持上传jpg,jpeg,png图片格式，比例为110:40">
                  {getFieldDecorator(`list[${i}].pic_url`, {
                    initialValue: item.pic_url,
                    rules: [
                      {
                        required: true,
                        message: '请上传图片',
                      },
                    ],
                  })(<ImageUploader ratio={110 / 40} />)}
                </Form.Item>
                <Form.Item label="跳转链接">
                  {getFieldDecorator(`list[${i}].link_url`, {
                    initialValue: item.link_url,
                    rules: [
                      {
                        required: true,
                        message: '请输入跳转链接',
                      },
                      {
                        pattern: /^https?:\/\//,
                        message: '请输入正确的URL',
                      },
                    ],
                  })(<Input placeholder="请输入输入跳转链接" />)}
                </Form.Item>
              </Row>
            </Row>
          )}
        </TMFormList>

        <Row style={{ textAlign: 'center', marginBottom: 16 }} id="add-btn-line">
          <Button
            onClick={() => this.formListRef.current?.addItem()}
            disabled={(this.formListRef.current?.total ?? 0) >= 20}
          >
            添加图片
          </Button>
        </Row>
        <Form.Item style={{ paddingLeft: 160 }}>
          {getFieldDecorator('length', {
            rules: [
              {
                validator: (rule: any, value: any, callback: any) => {
                  if (value > 0 && value < 3) {
                    callback('为保证客户端显示效果，请至少添加3张图片，或全部删除');
                  } else {
                    callback();
                  }
                },
              },
            ],
          })(<></>)}
        </Form.Item>

        <Divider type="horizontal"></Divider>

        <h3 style={{ marginLeft: 40 }}>报料推荐区</h3>
        <Form.Item label="循环轮播">
          {getFieldDecorator('round_carousel', {
            rules: [
              {
                required: true,
                message: '请设置是否轮播',
              },
            ],
            initialValue: editData.round_carousel,
            valuePropName: 'checked',
          })(<Switch />)}
          <span style={{ marginLeft: 10 }}>
            开关仅在7.0版本之前的报料推荐位生效，7.0及以后版本不支持自动滚动效果
          </span>
        </Form.Item>
        <Form.Item label="数据来源">
          {getFieldDecorator('ref_ids_type', {
            initialValue: editData.ref_ids_type,
            rules: [
              {
                required: true,
                message: '请选择样式',
              },
            ],
          })(
            <Radio.Group>
              <Radio value={0}>手动添加指定报料</Radio>
              <Radio value={1}>
                自动读取最新报料&emsp;
                <Tooltip title="最新有进展变化的，跟进中/已办结的报料（包括问政/帮办/帮帮团/应急求助/小店帮类型）">
                  <Icon type="question-circle" />
                </Tooltip>
              </Radio>
              <Radio value={2}>
                使用其他推荐位数据&emsp;
                <Tooltip title="自动同步当前频道或其他频道的报料推荐位数据；原报料推荐位设置了隐藏或删除时，当前报料推荐位也将无法正常显示">
                  <Icon type="question-circle" />
                </Tooltip>
              </Radio>
            </Radio.Group>
          )}
        </Form.Item>
        {getFieldValue('ref_ids_type') == 0 && (
          <Form.Item label="选择报料">
            {getFieldDecorator('report_list', {
              initialValue: editData.report_list,
              preserve: true,
              rules: [
                {
                  required: true,
                  message: '请关联内容',
                  type: 'array',
                },
                {
                  max: 20,
                  message: '最多关联20条内容',
                  type: 'array',
                },
                {
                  min: minLength,
                  message: `为保证客户端显示效果，报料数不能少于${minLength}条！`,
                  type: 'array',
                },
              ],
            })(
              <SearchAndInput
                max={20}
                func="searchRecommendReport"
                columns={columns}
                placeholder="输入编号或标题选择报料"
                body={{ audit_status: '1', status: '1,2,3' }}
                order={true}
                addOnTop={true}
                categoryTip="报料"
                onAddItem={this.onAddItem}
                indexKey={'list'}
                tips={'最多添加20条，仅限审核通过且处理状态为跟进中/已办结的报料'}
                selectMap={(v: any) => {
                  return `${v.number}-【${bizTypeMap(v.biz_type, '')}】${v.title}`;
                }}
              />
            )}
          </Form.Item>
        )}

        {getFieldValue('ref_ids_type') == 2 && (
          <Form.Item label="选择推荐位">
            {getFieldDecorator('ref_ids2', {
              initialValue: editData.ref_ids2 || undefined,
              preserve: true,
              rules: [{ required: true, message: '请选择要使用哪个推荐位的数据' }],
            })(
              <Select
                // value={reporter}
                placeholder="输入推荐位名称或ID"
                onSearch={(v) => this.handleRecommedSearch(v)}
                showSearch
                filterOption={false}
              >
                {this.state.recommendList.map((d: any) => (
                  <Select.Option key={`${d.id}`} value={`${d.id}`}>
                    {d.id}-【{d.channel_name}】{d.list_title}
                  </Select.Option>
                ))}
              </Select>
            )}
          </Form.Item>
        )}
      </Form>
    );
  }
}
