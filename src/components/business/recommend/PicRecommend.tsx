import React from 'react';
import AddForm from '@components/business/addForm';
import { releaseListApi } from '@app/api';
import { searchToObject } from '@app/utils/utils';

export default class PicRecommend extends React.Component<any> {
  state = {
    bannerData: Array(2).fill({
      pic_url: '',
      link_url: '',
      link_type: 0,
      channelArticles: [],
      columnList: [],
    }),
    bannerObj: { title: '', position: '', style: 0, round_carousel: false },
    id: undefined,
  };

  constructor(props: any) {
    super(props);
    this.props.wrappedComponentRef.current = this;
  }

  setFormItem = () => {
    return {
      pic_url: '',
      link_url: '',
      link_type: 0,
      channelArticles: [],
      columnList: [],
    };
  };

  getUpdateDate = (values: any) => {
    const { title, title_style, title_pic, position, style, round_carousel, jump_model_url } =
      values;
    const title_show = title_style >= 0;
    const data: any = {
      title,
      title_show,
      title_pic: title_style >= 1 ? title_pic : '',
      article_category: this.props.isCommunity ? 1 : 0,
      style,
      ref_type: 29,
      round_carousel,
      channel_id: searchToObject().channel_id,
      jump_model_url: jump_model_url || '',
      jump_enabled: Boolean(jump_model_url),
      ref_extensions: JSON.stringify(
        values.json.map((item: any) => {
          const { pic_url, pic, link_type, columnList, channelArticles } = item;
          let link_id;
          let link_url;
          if (link_type === 1) {
            link_id = columnList[0].id;
            link_url = columnList[0].url;
          } else if (link_type === 2) {
            link_id = channelArticles[0].uuid || channelArticles[0].id;
            link_url = channelArticles[0].url;
          } else {
            link_id = '';
            link_url = item.link_url || '';
          }
          return {
            pic_url: pic.url,
            link_url,
            link_type,
            link_id,
          };
        })
      ),
      id: this.state.id,
    };
    if (position) {
      data.position = position;
    }
    if (title_show) {
      data.title_style = title_style;
    }
    if (!data.id) {
      delete data.id;
    }
    return data;
  };

  setEditData = (data: any) => {
    if (data) {
      const {
        recommend: {
          id,
          title,
          show_title,
          title_style = 0,
          title_pic = '',
          position,
          style,
          cycle_carousel = false,
          jump_enabled = false,
          jump_model_url = '',
        },
        pic_list,
      } = data;
      this.setState(
        {
          id,
          bannerData: pic_list.map((item: any) => {
            const data: any = {
              pic_url: item.pic_url,
              link_url: item.link_url,
              link_type: item.link_type,
            };
            if (item.link_type === 1) {
              item.topic_list.forEach((item: any) => {
                item.created_user_name = item.created_by;
              });
              data.columnList = item.topic_list;
            } else if (item.link_type === 2) {
              data.channelArticles = item.article_list.map((v: any) => ({
                channel_name: v.channel_name,
                list_title: v.list_title,
                id: v.id,
                uuid: v.uuid || item.link_id,
                url: v.url,
              }));
            }
            return data;
          }),
          bannerObj: {
            title,
            title_style: show_title ? title_style : -1,
            title_pic,
            position,
            style,
            round_carousel: cycle_carousel,
            jump_model_url,
            jump_enabled,
          },
        },
        () => {
          this.props.wrappedComponentRef.current.init();
        }
      );
    } else {
      this.setState({
        editData: null,
      });
    }
  };

  render() {
    const { isEdit, maxPosition } = this.props;
    return !isEdit || this.state.id ? (
      <AddForm
        isEdit={isEdit}
        maxPosition={maxPosition}
        setEditData={this.setEditData}
        bannerData={this.state.bannerData} //复现数据
        bannerObj={this.state.bannerObj} //复现数据
        changeVisible={this.props.onEnd}
        showLoading={this.props.showLoading}
        wrappedComponentRef={(instance: any) => (this.props.wrappedComponentRef.current = instance)}
        getUpdateDate={this.getUpdateDate} //获取要提交的数据
        setFormItem={this.setFormItem()} //设置表单item
        joggle={
          isEdit ? releaseListApi.updateContentRecommend : releaseListApi.createContentRecommend
        } // 接口
        componentNames={'图片推荐位'} //判断使用的组件
        addLabel={'添加一张图片'}
        headName={'图片'}
        listLengthTip={(style: number) => {
          return style == 63 ? '最多添加20张图片' : '最多添加8张图片';
        }}
        listLength={(style: number, ref_ids3: number) => {
          return { 0: 8, 61: 8, 62: 8, 63: 20 }[style];
        }}
        disabled={{ 0: 2, 61: 4, 62: 4, 63: 4 }}
      />
    ) : null;
  }
}
