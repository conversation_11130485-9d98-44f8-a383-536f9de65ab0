import React from "react"
import Recommend<PERSON><PERSON><PERSON>, { RecommendFormLayout } from "./RecommendCommon"
import { SearchAndInput } from '@components/common'
import { Form, Icon, Tooltip } from "antd"
import Radio from "antd/es/radio"

// 话题推荐位
@(Form.create({ name: 'TopicRecommend' }) as any)
export default class TopicRecommend extends React.Component<any> {

  state = {
    editData: null,
    topicStyles: {}
  }

  saveDataTransfer = (values: any) => {
    // console.log('aaaaa', values)
    const { style, title, title_style, title_pic, position, ref_ids } = values
    const title_show = title_style >= 0
    const data = {
      ...values,
      article_category: this.props.isCommunity ? 1 : 0,
      title,
      title_show: style === 51 ? title_show : true,
      title_pic: title_style >= 1 ? title_pic : '',
      ref_type: 26,
    }
    if (!position) {
      delete data.position
    }
    if (!title_show) {
      delete data.title_style
    }
    if (style === 51) {
      const topicStyles: any = this.state.topicStyles
      data.ref_extensions = JSON.stringify(ref_ids.map((id: any) => ({ topic_id: id, topic_style: topicStyles[id] || 1 })))
    }
    return data
  }


  setEditData = (data: any) => {
    if (data) {
      const { topic_list, recommend: { title, show_title, title_style = 0, title_pic = '', position, style, ref_extensions } } = data
      const state = {
        editData: {
          title,
          title_style: show_title ? title_style : -1,
          title_pic,
          style,
          position,
          ref_ids: topic_list.map((item: any) => item.id),
          topic_list,
        },
        topicStyles: {} as any
      }
      if (style === 51) {
        const extensions = JSON.parse(ref_extensions)
        extensions.forEach((item: any) => state.topicStyles[item.topic_id] = item.topic_style)
      }
      this.setState(state)
    } else {
      this.setState({
        editData: null,
        topicStyles: {}
      })
    }
  }

  changeTopicStylesVal = (id: string, val: any) => {
    this.setState({
      topicStyles: {
        ...this.state.topicStyles,
        [id]: val
      }
    })
  }

  topicStyleChange = (id: string, val: any) => {
    this.changeTopicStylesVal(id, val)
  }

  onAddItem = (id: string) => {
    this.changeTopicStylesVal(id, 1)
  }

  onRemoveItem = (id: string) => {
    const topicStyles: any = { ...this.state.topicStyles }
    delete topicStyles[id]
    this.setState({ topicStyles })
  }

  render() {
    const { form: { getFieldDecorator, getFieldsValue } } = this.props
    const columns: any = [
      {
        title: '话题名称',
        key: 'name',
        dataIndex: 'name',
      },
    ]

    const styleTip = (style: number) => <img src={`/assets/${style === 0 ? 'topic_style1' : 'topic_style2'}.png`} width={150} height={style === 0 ? 72 : 85} />
    const editData: any = this.state.editData || {}
    const { isEdit } = this.props
    const { style = editData.style } = getFieldsValue()

    if (style === 51) {
      columns.push({
        title: '展示样式',
        key: 'topic_style',
        width: 300,
        render: (_: number, record: any, index: number) => {
          const topicStyles: any = this.state.topicStyles
          return <Radio.Group
            value={topicStyles[record.id] || 1}
            onChange={(e) => this.topicStyleChange(record.id, e.target.value)}>
            <Radio value={1}>紫色</Radio>
            <Radio value={2}>绿色</Radio>
            <Radio value={3}>橙色</Radio>
            <Radio value={4}>蓝色</Radio>
          </Radio.Group>
        }
      })
    }

    return (
      <Form {...RecommendFormLayout}>
        <RecommendCommon hiddenTitleType={style !== 51} {...this.props} editData={editData} />
        <Form.Item label="显示样式">
          {getFieldDecorator('style', {
            initialValue: isEdit ? editData.style : 0,
            rules: [{
              required: true,
              message: '请选择样式',
            }],
          })(
            <Radio.Group>
              <Radio value={0}>样式一&emsp;
                <Tooltip title={styleTip(0)}>
                  <Icon type="question-circle" />
                </Tooltip></Radio>
              <Radio value={51}>样式二&emsp;
                <Tooltip title={styleTip(1)}>
                  <Icon type="question-circle" />
                </Tooltip>
              </Radio>
            </Radio.Group>
          )}
        </Form.Item>
        {(!isEdit || editData.topic_list) &&
          <Form.Item label="推荐话题">
            {getFieldDecorator("ref_ids", {
              initialValue: editData.ref_ids,
              rules: [
                {
                  required: true,
                  message: "请选择推荐话题"
                },
                {
                  max: 10,
                  message: '最多关联10个话题',
                  type: 'array',
                },
                {
                  min: 2,
                  message: '为保证客户端显示效果，关联话题数不能少于2个！',
                  type: 'array',
                }
              ]
            })(
              <SearchAndInput
                max={10}
                func="recommendTopicSearchList"
                funcIndex='list'
                columns={columns}
                order={true}
                placeholder="请输入话题名称"
                initialValues={{ list: editData.topic_list }}
                body={{ type: '10' }}
                searchKey={'name'}
                apiWithPagination={true}
                displayChannel={'name'}
                selectOptionDisplay={(record: any) => `${record.name}`}
                onAddItem={this.onAddItem}
                onRemoveItem={this.onRemoveItem}
              />
            )}
          </Form.Item>}
      </Form>
    )
  }
}
