import React from 'react';
import { SearchAndInput } from '@components/common';
import { Form, Icon, Switch, Popover, Tooltip } from 'antd';
import moment from 'moment';
import Radio from 'antd/es/radio';
import RecommendCommon, { RecommendFormLayout } from './RecommendCommon';

// 地方号推荐位
@(Form.create({ name: 'ColumnRecommend' }) as any)
export default class ColumnRecommend extends React.Component<any> {
  state = {
    editData: null,
  };

  saveDataTransfer = (values: any) => {
    const { title, title_style, title_pic, position } = values;
    const title_show = title_style >= 0;
    const data = {
      ...values,
      article_category: 0,
      title,
      title_show,
      title_pic: title_style >= 1 ? title_pic : '',
      ref_type: 22,
    };
    if (position) {
      data.position = position;
    }
    if (title_show) {
      data.title_style = title_style;
    }
    return data;
  };

  setEditData = (data: any) => {
    if (data) {
      const {
        column_list,
        recommend: { title, show_title, title_style = 0, title_pic = '', style, cycle_carousel },
      } = data;
      const ref_ids: any = [];
      column_list.forEach((item: any) => {
        ref_ids.push(item.id);
        item.created_user_name = item.created_by;
      });
      this.setState({
        editData: {
          title,
          title_style: show_title ? title_style : -1,
          title_pic,
          style,
          round_carousel: cycle_carousel,
          ref_ids,
          channelArticles: column_list,
        },
      });
    } else {
      this.setState({
        editData: null,
      });
    }
  };

  render() {
    const {
      form: { getFieldDecorator },
    } = this.props;
    const styleTips = [
      <img src="/assets/column_style0_tip.jpg" width="230" height="35" />,
      <img src="/assets/column_style1_tip.jpg" width="230" height="66" />,
      <img src="/assets/column_style2_tip.jpg" width="230" height="86" />,
      <img src="/assets/column_style3_tip.png" width="230" height="95" />
    ];
    const columns = [
      { title: '地方号名称', dataIndex: 'name' },
      { title: '创建人', dataIndex: 'created_user_name' },
      {
        title: '创建时间',
        dataIndex: 'created_at',
        render: (text: number) => moment(text).format('YYYY-MM-DD'),
      },
    ];
    const editData: any = this.state.editData || { round_carousel: true, style: 33 };
    const { isEdit } = this.props;

    return (
      <Form {...RecommendFormLayout}>
        <RecommendCommon {...this.props} editData={editData} />
        <Form.Item label="循环轮播">
          {getFieldDecorator('round_carousel', {
            initialValue: editData.round_carousel,
            valuePropName: 'checked',
          })(<Switch />)}
        </Form.Item>
        <Form.Item label="样式选择">
          {getFieldDecorator('style', {
            initialValue: editData.style,
            rules: [
              {
                required: true,
                message: '请输选择样式',
                type: 'number',
              },
            ],
          })(
            <Radio.Group>
              {/* <Radio value={31}>
                样式一&emsp;
                <Tooltip title={styleTips[0]}>
                  <Icon type="question-circle" />
                </Tooltip>
              </Radio>
              <Radio value={32}>
                样式二&emsp;
                <Tooltip title={styleTips[1]}>
                  <Icon type="question-circle" />
                </Tooltip>
              </Radio> */}
              <Radio value={33}>
                样式一&emsp;
                <Tooltip title={styleTips[2]}>
                  <Icon type="question-circle" />
                </Tooltip>
              </Radio>
              <Radio value={34}>
                样式二&emsp;
                <Tooltip title={styleTips[3]}>
                  <Icon type="question-circle" />
                </Tooltip>
              </Radio>
            </Radio.Group>
          )}
        </Form.Item>

        {(!isEdit || editData.channelArticles) && (
          <Form.Item label="关联地方号" required={true}>
            {getFieldDecorator('ref_ids', {
              initialValue: editData.ref_ids,
              rules: [
                {
                  required: true,
                  message: '请关联地方号',
                  type: 'array',
                },
                {
                  max: 30,
                  message: '最多关联30条地方号',
                  type: 'array',
                },
              ],
            })(
              <SearchAndInput
                max={30}
                func="searchUGCTopic"
                columns={columns}
                placeholder="输入名称搜索地方号"
                initialValues={{ list: editData.channelArticles }}
                body={{ type: 0 }}
                order={true}
                addOnTop={true}
                searchKey="name"
                funcIndex="list"
                apiWithPagination={true}
                selectOptionDisplay={(record: any) => `#${record.name}#`}
              />
            )}
          </Form.Item>
        )}
      </Form>
    );
  }
}
