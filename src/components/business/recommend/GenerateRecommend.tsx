import React from 'react';
import AddForm from '@components/business/addForm';
import { releaseListApi } from '@app/api';
import { searchToObject } from '@app/utils/utils';

function formItemTemplate() {
  return {
    title: '',
    link_url: '',
    pic_url: '',
    link_type: 0,
    channelArticles: [],
    columnList: [],
    summary: '',
    colorInfo: {
      color_type: 1,
      auto_color: null,
      bottom_color: null,
    },
  };
}

export default class GenerateRecommend extends React.Component<any> {
  state = {
    bannerData: Array(3).fill(formItemTemplate()),
    bannerObj: { title: '', position: '', style: 71, round_carousel: false },
    id: undefined,
  };

  constructor(props: any) {
    super(props);
    this.props.wrappedComponentRef.current = this;
  }

  getUpdateDate = (values: any) => {
    const { title, title_style, title_pic, position, style, round_carousel, ref_ids3 } = values;
    const title_show = title_style >= 0;
    const data: any = {
      ref_ids3,
      title,
      title_show,
      title_pic: title_style >= 1 ? title_pic : '',
      article_category: 0,
      style,
      ref_type: 33,
      channel_id: searchToObject().channel_id,
      round_carousel,
      ref_extensions: JSON.stringify(
        values.json.map((item: any) => {
          const {
            title,
            link_type,
            pic_url,
            columnList,
            channelArticles,
            summary,
            colorInfo,
            pic,
          } = item;
          let link_id;
          let link_url;
          if (link_type === 1) {
            link_id = columnList[0].id;
            link_url = columnList[0].url;
          } else if (link_type === 2) {
            link_id = channelArticles[0].uuid || channelArticles[0].id;
            link_url = channelArticles[0].url;
          } else {
            link_id = '';
            link_url = item.link_url || '';
          }
          const data: any = {
            title,
            link_url,
            link_type,
            link_id,
          };
          if (style === 72 || style === 73) {
            data.pic_url = pic.url;
          }
          if (style === 73) {
            data.summary = summary;
            data.bg_color = colorInfo.bottom_color;
            data.color_type = colorInfo.color_type;
          }
          return data;
        })
      ),
      id: this.state.id,
    };
    if (position) {
      data.position = position;
    }
    if (title_show) {
      data.title_style = title_style;
    }
    if (!data.id) {
      delete data.id;
    }
    return data;
  };

  setEditData = (data: any) => {
    if (data) {
      const {
        recommend: {
          id,
          title,
          show_title,
          title_style = 0,
          title_pic = '',
          position,
          style,
          cycle_carousel,
          ref_ids3,
        },
        common_list,
      } = data;
      this.setState(
        {
          id,
          bannerData: common_list.map((item: any) => {
            const data: any = {
              title: item.title,
              link_url: item.link_url,
              link_type: item.link_type,
            };
            if (style === 72 || style === 73) {
              data.pic_url = item.pic_url;
            }
            if (style === 73) {
              data.summary = item.summary;
            }
            if (item.link_type === 1) {
              item.topic_list.forEach((item: any) => {
                item.created_user_name = item.created_by;
              });
              data.columnList = item.topic_list;
            } else if (item.link_type === 2) {
              data.channelArticles = item.article_list.map((v: any) => ({
                channel_name: v.channel_name,
                list_title: v.list_title,
                id: v.id,
                uuid: v.uuid || item.link_id,
                url: v.url,
              }));
            }
            const color_type = item.color_type === 0 ? 0 : 1;
            const bottom_color = item.bg_color || null;
            data.colorInfo = {
              color_type,
              auto_color: color_type === 1 ? bottom_color : null,
              bottom_color,
            };
            return data;
          }),
          bannerObj: {
            title,
            title_style: show_title ? title_style : -1,
            title_pic,
            position,
            style,
            round_carousel: cycle_carousel,
            ref_ids3,
          },
        },
        () => {
          this.props.wrappedComponentRef.current.init();
        }
      );
    } else {
      this.setState({
        editData: null,
      });
    }
  };

  render() {
    console.log('123123');

    const { isEdit, maxPosition } = this.props;
    return !isEdit || this.state.id ? (
      <AddForm
        isEdit={isEdit}
        maxPosition={maxPosition}
        setEditData={this.setEditData}
        bannerData={this.state.bannerData} //复现数据
        bannerObj={this.state.bannerObj} //复现数据
        changeVisible={this.props.onEnd}
        showLoading={this.props.showLoading}
        wrappedComponentRef={(instance: any) => (this.props.wrappedComponentRef.current = instance)}
        getUpdateDate={this.getUpdateDate} //获取要提交的数据
        setFormItem={formItemTemplate()} //设置表单item
        joggle={
          isEdit ? releaseListApi.updateContentRecommend : releaseListApi.createContentRecommend
        } // 接口
        componentNames="通用推荐位" //判断使用的组件
        addLabel="添加一条内容"
        headName="内容"
        listLengthTip={(style: number, ref_ids3: number) => {
          return `最多添加${style == 72 && ref_ids3 == 2 ? 50 : 20}条内容`;
        }}
        listLength={(style: number, ref_ids3: number) => {
          return style == 72 && ref_ids3 == 2 ? 50 : 20;
        }}
        disabled={{ 71: 3, 72: 4, 73: 4 }}
      />
    ) : null;
  }
}
