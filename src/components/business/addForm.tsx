import {
  Form,
  Input,
  message,
  Row,
  <PERSON><PERSON>,
  Divider,
  Col,
  Icon,
  Tooltip,
  Checkbox,
  Switch,
  Popconfirm,
} from 'antd';
import React from 'react';
import connectSession from '@utils/connectSession';
import '../../views/recommend/components/operationsPage.scss';
import scrollIntoView from 'dom-scroll-into-view';
import _ from 'lodash';
import FromItem from '@views/recommend/components/fromItem';
import CommunityFormItem from '@views/community/component/communityFormItem';
import PicFormItem from '@views/community/component/picFormItem';
import GenerateFormItem from '@views/community/component/generateFormItem';
import CircleFormItem from '@views/community/component/circleFormItem';
import RecommendCommon from './recommend/RecommendCommon';
import TmhFromItem from '@views/news/tmhFromItem';
import CompetitionItem from '@app/views/recommend/components/competitionItem';
import Radio from 'antd/es/radio';
import AggregationFormItem from '@app/views/community/component/AggregationFormItem';

@connectSession
@(Form.create({ name: 'AddForm' }) as any)
class AddForm extends React.Component<any, any> {
  constructor(props: any) {
    super(props);
    this.init(true);
  }

  init = (isInit: boolean) => {
    const props = this.props;
    const s = {
      json: props.bannerData?.focus_list?.records || props.bannerData || [],
    };
    const objectedJson: any = {};
    const keys = s.json.map((v: any, i: number) => {
      objectedJson[i] = v;
      return i;
    });
    const state = {
      bannerObj: props.bannerObj || {},
      v: '',
      ...s,
      json: objectedJson,
      keys,
      maxKey: keys[keys.length - 1],
      optionData: [],
      ref_ids: [],
      hasJump: props.bannerObj?.jump_enabled || false,
    };
    if (isInit) {
      this.state = state;
    } else {
      this.setState({ ...state });
      this.getRef_ids();
    }
  };

  setEditData = (data: any) => {
    if (this.props.setEditData) {
      this.props.setEditData(data);
    }
  };
  handleSubmit = (e?: any) => {
    if (e) {
      e.preventDefault();
    }
    this.doSubmit();
  };
  componentDidMount = () => {
    this.getRef_ids();
  };
  //id获取
  getRef_ids = () => {
    const { getFieldsValue, setFieldsValue } = this.props.form;
    const values = getFieldsValue();
    let ref_id: any = [];
    if (this.props.componentNames == '用户管理位' || this.props.componentNames === '圈子推荐位') {
      this.props.bannerData.forEach((el: any) => {
        ref_id.push(el.id);
      });
      // console.log('aaa', ref_id)
    } else {
      this.props.bannerData?.focus_list?.records.forEach((el: any, i: any) => {
        ref_id.push(el.channel_article_id);
        values.json[i].source_list_title = `${el.channel_article_id}-${el.source_list_title}`;
        this.state.json[i].source_list_title = `${el.channel_article_id}-${el.source_list_title}`;
      });
    }
    setFieldsValue({ ...values });
    this.setState({
      ref_ids: ref_id,
      json: this.state.json,
    });
  };
  //添加一图
  add = () => {
    const {
      form: { getFieldsValue },
      componentNames,
    } = this.props;
    let stateJson;
    if (
      componentNames === '图片推荐位' ||
      componentNames === '通用推荐位' ||
      componentNames === '聚合推荐位'
    ) {
      const values = getFieldsValue();
      stateJson = values.json;
    } else {
      stateJson = this.state.json;
    }

    const newKeys = [...this.state.keys, this.state.keys.length];
    const json = {
      ...stateJson,
      [this.state.keys.length]: {
        ...this.props.setFormItem,
      },
    };
    this.setState(
      {
        keys: newKeys,
        json: { ...json },
        data: [],
      },
      () => {
        scrollIntoView(
          document.getElementById('rolling-positioning'),
          document.getElementsByClassName('ant-drawer-wrapper-body')[0]
        );
      }
    );
  };
  //删除
  remove = (key: any) => {
    const { getFieldsValue, setFieldsValue } = this.props.form;
    this.state.ref_ids.splice(key, 1);
    const values = getFieldsValue();
    values['json'].splice(key, 1);
    setFieldsValue({
      ...values,
    });
    let keys = [];
    const newKeys = this.state.keys.filter((v: any) => v !== key);
    keys = newKeys.map((el: any, i: number) => i);
    this.setState(
      {
        keys: keys,
        ref_ids: this.state.ref_ids,
        json: { ...values['json'] },
      },
      () => {
        console.log(this.state.ref_ids, '回调');
      }
    );
  };

  //校验提交表单
  doSubmit = () => {
    this.props.form.validateFieldsAndScroll((err: any, values: any) => {
      if (!err) {
        if (this.props.showLoading) {
          this.props.showLoading(true);
        }
        const data = this.props.getUpdateDate(values, this.props.bannerData, this.state.ref_ids);
        const joggle: any = this.props.joggle;
        joggle(data)
          .then((res: any) => {
            message.success('操作成功');
            this.props.changeVisible(false);
            if (this.props.closeLoding) {
              this.props.closeLoding(false);
            }
            // 等弹窗隐藏了之后再重置loading状态，防止重复点击
            setTimeout(() => {
              if (this.props.showLoading) {
                this.props.showLoading(false);
              }
            }, 500);
          })
          .catch(() => {
            if (this.props.closeLoding) {
              this.props.closeLoding(false);
            }
            console.log(this.props);
            if (this.props.showLoading) {
              this.props.showLoading(false);
            }
          });
      } else {
        message.error('请检查表单内容');
        if (this.props.closeLoding) {
          this.props.closeLoding(false);
        }
      }
    });
  };
  //向上移动
  upMove = (v: any) => {
    const { getFieldsValue, setFieldsValue } = this.props.form;
    const value = getFieldsValue();
    const current = value.json[v];
    const next = value.json[v - 1];
    value.json[v] = next;
    value.json[v - 1] = current;
    setFieldsValue({
      ...value,
    });
    let obj = this.state.bannerObj;
    Object.keys(this.state.bannerObj).forEach((el) => {
      obj[el] = value[el];
    });
    this.setState(
      {
        json: [...value.json],
        bannerObj: obj,
      },
      () => {
        this.props.form.resetFields();
      }
    );
  };
  //向下移动
  downMove = (v: any) => {
    const { getFieldsValue, setFieldsValue } = this.props.form;
    const value = getFieldsValue();
    const current = value.json[v];
    const next = value.json[v + 1];
    value.json[v] = next;
    value.json[v + 1] = current;
    setFieldsValue({
      ...value,
    });
    let obj = this.state.bannerObj;
    Object.keys(this.state.bannerObj).forEach((el) => {
      obj[el] = value[el];
    });

    this.setState(
      {
        json: value.json,
        bannerObj: obj,
      },
      () => {
        this.props.form.resetFields();
        console.log(this.state.json, 'json回调');
      }
    );
  };
  changeRadio = (v: any, e: any) => {
    this.state.json[v].auto_title = e.target.value;
  };
  getTooltipTitle = () => {
    return (
      <>
        <div>1、需显示在哪个位置就输入对应数字，仅限1~9</div>
        <div>2、多个运营模块设置相同位置的数字时，轮播图先显示</div>
      </>
    );
  };
  titleChange = (v: number, e: any) => {
    this.state.json[v].title = e.target.value;
    this.setState({
      json: this.state.json,
    });
  };
  changeInput = (v: any) => {
    this.props.form.resetFields();
    const { getFieldsValue, setFieldsValue } = this.props.form;
    let val = getFieldsValue();
    val = v;
    setFieldsValue({
      ...val,
    });
    this.setState({
      json: { ...val.json },
    });
  };
  chooseSelectItem = (val: any, ids: [] = []) => {
    const { setFieldsValue } = this.props.form;
    setFieldsValue({
      ...val,
    });
    this.setState(
      {
        json: val.json,
        ref_ids: ids,
      },
      () => {
        // this.props.form.resetFields()
        // console.log(this.state.ref_ids);
      }
    );
  };

  handleStyleChange = (e: any) => {
    // 推荐位样式发生变化图片清空
    const { form, componentNames } = this.props;
    const { getFieldsValue, setFieldsValue } = form;
    const val = getFieldsValue();
    const style = e.target.value;
    // 默认直接清空，如果产品或者测试有要求，再弄数组保存之前的图片数据
    val.json.forEach((item: any) => {
      if (componentNames == '图片推荐位' || componentNames == '通用推荐位') {
      } else {
        item.pic_url = '';
      }
    });
    setFieldsValue({ ...val });
    // 获取最小值
    const minLength = this.props.disabled[style];
    let maxLength = 0;
    if (typeof this.props.listLength === 'number') {
      maxLength = this.props.listLength;
    } else {
      maxLength = this.props.listLength(style, val.ref_ids3);
    }

    const offset = minLength - val.json.length;
    let newKeys;
    if (offset > 0) {
      // 不足，进行添加
      newKeys = [...this.state.keys];
      for (let i = 0; i < offset; i++) {
        const k = newKeys.length;
        newKeys.push(k);
        val.json[k] = { ...this.props.setFormItem };
      }
    } else if (maxLength < val.json.length) {
      // 超过，进行删除
      newKeys = [...this.state.keys];
      const lenght = val.json.length - maxLength;
      for (let i = 0; i < lenght; i++) {
        newKeys.pop();
        val.json.pop();
      }
    }
    const newState: any = {
      json: { ...val.json },
    };
    if (newKeys) {
      newState.keys = newKeys;
    }
    this.setState(newState);
  };

  handleShowEffectChange = (e: any) => {
    const { form, componentNames } = this.props;
    const { getFieldsValue, setFieldsValue } = form;
    const val = getFieldsValue();
    const showEffect = e.target.value;
    // 获取最小值
    const minLength = this.props.disabled[val.style];
    let maxLength = 0;
    if (typeof this.props.listLength === 'number') {
      maxLength = this.props.listLength;
    } else {
      maxLength = this.props.listLength(val.style, val.ref_ids3);
    }

    const offset = minLength - val.json.length;
    let newKeys;
    if (offset > 0) {
      // 不足，进行添加
      newKeys = [...this.state.keys];
      for (let i = 0; i < offset; i++) {
        const k = newKeys.length;
        newKeys.push(k);
        val.json[k] = { ...this.props.setFormItem };
      }
    } else if (maxLength < val.json.length) {
      // 超过，进行删除
      newKeys = [...this.state.keys];
      const lenght = val.json.length - maxLength;
      for (let i = 0; i < lenght; i++) {
        newKeys.pop();
        val.json.pop();
      }
    }
    const newState: any = {
      json: { ...val.json },
    };
    if (newKeys) {
      newState.keys = newKeys;
    }
    this.setState(newState);
  };

  handleUserStyleChange = (e: any) => {
    const style = e.target.value;
    if (style === 0) {
      const {
        form: { setFields, getFieldsValue, getFieldError },
      } = this.props;
      const { json } = getFieldsValue();
      for (const key in json) {
        if (!json[key].reason) {
          // 获取是否有错误，如果有，就清除
          const fieldKey = `json[${key}].reason`;
          const errors = getFieldError(fieldKey);
          if (errors) {
            setFields({ [fieldKey]: { value: '', errors: null } });
          }
        }
      }
    }
  };

  render() {
    const {
      isEdit,
      form: { getFieldDecorator, setFieldsValue, getFieldsValue, getFieldValue },
      componentNames,
    } = this.props;
    const { style } = getFieldsValue();
    const formLayout = {
      labelCol: { span: 4 },
      wrapperCol: { span: 18 },
    };
    const picStyleTip = (style: number) => {
      let name;
      let height;
      switch (style) {
        case 0:
          name = 'pic_style0_tip';
          height = 49;
          break;
        case 1:
          name = 'pic_style1_tip';
          height = 115;
          break;
        case 2:
          name = 'pic_style2_tip';
          height = 77;
          break;
        case 3:
          name = 'pic_style3_tip';
          height = 57;
          break;
      }
      return <img src={`/assets/${name}.png`} width={200} height={height} />;
    };
    const generateStyleTip = (style: number) => {
      let name;
      let height;
      switch (style) {
        case 0:
          name = 'generate_style0_tip.jpg';
          height = 42;
          break;
        case 1:
          name = 'generate_style1_tip.png';
          height = 66;
          break;
        case 2:
          name = 'generate_style2_tip.svg';
          height = 89;
          break;
      }
      return <img src={`/assets/${name}`} width={230} height={height} />;
    };
    const userStyleTip = (style: number) => (
      <img
        src={`/assets/${style === 0 ? 'user_style0_tip.svg' : 'user_style1_tip.jpg'}`}
        width={210}
        height={style === 0 ? 115 : 75}
      />
    );

    let maxLength = 0;
    if (typeof this.props.listLength === 'number') {
      maxLength = this.props.listLength;
    } else {
      maxLength = this.props.listLength(style, getFieldsValue().ref_ids3);
    }

    console.log('maxLengthmaxLengthmaxLength', maxLength);

    return (
      <Form className="operations" {...formLayout} onSubmit={this.handleSubmit}>
        {/* {(componentNames == '推荐页运营位' || componentNames == '潮鸣号') && <Form.Item label="显示位置" labelAlign={'left'} className='form_radioform_radioform_radio'>
          {getFieldDecorator('position', {
            initialValue: this.props.bannerData.position,
            rules: [
              {
                required: true,
                message: '请输入显示位置',
              },
              {
                pattern: /^[1-9]$/,
                message: '请输入1-9的数字',
              }
            ],
          })(<Input style={{ width: '50%' }} maxLength={30} placeholder="请输入显示位置" />)}
          <Tooltip className='' title={this.getTooltipTitle()}>
            <Icon className='ant-tooltip_icon' type="question-circle-o" />
          </Tooltip>
        </Form.Item>
        } */}
        {(componentNames === '用户管理位' ||
          componentNames === '圈子推荐位' ||
          componentNames === '图片推荐位' ||
          componentNames === '通用推荐位' ||
          componentNames === '赛事日程推荐位' ||
          componentNames === '聚合推荐位') && (
          <RecommendCommon
            hiddenTitleType={componentNames === '圈子推荐位' || componentNames === '聚合推荐位'}
            form={this.props.form}
            editData={this.state.bannerObj}
            isEdit={isEdit}
            maxPosition={this.props.maxPosition}
          ></RecommendCommon>
        )}

        {/* {componentNames === '用户管理位' && (
          <Form.Item label="样式选择">
            {getFieldDecorator('style', {
              initialValue: this.state.bannerObj.style,
              rules: [
                {
                  required: true,
                  message: '请选择样式',
                },
              ],
            })(
              <Radio.Group onChange={this.handleUserStyleChange}>
                <Radio value={0}>
                  样式一&nbsp;
                  <Tooltip title={userStyleTip(0)}>
                    <Icon type="question-circle" />
                  </Tooltip>
                </Radio>
                <Radio value={91}>
                  样式二&nbsp;
                  <Tooltip title={userStyleTip(1)}>
                    <Icon type="question-circle" />
                  </Tooltip>
                </Radio>
              </Radio.Group>
            )}
          </Form.Item>
        )} */}

        {componentNames === '图片推荐位' && (
          <>
            <Form.Item label="样式选择">
              {getFieldDecorator('style', {
                initialValue: this.state.bannerObj.style,
                rules: [
                  {
                    required: true,
                    message: '请选择样式',
                  },
                ],
              })(
                <Radio.Group onChange={this.handleStyleChange}>
                  <Radio value={0}>
                    样式一&nbsp;
                    <Tooltip title={picStyleTip(0)}>
                      <Icon type="question-circle" />
                    </Tooltip>
                  </Radio>
                  <Radio value={61}>
                    样式二&nbsp;
                    <Tooltip title={picStyleTip(1)}>
                      <Icon type="question-circle" />
                    </Tooltip>
                  </Radio>
                  <Radio value={63}>
                    样式三&nbsp;
                    <Tooltip title={picStyleTip(3)}>
                      <Icon type="question-circle" />
                    </Tooltip>
                  </Radio>
                </Radio.Group>
              )}
            </Form.Item>
            <Form.Item className="ccr_more_jump" label="更多跳转" required>
              <Radio.Group
                value={this.state.hasJump}
                onChange={(e) => this.setState({ hasJump: e.target.value })}
              >
                <Radio value={false}>无跳转</Radio>
                <Radio value={true}>
                  有跳转
                  {this.state.hasJump &&
                    getFieldDecorator('jump_model_url', {
                      initialValue: this.state.bannerObj.jump_model_url,
                      rules: [
                        {
                          required: true,
                          message: '请输入要跳转的链接',
                        },
                        {
                          pattern: /^https?:\/\//,
                          message: '请填写正确的URL地址',
                        },
                      ],
                    })(<Input style={{ marginLeft: 8 }} placeholder="请输入要跳转的链接" />)}
                </Radio>
              </Radio.Group>
            </Form.Item>
            <Form.Item label="循环轮播">
              {getFieldDecorator('round_carousel', {
                initialValue: this.state.bannerObj.round_carousel,
                valuePropName: 'checked',
                rules: [
                  {
                    required: true,
                    message: '请选择是否循环轮播',
                  },
                ],
              })(<Switch />)}
            </Form.Item>
          </>
        )}

        {componentNames === '通用推荐位' && (
          <>
            <Form.Item label="样式选择">
              {getFieldDecorator('style', {
                initialValue: this.state.bannerObj.style,
                rules: [
                  {
                    required: true,
                    message: '请选择样式',
                  },
                ],
              })(
                <Radio.Group onChange={this.handleStyleChange}>
                  <Radio value={71}>
                    样式一&nbsp;
                    <Tooltip title={generateStyleTip(0)}>
                      <Icon type="question-circle" />
                    </Tooltip>
                  </Radio>
                  <Radio value={72}>
                    样式二&nbsp;
                    <Tooltip title={generateStyleTip(1)}>
                      <Icon type="question-circle" />
                    </Tooltip>
                  </Radio>
                  <Radio value={73}>
                    样式三&nbsp;
                    <Tooltip title={generateStyleTip(2)}>
                      <Icon type="question-circle" />
                    </Tooltip>
                  </Radio>
                </Radio.Group>
              )}
            </Form.Item>
            {getFieldValue('style') == 72 && (
              <Form.Item label="展示效果">
                {getFieldDecorator('ref_ids3', {
                  initialValue: `${this.state.bannerObj.ref_ids3 || 1}`,
                  rules: [
                    {
                      required: true,
                      message: '请选择展示效果',
                    },
                  ],
                })(
                  <Radio.Group onChange={this.handleShowEffectChange}>
                    <Radio value={'1'}>单行展示</Radio>
                    <Radio value={'2'}>双行展示</Radio>
                  </Radio.Group>
                )}
              </Form.Item>
            )}
            <Form.Item label="循环轮播">
              {getFieldDecorator('round_carousel', {
                initialValue: this.state.bannerObj.round_carousel,
                valuePropName: 'checked',
              })(<Switch />)}
            </Form.Item>
          </>
        )}

        {componentNames == '聚合推荐位' && (
          <Form.Item label="循环选中">
            {getFieldDecorator('round_carousel', {
              initialValue: this.state.bannerObj.round_carousel,
              valuePropName: 'checked',
            })(<Switch />)}
          </Form.Item>
        )}
        {componentNames == '圈子推荐位' && (
          <>
            <Form.Item label="样式选择">
              {getFieldDecorator('style', {
                initialValue: 111,
                rules: [
                  {
                    required: true,
                    message: '请选择样式',
                  },
                ],
              })(
                <Radio.Group onChange={this.handleStyleChange}>
                  <Radio value={111}>默认样式</Radio>
                </Radio.Group>
              )}
            </Form.Item>
            <Form.Item label="循环轮播">
              {getFieldDecorator('round_carousel', {
                initialValue: this.state.bannerObj.round_carousel,
                valuePropName: 'checked',
              })(<Switch />)}
            </Form.Item>
          </>
        )}

        {this.state.keys.map((v: any, i: number) => {
          return (
            <Row key={v}>
              <Row>
                <Divider type="horizontal" />
                {componentNames === '赛事日程推荐位' && (
                  <Col span={18} style={{ paddingLeft: 20 }}>
                    <span className="title">
                      {this.props.headName}
                      {i + 1}
                    </span>
                    {i === 0 && (
                      <span style={{ color: '#aaa', marginLeft: 10 }}>
                        客户端展示卡片顺序会按照进行中、未开始、已结束顺序自动排列展示
                      </span>
                    )}
                  </Col>
                )}
                {componentNames !== '赛事日程推荐位' && (
                  <Col span={18} style={{ paddingLeft: 60 }}>
                    {this.state.keys.length && i == 0 ? (
                      <Button
                        onClick={this.upMove.bind(this, v)}
                        className="btn_mar"
                        disabled
                        type="primary"
                        icon="up"
                      />
                    ) : (
                      <Button
                        onClick={this.upMove.bind(this, v)}
                        className="btn_mar"
                        type="primary"
                        icon="up"
                      />
                    )}
                    {this.state.keys.length && i != this.state.keys.length - 1 ? (
                      <Button
                        onClick={this.downMove.bind(this, v)}
                        className="btn_mar"
                        type="primary"
                        icon="down"
                      />
                    ) : (
                      <Button
                        onClick={this.downMove.bind(this, v)}
                        className="btn_mar"
                        disabled
                        type="primary"
                        icon="down"
                      />
                    )}
                    <span className="title">
                      {this.props.headName}
                      {i + 1}
                    </span>
                    {i === 0 && (
                      <span className="listLengthTip">
                        {typeof this.props.listLengthTip === 'string'
                          ? this.props.listLengthTip
                          : this.props.listLengthTip?.(
                              getFieldsValue().style,
                              getFieldsValue().ref_ids3
                            )}
                      </span>
                    )}
                  </Col>
                )}
                <Col span={6} style={{ textAlign: 'right' }}>
                  {componentNames == '聚合推荐位' ? (
                    <Popconfirm
                      title="确定要删除吗？"
                      onConfirm={() => this.remove(v)}
                      // onCancel={cancel}
                      icon={<Icon type="exclamation-circle" theme="twoTone" twoToneColor="red" />}
                      okText="确定"
                      cancelText="取消"
                      disabled={
                        this.state.keys.length <=
                        (typeof this.props.disabled === 'object'
                          ? this.props.disabled[style]
                          : this.props.disabled)
                      }
                    >
                      <Button
                        type="danger"
                        // onClick={() => this.remove(v)}
                        disabled={
                          this.state.keys.length <=
                          (typeof this.props.disabled === 'object'
                            ? this.props.disabled[style]
                            : this.props.disabled)
                        }
                      >
                        删除
                      </Button>
                    </Popconfirm>
                  ) : (
                    <Button
                      type="danger"
                      onClick={() => this.remove(v)}
                      disabled={
                        this.state.keys.length <=
                        (typeof this.props.disabled === 'object'
                          ? this.props.disabled[style]
                          : this.props.disabled)
                      }
                    >
                      删除
                    </Button>
                  )}
                </Col>
              </Row>
              <Row key={v}>
                <Col>
                  {componentNames === '推荐页运营位' && (
                    <FromItem
                      changeRadio={this.changeRadio}
                      chooseSelectItem={this.chooseSelectItem}
                      getFieldDecorator={getFieldDecorator}
                      getFieldsValue={getFieldsValue}
                      setFieldsValue={setFieldsValue}
                      ref_ids={this.state.ref_ids}
                      Form={Form}
                      json={this.state.json}
                      v={v}
                    />
                  )}
                  {componentNames === '图片推荐位' && (
                    <PicFormItem
                      chooseSelectItem={this.chooseSelectItem}
                      getFieldDecorator={getFieldDecorator}
                      getFieldsValue={getFieldsValue}
                      setFieldsValue={setFieldsValue}
                      form={this.props.form}
                      ref_ids={this.state.ref_ids}
                      Form={Form}
                      json={this.state.json}
                      bannerObj={this.props.bannerObj}
                      v={v}
                    />
                  )}
                  {componentNames === '通用推荐位' && (
                    <GenerateFormItem
                      chooseSelectItem={this.chooseSelectItem}
                      getFieldDecorator={getFieldDecorator}
                      getFieldsValue={getFieldsValue}
                      setFieldsValue={setFieldsValue}
                      ref_ids={this.state.ref_ids}
                      Form={Form}
                      form={this.props.form}
                      json={this.state.json}
                      bannerObj={this.props.bannerObj}
                      v={v}
                    />
                  )}
                  {componentNames === '圈子推荐位' && (
                    <CircleFormItem
                      chooseSelectItem={this.chooseSelectItem}
                      getFieldDecorator={getFieldDecorator}
                      getFieldsValue={getFieldsValue}
                      setFieldsValue={setFieldsValue}
                      ref_ids={this.state.ref_ids}
                      Form={Form}
                      json={this.state.json}
                      bannerObj={this.props.bannerObj}
                      v={v}
                      form={this.props.form}
                    />
                  )}
                  {componentNames === '用户管理位' && (
                    <CommunityFormItem
                      chooseSelectItem={this.chooseSelectItem}
                      getFieldDecorator={getFieldDecorator}
                      getFieldsValue={getFieldsValue}
                      setFieldsValue={setFieldsValue}
                      ref_ids={this.state.ref_ids}
                      Form={Form}
                      json={this.state.json}
                      bannerObj={this.props.bannerObj}
                      v={v}
                    />
                  )}
                  {componentNames === '潮鸣号' && (
                    <TmhFromItem
                      changeRadio={this.changeRadio}
                      chooseSelectItem={this.chooseSelectItem}
                      getFieldDecorator={getFieldDecorator}
                      getFieldsValue={getFieldsValue}
                      setFieldsValue={setFieldsValue}
                      ref_ids={this.state.ref_ids}
                      Form={Form}
                      json={this.state.json}
                      v={v}
                    />
                  )}
                  {componentNames === '赛事日程推荐位' && (
                    <CompetitionItem
                      chooseSelectItem={this.chooseSelectItem}
                      getFieldDecorator={getFieldDecorator}
                      getFieldsValue={getFieldsValue}
                      setFieldsValue={setFieldsValue}
                      ref_ids={this.state.ref_ids}
                      Form={Form}
                      json={this.state.json}
                      bannerObj={this.props.bannerObj}
                      v={v}
                    />
                  )}
                  {componentNames === '聚合推荐位' && (
                    <AggregationFormItem
                      chooseSelectItem={this.chooseSelectItem}
                      getFieldDecorator={getFieldDecorator}
                      getFieldsValue={getFieldsValue}
                      setFieldsValue={setFieldsValue}
                      ref_ids={this.state.ref_ids}
                      form={this.props.form}
                      json={this.state.json}
                      bannerObj={this.props.bannerObj}
                      v={v}
                      index={i}
                    />
                  )}
                </Col>
              </Row>
            </Row>
          );
        })}
        <Row
          id="rolling-positioning"
          className="add_btn"
          style={{ display: `${this.state.keys.length >= maxLength ? 'none' : ''}` }}
        >
          <Button onClick={this.add}>{this.props.addLabel}</Button>
        </Row>
      </Form>
    );
  }
}

export default AddForm;
