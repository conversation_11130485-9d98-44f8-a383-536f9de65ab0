import { sysApi as api, opApi as api2 } from '@app/api';
import { Form, Input, message, Select } from 'antd';
import React from 'react';
import { setMLoading } from '@utils/utils';
import connectSession from '@utils/connectSession';
import { ImageUploader } from '../common';

@connectSession
@(Form.create({ name: 'ColdStartForm' }) as any)
class ColdStartForm extends React.Component<any, any> {
  constructor(props: any) {
    super(props);
    const s = {
      title: '',
      ...this.props.formContent,
    };
    this.state = {
      ...this.props.formContent,
      // eslint-disable-next-line react/no-unused-state
      app_nav_list: [],
      // eslint-disable-next-line react/no-unused-state
      column_list: [],
    };

    console.log(this.props.formContent);
    this.getAssociatedChannel();
    this.getRelatedColumn();
  }

  handleSubmit = (e: any) => {
    e.preventDefault();
    this.doSubmit();
  };

  getAssociatedChannel = () => {
    api.getAppNavList({ category: 0 }).then((res: any) => {
      const filter = res.data.app_nav_list.records.filter(
        (item: any) => !item.collapsed && item.enabled
      );
      this.setState({
        // eslint-disable-next-line react/no-unused-state
        app_nav_list: filter,
      });
    });
  };

  getRelatedColumn = () => {
    api2.getColumnList({ current: 1, size: 100, is_studio: 1 }).then((res) => {
      this.setState({
        // eslint-disable-next-line react/no-unused-state
        column_list: res.data.list.records,
      });
    });
  };

  doSubmit = () => {
    this.props.form.validateFieldsAndScroll((err: any, values: any) => {
      if (!err) {
        setMLoading(this, true);
        const body = { ...values };
        const service = this.state.isEdit ? api.updateProposalWord : api.createProposalWord;
        if (this.state.isEdit) {
          body.id = this.state.detail.id;
        }
        if (!body.ref_nav_id) {
          delete body.ref_nav_id;
        }
        if (!body.ref_column_id) {
          delete body.ref_column_id;
        }
        service(body)
          .then(() => {
            setMLoading(this, false);
            message.success('添加成功');
            this.props.onEnd();
          })
          .catch(() => {
            setMLoading(this, false);
          });
      } else {
        message.error('请检查表单内容');
      }
    });
  };

  // validator = (rule: any, value: any, callback: Function) => {
  //   const values = this.props.form.getFieldsValue();
  //   console.log(values, rule, value);
  //   const key = parseInt(rule.field.replace(/json\[/g, '').replace(/\]\.name/g, ''), 10);
  //   const count = Object.keys(values.json).filter((k: any) => {
  //     const v = parseInt(k, 10);
  //     if (key !== v && value === values.json[v].name) {
  //       console.log('---true---', key, v, value, values.json[v].name);
  //       return true;
  //     }
  //     console.log('---false---', key, v, value, values.json[v].name);
  //     return false;
  //   }).length;
  //   if (count > 0) {
  //     callback('NAME不能重复');
  //   } else {
  //     callback();
  //   }
  // };

  render() {
    const { getFieldDecorator, getFieldsValue } = this.props.form;
    const formLayout = {
      labelCol: { span: 4 },
      wrapperCol: { span: 18 },
    };
    // const values = getFieldsValue();
    // const json = { ...this.state.json, ...(values.json ? values.json : {}) };
    return (
      <Form {...formLayout} onSubmit={this.handleSubmit}>
        <Form.Item label="偏好内容">
          {getFieldDecorator('content', {
            initialValue: this.state.detail.content,
            rules: [
              {
                required: true,
                message: '请输入标题',
              },
              {
                max: 30,
                message: '最多30个字',
              },
            ],
          })(<Input placeholder="请输入偏好内容名称" />)}
        </Form.Item>
        <Form.Item label="图标" extra="支持jpg,jpeg,png,gif图片大小为100*100">
          {getFieldDecorator('pic_url', {
            initialValue: this.state.detail.pic_url,
            rules: [
              {
                required: true,
                message: '请上图标',
              },
            ],
          })(
            <ImageUploader
              ratio={1 / 1}
              accept={['image/jpeg', 'image/png', 'image/jpg', 'image/gif']}
            />
          )}
        </Form.Item>
        <Form.Item label="关联频道">
          {getFieldDecorator('ref_nav_id', {
            initialValue: this.state.detail && this.state.detail.ref_nav_id,
          })(
            <Select allowClear={true} placeholder="请选择需要关联的频道">
              {this.state.app_nav_list.map((v: any) => (
                <Select.Option key={v.id} value={v.id.toString()}>
                  {v.name}
                </Select.Option>
              ))}
            </Select>
          )}
        </Form.Item>
        <Form.Item label="关联地方号">
          {getFieldDecorator('ref_column_id', {
            initialValue: this.state.detail && this.state.detail.ref_column_id,
          })(
            <Select allowClear={true} placeholder="请选择需要关联的地方号">
              {this.state.column_list.map((v: any) => (
                <Select.Option key={v.id} value={v.id.toString()}>
                  {v.name}
                </Select.Option>
              ))}
            </Select>
          )}
        </Form.Item>
      </Form>
    );
  }
}

export default ColdStartForm;
