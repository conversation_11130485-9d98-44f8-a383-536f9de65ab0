import { userApi as api } from '@app/api';
import { Form, Input, InputNumber, message, Select, Tooltip, Icon, Radio, Modal } from 'antd';
import React from 'react';
import { setMLoading } from '@utils/utils';
import connectSession from '@utils/connectSession';
import { CREDIT_TIP_PIC } from '@utils/constants';
import { MultiFileUploader } from '../common';

@connectSession
@(Form.create({ name: 'batchCreditForm' }) as any)
class   BatchCreditForm extends React.Component<any, any> {
  constructor(props: any) {
    super(props);
    this.state = {
      type: '0',
    };
  }

  componentDidMount() {
    setMLoading(this, false);
  }

  handleSubmit = (e: any) => {
    e.preventDefault();
    this.doSubmit();
  };

  doSubmit = () => {
    const fields = ['message', 'score'];
    if (this.state.type === '0') {
      fields.push('accounts');
    } else {
      fields.push('excel_url');
    }
    this.props.form.validateFieldsAndScroll(fields, (err: any, values: any) => {
      if (!err) {
        const body = { ...values };
        if (this.state.type === '1') {
          body.excel_url = body.excel_url.join(',');
        }
        setMLoading(this, true);
        api
          .createBatchCredit(body)
          .then((res: any) => {
            if (res.data && res.data.fail_url) {
              Modal.error({
                title: '以下用户操作失败',
                content: res.data.fail_url.split(',').map((v: any) => (
                  <p>
                    <a href={v}>{v}</a>
                  </p>
                )),
                onOk: () => {
                  setMLoading(this, false);
                  this.props.onEnd();
                },
              });
            } else {
              message.success('操作成功');
              setMLoading(this, false);
              this.props.onEnd();
            }
          })
          .catch(() => setMLoading(this, false));
      } else {
        message.error('请检查表单项');
      }
    });
  };

  render() {
    const { getFieldDecorator } = this.props.form;
    const formLayout = {
      labelCol: { span: 4 },
      wrapperCol: { span: 18 },
    };
    return (
      <Form {...formLayout}>
        <Form.Item label="用户ID">
          <Radio.Group
            value={this.state.type}
            onChange={(e) => this.setState({ type: e.target.value })}
          >
            <Radio value="0">输入用户ID</Radio>
            <Radio value="1">导入EXCEL</Radio>
          </Radio.Group>
          <Tooltip
            title={
              <div>
                <p>文件格式如下</p>
                <img src={CREDIT_TIP_PIC} style={{ width: 200 }} alt="123" />
              </div>
            }
          >
            <Icon type="question-circle" />
          </Tooltip>
        </Form.Item>
        {this.state.type === '0' ? (
          <Form.Item label="用户ID">
            {getFieldDecorator('accounts', {
              preserve: true,
              rules: [
                {
                  required: true,
                  message: '用户ID/用户长ID不能为空',
                },
              ],
            })(<Input.TextArea rows={8} placeholder="请输入用户ID/用户长ID，多个用户ID以换行分隔。" />)}
          </Form.Item>
        ) : (
          <Form.Item label="上传用户EXCEL">
            {getFieldDecorator('excel_url', {
              preserve: true,
              rules: [
                {
                  required: true,
                  message: '请上传用户',
                  type: 'array',
                },
              ],
            })(<MultiFileUploader accept=".xls,.xlsx" />)}
          </Form.Item>
        )}
        <Form.Item label="增减积分">
          {getFieldDecorator('score', {
            rules: [
              {
                required: true,
                message: '积分不能为空',
                type: 'number',
              },
            ],
          })(
            <InputNumber
              style={{ width: '100%' }}
              placeholder="+数字或-数字，上限不能超过10000"
              min={-10000}
              precision={0}
              max={10000}
            />
          )}
        </Form.Item>
        <Form.Item label="通知内容">
          {getFieldDecorator('message', {
            rules: [
              {
                required: true,
                message: '通知内容不能为空',
              },
              {
                max: 100,
                message: '通知内容长度不能超过100个字',
              },
            ],
          })(<Input.TextArea rows={4} placeholder="请输入100字以内的通知内容" />)}
        </Form.Item>
      </Form>
    );
  }
}

export default BatchCreditForm;
