/* eslint-disable array-callback-return */
/* eslint-disable no-nested-ternary */
import { releaseListApi as api } from '@app/api';
import { Row, Form, Table, message, Spin, Select, Button, Transfer, Modal } from 'antd';
import React from 'react';
import { setMLoading } from '@utils/utils';
import connectSession from '@utils/connectSession';
import debounce from 'lodash/debounce';
import clonedeep from 'lodash/cloneDeep';
import { TransferItem } from 'antd/es/transfer';

@connectSession
@(Form.create({ name: 'UGCTopicRecommendForm' }) as any)
class UGCTopicRecommendForm extends React.Component<any, any> {
  constructor(props: any) {
    super(props);
    console.log(props);
    this.state = {
      searchResults: [],
      news: [],
      id: null,
      key: Date.now(),
      channel_id: props.id,
    };
    this.fetchNews = debounce(this.fetchNews, 500);
  }
  // 搜索新闻
  fetchNews = (value: string) => {
    this.setState({ fetching: true });
    api
      .getTopicNewsList({
        keyword: value,
        channel_id: this.state.channel_id,
        type: 0,
      })
      .then((r: any) => {
        this.setState({ fetching: false, searched: true });
        this.setState({
          searchResults: r.data.article_list,
        });
      })
      .catch(() => {
        this.setState({ fetching: false });
      });
  };
  // 选项改变
  handleChange = (svalue: any) => {
    const value = JSON.parse(svalue[0]);
    const state = {
      id: value.id,
      news: [value],
      searchResults: [],
      searched: false,
    };
    this.setState({ ...state });
  };

  onSelectBlur = () => {
    this.setState({
      searchResults: [],
      searched: false,
    });
  };

  getColumns = () => {
    return [
      {
        title: 'ID',
        key: 'id',
        dataIndex: 'id',
        width: 90,
      },
      {
        title: '新闻标题',
        key: 'list_title',
        dataIndex: 'list_title',
        render: (text: any) => <span>{text}</span>,
      },

      // {
      //   title: '操作',
      //   key: 'op',
      //   render: (text: any, record: any) => (
      //     <a onClick={this.handleDelete.bind(this, record)}>删除</a>
      //   ),
      //   width: 70,
      // },
    ];
  };

  // handleDelete = (record: any) => {
  //   this.setState({
  //     articleIds: ids,
  //     news: articleList,
  //   });
  // };

  handleSubmit = (e: any) => {
    e.preventDefault();
    this.doSubmit();
  };
  // 提交
  doSubmit = () => {
    if (this.state.news.length === 0) {
      message.error('请添加稿件');
      return;
    }
    setMLoading(this, true);
    api
      .creatTmhTopping({ channel_id: this.state.channel_id, article_id: this.state.id })
      .then((res: any) => {
        console.log(res);
        message.success('添加成功');
        setMLoading(this, false);
        this.props.onEnd();
      })
      .catch((e) => {
        setMLoading(this, false);
        console.error(e);
      });
  };

  render() {
    const formLayout = {
      labelCol: { span: 4 },
      wrapperCol: { span: 18 },
    };
    return (
      <Form {...formLayout} onSubmit={this.handleSubmit}>
        <Form.Item label="添加新闻" required={true}>
          <Row>
            <Select
              mode="multiple"
              value={[]}
              placeholder="输入ID或标题"
              notFoundContent={
                this.state.fetching ? (
                  <Spin size="small" style={{ margin: 'auto' }} />
                ) : this.state.searched ? (
                  '无结果'
                ) : null
              }
              filterOption={false}
              onSearch={this.fetchNews}
              onChange={this.handleChange}
              onBlur={this.onSelectBlur}
              style={{ width: 280 }}
            >
              {this.state.searchResults.map((d: any, i: number) => (
                <Select.Option key={`${i}`} value={JSON.stringify(d)}>
                  {`${d.id} ${d.list_title}`}
                </Select.Option>
              ))}
            </Select>
          </Row>
          <Row style={{ marginTop: 8 }}>
            <Table
              columns={this.getColumns()}
              rowKey="id"
              dataSource={this.state.news}
              pagination={false}
            />
          </Row>
        </Form.Item>
      </Form>
    );
  }
}

export default UGCTopicRecommendForm;
