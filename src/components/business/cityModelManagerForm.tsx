import { releaseListApi as api } from '@app/api';
import { Form, Input, message } from 'antd';
import React from 'react';
import { setMLoading } from '@utils/utils';
import connectSession from '@utils/connectSession';
@connectSession
@(Form.create({ name: 'cityModelManagerForm' }) as any)
class CityModelManagerForm extends React.Component<any, any> {
  constructor(props: any) {
    super(props);
    this.state = {
      city_code:'',
      ...props.formContent,
    };
  }
  componentDidMount() {
    if(this.state.city_code == 0) {
        this.setState(
            {city_code:''}
        )
    }
  }
  handleSubmit = (e: any) => {
    e.preventDefault();
    this.doSubmit();
  }
  doSubmit = () => {
    this.props.form.validateFieldsAndScroll((err: any, values: any) => {
      if (!err) {
        const body = {
            ...values,
            category_id:2,
            id: this.state.id
        }
        const type = new RegExp("^[0-9]*[1-9][0-9]*$"); 
        if(!type.test(body.city_code)) {
            message.error('输入位置只能填写正整数');
            return
        }
        if(body.city_code.length > 10) {
          message.error('城市编码不能大于10位数字')
          return
        }
        setMLoading(this, true);
        
        api.getAssociatedArea({ ...body }).then(() => {
            setMLoading(this, false);
            message.success('操作成功');
            this.props.onEnd();
        }).catch(() => {
            setMLoading(this, false);
        })
      } else {
        message.error('请检查表单内容');
      }
    });
  }
  render() {
    const { getFieldDecorator } = this.props.form;
    const formLayout = {
      labelCol: { span: 4 },
      wrapperCol: { span: 18 },
    };
    return (
      <Form {...formLayout} onSubmit={this.handleSubmit} ref="form">
        <p style={{marginLeft: 60}}>名称：{this.state.name}</p>
        <p style={{marginLeft: 60}}>ID：{this.state.id}</p>
        <p style={{marginLeft: 60}}>天目蓝云ID：{this.state.mlf_id}</p>
        <Form.Item label="城市编码">
          {getFieldDecorator('city_code', {
            initialValue: this.state.city_code,
            rules: [
              {
                required: true,
                message: '请填写城市编码',
              },
              {
                pattern: RegExp("^[0-9]*[1-9][0-9]*$"),
                message: '输入位置只能填写正整数',
              },
              // {
              //   max:10,
              //   message: '城市编码不能大于10位数字'
              // }
            ],
          })(<Input placeholder="请输入城市编码" />)}
        </Form.Item>
      </Form>
    );
  }
}
export default CityModelManagerForm;