import React, { forwardRef, Ref, useImperative<PERSON>andle, useRef, useState, useEffect } from 'react';
import {
    Form,
    DatePicker,
    Button,
    Input,
    Modal,
    InputNumber,
    Icon,
    Col, Tooltip,
    message,
    Popconfirm
} from 'antd';

const { TextArea } = Input;
import ImageUploader from '@components/common/imageUploader';
import moment from "moment";
import { opApi as api } from "@app/api";
import { cloneDeep } from "lodash";
import { getFileItem } from "antd/es/upload/utils";
import uuid from 'uuid';

interface FormRef {
    validateFields: (param: (...arg: any) => void) => void;
    getFieldsValue?: () => any;
    setFieldsValue?: () => void;
}

const FingerContext = React.createContext();
// 基础内容
const ContentForm =
    forwardRef<FormRef, {
        form: any,
        onClose: () => void,
        save: (val: number) => void,
        btnStatus: any,
        formContent: any
    }>
        (({ form, onClose, save, btnStatus, formContent }, ref) => {
            const { getFieldDecorator, validateFields } = form;
            const formRef = useRef(null);
            return (
                <Form ref={formRef} labelCol={{ span: 4 }} wrapperCol={{ span: 18 }}>
                    <Form.Item label="选择日期">
                        {getFieldDecorator('pub_date', {
                            initialValue: formContent?.pub_date ? moment(`${formContent.pub_date}`, 'YYYYMMDD') : null,
                            rules: [
                                {
                                    required: true,
                                    message: '请选择日期',
                                },
                            ],
                        })(<DatePicker disabled={!btnStatus} style={{ width: '100%' }} onChange={() => {
                        }} />)}
                    </Form.Item>
                    <Form.Item label="封面" extra="支持jpg、png、gif等格式，比例为 3:4">
                        {getFieldDecorator('cover_image', {
                            initialValue: formContent?.cover_image,
                            rules: [
                                {
                                    required: true,
                                    message: '请选择封面',
                                },
                            ],
                        })(
                            <ImageUploader
                                disabled={formContent?.edit === ''}
                                imgsize={1024}
                                isCutting={false}
                                ratio={3 / 4}
                                accept={['image/jpeg', 'image/png', 'image/jpg', 'image/gif']}
                            />
                        )}
                    </Form.Item>
                    <Form.Item wrapperCol={{ offset: 8, span: 16 }}>
                        <Button onClick={() => {
                            onClose()
                        }}>取消</Button>
                        &nbsp;&nbsp;&nbsp;
                        <Button type="primary" onClick={() => {
                            save(1)
                        }}>保存</Button>
                    </Form.Item>
                </Form>
            );
        });
const Content = Form.create({ name: 'ContentForm' })(ContentForm);

// 内容关联
let count = 0;
const RelatedForm =
    forwardRef<FormRef, {
        form: any,
        onClose: () => void,
        save: (val: number) => void,
        btnStatus: any,
        formContent: any
    }>
        (({ form, onClose, save, btnStatus, formContent }, ref) => {
            const formRef = useRef(null);
            const { getFieldDecorator, setFieldsValue, getFieldsValue } = form;
            const [content_list, setList] = useState(formContent?.content_list ? formContent?.content_list : [{
                id: uuid(),
                title: '',
                description: '',
                url: '',
            }])
            const addInput = () => {
                count += 1;
                const { content_list: lastContentList } = getFieldsValue()
                const result = [
                    ...lastContentList,
                    {
                        id: uuid(),
                        title: '',
                        description: '',
                        url: '',
                    }]
                setList(result)
                setTimeout(() => {
                    setFieldsValue({ content_list: result })
                }, 0);

            };
            const delectList = (t: any) => {
                const { content_list: lastContentList } = getFieldsValue()
                const arrayConment: any = [];
                lastContentList.forEach((data: any, i: number) => {
                    if (data.id !== t.id) {
                        arrayConment.push(data);
                    }
                });
                setList(arrayConment)
                setTimeout(() => {
                    setFieldsValue({ content_list: arrayConment })
                }, 0);
            };
            const handleMove = (data, index) => {
                let position = index + 1

                Modal.confirm({
                    width: 250,
                    content: (<>位置:  <InputNumber
                        min={1}
                        max={content_list.length}
                        defaultValue={position}
                        onChange={(e: any) => { position = e }} /></>),
                    icon: null,
                    onOk() {
                        const { content_list: lastContentList } = getFieldsValue()
                        let item = lastContentList[index]
                        let newArray = [...lastContentList];
                        newArray.splice(index, 1)
                        newArray.splice(position - 1, 0, item);
                        setList(newArray)
                        setTimeout(() => {
                            setFieldsValue({ content_list: newArray })
                        }, 0);
                    },
                    onCancel() { },
                });
            }
            return (
                <Form ref={formRef} labelCol={{ span: 4 }} wrapperCol={{ span: 18 }}>
                    {content_list.map((data: any, index: number) => {
                        return (
                            <div key={`${index}-${data.id}`}>
                                <Form.Item label="id" style={{ display: 'none' }}>
                                    {getFieldDecorator(`content_list[${index}].id`, {
                                        initialValue: data.id
                                    })(<Input disabled />)}
                                </Form.Item>
                                <Form.Item label="标题">
                                    <h3 style={{ display: "flex", position: "absolute", width: 60, top: '-12px', left: '-105px' }}>内容{index + 1}</h3>
                                    {getFieldDecorator(`content_list[${index}].title`, {
                                        initialValue: data.title,
                                        rules: [
                                            {
                                                required: true,
                                                message: '标题不能为空',
                                            },
                                        ],
                                    })(
                                        <Input placeholder="请输入标题，最多输入40字" maxLength={40} />
                                    )}
                                    {
                                        content_list.length > 1 &&
                                        <div style={{ display: "flex", position: "absolute", width: 60, top: '-12px', right: '-60px' }}>
                                            <div
                                                style={{
                                                    height: '40px',
                                                    lineHeight: '40px',
                                                    width: '40px',
                                                    textAlign: 'center',
                                                    cursor: 'pointer',
                                                }}
                                            >
                                                <Icon type="swap" style={{ transform: 'rotate(90deg)' }}
                                                    onClick={() => { handleMove(data, index) }} />
                                            </div>

                                            <div
                                                style={{
                                                    height: '40px',
                                                    lineHeight: '40px',
                                                    width: '40px',
                                                    textAlign: 'center',
                                                    cursor: 'pointer',
                                                }}
                                            >
                                                <Popconfirm
                                                    placement="top"
                                                    title="确定要删除吗？"
                                                    okText="确定"
                                                    cancelText="取消"
                                                    icon={<Icon type="exclamation-circle" theme="twoTone" twoToneColor="red" />}
                                                    onConfirm={() => {delectList(data)}}
                                                >
                                                <Icon type="delete" />
                                                </Popconfirm>
                                            </div>
                                        </div>
                                    }

                                </Form.Item>
                                <Form.Item label="简介">
                                    {getFieldDecorator(`content_list[${index}].description`, {
                                        initialValue: data.description,
                                        rules: [
                                            {
                                                required: true,
                                                message: '请填写简介',
                                            },
                                        ],
                                    })(<TextArea placeholder="请输入简介，最多可输入60字" maxLength={60} />)}
                                </Form.Item>
                                <Form.Item label="URL">
                                    {getFieldDecorator(`content_list[${index}].url`, {
                                        initialValue: data.url,
                                        rules: [
                                            {
                                                required: false,
                                                message: '请填写url',
                                            },
                                        ],
                                    })(<Input placeholder="请输入跳转链接" />)}
                                </Form.Item>
                                <Form.Item label="图片" extra="支持jpg、png、gif等格式，比例为 16:9">
                                    {getFieldDecorator(`content_list[${index}].image`, {
                                        initialValue: data.image,
                                        rules: [
                                            {
                                                required: false,
                                                message: '请选择图片',
                                            },
                                        ],
                                    })(
                                        <ImageUploader
                                            imgsize={1024}
                                            isCutting={false}
                                            ratio={16 / 9}
                                            accept={['image/jpeg', 'image/png', 'image/jpg', 'image/gif']}
                                        />
                                    )}
                                </Form.Item>
                            </div>
                        );
                    })}
                    {!formContent?.disabled && content_list.length < 20 && (
                        <Form.Item>
                            <Col span={9} />
                            <Col span={24}>
                                <Button onClick={addInput} style={{width: '100%', marginLeft: 100}}> <Icon type="plus-circle-o" />添加内容</Button>
                            </Col>
                            <Col span={9} />
                        </Form.Item>
                    )}
                    <Form.Item wrapperCol={{ offset: 8, span: 16 }}>
                        <Button onClick={() => {
                            onClose()
                        }}>取消</Button>
                        &nbsp;&nbsp;&nbsp;
                        <Tooltip title={btnStatus ? '请先选择日期和封面图，保存后再填写该部分数据' : null} >
                            <Button
                                type="primary"
                                disabled={btnStatus}
                                onClick={() => {
                                    save(2)
                                }}>保存</Button>
                        </Tooltip>
                    </Form.Item>
                </Form>
            );
        });
const Related = Form.create({ name: 'RelatedForm' })(RelatedForm);

// 分享信息
const ShareForm = forwardRef<FormRef, {
    form: any,
    onClose: () => void,
    save: (val: number) => void,
    btnStatus: any,
    formContent: any
}>
    (({ form, onClose, save, btnStatus, formContent }, ref) => {
        const { getFieldDecorator, validateFields } = form;
        const formRef = useRef<any>(null);
        return (
            <Form ref={formRef} labelCol={{ span: 4 }} wrapperCol={{ span: 18 }}>
                <p style={{ height: '40px', lineHeight: '40px',fontWeight:600 }}>分享信息</p>
                <>
                    <Form.Item label="分享标题">
                        {getFieldDecorator('share_title', {
                            initialValue: formContent?.share_title,
                            rules: [
                                {
                                    required: false,
                                    message: '请填写分享标题',
                                },
                            ],
                        })(<Input placeholder=" 最多24字，如不填写，默认显示第一条内容标题或「指尖读报」" maxLength={24} />)}
                    </Form.Item>
                    <Form.Item label="分享副标题">
                        {getFieldDecorator('share_subtitle', {
                            initialValue: formContent?.share_subtitle,
                            rules: [
                                {
                                    required: false,
                                    message: '请填写分享副标题',
                                },
                            ],
                        })(<Input placeholder="最多30字，如不填写，默认显示「来自读嘉客户端」" maxLength={30} />)}
                    </Form.Item>
                    <Form.Item
                        label={
                            <span style={{ position: 'relative' }}>
                                <Tooltip placement='left' title={<img src='/assets/share_background.png' width={210} height={288} />}>
                                    <Icon type="question-circle" style={{ position: 'absolute', left: -20, top: 3 }} />
                                </Tooltip>
                                分享背景图
                            </span>
                        }
                        extra="支持jpg、png等格式,比例为4:3">
                        {getFieldDecorator('share_image', {
                            initialValue: formContent?.share_image,
                            rules: [
                                {
                                    required: false,
                                    message: '请选择封面',
                                },
                            ],
                        })(
                            <ImageUploader
                                imgsize={1024}
                                isCutting={false}
                                ratio={4 / 3}
                                accept={['image/jpeg', 'image/png', 'image/jpg', 'image/gif']}
                            />
                        )}
                    </Form.Item>
                </>
                <Form.Item wrapperCol={{ offset: 8, span: 16 }}>
                    <Button onClick={() => {
                        onClose()
                    }}>取消</Button>
                    &nbsp;&nbsp;&nbsp;
                    <Tooltip title={btnStatus ? '请先选择日期和封面图，保存后再填写该部分数据' : null}>
                        <Button type="primary"
                            onClick={() => { save(3) }}
                            disabled={btnStatus}>保存</Button>
                    </Tooltip>

                </Form.Item>
            </Form>
        );
    });
const Share = Form.create({ name: 'ShareForm' })(ShareForm)


export interface FingerPaperFormRef {
    submit: () => void;
}

const FingerPaperForm = forwardRef<FingerPaperFormRef, any>((props, ref) => {
    const ContentRef = useRef<FormRef>(null);
    const RelatedRef = useRef<FormRef>(null);
    const ShareRef = useRef<FormRef>(null);
    const DataRef = useRef<FormRef>(null);
    const [btnStatus, setBtnStatus] = useState(true)
    const contentValid = () => {
        return new Promise((resolve, reject) => {
            ContentRef.current?.validateFields((err, values) => {
                if (!err) {
                    resolve(values);
                }
                reject();
            });
        });
    };
    const formatDateToYYMMDD = (date) => {
        return moment(date).format("YYYYMMDD");
    }

    const dataValid = () => {
        return new Promise((resolve, reject) => {
            DataRef.current?.validateFields((err, values) => {
                if (!err) {
                    resolve(values);
                }
                reject();
            });
        });
    };
    useEffect(() => {
        if (props.formContent) {
            setBtnStatus(false)
        }
    }, []);
    useImperativeHandle(
        ref,
        () => {
            return {
                submit() {
                    const promiseList = [];
                    promiseList.push(contentValid());
                    listRef.current.forEach((item) => {
                        const itemValid = () => {
                            return new Promise((resolve, reject) => {
                                item.validateFields((err, values) => {
                                    if (!err) {
                                        resolve(values);
                                    }
                                    reject();
                                });
                            });
                        };
                        promiseList.push(itemValid());
                    });
                    promiseList.push(dataValid());
                    Promise.allSettled(promiseList).then((res) => {
                        const result = res.some((item) => item.status === 'rejected');
                        if (!result) {
                            const content = [];

                            res.forEach((item, index) => {
                                if (index !== 0 && index !== res.length - 1) {
                                    content.push(item.value);
                                }
                            });
                            const body = {
                                ...res[0].value,
                                content,
                                ...res.at(-1).value,
                            };
                            let date = ''
                            body.pub_date = formatDateToYYMMDD(body.pub_date)
                            body.save_type = 1
                            api.createFingerPager(body).then(res => {
                            })
                            console.log(body);
                        }
                    });
                },
            };
        },
        []
    );
    const onClose = () => {
        props.onClose()
    }
    const save = (save_type: number) => {
        const arr = ['', ContentRef, RelatedRef, ShareRef]
        let data = {
            save_type
        }
        arr[save_type].current?.validateFields((err, values) => {
            let pub_date = arr[1].current.getFieldValue('pub_date')
            if (!err) {
                switch (save_type) {
                    case 1:
                        values.pub_date = formatDateToYYMMDD(values.pub_date)
                        data = { ...data, ...values }
                        break;
                    case 2:
                        values.pub_date = formatDateToYYMMDD(pub_date)
                        let list = values.content_list
                        let arr = list.filter(item => item !== "" && item !== null && item !== undefined).map((v: any) => {
                            const { id, ...result } = v
                            return result
                        })
                        values.content_list = arr
                        data = { ...data, ...values }
                        break;
                    case 3:
                        values.pub_date = formatDateToYYMMDD(pub_date)
                        data = { ...data, ...values }
                        break;
                }
                if (!('pub_date' in data)) {
                    data.pub_date = props.formContent?.pub_date
                }
                // 新建
                if (btnStatus) {
                    api.createFingerPager(data).then(() => {
                        message.success('操作成功');
                        setBtnStatus(false)
                        console.log(props)
                        props.getData()
                    })
                    // 编辑
                } else {
                    api.updateFingerPager(data).then(() => {
                        message.success('操作成功');
                        props.getData()
                    })
                }
            }
        });
    }
    return (
        <FingerContext.Provider>
            <Content ref={ContentRef} onClose={onClose} save={save} btnStatus={btnStatus}
                formContent={props.formContent} />

            <Related ref={RelatedRef} onClose={onClose} save={save} btnStatus={btnStatus}
                formContent={props.formContent} />

            <Share ref={ShareRef} onClose={onClose} save={save} btnStatus={btnStatus} formContent={props.formContent} />

        </FingerContext.Provider>
    );
});

export default FingerPaperForm;
