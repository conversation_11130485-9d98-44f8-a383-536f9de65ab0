import { releaseListApi } from '@app/api';
import ImageUploader from '@components/common/imageUploader';
import A from '@components/common/a';
import SearchAndInput from '@components/common/newsSearchAndInput';
import {
  Form,
  Icon,
  Input,
  InputNumber,
  message,
  Radio,
  Tooltip,
  Modal
} from 'antd';
import React from 'react';
import { setMLoading } from '@utils/utils';
import connectSession from '@utils/connectSession';


@connectSession
@(Form.create({ name: 'HotspotRecommendForm' }) as any)
class HotspotRecommendForm extends React.Component<any, any> {
  constructor(props: any) {
    super(props);
    this.state = { ...props.formContent, ref_ids: [] };
    const { first_list_pic, first_list_custom_scale = '16:9' } = this.state.extend
    this.state.channelArticles.forEach((article: any, index: number) => {
      if (index === 0) {
        article.picUrl = first_list_pic
        article.picScale = first_list_custom_scale
      }
      this.state.ref_ids.push(article.id);
    });
  }

  handleSubmit = (e: any) => {
    e.preventDefault();
    this.doSubmit();
  };

  doSubmit = () => {
    this.props.form.validateFieldsAndScroll((err: any, values: any) => {
      if (!err) {
        if (values.ref_ids.length < 3) {
          message.error('至少关联3条稿件');
          return
        }
        setMLoading(this, true);
        let api: keyof typeof releaseListApi = 'createSummaryRecommend';
        if (this.state.id) {
          api = 'updateSummaryRecommend';
        }
        const channelArticle = this.state.channelArticles[0]
        const extend = JSON.stringify({
          ...values.extend,
          first_list_pic: channelArticle.picUrl,
          first_list_custom_scale: channelArticle.picScale,
        })
        try {
          releaseListApi[api]({
            ...values,
            extend,
            [this.state.id ? 'id' : 'channel_id']: this.state.id || this.props.channelId,
            channel_id: this.props.channelId,
            type: 27,
            style: 3,
            display_pic: 0,
          })
            .then(() => {
              setMLoading(this, false);
              message.success('操作成功');
              this.props.onEnd();
            })
            .catch(() => {
              setMLoading(this, false);
            });
        } catch (errr) {
          console.error(errr);
        }
      } else {
        message.error('请检查表单内容');
      }
    });
  };

  editPic = (url: string, index: number, picScale: string) => {
    let pic: string = url;
    let scale: string = picScale;
    let modal: any;
    const picChange = (u: string, picScale: string) => {
      pic = u;
      scale = picScale || '16:9';
      modal.update({
        content: (
          <>
            <ImageUploader value={pic} imgMaxWidth={300} onChange={picChange} ratioType={scale} showRatioTypeChange />
            <p>比例{scale}</p>
          </>
        ),
      });
    };
    modal = Modal.confirm({
      title: '编辑图片',
      content: (
        <>
          <ImageUploader value={pic} imgMaxWidth={300} onChange={picChange} ratioType={scale} showRatioTypeChange />
          <p>比例{scale}</p>
        </>
      ),
      onOk: (destroy: Function) => {
        const articles = [...this.state.channelArticles];
        articles[index].picUrl = pic;
        articles[index].picScale = scale;
        this.setState({ channelArticles: articles })
        destroy();
      },
    });
  };

  getColumns = () => {
    return [
      {
        title: 'ID',
        dataIndex: 'id',
        width: 80,
      },
      {
        title: '新闻频道',
        key: 'type',
        dataIndex: 'channel_name',
        width: 90,
      },
      {
        title: '新闻标题',
        key: 'list_title',
        dataIndex: 'list_title',
      },
      {
        title: '列表图',
        dataIndex: 'picUrl',
        width: 90,
        align: 'center',
        render: (text: string, record: any, index: number) => (
          <>
            {index === 0 ? <A onClick={() => this.editPic(text, index, record.picScale)}>{text ? '修改' : '上传'}</A> : '/'}
          </>
        ),
      },
    ];
  };

  articlesChange = (data: any) => {
    data.channelArticles.forEach((article: any, index: number) => {
      if (index > 0 || !article.picScale) {
        article.picUrl = ''
        article.picScale = '16:9'
      }
    });
    this.setState({ ...data });
  };

  styleTip = (style: number) => {
    return (
      <img src={`/assets/${style === 0 ? 'hotRecommendWord' : 'hotRecommendPic'}.png`} width={150} height={style === 0 ? 212 : 290} />
    )
  }

  render() {
    const { getFieldDecorator } = this.props.form;
    const { channelArticles } = this.state
    const formLayout = {
      labelCol: { span: 4 },
      wrapperCol: { span: 18 },
    };

    const text = (
      <span>
        <p>输入说明：</p>
        <p>
          直接输入阿拉伯数字代表位置序号，例如要在头条频道信息流第8个位置插入该推荐位，则直接输入“8”即可。
        </p>
        <p>最大输入数字为500</p>
        <br />
        <p>注：</p>
        <p>直接在输入的指定位置上插入推荐位显示，不参与稿件的排序，且优先于媒立方稿件展示</p>
      </span>
    );
    return (
      <Form {...formLayout} onSubmit={this.handleSubmit}>
        <Form.Item label="推荐位名称">
          {getFieldDecorator('recommend_name', {
            initialValue: this.state.recommend_name,
            rules: [
              {
                required: true,
                message: '请输入名称',
              },
              {
                max: 15,
                message: '名称最长不能超过15个字',
              },
            ],
          })(<Input placeholder="请输入名称" />)}
        </Form.Item>
        <Form.Item label="显示位置">
          {getFieldDecorator('position', {
            initialValue: this.state.position,
            rules: [
              {
                required: true,
                message: '请输入位置序号',
                type: 'number',
              },
              {
                max: 500,
                message: '最大不能超过500',
                type: 'number',
              },
            ],
          })(<InputNumber style={{ width: 200 }} placeholder="请输入位置序号" min={1} max={500} />)}
          &emsp;
          <Tooltip title={text}>
            <Icon type="question-circle" />
          </Tooltip>
        </Form.Item>
        <Form.Item label="显示样式">
          {getFieldDecorator('extend.show_style', {
            rules: [{
              required: true,
              message: '请选择样式',
            }],
            initialValue: this.state.extend.show_style,
          })(
            <Radio.Group>
              <Radio value={0}>默认+纯文&emsp;
                <Tooltip title={this.styleTip(0)}>
                  <Icon type="question-circle" />
                </Tooltip></Radio>
              <Radio value={1}>默认+左图右文&emsp;
                <Tooltip title={this.styleTip(1)}>
                  <Icon type="question-circle" />
                </Tooltip>
              </Radio>
            </Radio.Group>
          )}
        </Form.Item>
        <Form.Item label="显示稿件数" extra="列表显示稿件数，必须≥1，≤10的正整数">
          {getFieldDecorator('extend.show_articles_count', {
            initialValue: this.state.extend.show_articles_count,
            rules: [
              {
                required: true,
                message: '请输入列表显示稿件数',
                type: 'number',
              },
              {
                max: 10,
                message: '最大不能超过10',
                type: 'number',
              },
            ],
          })(<InputNumber style={{ width: 200 }} placeholder="请输入列表显示稿件数" min={1} max={10} />)}
        </Form.Item>

        <Form.Item label="关联稿件" required={true}>
          {getFieldDecorator('ref_ids', {
            initialValue: this.state.ref_ids,
            rules: [
              {
                required: true,
                message: '请关联稿件',
                type: 'array',
              },
            ],
          })(
            <SearchAndInput
              max={50}
              func="listArticleRecommendSearch"
              columns={this.getColumns()}
              placeholder="输入ID或标题关联稿件"
              initialValues={{ list: channelArticles }}
              triggerInitialValueChange={this.articlesChange}
              body={{ doc_types: '2,3,4,5,8,9' }}
              order={true}
              addOnTop={true}
              addOnTopIndex={channelArticles.length > 0 && channelArticles[0].picUrl ? 1 : 0}
            />
            // <SearchAndInput
            //   max={30}
            //   func="listArticleRecommendSearch"
            //   columns={this.getColumns()}
            //   placeholder="输入名称搜索地方号"
            //   initialValues={{ list: this.state.channelArticles }}
            //   triggerInitialValueChange={this.articlesChange}
            //   body={{ doc_types: '2,3' }}
            //   order={true}
            //   addOnTop={true}
            //   searchKey="name"
            //   funcIndex="list"
            //   apiWithPagination={true}
            //   selectOptionDisplay={(record: any) => `#${record.name}#`}
            // />
          )}
        </Form.Item>
      </Form>
    );
  }
}

export default HotspotRecommendForm;
