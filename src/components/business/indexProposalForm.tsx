import { opApi as api } from '@app/api';
import {
  Checkbox,
  Form,
  Input,
  message,
  Select,
  Radio,
  Row,
  Col,
  Tooltip,
  Icon,
  Modal,
} from 'antd';
import React from 'react';
import { setMLoading } from '@utils/utils';
import connectSession from '@utils/connectSession';
import { CommonObject } from '@app/types';
import { ImageUploader, MultiFileUploader } from '../common';
import ArLinkInput, { arLinkValidator, isArLink } from '../common/arLinkInput';

@connectSession
@(Form.create({ name: 'indexProposalForm' }) as any)
class IndexProposalForm extends React.Component<any, any> {
  constructor(props: any) {
    super(props);
    const s = {
      ...props.formContent,
    };
    this.state = {
      ...s,
      url: isArLink(s.url) ? '' : s.url,
      arUrl: isArLink(s.url) ? s.url : '',
      linkType: isArLink(s.url) ? 1 : 0,
      origin_user: props.formContent.origin_user.split(','),
      originalFiles: props.formContent.origin_user.split(','),
    };
  }

  componentDidMount() {}

  handleSubmit = (e: any) => {
    e.preventDefault();
    this.doSubmit();
  };

  doSubmit = () => {
    const fields = ['title', 'pic_url'];
    let fileFlag = false;
    const extraBody: CommonObject = {};

    if (this.state.linkType === 0) {
      fields.push('url');
    } else {
      fields.push('arUrl');
    }
    this.props.form.validateFieldsAndScroll(fields, (err: any, values: any) => {
      if (!err) {
        const {
          routeInfo: { from, id, isFloatWindow },
        } = this.props;
        setMLoading(this, true);
        let func = isFloatWindow ? 'createChannelFloatWindow' : 'createProposal';
        const body = {
          ...values,
          ...extraBody,
          to_all: 1,
          type: 4,
        };
        if (fileFlag) {
          body.visible = values.visible === 1;
        }
        if (this.state.id) {
          func = isFloatWindow ? 'updateChannelFloatWindow' : 'updateProposal';
          body.id = this.state.id;
        }
        if (this.state.linkType === 1) {
          body.url = values.arUrl;
          delete body.arUrl;
        }
        if (isFloatWindow) {
          body.channel_id = id;
        }
        api[func as keyof typeof api](body)
          .then((r: any) => {
            setMLoading(this, false);
            message.success('操作成功');
            if (r.data && r.data.fail_visible_user) {
              Modal.error({
                title: '导入文件中的以下用户找不到，可下载查看：',
                content: r.data.fail_visible_user.split(',').map((v: any) => (
                  <Row key={v}>
                    <a href={v}>{v}</a>
                  </Row>
                )),
                onOk: () => {
                  this.props.onEnd();
                },
              });
              return;
            }
            this.props.onEnd();
          })
          .catch(() => {
            setMLoading(this, false);
          });
      } else {
        message.error('请检查表单内容');
      }
    });
  };

  validateUsers = (rule: any, value: any, callback: Function) => {
    if (this.state.origin_user.length === 0 && this.state.visible_org === 0) {
      callback('机构用户勾选和上传文件，至少填写一项');
    } else {
      callback();
    }
  };

  toAllChange = () => {
    setTimeout(() => {
      this.props.form.setFieldsValue({ visible: 1 });
    }, 300);
    this.clearOrg();
  };

  orgChange = (e: any) => {
    this.setState({
      visible_org: e.target.checked ? 1 : 0,
    });
    this.props.form.setFieldsValue({
      visible: this.props.form.getFieldsValue(['visible']).visible,
    });
  };

  clearOrg = () => {
    this.setState({
      visible_org: 0,
      origin_user: '',
    });
    this.props.form.setFieldsValue({
      visible: this.props.form.getFieldsValue(['visible']).visible,
    });
  };

  handleFileChange = (value: string) => {
    this.setState({
      origin_user: value,
    });
    this.props.form.setFieldsValue({
      visible: this.props.form.getFieldsValue(['visible']).visible,
    });
  };

  render() {
    const { getFieldDecorator } = this.props.form;
    const formLayout = {
      labelCol: { span: 4 },
      wrapperCol: { span: 18 },
    };
    return (
      <Form {...formLayout} onSubmit={this.handleSubmit}>
        <Form.Item label="名称">
          {getFieldDecorator('title', {
            initialValue: this.state.title,
            rules: [
              {
                required: true,
                message: '请填写推荐位名称',
              },
              {
                max: 20,
                message: '推荐位名称最长不能超过20个字',
              },
            ],
          })(<Input placeholder="请输入推荐位名称" />)}
        </Form.Item>
        {/* <Form.Item label="类型">
          <Radio.Group
            value={this.state.linkType}
            onChange={(e: any) => this.setState({ linkType: e.target.value })}
          >
            <Radio value={0}>链接</Radio>
            <Radio value={1}>AR</Radio>
          </Radio.Group>
        </Form.Item> */}
        {this.state.linkType === 0 && (
          <Form.Item label="链接">
            {getFieldDecorator('url', {
              initialValue: this.state.url,
              preserve: true,
              rules: [
                {
                  required: true,
                  message: '请输入链接',
                },
                {
                  pattern: /^https?:\/\//,
                  message: '请输入正确的链接格式',
                },
              ],
            })(<Input placeholder="请输入链接" />)}
          </Form.Item>
        )}
        {this.state.linkType === 1 && (
          <Form.Item label="AR信息" required>
            {getFieldDecorator('arUrl', {
              initialValue: this.state.arUrl,
              preserve: true,
              rules: [
                {
                  validator: arLinkValidator,
                },
              ],
            })(<ArLinkInput />)}
          </Form.Item>
        )}
        <Form.Item label="图片" extra="支持jpg,jpeg,png,gif图片格式">
          {getFieldDecorator('pic_url', {
            initialValue: this.state.pic_url,
            rules: [
              {
                required: true,
                message: '请上传图片',
              },
            ],
          })(
            <ImageUploader
              ratio={1 / 1}
              accept={['image/jpeg', 'image/png', 'image/jpg', 'image/gif']}
            />
          )}
        </Form.Item>
        {/* <Form.Item label="面向用户">
          {getFieldDecorator('to_all', {
            initialValue: this.state.to_all,
          })(
            <Select onChange={this.toAllChange}>
              <Select.Option value={1}>全部用户</Select.Option>
              <Select.Option value={0}>部分用户</Select.Option>
            </Select>
          )}
        </Form.Item>
        {values.to_all === 0 && (
          <>
            <Form.Item
              label={
                <Tooltip placement="bottom" title={text} arrowPointAtCenter={true}>
                  <Icon type="question-circle" style={{ marginTop: 9 }} />
                </Tooltip>
              }
              colon={false}
            >
              {getFieldDecorator('visible', {
                initialValue: this.state.visible,
                preserve: true,
                rules: [
                  {
                    validator: this.validateUsers.bind(this),
                  },
                ],
              })(
                <Radio.Group style={{ width: '100%' }} onChange={this.clearOrg}>
                  <Radio style={radioStyle} value={1}>
                    给谁看
                  </Radio>
                  {values.visible === 1 && (
                    <Row style={{ marginLeft: 22 }} gutter={16}>
                      <Col span={5}>
                        <Checkbox checked={this.state.visible_org === 1} onChange={this.orgChange}>
                          机构用户
                        </Checkbox>
                        <Icon type="plus" />
                      </Col>
                      <Col span={17}>
                        <MultiFileUploader
                          accept=".xls,.xlsx"
                          value={this.state.origin_user}
                          onChange={this.handleFileChange}
                        />
                      </Col>
                    </Row>
                  )}
                  <Radio style={radioStyle} value={0}>
                    不给谁看
                  </Radio>
                  {values.visible === 0 && (
                    <Row style={{ marginLeft: 22 }} gutter={16}>
                      <Col span={5}>
                        <Checkbox checked={this.state.visible_org === 1} onChange={this.orgChange}>
                          机构用户
                        </Checkbox>
                        <Icon type="plus" />
                      </Col>
                      <Col span={17}>
                        <MultiFileUploader
                          accept=".xls,.xlsx"
                          value={this.state.origin_user}
                          onChange={this.handleFileChange}
                        />
                      </Col>
                    </Row>
                  )}
                </Radio.Group>
              )}
            </Form.Item>
          </>
        )} */}
      </Form>
    );
  }
}

export default IndexProposalForm;
