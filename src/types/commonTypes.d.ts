import { History } from 'history';
import { Dispatch, AnyAction } from 'redux';
import { ThunkDispatch } from 'redux-thunk';
import { Session, TableCache, TableList } from './systemTypes';
import { AppState } from '@app/utils/configureStore';

export interface RequestInfo {
  requestId: string;
  timestamp: string;
  blob?: boolean;
}

export interface RequestBody {
  [key: string]: any;
}

export interface CommonState {
  [key: string]: any;
}

export interface CommonObject {
  [key: string]: any;
}

export interface ICommonProps {
  breadCrumb: Array<string>;
  selectKeys?: Array<string>;
  openKeys?: Array<string>;
  dispatch: ThunkDispatch<AppState, void, AnyAction>;
}

export interface IComponentProps {
  dispatch: ThunkDispatch<AppState, void, AnyAction>;
}

export interface ISessionProps {
  session: Session;
}

export interface ITableProps<TREC = CommonObject, TALL = CommonObject> {
  session: Session;
  tableList: TableList<TREC, TALL>;
}

export interface IAllProps<TREC = CommonObject, TALL = CommonObject> {
  session: Session;
  tableList: TableList<TREC, TALL>;
  config: CommonObject;
  tableCache: TableCache<TREC, TALL>;
}

export interface IHistoryProps {
  history: History;
}
