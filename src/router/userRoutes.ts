import { lazy } from 'react';

const AvatarManager = lazy(() => import('@views/users/avatarManager'));
const PrivilegeGroup = lazy(() => import('@views/users/privilegeGroup'));
const BatchCredits = lazy(() => import('@views/users/batchCredits'));
const PrivilegeGroupUser = lazy(() => import('@views/users/privilegeGroupUser'));
const WaistcoatUser = lazy(() => import('@views/users/waistcoatUser'));
const IncentiveProblems = lazy(() => import('@views/users/incentiveProblems'));
const UserAgreement = lazy(() => import('@views/users/userAgreement'));
const UserList = lazy(() => import('@views/users/userList'));
const CreditPolicy = lazy(() => import('@views/users/creditPolicy'));
const UserMessageList = lazy(() => import('@views/users/userMessage'));
const UserRecommend = lazy(() => import('@views/users/userRecommend'));
const UserFeedback = lazy(() => import('@views/users/userFeedback'));
const DeletePolicy = lazy(() => import('@views/users/userDeleteAgreement'));
const FAQ = lazy(() => import('@app/views/users/FAQ'));
const OrgnizationUsers = lazy(() => import('@app/views/users/orgnizationUsers'));
const VirtualAccount = lazy(() => import('@app/views/users/virtualAccount'));
const OrgUserArticleList = lazy(() => import('@app/views/users/orgUsersArticles'));
const OrgAccountMgrlist = lazy(() => import('@app/views/users/orgAccountMgrlist'));
const CashoutPolicy = lazy(() => import('@views/users/cashoutAgreement'));
const OrgnizationCategory = lazy(() => import('@views/users/orgnizationCategory'));
const ChaokeStar = lazy(() => import('@views/users/chaokeStar'));
const OrgRankList = lazy(() => import('@views/users/orgRank/ranklist'));
const NewCmhRankList = lazy(() => import('@views/users/orgRank/newCmhRankList'));
const NewCkRankList = lazy(() => import('@views/users/orgRank/newCkRankList'));
const SingleOrgRank = lazy(() => import('@views/users/orgRank/singleRank'));
const SingleCmhRank = lazy(() => import('@views/users/orgRank/singleCmhRank'));
const CmhRankClassMgr = lazy(() => import('@views/users/orgRank/cmhRankClassMgr'));
const SingleCkRank = lazy(() => import('@views/users/orgRank/singleCkRank'));
const CkRankBlacklist = lazy(() => import('@app/views/users/orgRank/ckRankBlacklist'));
const RankLogs = lazy(() => import('@views/users/orgRank/operateLog'));
const CreditLevel = lazy(() => import('@views/users/creditLevel'));
const UserImageAudit = lazy(() => import('@views/users/userImageAudit'));
const equityMgr = lazy(() => import('@views/users/equityMgr'));
const WhiteList = lazy(() => import('@views/users/whiteList'));
const AuthApply = lazy(() => import('@views/users/authApply'));
const LongVideoApply = lazy(() => import('@views/users/longVideoApply'));
const CmhAgreement = lazy(() => import('@views/users/cmhAgreement'));
const CmhAuthQuestion = lazy(() => import('@views/users/cmhAuthQuestion'));
const SpiderBind = lazy(() => import('@views/users/SpiderBind'));
const BusinessCooperation = lazy(() => import('@views/users/businessCooperation'));
export default [
  {
    path: '/cooperation',
    component: BusinessCooperation,
    routeProps: {
      breadCrumb: ['用户中心', '合作平台'],
      selectKeys: ['/view/cooperation'],
      openKeys: ['/view/userPages'],
    },
    permission: '',
  },
  {
    path: '/userAgreement',
    component: UserAgreement,
    routeProps: {
      breadCrumb: ['用户中心', '用户协议管理'],
      selectKeys: ['/view/userAgreement'],
      openKeys: ['/view/userPages'],
    },
    permission: '',
  },
  {
    path: '/profileSetting',
    component: AvatarManager,
    routeProps: {
      breadCrumb: ['用户中心', '用户默认头像配置'],
      selectKeys: ['/view/profileSetting'],
      openKeys: ['/view/userPages'],
    },
    permission: '',
  },
  {
    path: '/userList',
    component: UserList,
    routeProps: {
      breadCrumb: ['用户中心', '用户列表'],
      selectKeys: ['/view/userList'],
      openKeys: ['/view/userPages'],
    },
    permission: '',
  },
  {
    path: '/privilegeGroup',
    component: PrivilegeGroup,
    routeProps: {
      breadCrumb: ['用户中心', '用户列表', '特权组管理'],
      selectKeys: ['/view/userList'],
      openKeys: ['/view/userPages'],
    },
    permission: '',
  },
  {
    path: '/privilegeGroupUser/:id/:name',
    component: PrivilegeGroupUser,
    routeProps: {
      breadCrumb: ['用户中心', '用户列表', '特权组管理'],
      selectKeys: ['/view/userList'],
      openKeys: ['/view/userPages'],
    },
    permission: '',
  },
  {
    path: '/batchCredits',
    component: BatchCredits,
    routeProps: {
      breadCrumb: ['用户中心', '积分管理', '查看批量积分'],
      selectKeys: ['/view/scorerule'],
      openKeys: ['/view/userPages'],
    },
    permission: '',
  },
  {
    path: '/waistcoatUser',
    component: WaistcoatUser,
    routeProps: {
      breadCrumb: ['用户中心', '用户列表', '马甲用户管理'],
      selectKeys: ['/view/userList'],
      openKeys: ['/view/userPages'],
    },
    permission: '',
  },
  {
    path: '/incentiveProblems',
    component: IncentiveProblems,
    routeProps: {
      breadCrumb: ['用户中心', '用户列表', '激励常见问题'],
      selectKeys: ['/view/incentiveProblems'],
      openKeys: ['/view/userPages'],
    },
    permission: '',
  },
  {
    path: '/scorerule',
    component: CreditPolicy,
    routeProps: {
      breadCrumb: ['用户中心', '积分管理'],
      selectKeys: ['/view/scorerule'],
      openKeys: ['/view/userPages'],
    },
    permission: '',
  },
  {
    path: '/usermessageList',
    component: UserMessageList,
    routeProps: {
      breadCrumb: ['用户中心', '用户消息管理'],
      selectKeys: ['/view/usermessageList'],
      openKeys: ['/view/userPages'],
    },
    permission: '',
  },
  {
    path: '/followRecommendManage',
    component: UserRecommend,
    routeProps: {
      breadCrumb: ['用户中心', '推荐关注用户'],
      selectKeys: ['/view/followRecommendManage'],
      openKeys: ['/view/userPages'],
    },
    permission: '',
  },
  {
    path: '/feedbackList',
    component: UserFeedback,
    routeProps: {
      breadCrumb: ['用户中心', '用户反馈'],
      selectKeys: ['/view/feedbackList'],
      openKeys: ['/view/userPages'],
    },
    permission: '',
  },
  {
    path: '/logOffMgr',
    component: DeletePolicy,
    routeProps: {
      breadCrumb: ['用户中心', '用户注销协议'],
      selectKeys: ['/view/logOffMgr'],
      openKeys: ['/view/userPages'],
    },
    permission: '',
  },
  {
    path: '/helpList',
    component: FAQ,
    routeProps: {
      breadCrumb: ['用户中心', '帮助问题管理'],
      selectKeys: ['/view/helpList'],
      openKeys: ['/view/userPages'],
    },
    permission: '',
  },
  {
    path: '/orgAccountList',
    component: OrgnizationUsers,
    routeProps: {
      breadCrumb: ['用户中心', '潮鸣号管理'],
      selectKeys: ['/view/orgAccountList'],
      openKeys: ['/view/userPages'],
    },
    permission: '',
  },
  {
    path: '/authApply',
    component: AuthApply,
    routeProps: {
      breadCrumb: ['用户中心', '潮鸣号管理', '审核认证申请'],
      selectKeys: ['/view/orgAccountList'],
      openKeys: ['/view/userPages'],
    },
    permission: '',
  },
  {
    path: '/longVideoApply',
    component: LongVideoApply,
    routeProps: {
      breadCrumb: ['用户中心', '潮鸣号管理', '审核长视频申请'],
      selectKeys: ['/view/longVideoApply'],
      openKeys: ['/view/userPages'],
    },
    permission: '',
  },
  {
    path: '/cmhAgreement',
    component: CmhAgreement,
    routeProps: {
      breadCrumb: ['用户中心', '潮鸣号管理'],
      selectKeys: ['/view/orgAccountList'],
      openKeys: ['/view/userPages'],
    },
    permission: '',
  },
  {
    path: '/cmhAuthQuestion',
    component: CmhAuthQuestion,
    routeProps: {
      breadCrumb: ['用户中心', '潮鸣号管理'],
      selectKeys: ['/view/orgAccountList'],
      openKeys: ['/view/userPages'],
    },
    permission: '',
  },
  {
    path: '/virtualAccountList',
    component: VirtualAccount,
    routeProps: {
      breadCrumb: ['用户中心', '邀请码管理'],
      selectKeys: ['/view/virtualAccountList'],
      openKeys: ['/view/userPages'],
    },
    permission: '',
  },
  {
    path: '/orgUserArticleList/:id/:name',
    component: OrgUserArticleList,
    routeProps: {
      breadCrumb: ['用户中心', '潮鸣号管理'],
      selectKeys: ['/view/orgAccountList'],
      openKeys: ['/view/userPages'],
    },
    permission: '',
  },
  {
    path: '/cashoutMgr',
    component: CashoutPolicy,
    routeProps: {
      breadCrumb: ['用户中心', '提现协议管理'],
      selectKeys: ['/view/cashoutMgr'],
      openKeys: ['/view/userPages'],
    },
    permission: '',
  },
  {
    path: '/userRightsInterests',
    component: equityMgr,
    routeProps: {
      breadCrumb: ['用户中心', '用户权益'],
      selectKeys: ['/view/userRightsInterests'],
      openKeys: ['/view/userPages'],
    },
    permission: '',
  },
  {
    path: '/mediaReviewWhiteMgr',
    component: WhiteList,
    routeProps: {
      breadCrumb: ['用户中心', '机审白名单 '],
      selectKeys: ['/view/mediaReviewWhiteMgr'],
      openKeys: ['/view/userPages'],
    },
    permission: '',
  },
  {
    path: '/tmhClassMgr',
    component: OrgnizationCategory,
    routeProps: {
      breadCrumb: ['用户中心', '潮鸣号分类管理'],
      selectKeys: ['/view/tmhClassMgr'],
      openKeys: ['/view/userPages'],
    },
    permission: '',
  },
  {
    path: '/orgAccountMgrList/:id/:name',
    component: OrgAccountMgrlist,
    routeProps: {
      breadCrumb: ['用户中心', '潮鸣号分类'],
      selectKeys: ['/view/tmhClassMgr'],
      openKeys: ['/view/userPages'],
    },
    permission: '',
  },
  {
    path: '/chaokestar',
    component: ChaokeStar,
    routeProps: {
      breadCrumb: ['用户中心', '潮客星榜'],
      selectKeys: ['/view/chaokestar'],
      openKeys: ['/view/userPages'],
    },
    permission: '',
  },
  {
    path: '/tmhRankMgr',
    component: OrgRankList,
    routeProps: {
      breadCrumb: ['用户中心', '潮鸣号榜单'],
      selectKeys: ['/view/tmhRankMgr'],
      openKeys: ['/view/userPages'],
    },
    permission: '',
  },
  {
    path: '/ck_rank',
    component: NewCkRankList,
    routeProps: {
      breadCrumb: ['用户中心', '潮客榜单'],
      selectKeys: ['/view/ck_rank'],
      openKeys: ['/view/userPages'],
    },
    permission: '',
  },
  {
    path: '/cmh_rank',
    component: NewCmhRankList,
    routeProps: {
      breadCrumb: ['用户中心', '潮鸣号榜单'],
      selectKeys: ['/view/cmh_rank'],
      openKeys: ['/view/userPages'],
    },
    permission: '',
  },
  {
    path: '/singleCmhRank/:id/:rank_type/:category/:rank_name',
    component: SingleCmhRank,
    routeProps: {
      breadCrumb: ['用户中心', '潮鸣号榜单'],
      selectKeys: ['/view/cmh_rank'],
      openKeys: ['/view/userPages'],
    },
    permission: '',
  },
  {
    path: '/cmhRankClassMgr',
    component: CmhRankClassMgr,
    routeProps: {
      breadCrumb: ['用户中心', '潮鸣号榜单', '参评主体管理'],
      selectKeys: ['/view/cmh_rank'],
      openKeys: ['/view/userPages'],
    },
    permission: '',
  },
  {
    path: '/singleCkRank/:id/:rank_type/:rank_name',
    component: SingleCkRank,
    routeProps: {
      breadCrumb: ['用户中心', '潮客榜单'],
      selectKeys: ['/view/ck_rank'],
      openKeys: ['/view/userPages'],
    },
    permission: '',
  },
  {
    path: '/ckRankBlackList',
    component: CkRankBlacklist,
    routeProps: {
      breadCrumb: ['用户中心', '潮客榜单', '黑名单管理'],
      selectKeys: ['/view/ck_rank'],
      openKeys: ['/view/userPages'],
    },
    permission: '',
  },
  {
    path: '/singleRank/:type',
    component: SingleOrgRank,
    routeProps: {
      breadCrumb: ['用户中心', '潮鸣号榜单'],
      selectKeys: ['/view/tmhRankMgr'],
      openKeys: ['/view/userPages'],
    },
    permission: '',
  },
  {
    path: '/ranklog/:type',
    component: RankLogs,
    routeProps: {
      breadCrumb: ['用户中心', '潮鸣号榜单'],
      selectKeys: ['/view/tmhRankMgr'],
      openKeys: ['/view/userPages'],
    },
    permission: '',
  },
  {
    path: '/creditlevel',
    component: CreditLevel,
    routeProps: {
      breadCrumb: ['用户中心', '积分等级管理'],
      selectKeys: ['/view/creditlevel'],
      openKeys: ['/view/userPages'],
    },
    permission: '',
  },
  {
    path: '/headAuditMgr',
    component: UserImageAudit,
    routeProps: {
      breadCrumb: ['用户中心', '用户头像审核'],
      selectKeys: ['/view/headAuditMgr'],
      openKeys: ['/view/userPages'],
      pageType: 0,
      featureCode: ['head_chaoke_audit_switch', 'head_tmh_audit_switch'],
    },
    permission: '',
  },
  {
    path: '/bgAuditMgr',
    component: UserImageAudit,
    routeProps: {
      breadCrumb: ['用户中心', '用户主页背景审核'],
      selectKeys: ['/view/bgAuditMgr'],
      openKeys: ['/view/userPages'],
      pageType: 1,
      featureCode: ['bg_chaoke_audit_switch', 'bg_tmh_audit_switch'],
    },
    permission: '',
  },
  {
    path: '/nickNameAuditMgr',
    component: UserImageAudit,
    routeProps: {
      breadCrumb: ['用户中心', '用户昵称审核'],
      selectKeys: ['/view/nickNameAuditMgr'],
      openKeys: ['/view/userPages'],
      pageType: 2,
      featureCode: ['nick_chaoke_audit_switch', 'nick_tmh_audit_switch'],
    },
    permission: '',
  },
  {
    path: '/informationAuditMgr',
    component: UserImageAudit,
    routeProps: {
      breadCrumb: ['用户中心', '用户简介审核'],
      selectKeys: ['/view/informationAuditMgr'],
      openKeys: ['/view/userPages'],
      pageType: 3,
      featureCode: ['information_chaoke_audit_switch', 'information_tmh_audit_switch'],
    },
    permission: '',
  },
  {
    path: '/spiderBind',
    component: SpiderBind,
    routeProps: {
      breadCrumb: ['用户中心', '绑定抓取账号'],
      selectKeys: ['/view/spiderBind'],
      openKeys: ['/view/userPages'],
    },
    permission: '',
  },
];
