import DashBoard from '@views/other/dashboard';

import opRoutes from './opRoutes';
import releaseListRoutes from './releaseListRoutes';
import serviceRoutes from './serviceRoutes';
import systemRoutes from './systemRoutes';
import userRoutes from './userRoutes';
import devOpsRoutes from './devOpsRoutes';
import recommendPage from './recommendPage';
import communityRoutes from './communityRoutes';
import creatorRouters from './creatorRouters';
import paperRoutes from './paperRoutes';
import reporterRoutes from './reporterRoutes';
import commentRoutes from './commentRoutes';
export default [
  {
    path: '/dashboard',
    component: DashBoard,
    routeProps: {
      breadCrumb: [],
      selectKeys: [],
    },
    permission: '',
  },
  ...releaseListRoutes,
  ...systemRoutes,
  ...opRoutes,
  ...serviceRoutes,
  ...userRoutes,
  ...devOpsRoutes,
  ...recommendPage,
  ...communityRoutes,
  ...creatorRouters,
  ...paperRoutes,
  ...reporterRoutes,
  ...commentRoutes
];
