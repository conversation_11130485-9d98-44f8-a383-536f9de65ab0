import { lazy } from 'react';

const ExamineMgr = lazy(() => import('../views/comment/examineMgr'));
const ReportMgr = lazy(() => import('../views/comment/reportMgr'));
const AppealMgr = lazy(() => import('../views/comment/appealMgr'));
const Parameter = lazy(() => import('../views/comment/parameter'));

export default [
  {
    path: '/comment/examineMgr',
    component: ExamineMgr,
    routeProps: {
      breadCrumb: ['运营管理', '评论审核', '常规审核'],
      selectKeys: ['/view/comment/examineMgr'],
      openKeys: ['/view/operatePages'],
    },
    permission: '',
  },
  {
    path: '/comment/reportMgr',
    component: ReportMgr,
    routeProps: {
      breadCrumb: ['运营管理', '评论审核', '举报审核'],
      selectKeys: ['/view/comment/reportMgr'],
      openKeys: ['/view/operatePages'],
    },
    permission: '',
  },
  {
    path: '/comment/appealMgr',
    component: AppealMgr,
    routeProps: {
      breadCrumb: ['运营管理', '评论审核', '申诉审核'],
      selectKeys: ['/view/comment/appealMgr'],
      openKeys: ['/view/operatePages'],
    },
    permission: '',
  },
  {
    path: '/comment/parameter',
    component: Parameter,
    routeProps: {
      breadCrumb: ['运营管理', '评论审核', '参数配置'],
      selectKeys: ['/view/comment/parameter'],
      openKeys: ['/view/operatePages'],
    },
    permission: '',
  },
]