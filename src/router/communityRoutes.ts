import { lazy } from 'react';

const CommunityRecommend = lazy(() => import('@app/views/community/communityRecommend'));
const CircleMgr = lazy(() => import('@app/views/community/circleMgr'));
const CircleBlockMgr = lazy(() => import('@app/views/community/circleBlockMgr'));
const CircleContentMgr = lazy(() => import('@app/views/community/circleContentMgr'));
const CirclePictureMgr = lazy(() => import('@app/views/community/circlePictureMgr'));
const CircleOwnerMgr = lazy(() => import('@app/views/community/circleOwnerMgr'));
const CircleAuthorityMgr = lazy(() => import('@app/views/community/circleAuthorityMgr'));
const BoosterCardMgr = lazy(() => import('@app/views/community/boosterCardMgr'));
const BoosterContentMgr = lazy(() => import('@app/views/community/boosterContentMgr'));
const RecommendedUserOperations = lazy(() => import('@app/views/community/component/recommendedUserOperations'));
const TopicRecommendation = lazy(() => import('@app/views/community/component/topicRecommendation'));
const BannerManage = lazy(() => import('@app/views/community/component/bannerManage'));
const TmhList = lazy(() => import('@views/news/tmh'));
const MCNList = lazy(() => import('@views/news/chaokeList'));
const AssistantManager = lazy(() => import('@app/views/operates/assistantManager'));
const UGCAudit = lazy(() => import('@app/views/community/ugcAudit'));
const UGCAuditBlackList = lazy(() => import('@app/views/community/ugcAuditBlackList'));
const UGCPublishRuleMgr = lazy(() => import('@app/views/community/UGCPublishRuleMgr'));
const TopicManager = lazy(() => import('@views/operates/topicManager'));
const TopicAudit = lazy(() => import('@views/operates/topicAudit'));
const UGCTopicRecommend = lazy(() => import('@views/operates/ugcTopicRecommend'));
const TopicArticleList = lazy(() => import('@app/views/operates/topicArticleList'));
const TopManuscript = lazy(() => import('@app/views/operates/topManuscriptMgr'));
const longVideo = lazy(() => import('@app/views/operates/longVideo'));
const longVideoUser = lazy(() => import('@app/views/operates/longVideoUser'));

export default [
  {
    path: '/contentAudit',
    component: UGCAudit,
    routeProps: {
      breadCrumb: ['嘉友圈管理', '帖子审核'],
      selectKeys: ['/view/contentAudit'],
      openKeys: ['/view/communityPages'],
    },
    permission: '',
  },
  {
    path: '/ugcAuditBlackList',
    component: UGCAuditBlackList,
    routeProps: {
      breadCrumb: ['嘉友圈管理', '帖子审核', '发布黑名单'],
      selectKeys: ['/view/contentAudit'],
      openKeys: ['/view/communityPages'],
    },
    permission: '',
  },
  {
    path: '/ugcPublishRuleMgr',
    component: UGCPublishRuleMgr,
    routeProps: {
      breadCrumb: ['嘉友圈管理', '帖子审核', '发帖行为规范'],
      selectKeys: ['/view/contentAudit'],
      openKeys: ['/view/communityPages'],
    },
    permission: '',
  },
  {
    path: '/communityRecommend',
    component: CommunityRecommend,
    routeProps: {
      breadCrumb: ['嘉友圈管理', '推荐页管理'],
      selectKeys: ['/view/communityRecommend'],
      openKeys: ['/view/communityPages'],
    },
    permission: '',
  },
  {
    path: '/circleAIMgr',
    component: AssistantManager,
    routeProps: {
      breadCrumb: ['嘉友圈管理', '数字圈主'],
      selectKeys: ['/view/circleMgr'],
      openKeys: ['/view/communityPages'],
      category: 2
    },
    permission: '',
  },
  {
    path: '/circleMgr',
    component: CircleMgr,
    routeProps: {
      breadCrumb: ['嘉友圈管理', '圈子管理'],
      selectKeys: ['/view/circleMgr'],
      openKeys: ['/view/communityPages'],
    },
    permission: '',
  },
  {
    path: '/circleAuthorityMgr',
    component: CircleAuthorityMgr,
    routeProps: {
      breadCrumb: ['嘉友圈管理', '用户内容审核权限'],
      selectKeys: ['/view/circleAuthorityMgr'],
      openKeys: ['/view/communityPages'],
    },
    permission: '',
  },
  {
    path: '/boosterCardMgr',
    component: BoosterCardMgr,
    routeProps: {
      breadCrumb: ['嘉友圈管理', '助推卡管理'],
      selectKeys: ['/view/boosterCardMgr'],
      openKeys: ['/view/communityPages'],
    },
    permission: '',
  },
  {
    path: '/boosterContentMgr',
    component: BoosterContentMgr,
    routeProps: {
      breadCrumb: ['嘉友圈管理', '助推内容列表'],
      selectKeys: ['/view/boosterContentMgr'],
      openKeys: ['/view/communityPages'],
    },
    permission: '',
  },
  {
    path: '/circleBlockMgr/:id/:name',
    component: CircleBlockMgr,
    routeProps: {
      breadCrumb: ['嘉友圈管理', '圈子管理'],
      selectKeys: ['/view/circleBlockMgr'],
      openKeys: ['/view/communityPages'],
    },
    permission: '',
  },
  {
    path: '/circleContentMgr/:id/:name',
    component: CircleContentMgr,
    routeProps: {
      breadCrumb: ['嘉友圈管理', '圈子管理'],
      openKeys: ['/view/communityPages'],
    },
    permission: '',
  },
  {
    path: '/circlePictureMgr/:id/:name',
    component: CirclePictureMgr,
    routeProps: {
      breadCrumb: ['嘉友圈管理', '圈子管理'],
      openKeys: ['/view/communityPages'],
    },
    permission: '',
  },
  {
    path: '/circleOwnerMgr/:id/:name',
    component: CircleOwnerMgr,
    routeProps: {
      breadCrumb: ['嘉友圈管理', '圈主管理'],
      openKeys: ['/view/communityPages'],
    },
    permission: '',
  },
  {
    path: '/contentList',
    component: MCNList,
    routeProps: {
      breadCrumb: ['嘉友圈管理', '帖子管理'],
      selectKeys: ['/view/contentList'],
      openKeys: ['/view/communityPages'],
    },
    permission: '',
  },
  {
    path: '/tmh',
    component: TmhList,
    routeProps: {
      breadCrumb: ['嘉友圈管理', '潮鸣号内容管理'],
      selectKeys: ['/view/tmh'],
      openKeys: ['/view/communityPages'],
    },
    permission: '',
  },
  {
    path: '/recommendedUserOperations',
    component: RecommendedUserOperations,
    routeProps: {
      breadCrumb: ['嘉友圈管理', '推荐页管理', '推荐用户运营位'],
      selectKeys: ['/view/recommendedUserOperations'],
      openKeys: ['/view/communityPages'],
    },
    permission: '',
  },
  {
    path: '/bannerManage',
    component: BannerManage,
    routeProps: {
      breadCrumb: ['嘉友圈管理', '推荐页管理', '轮播图管理'],
      selectKeys: ['/view/bannerManage'],
      openKeys: ['/view/communityPages'],
    },
    permission: '',
  },
  {
    path: '/topicRecommendation',
    component: TopicRecommendation,
    routeProps: {
      breadCrumb: ['嘉友圈管理', '推荐页管理', '话题推荐位'],
      selectKeys: ['/view/topicRecommendation'],
      openKeys: ['/view/communityPages'],
    },
    permission: '',
  },
  {
    path: '/ugcTopicMgr',
    component: TopicManager,
    routeProps: {
      breadCrumb: ['嘉友圈管理', '话题管理'],
      selectKeys: ['/view/ugcTopicMgr'],
      openKeys: ['/view/communityPages'],
    },
    permission: '',
  },
  {
    path: '/auditTopic',
    component: TopicAudit,
    routeProps: {
      breadCrumb: ['嘉友圈管理', '话题管理', '审核话题'],
      selectKeys: ['/view/ugcTopicMgr'],
      openKeys: ['/view/communityPages'],
    },
    permission: '',
  },
  {
    path: '/ugcTopicRecommend',
    component: UGCTopicRecommend,
    routeProps: {
      breadCrumb: ['嘉友圈管理', '话题管理', '话题推荐'],
      selectKeys: ['/view/ugcTopicRecommend'],
      openKeys: ['/view/communityPages'],
    },
    permission: '',
  },
  {
    path: '/ugcTopicArticle/:id/:name/:type',
    component: TopicArticleList,
    routeProps: {
      breadCrumb: ['嘉友圈管理', '话题管理'],
      selectKeys: ['/view/ugcTopicMgr'],
      openKeys: ['/view/communityPages'],
    },
    permission: '',
  },
  {
    path: '/TopManuscript/:id/:type',
    component: TopManuscript,
    routeProps: {
      breadCrumb: ['嘉友圈管理', '话题管理', '置顶帖子管理'],
      selectKeys: ['/view/ugcTopicMgr'],
      openKeys: ['/view/communityPages'],
    },
    permission: '',
  },
  {
    path: '/longVideoDurationMgr',
    component: longVideo,
    routeProps: {
      breadCrumb: ['嘉友圈管理', '发布长视频权限'],
      selectKeys: ['/view/longVideoDurationMgr'],
      openKeys: ['/view/communityPages'],
    },
    permission: '',
  },
  {
    path: '/longVideoUser/:id',
    component: longVideoUser,
    routeProps: {
      breadCrumb: ['嘉友圈管理', '发布长视频权限', '长视频用户管理'],
      selectKeys: ['/view/longVideoUser'],
      openKeys: ['/view/communityPages'],
    },
    permission: '',
  },
]