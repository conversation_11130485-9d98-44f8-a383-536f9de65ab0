import { lazy } from 'react';

const AdminList = lazy(() => import('@views/system/adminList'));
const ChannelList = lazy(() => import('@views/system/channelList'));
const ReadPreferenceManager = lazy(() => import('@views/system/readPreferenceManager'));
const ShortURLManager = lazy(() => import('@views/system/shortURLManager'));
const InterfaceManager = lazy(() => import('@views/system/interfaceManager'));
const NavManager = lazy(() => import('@views/system/navManager'));
const DesktopGuide = lazy(() => import('@views/system/desktopGuide'));
const NoticeManager = lazy(() => import('@views/system/noticeManager'));
const PermissionGroup = lazy(() => import('@views/system/permissionGroup'));
const PermissionGroupUsers = lazy(() => import('@views/system/permissionGroupUsers'));
const PermissionManager = lazy(() => import('@views/system/permissionManager'));
const ReleaseManager = lazy(() => import('@views/system/releaseManager'));
const AppCrashManager = lazy(() => import('@app/views/system/appCrashManager'));
const ColdStartManager = lazy(() => import('@app/views/system/coldStartManager'));
const ChaokeRecommend = lazy(() => import('@app/views/system/chaokeRecommend'));
const ThematiTemplate = lazy(() => import('@app/views/system/thematiTemplate'));
const OperationLog = lazy(() => import('@app/views/system/operationLog'));
const ConfigOptionMgr = lazy(() => import('@app/views/system/configOptionMgr'));
const AiChannel = lazy(() => import('@app/views/system/aiChannelMgr'));
const AppFeatureSwitch = lazy(() => import('@views/operates/appFeatureSwitch'));
const AboutManager = lazy(() => import('@views/operates/aboutManager'));
const PrivacyPolicy = lazy(() => import('@views/operates/privacyPolicy'));
const WhiteHost = lazy(() => import('@views/operates/whiteHost'));
const ImageDetectionMgr = lazy(() => import('@app/views/creator/imageDetectionMgr'));

export default [
  {
    path: '/channel_ai',
    component: AiChannel,
    routeProps: {
      breadCrumb: ['系统管理', 'AI频道'],
      selectKeys: ['/view/channel_ai'],
      openKeys: ['/view/systemPages'],
    },
    permission: '',
  },
  {
    path: '/appFeatureSwitch',
    component: AppFeatureSwitch,
    routeProps: {
      breadCrumb: ['系统管理', '特殊功能管理'],
      selectKeys: ['/view/appFeatureSwitch'],
      openKeys: ['/view/systemPages'],
    },
    permission: '',
  },
  {
    path: '/adminList',
    component: AdminList,
    routeProps: {
      breadCrumb: ['系统管理', '管理员列表'],
      selectKeys: ['/view/adminList'],
      openKeys: ['/view/systemPages'],
    },
    permission: '',
  },
  {
    path: '/permissionGroup',
    component: PermissionGroup,
    routeProps: {
      breadCrumb: ['系统管理', '权限组列表'],
      selectKeys: ['/view/permissionGroup'],
      openKeys: ['/view/systemPages'],
    },
    permission: '',
  },
  {
    path: '/permissionGroupUsers/:id/:name',
    component: PermissionGroupUsers,
    routeProps: {
      breadCrumb: ['系统管理', '权限组列表'],
      selectKeys: ['/view/permissionGroup'],
      openKeys: ['/view/systemPages'],
    },
    permission: '',
  },
  {
    path: '/permissionManager',
    component: PermissionManager,
    routeProps: {
      breadCrumb: ['系统管理', '权限管理'],
      selectKeys: ['/view/permissionManager'],
      openKeys: ['/view/systemPages'],
    },
    permission: '',
  },
  {
    path: '/appNavManager',
    component: NavManager,
    routeProps: {
      breadCrumb: ['系统管理', '导航管理'],
      selectKeys: ['/view/appNavManager'],
      openKeys: ['/view/systemPages'],
    },
    permission: '',
  },
  {
    path: '/desktopGuide',
    component: DesktopGuide,
    routeProps: {
      breadCrumb: ['系统管理', '导航管理', '添加到桌面'],
      selectKeys: ['/view/appNavManager'],
      openKeys: ['/view/systemPages'],
    },
    permission: '',
  },
  {
    path: '/channelManager',
    component: ChannelList,
    routeProps: {
      breadCrumb: ['系统管理', '频道管理'],
      selectKeys: ['/view/channelManager'],
      openKeys: ['/view/systemPages'],
    },
    permission: '',
  },
  {
    path: '/readPreferenceManager',
    component: ReadPreferenceManager,
    routeProps: {
      breadCrumb: ['系统管理', '阅读偏好管理'],
      selectKeys: ['/view/readPreferenceManager'],
      openKeys: ['/view/systemPages'],
    },
    permission: '',
  },
  {
    path: '/releaseManager',
    component: ReleaseManager,
    routeProps: {
      breadCrumb: ['系统管理', '版本管理'],
      selectKeys: ['/view/releaseManager'],
      openKeys: ['/view/systemPages'],
    },
    permission: '',
  },
  {
    path: '/noticeManager',
    component: NoticeManager,
    routeProps: {
      breadCrumb: ['系统管理', '公告管理'],
      selectKeys: ['/view/noticeManager'],
      openKeys: ['/view/systemPages'],
    },
    permission: '',
  },
  {
    path: '/interfaceSwitchMgr',
    component: InterfaceManager,
    routeProps: {
      breadCrumb: ['系统管理', '接口开关'],
      selectKeys: ['/view/interfaceSwitchMgr'],
      openKeys: ['/view/systemPages'],
    },
    permission: '',
  },
  {
    path: '/clientErrorLog',
    component: AppCrashManager,
    routeProps: {
      breadCrumb: ['系统管理', '用户报错'],
      selectKeys: ['/view/clientErrorLog'],
      openKeys: ['/view/systemPages'],
    },
    permission: '',
  },
  {
    path: '/hobbyCntMgr',
    component: ColdStartManager,
    routeProps: {
      breadCrumb: ['系统管理', '冷启动偏好内容管理'],
      selectKeys: ['/view/hobbyCntMgr'],
      openKeys: ['/view/systemPages'],
    },
    permission: '',
  },
  {
    path: '/chaokeRecommend',
    component: ChaokeRecommend,
    routeProps: {
      breadCrumb: ['系统管理', '潮友推荐服务'],
      selectKeys: ['/view/chaokeRecommend'],
      openKeys: ['/view/systemPages'],
    },
    permission: '',
  },
  {
    path: '/adminLogMgr',
    component: OperationLog,
    routeProps: {
      breadCrumb: ['系统管理', '操作日志'],
      selectKeys: ['/view/adminLogMgr'],
      openKeys: ['/view/systemPages'],
    },
    permission: '',
  },
  {
    path: '/configOptionMgr',
    component: ConfigOptionMgr,
    routeProps: {
      breadCrumb: ['系统管理', '配置项管理'],
      selectKeys: ['/view/configOptionMgr'],
      openKeys: ['/view/systemPages'],
    },
    permission: '',
  },
  {
    path: '/shortUrlMgr',
    component: ShortURLManager,
    routeProps: {
      breadCrumb: ['系统管理', '短链接管理'],
      selectKeys: ['/view/shortUrlMgr'],
      openKeys: ['/view/systemPages'],
    },
    permission: '',
  },
  {
    path: '/aboutManager',
    component: AboutManager,
    routeProps: {
      breadCrumb: ['系统管理', '关于管理'],
      selectKeys: ['/view/aboutManager'],
      openKeys: ['/view/systemPages'],
    },
    permission: '',
  },
  {
    path: '/privacyPolicyMgr',
    component: PrivacyPolicy,
    routeProps: {
      breadCrumb: ['系统管理', '隐私政策'],
      selectKeys: ['/view/privacyPolicyMgr'],
      openKeys: ['/view/systemPages'],
    },
    permission: '',
  },
  // {
  //   path: '/subjectTemplateConfigMgr',
  //   component: ThematiTemplate,
  //   routeProps: {
  //     breadCrumb: ['系统管理', '专题模板配置'],
  //     selectKeys: ['/view/subjectTemplateConfigMgr'],
  //     openKeys: ['/view/subjectTemplateConfigMgr'],
  //   },
  //   permission: '',
  // },
  {
    path: '/shareWhiteHost',
    component: WhiteHost,
    routeProps: {
      breadCrumb: ['系统管理', '分享白名单'],
      selectKeys: ['/view/shareWhiteHost'],
      openKeys: ['/view/systemPages'],
    },
    permission: '',
  },
  {
    path: '/reviewImageConfigMgr',
    component: ImageDetectionMgr,
    routeProps: {
      breadCrumb: ['系统管理', '图片检测配置'],
      selectKeys: ['/view/reviewImageConfigMgr'],
      openKeys: ['/view/systemPages'],
    },
    permission: '',
  },


];
