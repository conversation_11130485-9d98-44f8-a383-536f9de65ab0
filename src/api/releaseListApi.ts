/* eslint-disable import/prefer-default-export */
import {
  RequestBody,
  RequestInfo,
  IResReleaseListAllData,
  IResFocusListAllData,
  IResChannelRecommendAllData,
  IOperationLogRes,
  TChannelRecommendArticleListRes,
  TChannelRecommendDetailRes,
  IResTopNewsListAllData,
} from '@app/types';
import fetch from '@app/utils/fetch';
import {
  TUserDeletedNewsResData,
  TUserDeletedListFilter,
  TUserComplaintListFilter,
  TUserComplaintNewsResData,
  TUserComplaintTag,
  TListArticleRecommendAllData,
} from '@app/views/news/news';
import { getArticleItem } from '@app/views/news/sortArticleItem';

export const releaseListApi = {
  exportReleaseList: (body: RequestBody) =>
    fetch.blob<IResReleaseListAllData>('channel_article/release_list', body),
  getReleaseList: (body: RequestBody, request?: RequestInfo) =>
    fetch.get<IResReleaseListAllData>('channel_article/release_list', body, request),
  getFocusList: (body: RequestBody, request: RequestInfo) =>
    fetch.get<IResFocusListAllData>('channel_focus/list', body, request),
  getRecommendsList: (body: RequestBody, request: RequestInfo) =>
    fetch.get<IResChannelRecommendAllData>('channel_recommend/list', body, request),

  getCommentSystemUrl: (body: RequestBody = {}) =>
    fetch.get<{ url: string }>('comment/find_comment_view_url', body),
  toMlf: (type: 'mlf_edit_url' | 'mlf_detail_url', body: RequestBody) =>
    fetch.get<{ url: string }>(`channel_article/${type}`, body),
  getOperateLog: (body: RequestBody) => fetch.get<IOperationLogRes>('channel_article/logs', body),
  getRecommendOperateLog: (body: RequestBody) =>
    fetch.get<IOperationLogRes>('content_recommend/logs', body),
  revokeNews: (body: RequestBody) => fetch.post<{}>('channel_article/cancel_release', body),
  revokeAllNews: (body: RequestBody) =>
    fetch.post<{}>('channel_article/cancel_tochannel_release', body),
  pushTop: (body: RequestBody) => fetch.post<{}>('channel_article/push_top', body),
  pushMultiChannel: (body: RequestBody) => fetch.post<{}>('channel_article/push_multi_channel', body),
  releaseExchangeOrder: (body: RequestBody) =>
    fetch.post<{}>('channel_article/exchange_order', body),
  releaseChangeVisible: (body: RequestBody) => fetch.post<{}>('channel_article/set_visible', body),
  releaseCancelFix: (body: RequestBody) => fetch.post<{}>('channel_article/cancel_fixed', body),
  releaseChangeOrder: (body: RequestBody) => fetch.post<{}>('channel_article/move_position', body),
  focusChangeCarousel: (body: RequestBody) => fetch.get<{}>('channel/set_properties', body),
  focusChangePicMode: (body: RequestBody) => fetch.get<{}>('channel/set_focus_pic_mode', body),
  focusChangeDisplayed: (body: RequestBody) =>
    fetch.post<{}>('channel_focus/update_displayed', body),
  focusExchangeOrder: (body: RequestBody) => fetch.post<{}>('channel_focus/exchange_order', body),
  getChannelRecommendArticles: (body: RequestBody) =>
    fetch.get<TChannelRecommendArticleListRes>('channel_recommend/list_articles', body),
  channelRecommendChangeMode: (body: RequestBody) =>
    fetch.post<{}>('channel_recommend/switch', body),
  channelRecommendDelete: (body: RequestBody) => fetch.post<{}>('channel_recommend/delete', body),
  channelRecommendChangeStatus: (body: RequestBody) =>
    fetch.post<{}>('channel_recommend/update_status', body),
  getChannelRecommendDetail: (body: RequestBody) =>
    fetch.get<TChannelRecommendDetailRes>('channel_recommend/detail', body),
  channelRecommendSort: (body: RequestBody) =>
    fetch.post<{}>('channel_recommend/update_sort', body),
  submitChannelRecommendDetail: (type: string, body: RequestBody) =>
    fetch.post<{}>(`channel_recommend/${type}`, body),

  releaseChangeRecommend: (body: RequestBody) =>
    fetch.post<{}>('channel_article/update_proposal_hidden', body),

  // 用户发起的删除内容
  getUserDeletedList: (body: TUserDeletedListFilter, request: RequestInfo) =>
    fetch.get<TUserDeletedNewsResData>('user_video/list_deleted', body, request),
  deleteUserDeleted: (body: { id: number }) => fetch.post('/user_video/delete', body),

  // 潮客频道独立接口
  chaokeChangeOrder: (body: RequestBody) => fetch.post('ugc_article/move_position', body),
  chaokeExchangeOrder: (body: RequestBody) => fetch.post('ugc_article/exchange_order', body),
  chaokeChangeVisible: (body: RequestBody) => fetch.post('ugc_article/set_visible', body),
  chaokeRevokeNews: (body: RequestBody) => fetch.post('ugc_article/cancel_release', body),

  // 用户投诉内容
  getUserComplaintTags: () =>
    fetch.get<{ list: TUserComplaintTag[] }>('account_complaint/tag_list'),
  getUserComplaintList: (body: TUserComplaintListFilter, request: RequestInfo) =>
    fetch.get<TUserComplaintNewsResData>('account_complaint/list', body, request),

  // 置顶新闻管理
  getTopArticlesList: (body: RequestBody, request: RequestInfo) =>
    fetch.get<IResTopNewsListAllData>('articles_top/list', body, request),
  updateTopArticleSort: (body: RequestBody) => fetch.post('articles_top/update_sort', body),
  cancelTopArticle: (body: RequestBody) => fetch.post('articles_top/cancel', body),
  createTopArticle: (body: RequestBody) => fetch.post('articles_top/save', body),

  // 置顶新闻管理
  getImportantArticlesList: (body: RequestBody, request: RequestInfo) =>
    fetch.get<IResTopNewsListAllData>('articles_top/list', { ...body, type: 1 }, request),
  updateImportantArticleSort: (body: RequestBody) =>
    fetch.post('articles_top/update_sort', { ...body, type: 1 }),
  cancelImportantArticle: (body: RequestBody) =>
    fetch.post('articles_top/cancel', { ...body, type: 1 }),
  createImportantArticle: (body: RequestBody) =>
    fetch.post('articles_top/save', { ...body, type: 1 }),

  // 视频轮播和汇总新闻推荐位
  getListArticleRecommendList: (body: RequestBody, request: RequestInfo) =>
    fetch.get<TListArticleRecommendAllData>(
      'channel_recommend/list_article_recommend',
      body,
      request
    ),
  createListArticleRecommend: (body: RequestBody) =>
    fetch.post('channel_recommend/create_single_recommend', body),
  updateListArticleRecommendPic: (body: RequestBody) =>
    fetch.post('type_recommend/update_pic_url', body),
  updateListArticleRecommendSort: (body: RequestBody) =>
    fetch.post('type_recommend/update_sort', body),
  deleteListArticleRecommend: (body: RequestBody) => fetch.post('type_recommend/delete', body),

  // 推荐位排序接口
  updateRecommendPosition: (body: RequestBody) =>
    fetch.post('type_recommend/update_position', body),

  // 潮客潮语
  getCommentRecommendList: (body: RequestBody, request: RequestInfo) =>
    fetch.get('channel_recommend/list_comment_recommend', body, request),
  createCommentRecommend: (body: RequestBody) =>
    fetch.json('channel_recommend/create_comment_recommend', body),
  deleteCommentRecommend: (body: RequestBody) => fetch.post('type_recommend/delete', body),
  updateCommentRecommendSort: (body: RequestBody) => fetch.post('type_recommend/update_sort', body),
  getCommentList: (body: RequestBody) => fetch.get('comment/list', body),
  getHotNewsList: () => fetch.get('channel_recommend/list_hot_news'),
  getCommentNewsList: (body: RequestBody) => fetch.get('channel_article/search_article', body),

  // 稿件播放量数据
  getArticlePVDetail: (body: RequestBody) => fetch.get('article_metadata/detail', body),
  updateArticlePV: (body: RequestBody) => fetch.post('article_metadata/change_pv', body),

  // 直播置顶稿件
  getLiveTopList: (body: RequestBody, request?: RequestInfo) =>
    fetch.get('live_top/list', body, request),
  createLiveTop: (body: RequestBody) => fetch.post('live_top/create', body),
  exchangeLiveTopOrder: (body: RequestBody) => fetch.post('live_top/update_sort', body),
  deleteLiveTop: (body: RequestBody) => fetch.post('live_top/delete', body),

  // 直播推荐位
  getLiveRecommendList: (body: RequestBody, request?: RequestInfo) =>
    fetch.get('live_recommend/list', body, request),
  createLiveRecommend: (body: RequestBody) => fetch.post('live_recommend/create', body),
  deleteLiveRecommend: (body: RequestBody) => fetch.post('live_recommend/delete', body),
  updateLiveRecommend: (body: RequestBody) => fetch.post('live_recommend/update', body),
  updateLiveRecommendStatus: (body: RequestBody) =>
    fetch.post('live_recommend/update_status', body),
  getLiveRecommendArticleList: (body: RequestBody, request?: RequestInfo) =>
    fetch.get('live_recommend/list_articles', body, request),
  updateLiveRecommendArticleSort: (body: RequestBody) =>
    fetch.post('live_recommend/update_article_sort', body),

  updateFocusPosition: (body: RequestBody) => fetch.post('channel/set_position', body),

  getLiveInfo: (body: RequestBody) => fetch.get('channel_article/get_live_info', body),
  updateLiveInfo: (body: RequestBody) => fetch.post('channel_article/update_live_info', body),

  getReleaseOperateRecommendList: (body: RequestBody, request?: RequestInfo) =>
    fetch.get('release_recommend/list', body, request),
  updateRORecommend: (body: RequestBody) => fetch.post('release_recommend/update', body),
  createRORecommend: (body: RequestBody) => fetch.post('release_recommend/create', body),
  deleteRORecommend: (body: RequestBody) => fetch.post('release_recommend/delete', body),
  updateRORecommendStatus: (body: RequestBody) =>
    fetch.post('release_recommend/update_status', body),

  // 视频轮播推荐位
  getVideoCarouselRecommendList: (body: RequestBody, request?: RequestInfo) =>
    fetch.get('recommend/list', { type: 7, ...body }, request),
  updateVideoCarouselRecommend: (body: RequestBody) =>
    fetch.post('recommend/update', { type: 7, ...body }),
  createVideoCarouselRecommend: (body: RequestBody) =>
    fetch.post('recommend/create', { type: 7, ...body }),
  deleteVideoCarouselRecommend: (body: RequestBody) =>
    fetch.post('recommend/delete', { type: 7, ...body }),
  updateVideoCarouselRecommendStatus: (body: RequestBody) =>
    fetch.post('recommend/update_status', { type: 7, ...body }),
  getVideoCarouselRecommendDetail: (body: RequestBody) =>
    fetch.get('recommend/get_recommend_detail', { type: 7, ...body }),
  updateVideoCarouselRecommendArticleSort: (body: RequestBody) =>
    fetch.post('recommend/update_article_sort', { type: 7, ...body }),
  deleteVideoCarouselRecommendArticle: (body: RequestBody) =>
    fetch.post('recommend/article_delete', { type: 7, ...body }),

  // 广告位
  getAdList: (body: RequestBody, request: RequestInfo) =>
    fetch.get('advertisement_recommend/list', body, request),
  getAdDetail: (body: RequestBody) => fetch.get('advertisement_recommend/detail', body),
  createAd: (body: RequestBody) => fetch.json('advertisement_recommend/create', body),
  deleteAd: (body: RequestBody) => fetch.post('advertisement_recommend/delete', body),
  updateAd: (body: RequestBody) => fetch.json('advertisement_recommend/update', body),
  updateAdStatus: (body: RequestBody) => fetch.post('advertisement_recommend/update_status', body),

  // 新闻汇总推荐位
  getSummaryRecommentList: (body: RequestBody, request?: RequestInfo) =>
    fetch.get('recommend/list', { type: 8, ...body }, request),
  updateSummaryRecommend: (body: RequestBody) =>
    fetch.post('recommend/update', { type: 8, ...body }),
  createSummaryRecommend: (body: RequestBody) =>
    fetch.post('recommend/create', { type: 8, ...body }),
  deleteSummaryRecommend: (body: RequestBody) =>
    fetch.post('recommend/delete', { type: 8, ...body }),
  updateSummaryRecommendStatus: (body: RequestBody) =>
    fetch.post('recommend/update_status', { type: 8, ...body }),
  getSummaryRecommendDetail: (body: RequestBody) =>
    fetch.get('recommend/get_recommend_detail', { type: 8, ...body }),
  updateSummaryRecommendArticleSort: (body: RequestBody) =>
    fetch.post('recommend/update_article_sort', { type: 8, ...body }),
  deleteSummaryRecommendArticle: (body: RequestBody) =>
    fetch.post('recommend/article_delete', { type: 8, ...body }),
  // 话题推荐位
  getTopicRecommendList: (body: RequestBody, request: RequestInfo) =>
    fetch.get('topic_recommend/list', body, request),
  getTopicRecommendDetail: (body: RequestBody) =>
    fetch.get('topic_recommend/get_recommend_detail', body),
  createTopicRecommend: (body: RequestBody) => fetch.post('topic_recommend/create', body),
  updateTopicRecommend: (body: RequestBody) => fetch.post('topic_recommend/update', body),
  updateTopicRecommendStatus: (body: RequestBody) =>
    fetch.post('topic_recommend/update_status', body),
  deleteTopicRecommend: (body: RequestBody) => fetch.post('topic_recommend/delete', body),
  sortTopicRecommendTopic: (body: RequestBody) =>
    fetch.post('topic_recommend/update_topic_sort', body),

  // 站队组件设置
  getCommentUpdataH5ActivityUrl: (body: RequestBody) =>
    fetch.post('channel_article/updateH5ActivityUrl', body),

  //
  saveUgcVideoRecommend: (body: RequestBody) => fetch.post('ugc_video_recommend/save', body),
  getUgcVideoRecommendlist: () => fetch.get('ugc_video_recommend/list'),

  // 关联活动
  getActList: (body: RequestBody, request?: RequestInfo) =>
    fetch.get('activity/enableList', body, request),
  saveActivity: (body: RequestBody) => fetch.post('channel_article/associatedActivity', body),
  getReadyAct: (body: RequestBody, request?: RequestInfo) =>
    fetch.get('channel_article/getAssociatedActivity', body, request),

  // 潮鸣号置顶稿件
  // 获取列表
  getTmhToppingList: (body: RequestBody, request?: RequestInfo) =>
    fetch.get('article_video_top/list', body, request),
  // 排序
  sortTmhTopping: (body: RequestBody) => fetch.post('article_video_top/update_sort', body),
  // 新建
  creatTmhTopping: (body: RequestBody) => fetch.post('article_video_top/save', body),
  // 取消置顶
  cancelTmhTopping: (body: RequestBody) => fetch.post('article_video_top/cancel', body),
  // 获取频道下稿件列表
  getTopicNewsList: (body: RequestBody, request?: RequestInfo) =>
    fetch.get('channel_article/search_of_channel', body, request),
  /* 以下接口暂时不用
  submitNewsCardDetail: (type: string, body: RequestBody) =>
    fetch.post(`channel_news_card/${type}`, body),
  newsCardDelete: (body: RequestBody) => fetch.post('channel_news_card/delete', body),
  newsCardChangeStatus: (body: RequestBody) => fetch.post('channel_news_card/update_status', body),
  getNewsCardDetail: (body: RequestBody) => fetch.get('channel_news_card/detail', body),

  getColumnRecommendList: (body: RequestBody, request: RequestInfo) =>
    fetch.get('column_recommend/list', body, request),
  getColumnRecommendClasses: (body: RequestBody) => fetch.get('column_recommend/list_class', body),
  getColumnRecommendColumns: (body: RequestBody) => fetch.get('column_recommend/list_column', body),
  sortColumnRecommend: (body: RequestBody) => fetch.post('column_recommend/sort', body),
  enableColumnRecommend: (type: string, body: RequestBody) =>
    fetch.post(`column_recommend/${type}`, body),
  deleteColumnRecommend: (body: RequestBody) => fetch.post('column_recommend/delete', body),
  createColumnRecommend: (body: RequestBody) => fetch.post('column_recommend/create', body),
  */

  // 4.0.0 直播模块
  // 删除活动项目接口
  deleteLiveActive: (body: RequestBody) => fetch.post('live_market/delete', body),
  // 活动项目列表数据展示
  getLiveList: (body: RequestBody, request?: RequestInfo) =>
    fetch.get('live_market/list', body, request),
  // 活动项目选择 (下拉接口)
  qySelectList: (body: RequestBody, request?: RequestInfo) =>
    fetch.get('live_market/qy_activity_list', body, request),
  // 添加方案至列表接口
  saveToLive: (body: RequestBody) => fetch.post('live_market/save', body),
  // 直播广播保存
  saveBroadcast: (body: RequestBody) => fetch.post('live_propaganda/save_broadcast', body),
  // 直播广播数据展示
  getBroadcastList: (body: RequestBody, request?: RequestInfo) =>
    fetch.get('live_propaganda/get_broadcast', body, request),
  // 浮窗广告保存/更新
  saveSuspendedAd: (body: RequestBody) => fetch.post('live_propaganda/save_suspended_ad', body),
  //浮窗广告展示
  getSuspendedAdDetail: (body: RequestBody, request?: RequestInfo) =>
    fetch.get('live_propaganda/get_suspended_ad', body, request),
  // 获取直播间类型
  getLiveType: (body: RequestBody, request?: RequestInfo) =>
    fetch.get('channel_article/get_live_room_type', body, request),
  // 直播间类型保存/更新
  getLiveTypeSave: (body: RequestBody) => fetch.post('channel_article/save_live_room_type', body),

  // 城市频道 关联城市行政区划
  getAssociatedArea: (body: RequestBody) => fetch.post('channel/associated_area_gov', body),
  // 查询省市行政区划列表
  getAreaGovList: (body: RequestBody, request?: RequestInfo) =>
    fetch.get('area_gov/list', body, request),
  // 更新省市行政区划位置排序
  sortAreaGov: (body: RequestBody) => fetch.post('area_gov/update_sort', body),
  // 更新省市行政区划显示状态
  showAreaGovStatus: (body: RequestBody) => fetch.post('area_gov/update_show_status', body),
  // 稿件置频道的后的数据查询接口
  getToChannelList: (body: RequestBody) => fetch.get('channel_article/to_channel_list', body),
  // 签发平台—推至/取消学习强国
  changePushShuCangStatus: (body: RequestBody) =>
    fetch.post('channel_article/update_push_shu_cang_qg', body),
  // 稿件显示/隐藏设置
  getVisibleConfig: (body: RequestBody) => fetch.get('channel_article/visible_config', body),
  // 外部撤稿(设置分发设置)
  withoutRevokeNews: (body: RequestBody) => fetch.post('channel_article/set_rss_config', body),

  // 内容推荐位创建接口
  createContentRecommend: (body: RequestBody) => fetch.post('content_recommend/create', body),
  // 内容推荐位更新接口
  updateContentRecommend: (body: RequestBody) => fetch.post('content_recommend/update', body),
  // 内容推荐位详情接口
  getContentRecommend: (body: RequestBody) => fetch.get('content_recommend/detail', body),

  // 慢直播查询城市慢直播标题
  getSlowLiveTitle: () => fetch.get('channel_article/get_title_slow_live'),
  // 慢直播设置城市慢直播标题
  setSlowLiveTitle: (body: RequestBody) => fetch.post('channel_article/set_title_slow_live', body),
  // 慢直播查询慢直播设置
  getSlowLiveConfig: (body: RequestBody) => fetch.get('channel_article/get_config_slow_live', body),
  // 慢直播调整慢直播设置
  configSlowLive: (body: RequestBody) => fetch.post('channel_article/config_slow_live', body),
  // 取消/设置为慢直播
  toggleSlowLive: (body: RequestBody) => fetch.post('channel_article/set_slow_live', body),
  // 切换展示样式 live_show_type 直播展示状态，0-预告，1-直播单列，2-直播双列
  toggleShowType: (body: RequestBody) => fetch.post('channel_article/update_live_show_type', body),

  // 慢直播排序
  slowLiveExchangeOrder: (body: RequestBody) =>
    fetch.post<{}>('channel_article/slow_live_exchange_order', body),

  // 预览详情页
  previewDetail: (body: RequestBody) => fetch.get('channel_article/preview', body),

  // 亚运频道焦点图，走马灯保存接口
  contentRecommendFocusSave: (body: RequestBody) =>
    fetch.post('content_recommend/focus_save', body),
  // 亚运频道焦点图，走马灯获取接口
  contentRecommendFocusDetail: (body: RequestBody) =>
    fetch.get('content_recommend/focus_detail', body),
  // 图片色调获取接口
  contentRecommendGetColor: (body: RequestBody) =>
    fetch.get('content_recommend/pic_info_get', body),

  // 稿件解锁
  articleUnlock: (body: RequestBody) => fetch.get('channel_article/unlock', body),

  getCircleArticleFixDetail: (body: RequestBody) => fetch.get('circle/article_fix/detail', body),
  editCircleArticleFixDetail: (body: RequestBody) => fetch.post('circle/article_fix/edit', body),

  channelBatchSort: (url: string, body: RequestBody) => fetch.json(url, body),

  getArticleEditUrl: (body: RequestBody) => fetch.get('ugc_article/edit_url', body),

  //稿件相关服务保存接口
  channelArticleRelatedServicesSave: (body: RequestBody) => fetch.post('channel_article/related_services_save', body),
};
