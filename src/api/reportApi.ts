/* eslint-disable import/prefer-default-export */
import { IOperationLogRes, RequestBody, RequestInfo } from '@app/types';
import fetch from '@app/utils/fetch';
import { ProposalAllData, TopicAllData } from '@app/views/operates/operates';

export const reportApi = {
  // 记者搜索
  searchReportUser: (body: RequestBody) => fetch.get('report/user/search', body),
  // 推荐报料搜索
  searchRecommendReport: (body: RequestBody) => fetch.get('report/search', body),

  // 报料详情
  getReportDetail: (body: RequestBody) => fetch.get('report/detail', body),
  getOriginalReportDetail: (body: RequestBody) => fetch.get('report/copy_detail', body),
  // 修改审核状态 0-待审核，1-审核通过，2-审核不通过
  changeAuditStatus: (body: RequestBody) => fetch.post('report/change_audit_status', body),
  // 删除报料 deep=1删除 后台删除 可以不传
  // deleteReport: (body: RequestBody) => fetch.post('report/delete', body),
  // 编辑爆料
  updateReport: (body: RequestBody) => fetch.json('report/update', body),
  // 修改领域
  updateReportClassify: (body: RequestBody) => fetch.post('report/update_classify', body),
  updateReportTime: (body: RequestBody) => fetch.post('report/update_report_time', body),

  // 指派记者
  updateReporter: (body: RequestBody) => fetch.post('report/update_reporter', body),
  // 关联稿件
  reportRelatedArticle: (body: RequestBody) => fetch.post('report/update_related_article', body),
  // 设为办结
  setReportFinish: (body: RequestBody) => fetch.post('report/finish', body),

  // 编辑功能图片配置
  updatePicConfig: (body: RequestBody) => fetch.post('type_recommend/report_pic_config_save', body),
  getPicConfig: (body: RequestBody) => fetch.get('type_recommend/report_pic_config_detail', body),

  // 更新备注
  updateReportRemark: (body: RequestBody) => fetch.post('report/update_remark', body),

  // 进展
  // 进展列表
  reportProcessList: (body: RequestBody, request?: RequestInfo) =>
    fetch.get('report/process/list', body, request),
  // 添加进展
  addReportProcess: (body: RequestBody) => fetch.post('report/process/create', body),
  // 更新进展
  updateReportProcess: (body: RequestBody) => fetch.post('report/process/update', body),
  // 删除进展
  deleteReportProcess: (body: RequestBody) => fetch.post('report/process/delete', body),

  // 回复
  // 回复列表
  reportReplyList: (body: RequestBody, request?: RequestInfo) =>
    fetch.get('report/reply/list', body, request),
  // 添加进展
  addReportComment: (body: RequestBody) => fetch.post('report/reply/create', body),
  // 更新进展
  updateReportComment: (body: RequestBody) => fetch.post('report/reply/update', body),
  // 删除进展
  deleteReportComment: (body: RequestBody) => fetch.post('report/reply/delete', body),
  // 报料列表
  getMaterialsList: (body: RequestBody, request?: RequestInfo) =>
    fetch.get('report/audit_list', body, request),
  // 跟进列表
  getFollowList: (body: RequestBody, request?: RequestInfo) =>
    fetch.get('report/list', body, request),
  // 推荐列表
  getRecommendMatList: (body: RequestBody, request?: RequestInfo) =>
    fetch.get('report/recommend/list', body, request),
  // 推荐列表排序
  sortRecommendMatList: (body: RequestBody) => fetch.post('report/recommend/update_sort', body),
  // 取消推荐
  cancelRecommendReport: (body: RequestBody) => fetch.post('report/recommend/cancel', body),

  // 记者/专家列表接口
  getReportUserList: (body: RequestBody, request?: RequestInfo) =>
    fetch.get('report/user/list', body, request),
  // 记者/专家排序接口
  sortReportUser: (body: RequestBody) => fetch.post('report/user/exchange_order', body),
  // 记者/专家指定位置排序
  moveReportUserPosition: (body: RequestBody) => fetch.post('report/user/move_position', body),
  // 记者/专家删除接口
  delReportUser: (body: RequestBody) => fetch.post('report/user/delete', body),
  // 添加推荐
  addRecommendReport: (body: RequestBody) => fetch.post('report/recommend/add', body),

  // 创建记者
  createReportUser: (body: RequestBody) => fetch.json('report/user/create', body),
  // 更新记者
  updateReportUser: (body: RequestBody) => fetch.json('report/user/update', body),

  getRecommendReportCount: (body: RequestBody) => fetch.get('report/articlenum_query', body),
  saveReportHomeConfig: (body: RequestBody) => fetch.post('report/home/<USER>/edit', body),

  getReportHomeConfigList: (body: RequestBody, request?: RequestInfo) =>
    fetch.get('report/home/<USER>/list', body, request),
  getReportRecommendDataList: (body: RequestBody, request?: RequestInfo) =>
    fetch.get('recommend_data/list', body, request),


  // 领域分类列表
  getReportFieldList: (body: RequestBody, request?: RequestInfo) =>
    fetch.get('report/field/list', body, request),
  getAllReportFieldList: (body: RequestBody, request?: RequestInfo) =>
    fetch.get('report/field/all_simple_list', body, request),
  getSimpleReportFieldList: (body: RequestBody, request?: RequestInfo) =>
    fetch.get('report/field/simple_list', body, request),
  // 创建记者
  createReportField: (body: RequestBody) => fetch.post('report/field/create', body),
  eidtReportField: (body: RequestBody) => fetch.post('report/field/edit', body),
  deleteReportField: (body: RequestBody) => fetch.post('report/field/delete', body),
  sortReportField: (body: RequestBody) => fetch.post('report/field/update_sort', body),
  // 领域分类权限
  getReportFieldPermList: (body: RequestBody, request?: RequestInfo) =>
    fetch.get('report/field/permission/list', body, request),
  createReportFieldPerm: (body: RequestBody) => fetch.post('report/field/permission/save', body),
  eidtReportFieldPerm: (body: RequestBody) => fetch.post('report/field/permission/edit', body),
  deleteReportFieldPerm: (body: RequestBody) => fetch.post('report/field/permission/delete', body),
  saveRecommendReportCount: (body: RequestBody) => fetch.post('report/articlenum_save', body),

  setReportProcess: (body: RequestBody) => fetch.post('report/process', body),
  setReportFocus: (body: RequestBody) => fetch.post('report/focus', body),

  getTagList: (body: RequestBody) => fetch.get('report/user/tag/list', body),

  createReportRecommendData: (body: RequestBody) => fetch.post('recommend_data/create', body),
  editReportRecommendData: (body: RequestBody) => fetch.post('recommend_data/update', body),
  deleteReportRecommendData: (body: RequestBody) => fetch.post('recommend_data/delete', body),
  changeReportRecommendDataStatus: (body: RequestBody) => fetch.post('recommend_data/update_status', body),
  getReportRecommendDataDetail: (body: RequestBody) => fetch.get('recommend_data/detail', body),
  sortReportRecommendData: (body: RequestBody) => fetch.post('recommend_data/update_sort', body),

  getReportServiceZoneDetail: (body: RequestBody) => fetch.get('recommend_data/zone_detail', body),
  saveReportServiceZone: (body: RequestBody) => fetch.post('recommend_data/zone_save', body),
  
  // 保存一线调查模块配置
  saveInvestigationConfig: (body: RequestBody) => fetch.post('report/investigation/config/save', body),
  // 获取一线调查模块配置详情
  getInvestigationConfig: (body: RequestBody) => fetch.get('report/investigation/config/detail', body),

  // 一线首页列表
  getFirstTierList: (body: RequestBody, request?: RequestInfo) =>
    fetch.get('report/recommend_carousel/list', body, request),
  // 一线首页排序
  sortFirstTierList: (body: RequestBody) => fetch.post('report/recommend_carousel/update_sort', body),
  // 一线首页上下架
  changeFirstTierListStatus: (body: RequestBody) => fetch.post('report/recommend_carousel/update_status', body),
  // 一线首页添加
  addFirstTierList: (body: RequestBody) => fetch.post('report/recommend_carousel/create', body),
  // 一线首页编辑
  editFirstTierList: (body: RequestBody) => fetch.post('report/recommend_carousel/update', body),
  // 一线首页删除
  deleteFirstTierList: (body: RequestBody) => fetch.post('report/recommend_carousel/delete', body),
  // 一线首页详情
  getFirstTierListDetail: (body: RequestBody) => fetch.get('report/recommend_carousel/detail', body),
  // 一线首页轮播配置保存
  saveFirstTierCarouselConfig: (body: RequestBody) => fetch.post('report/recommend_carousel/update_carousel', body),
  // 一线调查模块详情
  getFirstTierModuleDetail: (body: RequestBody) => fetch.get('report/recommend_carousel/online_config_detail', body),
  // 一线调查模块保存
  saveFirstTierModuleDetail: (body: RequestBody) => fetch.post('report/recommend_carousel/online_config_save', body),
};