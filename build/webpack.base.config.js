const webpack = require('webpack');
const path = require('path');
const MiniCssExtractPlugin = require('mini-css-extract-plugin');
const os = require('os');
const HappyPack = require('happypack');
const ProgressBarPlugin = require('progress-bar-webpack-plugin');
const CopyWebpackPlugin = require('copy-webpack-plugin');
const AntdDayjsWebpackPlugin = require('antd-dayjs-webpack-plugin');
const chalk = require('chalk');
const theme = require('./theme');

const devMode = process.env.BUILD_TYPE !== 'build';
// const distPath = `dist_${process.env.NODE_ENV}`;
const distPath = `react-admin`;
const happyThreadPool = HappyPack.ThreadPool({ size: os.cpus().length });

module.exports = {
  entry: [
    'core-js/stable',
    'regenerator-runtime/runtime',
    path.join(__dirname, '../src/index.tsx'),
  ],
  output: {
    path: path.resolve(__dirname, `../${distPath}/static`),
    filename: 'js/[name].[hash:8].js',
    chunkFilename: 'js/[name].[chunkhash:8].js',
    publicPath: devMode ? '/' : '/static/',
  },
  // Enable sourcemaps for debugging webpack's output.
  devtool: 'source-map',
  stats: {
    assets: false,
    maxModules: 0,
  },
  module: {
    rules: [
      // All files with a '.ts' or '.tsx' extension will be handled by 'awesome-typescript-loader'.
      {
        test: /\.(tsx?)|(jsx?)$/,
        use: [{ loader: 'happypack/loader?id=happyBabel' }],
        exclude: path.join(__dirname, '../node_modules/'),
      },
      // {  //linter
      //   test: /\.tsx?$/,
      //   enforce: 'pre',
      //   use: [{
      //     loader: "tslint-loader",
      //     options: {
      //       tsConfigFile: '../tsconfig.json',
      //     }
      //   }],
      //   exclude: path.join(__dirname, '../node_modules/')
      // },
      {
        test: /\.(sa|sc|c)ss$/,
        use: [devMode ? MiniCssExtractPlugin.loader : 'style-loader', 'css-loader', 'sass-loader'],
      },
      {
        test: /\.less$/,
        use: [
          {
            loader: 'style-loader',
          },
          {
            loader: 'css-loader',
          },
          {
            loader: 'less-loader',
            options: {
              modifyVars: theme,
              javascriptEnabled: true,
            },
          },
        ],
      },
      {
        test: /\.(png|jpeg|jpg|gif)$/,
        use: [
          {
            loader: 'url-loader',
            options: {
              limit: 5 * 1024,
              outputPath: 'images',
            },
          },
        ],
      },

      // All output '.js' files will have any sourcemaps re-processed by 'source-map-loader'.
      // { enforce: 'pre', test: /\.js$/, loader: 'source-map-loader' },
    ],
  },

  resolve: {
    extensions: ['.ts', '.tsx', '.js', '.json'],
    alias: {
      '@app': path.resolve(__dirname, '../src'),
      '@utils': path.resolve(__dirname, '../src/utils'),
      '@components': path.resolve(__dirname, '../src/components'),
      '@action': path.resolve(__dirname, '../src/action'),
      '@views': path.resolve(__dirname, '../src/views'),
    },
  },

  // When importing a module whose path matches one of the following, just
  // assume a corresponding global variable exists and use that instead.
  // This is important because it allows us to avoid bundling all of our
  // dependencies, which allows browsers to cache those libraries between builds.
  // externals: {
  //   "react": "React",
  //   "react-dom": "ReactDOM"
  // },

  plugins: [
    new MiniCssExtractPlugin({
      filename: 'css/[name].[hash:8].css',
      chunkFilename: 'css/[id].[hash:8].css',
      ignoreOrder: true,
    }),
    new HappyPack({
      id: 'happyBabel',
      loaders: [
        {
          loader: 'babel-loader',
          options: {
            cacheDirectory: true,
          },
        },
      ],
      threadPool: happyThreadPool,
      verbose: true,
    }),
    new AntdDayjsWebpackPlugin(),
    new ProgressBarPlugin({
      format: `build [:bar] ${chalk.green.bold(':percent')} (:elapsed seconds) [:msg]`,
      clear: true,
    }),
    new webpack.DefinePlugin({
      BUILD_ENV: JSON.stringify(process.env.NODE_ENV),
    }),
    new CopyWebpackPlugin([
      {
        from: path.resolve(__dirname, '../src/assets'),
        to: path.resolve(__dirname, `../${distPath}/assets`),
      },
    ]),
  ],
};
