const path = require('path');
const merge = require('webpack-merge');
const HtmlWebpackPlugin = require('html-webpack-plugin');
const OptimizeCSSAssetsPlugin = require('optimize-css-assets-webpack-plugin');
const TerserPlugin = require('terser-webpack-plugin');
const { CleanWebpackPlugin } = require('clean-webpack-plugin');
const CopyWebpackPlugin = require('copy-webpack-plugin');
const baseConfig = require('./webpack.base.config');

const distPath = 'react-admin';

const prodConfig = merge(baseConfig, {
  optimization: {
    runtimeChunk: {
      name: 'manifest',
    },
    splitChunks: {
      cacheGroups: {
        commons: {
          chunks: 'initial',
          minChunks: 2,
          maxInitialRequests: 5,
          minSize: 0,
        },
        styles: {
          name: 'styles',
          test: /\.css$/,
          chunks: 'all',
          enforce: true,
        },
      },
    },
    minimizer: [
      new OptimizeCSSAssetsPlugin({}),
      new TerserPlugin({
        terserOptions: {
          parallel: true,
          warnings: false,
          output: {
            beautify: false,
          },
        },
      }),

      // new WebpackParallelUglifyPlugin({
      //   uglifyJS: {
      //     output: {
      //       beautify: false,
      //       comments: false
      //     },
      //     compress: {
      //       warnings: false,
      //       // drop_console: true,
      //       collapse_vars: true,
      //       reduce_vars: true
      //     }
      //   }
      // })
    ],
  },
  plugins: [
    new CleanWebpackPlugin({
      cleanOnceBeforeBuildPatterns: [path.join(__dirname, `../${distPath}/`)],
      verbose: true,
      dry: false,
    }),
    new HtmlWebpackPlugin({
      filename: path.join(__dirname, `../${distPath}/index.html`),
      template: path.join(__dirname, '../src/index.html'),
      inject: true,
      hash: true,
      title: '浙江新闻管理后台',
    }),
  ],
});

module.exports = prodConfig;
