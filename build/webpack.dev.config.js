const webpack = require('webpack');
const path = require('path');
const merge = require('webpack-merge');
const HtmlWebpackPlugin = require('html-webpack-plugin');
const FriendlyErrorsWebpackPlugin = require('friendly-errors-webpack-plugin');
const baseConfig = require('./webpack.base.config');

const ip = '0.0.0.0';
let proxyTarget = 'https://test-admin.jiaxingren.com'; // '**************:25500'; //
switch (process.env.NODE_ENV) {
  case 'test':
    proxyTarget = 'https://test-admin.jiaxingren.com';
    break;
  case 'testb':
    proxyTarget = 'https://test-admin.jiaxingren.com';
    break;
  case 'prod':
    proxyTarget = 'https://app-admin.zjol.com.cn/';
    break;
  case 'prev':
    proxyTarget = 'https://adminprev.zjol.com.cn';
    break;
  default:
    proxyTarget = 'https://test-admin.jiaxingren.com';
    break;
}

console.log('代理地址');
console.log(proxyTarget);
const devConfig = merge(baseConfig, {
  devServer: {
    // contentBase: path.join(__dirname, distPath),
    contentBase: path.join(__dirname, '../src'),
    publicPath: '/',
    compress: true,
    host: ip,
    port: 3456,
    hot: true,
    inline: true,
    open: false,
    clientLogLevel: 'warning',
    quiet: true,
    historyApiFallback: true,
    disableHostCheck: true,
    https: false,
    proxy: {
      '/endpoint': {
        target: proxyTarget,
        changeOrigin: true,
        secure: false,
      },
    },
  },
  plugins: [
    new webpack.HotModuleReplacementPlugin(),
    new webpack.NamedModulesPlugin(),
    new HtmlWebpackPlugin({
      filename: 'index.html',
      template: path.join(__dirname, '../src/index.html'),
      inject: true,
      hash: true,
      title: '浙江新闻管理后台',
    }),
    new FriendlyErrorsWebpackPlugin({
      compilationSuccessInfo: {
        messages: [`running on http://${ip}:3456, api proxy to ${proxyTarget}`],
      },
      onError: '/n',
    }),
  ],
});

module.exports = devConfig;
