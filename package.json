{"name": "jx-admin", "version": "2.0.0", "description": "Admin site for zjol app", "main": "index.js", "scripts": {"dev": "cross-env NODE_ENV=test webpack-dev-server --mode=development --config build/webpack.dev.config.js", "prev": "cross-env NODE_ENV=prev webpack-dev-server --mode=development --config build/webpack.dev.config.js", "prod": "cross-env NODE_ENV=prod webpack-dev-server --mode=development --config build/webpack.dev.config.js", "build-prod": "cross-env NODE_ENV=prod BUILD_TYPE=build webpack --config build/webpack.prod.config.js", "build-prev": "cross-env NODE_ENV=prev BUILD_TYPE=build webpack --config build/webpack.prod.config.js", "build-test": "cross-env NODE_ENV=test BUILD_TYPE=build webpack --config build/webpack.prod.config.js", "build-test-b": "cross-env NODE_ENV=testb BUILD_TYPE=build webpack --config build/webpack.prod.config.js", "test": "cross-env NODE_ENV=test webpack-dev-server --mode=development --config build/webpack.dev.config.js", "test-b": "cross-env NODE_ENV=testb webpack-dev-server --mode=development --config build/webpack.dev.config.js", "build-all": "concurrently \"npm run build-test\" \"npm run build-prev\" \"npm run build-prod\" "}, "repository": {"type": "git", "url": "https://core.tidenews.com.cn/code/chao-app/jx_admin.git"}, "author": "<PERSON><PERSON>", "license": "ISC", "dependencies": {"@types/cropperjs": "^1.1.5", "@types/lodash": "^4.14.136", "@types/react": "^16.9.1", "@types/react-dom": "^16.8.5", "@types/uuid": "^3.4.5", "antd": "^3.26.3", "antd-dayjs-webpack-plugin": "0.0.8", "braft-editor": "^2.3.7", "concurrently": "^7.0.0", "core-js": "^3.2.0", "cropperjs": "^1.5.5", "dom-scroll-into-view": "^2.0.1", "emoji-mart": "^3.0.1", "exceljs": "^4.2.1", "history": "^4.9.0", "immutable": "^4.0.0-rc.12", "isomorphic-fetch": "^2.2.1", "js-base64": "^3.5.2", "js-sha256": "^0.9.0", "lodash": "^4.17.15", "moment": "^2.24.0", "react": "^16.9.0", "react-clipboardjs-copy": "^1.3.1", "react-dom": "^16.9.0", "react-hot-loader": "^4.12.11", "react-lazy-load": "^3.0.13", "react-photo-view": "^1.2.4", "react-redux": "^7.1.0", "react-router": "^5.2.0", "react-router-dom": "^5.2.0", "react-sortable-hoc": "^2.0.0", "redux": "^4.0.4", "redux-logger": "^3.0.6", "redux-thunk": "^2.3.0", "regenerator-runtime": "^0.13.3", "uuid": "^3.3.2", "wangeditor-for-react": "^1.5.6"}, "devDependencies": {"@babel/core": "^7.5.5", "@babel/plugin-proposal-class-properties": "^7.5.5", "@babel/plugin-proposal-decorators": "^7.4.4", "@babel/plugin-proposal-do-expressions": "^7.5.0", "@babel/plugin-proposal-export-default-from": "^7.5.2", "@babel/plugin-proposal-export-namespace-from": "^7.5.2", "@babel/plugin-proposal-function-bind": "^7.2.0", "@babel/plugin-proposal-function-sent": "^7.5.0", "@babel/plugin-proposal-json-strings": "^7.2.0", "@babel/plugin-proposal-logical-assignment-operators": "^7.2.0", "@babel/plugin-proposal-nullish-coalescing-operator": "^7.4.4", "@babel/plugin-proposal-numeric-separator": "^7.2.0", "@babel/plugin-proposal-object-rest-spread": "^7.5.5", "@babel/plugin-proposal-optional-chaining": "^7.2.0", "@babel/plugin-proposal-throw-expressions": "^7.2.0", "@babel/plugin-syntax-dynamic-import": "^7.2.0", "@babel/plugin-syntax-import-meta": "^7.2.0", "@babel/preset-env": "^7.5.5", "@babel/preset-react": "^7.0.0", "@babel/preset-typescript": "^7.3.3", "@types/braft-editor": "^1.9.3", "@types/emoji-mart": "^3.0.5", "@types/isomorphic-fetch": "0.0.35", "@types/react-redux": "^7.1.1", "@types/react-router": "^5.1.8", "@types/react-router-dom": "^5.1.5", "@types/redux-logger": "^3.0.7", "@typescript-eslint/eslint-plugin": "^3.5.0", "@typescript-eslint/parser": "^3.5.0", "babel-loader": "^8.0.6", "babel-plugin-import": "^1.12.0", "chalk": "^2.4.2", "clean-webpack-plugin": "^3.0.0", "compression-webpack-plugin": "^3.0.0", "copy-webpack-plugin": "^5.0.4", "cross-env": "^5.2.1", "css-loader": "^3.2.0", "eslint": "^7.3.1", "eslint-config-airbnb-typescript": "^8.0.2", "eslint-config-prettier": "^6.11.0", "eslint-plugin-import": "^2.22.0", "eslint-plugin-jsx-a11y": "^6.3.1", "eslint-plugin-prettier": "^3.1.4", "eslint-plugin-react": "^7.20.2", "eslint-plugin-react-hooks": "^4.0.4", "friendly-errors-webpack-plugin": "^1.7.0", "happypack": "^5.0.1", "html-webpack-plugin": "^3.2.0", "less": "^3.9.0", "less-loader": "^5.0.0", "mini-css-extract-plugin": "^0.8.0", "optimize-css-assets-webpack-plugin": "^5.0.3", "path": "^0.12.7", "prettier": "^2.0.5", "progress-bar-webpack-plugin": "^1.12.1", "sass": "^1.42.1", "sass-loader": "^7.2.0", "source-map-loader": "^0.2.4", "style-loader": "^1.0.0", "terser-webpack-plugin": "^1.4.1", "typescript": "^3.5.3", "webpack": "^4.39.1", "webpack-cli": "^3.3.6", "webpack-dev-server": "^3.8.0", "webpack-merge": "^4.2.1"}, "browserslist": ["> 1%", "last 5 versions", "not ie <= 8"]}