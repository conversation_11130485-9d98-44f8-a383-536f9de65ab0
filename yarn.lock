# THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
# yarn lockfile v1


"@ant-design/colors@^3.1.0":
  "integrity" "sha1-WtQ9YZ6RHzSI66wwPWBuZqhCOQM="
  "resolved" "https://registry.npm.taobao.org/@ant-design/colors/download/@ant-design/colors-3.2.2.tgz"
  "version" "3.2.2"
  dependencies:
    "tinycolor2" "^1.4.1"

"@ant-design/create-react-context@^0.2.4":
  "integrity" "sha1-9fWpFjtHcgl3EoNzl60w4i55+Fg="
  "resolved" "https://registry.npm.taobao.org/@ant-design/create-react-context/download/@ant-design/create-react-context-0.2.5.tgz"
  "version" "0.2.5"
  dependencies:
    "gud" "^1.0.0"
    "warning" "^4.0.3"

"@ant-design/css-animation@^1.7.2":
  "integrity" "sha1-YKHJcAFOhrKPlAUQ1p5QPkKPETY="
  "resolved" "https://registry.npm.taobao.org/@ant-design/css-animation/download/@ant-design/css-animation-1.7.3.tgz"
  "version" "1.7.3"

"@ant-design/icons-react@~2.0.1":
  "integrity" "sha1-F6JRNXGrMXrKKSfljOol3THlNvs="
  "resolved" "https://registry.npm.taobao.org/@ant-design/icons-react/download/@ant-design/icons-react-2.0.1.tgz"
  "version" "2.0.1"
  dependencies:
    "@ant-design/colors" "^3.1.0"
    "babel-runtime" "^6.26.0"

"@ant-design/icons@^2.0.0", "@ant-design/icons@~2.1.1":
  "integrity" "sha1-e5wI3/1PXUHbZn2dvl4BB9C9mko="
  "resolved" "https://registry.npm.taobao.org/@ant-design/icons/download/@ant-design/icons-2.1.1.tgz"
  "version" "2.1.1"

"@babel/code-frame@^7.14.5":
  "integrity" "sha1-I7CNdA6D9JxeWZRfvxtD6Au/Tts="
  "resolved" "https://registry.nlark.com/@babel/code-frame/download/@babel/code-frame-7.14.5.tgz"
  "version" "7.14.5"
  dependencies:
    "@babel/highlight" "^7.14.5"

"@babel/code-frame@7.12.11":
  "integrity" "sha1-9K1DWqJj25NbjxDyxVLSP7cWpj8="
  "resolved" "https://registry.nlark.com/@babel/code-frame/download/@babel/code-frame-7.12.11.tgz"
  "version" "7.12.11"
  dependencies:
    "@babel/highlight" "^7.10.4"

"@babel/compat-data@^7.13.11", "@babel/compat-data@^7.14.5", "@babel/compat-data@^7.14.7":
  "integrity" "sha1-ewR9ejqJpn0iWNxh9gTwmPG8fgg="
  "resolved" "https://registry.nlark.com/@babel/compat-data/download/@babel/compat-data-7.14.7.tgz"
  "version" "7.14.7"

"@babel/core@^7.0.0", "@babel/core@^7.0.0-0", "@babel/core@^7.12.0", "@babel/core@^7.13.0", "@babel/core@^7.4.0-0", "@babel/core@^7.5.5":
  "integrity" "sha1-4IFOwalQAy/xbBOich3jmoQW/Ks="
  "resolved" "https://registry.nlark.com/@babel/core/download/@babel/core-7.14.6.tgz?cache=0&sync_timestamp=1623707878699&other_urls=https%3A%2F%2Fregistry.nlark.com%2F%40babel%2Fcore%2Fdownload%2F%40babel%2Fcore-7.14.6.tgz"
  "version" "7.14.6"
  dependencies:
    "@babel/code-frame" "^7.14.5"
    "@babel/generator" "^7.14.5"
    "@babel/helper-compilation-targets" "^7.14.5"
    "@babel/helper-module-transforms" "^7.14.5"
    "@babel/helpers" "^7.14.6"
    "@babel/parser" "^7.14.6"
    "@babel/template" "^7.14.5"
    "@babel/traverse" "^7.14.5"
    "@babel/types" "^7.14.5"
    "convert-source-map" "^1.7.0"
    "debug" "^4.1.0"
    "gensync" "^1.0.0-beta.2"
    "json5" "^2.1.2"
    "semver" "^6.3.0"
    "source-map" "^0.5.0"

"@babel/generator@^7.14.5":
  "integrity" "sha1-hI17nwMcrKnQzQrwGwY/Im9S14U="
  "resolved" "https://registry.nlark.com/@babel/generator/download/@babel/generator-7.14.5.tgz"
  "version" "7.14.5"
  dependencies:
    "@babel/types" "^7.14.5"
    "jsesc" "^2.5.1"
    "source-map" "^0.5.0"

"@babel/helper-annotate-as-pure@^7.14.5":
  "integrity" "sha1-e/R47Dtxcm1WqMpXdbBG/CmHnmE="
  "resolved" "https://registry.nlark.com/@babel/helper-annotate-as-pure/download/@babel/helper-annotate-as-pure-7.14.5.tgz"
  "version" "7.14.5"
  dependencies:
    "@babel/types" "^7.14.5"

"@babel/helper-builder-binary-assignment-operator-visitor@^7.14.5":
  "integrity" "sha1-uTm0P4w3dlRDoZrnStixWXjgoZE="
  "resolved" "https://registry.nlark.com/@babel/helper-builder-binary-assignment-operator-visitor/download/@babel/helper-builder-binary-assignment-operator-visitor-7.14.5.tgz"
  "version" "7.14.5"
  dependencies:
    "@babel/helper-explode-assignable-expression" "^7.14.5"
    "@babel/types" "^7.14.5"

"@babel/helper-compilation-targets@^7.13.0", "@babel/helper-compilation-targets@^7.14.5":
  "integrity" "sha1-epnF0JZ5Eely/iw0EffVtJhJjs8="
  "resolved" "https://registry.nlark.com/@babel/helper-compilation-targets/download/@babel/helper-compilation-targets-7.14.5.tgz?cache=0&sync_timestamp=1623280417606&other_urls=https%3A%2F%2Fregistry.nlark.com%2F%40babel%2Fhelper-compilation-targets%2Fdownload%2F%40babel%2Fhelper-compilation-targets-7.14.5.tgz"
  "version" "7.14.5"
  dependencies:
    "@babel/compat-data" "^7.14.5"
    "@babel/helper-validator-option" "^7.14.5"
    "browserslist" "^4.16.6"
    "semver" "^6.3.0"

"@babel/helper-create-class-features-plugin@^7.14.5", "@babel/helper-create-class-features-plugin@^7.14.6":
  "integrity" "sha1-8RRGm2wG+LXFnGxOdGIfUIU2JUI="
  "resolved" "https://registry.nlark.com/@babel/helper-create-class-features-plugin/download/@babel/helper-create-class-features-plugin-7.14.6.tgz"
  "version" "7.14.6"
  dependencies:
    "@babel/helper-annotate-as-pure" "^7.14.5"
    "@babel/helper-function-name" "^7.14.5"
    "@babel/helper-member-expression-to-functions" "^7.14.5"
    "@babel/helper-optimise-call-expression" "^7.14.5"
    "@babel/helper-replace-supers" "^7.14.5"
    "@babel/helper-split-export-declaration" "^7.14.5"

"@babel/helper-create-regexp-features-plugin@^7.14.5":
  "integrity" "sha1-x9WsXpz2IcJgV3Ivt6ikxYiTWMQ="
  "resolved" "https://registry.nlark.com/@babel/helper-create-regexp-features-plugin/download/@babel/helper-create-regexp-features-plugin-7.14.5.tgz"
  "version" "7.14.5"
  dependencies:
    "@babel/helper-annotate-as-pure" "^7.14.5"
    "regexpu-core" "^4.7.1"

"@babel/helper-define-polyfill-provider@^0.2.2":
  "integrity" "sha1-BSXt7FCUZTooJojTTYRuTHXpwLY="
  "resolved" "https://registry.nlark.com/@babel/helper-define-polyfill-provider/download/@babel/helper-define-polyfill-provider-0.2.3.tgz"
  "version" "0.2.3"
  dependencies:
    "@babel/helper-compilation-targets" "^7.13.0"
    "@babel/helper-module-imports" "^7.12.13"
    "@babel/helper-plugin-utils" "^7.13.0"
    "@babel/traverse" "^7.13.0"
    "debug" "^4.1.1"
    "lodash.debounce" "^4.0.8"
    "resolve" "^1.14.2"
    "semver" "^6.1.2"

"@babel/helper-explode-assignable-expression@^7.14.5":
  "integrity" "sha1-iqcucIIFx7tkPkXHO0OGzfKh9kU="
  "resolved" "https://registry.nlark.com/@babel/helper-explode-assignable-expression/download/@babel/helper-explode-assignable-expression-7.14.5.tgz"
  "version" "7.14.5"
  dependencies:
    "@babel/types" "^7.14.5"

"@babel/helper-function-name@^7.14.5":
  "integrity" "sha1-ieLEdJcvFdjiM7Uu6MSA4s/NUMQ="
  "resolved" "https://registry.nlark.com/@babel/helper-function-name/download/@babel/helper-function-name-7.14.5.tgz?cache=0&sync_timestamp=1623280385237&other_urls=https%3A%2F%2Fregistry.nlark.com%2F%40babel%2Fhelper-function-name%2Fdownload%2F%40babel%2Fhelper-function-name-7.14.5.tgz"
  "version" "7.14.5"
  dependencies:
    "@babel/helper-get-function-arity" "^7.14.5"
    "@babel/template" "^7.14.5"
    "@babel/types" "^7.14.5"

"@babel/helper-get-function-arity@^7.14.5":
  "integrity" "sha1-Jfv6V5sJN+7h87gF7OTOOYxDGBU="
  "resolved" "https://registry.nlark.com/@babel/helper-get-function-arity/download/@babel/helper-get-function-arity-7.14.5.tgz"
  "version" "7.14.5"
  dependencies:
    "@babel/types" "^7.14.5"

"@babel/helper-hoist-variables@^7.14.5":
  "integrity" "sha1-4N0nwzp45XfXyIhJFqPn7x98f40="
  "resolved" "https://registry.nlark.com/@babel/helper-hoist-variables/download/@babel/helper-hoist-variables-7.14.5.tgz"
  "version" "7.14.5"
  dependencies:
    "@babel/types" "^7.14.5"

"@babel/helper-member-expression-to-functions@^7.14.5":
  "integrity" "sha1-l+ViRL65QhH+J3vYGOOjKcZveXA="
  "resolved" "https://registry.nlark.com/@babel/helper-member-expression-to-functions/download/@babel/helper-member-expression-to-functions-7.14.7.tgz?cache=0&sync_timestamp=1624312632792&other_urls=https%3A%2F%2Fregistry.nlark.com%2F%40babel%2Fhelper-member-expression-to-functions%2Fdownload%2F%40babel%2Fhelper-member-expression-to-functions-7.14.7.tgz"
  "version" "7.14.7"
  dependencies:
    "@babel/types" "^7.14.5"

"@babel/helper-module-imports@^7.0.0", "@babel/helper-module-imports@^7.12.13", "@babel/helper-module-imports@^7.14.5":
  "integrity" "sha1-bRpE32o4yVeqfDEtoHZCnxG0IvM="
  "resolved" "https://registry.nlark.com/@babel/helper-module-imports/download/@babel/helper-module-imports-7.14.5.tgz"
  "version" "7.14.5"
  dependencies:
    "@babel/types" "^7.14.5"

"@babel/helper-module-transforms@^7.14.5":
  "integrity" "sha1-feQvENeJtCPrkC69JAMcp3yx4Q4="
  "resolved" "https://registry.nlark.com/@babel/helper-module-transforms/download/@babel/helper-module-transforms-7.14.5.tgz?cache=0&sync_timestamp=1623280405923&other_urls=https%3A%2F%2Fregistry.nlark.com%2F%40babel%2Fhelper-module-transforms%2Fdownload%2F%40babel%2Fhelper-module-transforms-7.14.5.tgz"
  "version" "7.14.5"
  dependencies:
    "@babel/helper-module-imports" "^7.14.5"
    "@babel/helper-replace-supers" "^7.14.5"
    "@babel/helper-simple-access" "^7.14.5"
    "@babel/helper-split-export-declaration" "^7.14.5"
    "@babel/helper-validator-identifier" "^7.14.5"
    "@babel/template" "^7.14.5"
    "@babel/traverse" "^7.14.5"
    "@babel/types" "^7.14.5"

"@babel/helper-optimise-call-expression@^7.14.5":
  "integrity" "sha1-8nOVqGGeBmWz8DZM3bQcJdcbSZw="
  "resolved" "https://registry.nlark.com/@babel/helper-optimise-call-expression/download/@babel/helper-optimise-call-expression-7.14.5.tgz"
  "version" "7.14.5"
  dependencies:
    "@babel/types" "^7.14.5"

"@babel/helper-plugin-utils@^7.0.0", "@babel/helper-plugin-utils@^7.10.4", "@babel/helper-plugin-utils@^7.12.13", "@babel/helper-plugin-utils@^7.13.0", "@babel/helper-plugin-utils@^7.14.5", "@babel/helper-plugin-utils@^7.8.0", "@babel/helper-plugin-utils@^7.8.3":
  "integrity" "sha1-WsgizpfuxGdBq3ClF5ceRDpwxak="
  "resolved" "https://registry.nlark.com/@babel/helper-plugin-utils/download/@babel/helper-plugin-utils-7.14.5.tgz?cache=0&sync_timestamp=1623280296194&other_urls=https%3A%2F%2Fregistry.nlark.com%2F%40babel%2Fhelper-plugin-utils%2Fdownload%2F%40babel%2Fhelper-plugin-utils-7.14.5.tgz"
  "version" "7.14.5"

"@babel/helper-remap-async-to-generator@^7.14.5":
  "integrity" "sha1-UUOckTYSlY9UqYek/8nuWHogRdY="
  "resolved" "https://registry.nlark.com/@babel/helper-remap-async-to-generator/download/@babel/helper-remap-async-to-generator-7.14.5.tgz?cache=0&sync_timestamp=1623280405954&other_urls=https%3A%2F%2Fregistry.nlark.com%2F%40babel%2Fhelper-remap-async-to-generator%2Fdownload%2F%40babel%2Fhelper-remap-async-to-generator-7.14.5.tgz"
  "version" "7.14.5"
  dependencies:
    "@babel/helper-annotate-as-pure" "^7.14.5"
    "@babel/helper-wrap-function" "^7.14.5"
    "@babel/types" "^7.14.5"

"@babel/helper-replace-supers@^7.14.5":
  "integrity" "sha1-DswLA8Qc1We0Ak6gFhNMKEFKu5Q="
  "resolved" "https://registry.nlark.com/@babel/helper-replace-supers/download/@babel/helper-replace-supers-7.14.5.tgz?cache=0&sync_timestamp=1623280401049&other_urls=https%3A%2F%2Fregistry.nlark.com%2F%40babel%2Fhelper-replace-supers%2Fdownload%2F%40babel%2Fhelper-replace-supers-7.14.5.tgz"
  "version" "7.14.5"
  dependencies:
    "@babel/helper-member-expression-to-functions" "^7.14.5"
    "@babel/helper-optimise-call-expression" "^7.14.5"
    "@babel/traverse" "^7.14.5"
    "@babel/types" "^7.14.5"

"@babel/helper-simple-access@^7.14.5":
  "integrity" "sha1-ZuqFz1O6C05Yi6d/yBP1OryqQcQ="
  "resolved" "https://registry.nlark.com/@babel/helper-simple-access/download/@babel/helper-simple-access-7.14.5.tgz"
  "version" "7.14.5"
  dependencies:
    "@babel/types" "^7.14.5"

"@babel/helper-skip-transparent-expression-wrappers@^7.14.5":
  "integrity" "sha1-lvSGrAUMqfRLAJ++W305TKs6DuQ="
  "resolved" "https://registry.nlark.com/@babel/helper-skip-transparent-expression-wrappers/download/@babel/helper-skip-transparent-expression-wrappers-7.14.5.tgz"
  "version" "7.14.5"
  dependencies:
    "@babel/types" "^7.14.5"

"@babel/helper-split-export-declaration@^7.14.5":
  "integrity" "sha1-IrI6VO9RwrdgXYUZMMGXbdC8aTo="
  "resolved" "https://registry.nlark.com/@babel/helper-split-export-declaration/download/@babel/helper-split-export-declaration-7.14.5.tgz"
  "version" "7.14.5"
  dependencies:
    "@babel/types" "^7.14.5"

"@babel/helper-validator-identifier@^7.14.5":
  "integrity" "sha1-0PDid8US4Mk4J3+qhaOWjJpEwOg="
  "resolved" "https://registry.nlark.com/@babel/helper-validator-identifier/download/@babel/helper-validator-identifier-7.14.5.tgz?cache=0&sync_timestamp=1623280305128&other_urls=https%3A%2F%2Fregistry.nlark.com%2F%40babel%2Fhelper-validator-identifier%2Fdownload%2F%40babel%2Fhelper-validator-identifier-7.14.5.tgz"
  "version" "7.14.5"

"@babel/helper-validator-option@^7.14.5":
  "integrity" "sha1-bnKh//GNXfy4eOHmLxoCHEty1aM="
  "resolved" "https://registry.nlark.com/@babel/helper-validator-option/download/@babel/helper-validator-option-7.14.5.tgz?cache=0&sync_timestamp=1623280304150&other_urls=https%3A%2F%2Fregistry.nlark.com%2F%40babel%2Fhelper-validator-option%2Fdownload%2F%40babel%2Fhelper-validator-option-7.14.5.tgz"
  "version" "7.14.5"

"@babel/helper-wrap-function@^7.14.5":
  "integrity" "sha1-WRnRFb8P4yi4pdY7y2EPUWAfK/8="
  "resolved" "https://registry.nlark.com/@babel/helper-wrap-function/download/@babel/helper-wrap-function-7.14.5.tgz?cache=0&sync_timestamp=1623280400923&other_urls=https%3A%2F%2Fregistry.nlark.com%2F%40babel%2Fhelper-wrap-function%2Fdownload%2F%40babel%2Fhelper-wrap-function-7.14.5.tgz"
  "version" "7.14.5"
  dependencies:
    "@babel/helper-function-name" "^7.14.5"
    "@babel/template" "^7.14.5"
    "@babel/traverse" "^7.14.5"
    "@babel/types" "^7.14.5"

"@babel/helpers@^7.14.6":
  "integrity" "sha1-W1gwa5XxtH4qAZlDT6hlj6bCFjU="
  "resolved" "https://registry.nlark.com/@babel/helpers/download/@babel/helpers-7.14.6.tgz?cache=0&sync_timestamp=1623708032832&other_urls=https%3A%2F%2Fregistry.nlark.com%2F%40babel%2Fhelpers%2Fdownload%2F%40babel%2Fhelpers-7.14.6.tgz"
  "version" "7.14.6"
  dependencies:
    "@babel/template" "^7.14.5"
    "@babel/traverse" "^7.14.5"
    "@babel/types" "^7.14.5"

"@babel/highlight@^7.10.4", "@babel/highlight@^7.14.5":
  "integrity" "sha1-aGGlLwOWZAUAH2qlNKAaJNmejNk="
  "resolved" "https://registry.nlark.com/@babel/highlight/download/@babel/highlight-7.14.5.tgz"
  "version" "7.14.5"
  dependencies:
    "@babel/helper-validator-identifier" "^7.14.5"
    "chalk" "^2.0.0"
    "js-tokens" "^4.0.0"

"@babel/parser@^7.14.5", "@babel/parser@^7.14.6", "@babel/parser@^7.14.7":
  "integrity" "sha1-YJlyDIg5yoZaJjfmyFhS6tC9tZU="
  "resolved" "https://registry.nlark.com/@babel/parser/download/@babel/parser-7.14.7.tgz"
  "version" "7.14.7"

"@babel/plugin-bugfix-v8-spread-parameters-in-optional-chaining@^7.14.5":
  "integrity" "sha1-S0ZzAuFUjtOxvkO+rizJz0Xgu34="
  "resolved" "https://registry.nlark.com/@babel/plugin-bugfix-v8-spread-parameters-in-optional-chaining/download/@babel/plugin-bugfix-v8-spread-parameters-in-optional-chaining-7.14.5.tgz?cache=0&sync_timestamp=1623280386537&other_urls=https%3A%2F%2Fregistry.nlark.com%2F%40babel%2Fplugin-bugfix-v8-spread-parameters-in-optional-chaining%2Fdownload%2F%40babel%2Fplugin-bugfix-v8-spread-parameters-in-optional-chaining-7.14.5.tgz"
  "version" "7.14.5"
  dependencies:
    "@babel/helper-plugin-utils" "^7.14.5"
    "@babel/helper-skip-transparent-expression-wrappers" "^7.14.5"
    "@babel/plugin-proposal-optional-chaining" "^7.14.5"

"@babel/plugin-proposal-async-generator-functions@^7.14.7":
  "integrity" "sha1-eEpIw9jtBz9lrc8wtXvL9sgRms4="
  "resolved" "https://registry.nlark.com/@babel/plugin-proposal-async-generator-functions/download/@babel/plugin-proposal-async-generator-functions-7.14.7.tgz"
  "version" "7.14.7"
  dependencies:
    "@babel/helper-plugin-utils" "^7.14.5"
    "@babel/helper-remap-async-to-generator" "^7.14.5"
    "@babel/plugin-syntax-async-generators" "^7.8.4"

"@babel/plugin-proposal-class-properties@^7.14.5", "@babel/plugin-proposal-class-properties@^7.5.5":
  "integrity" "sha1-QNHuFAxbHjGjUPT17tlFCWVZtC4="
  "resolved" "https://registry.nlark.com/@babel/plugin-proposal-class-properties/download/@babel/plugin-proposal-class-properties-7.14.5.tgz?cache=0&sync_timestamp=1623280411002&other_urls=https%3A%2F%2Fregistry.nlark.com%2F%40babel%2Fplugin-proposal-class-properties%2Fdownload%2F%40babel%2Fplugin-proposal-class-properties-7.14.5.tgz"
  "version" "7.14.5"
  dependencies:
    "@babel/helper-create-class-features-plugin" "^7.14.5"
    "@babel/helper-plugin-utils" "^7.14.5"

"@babel/plugin-proposal-class-static-block@^7.14.5":
  "integrity" "sha1-FY6eENRJw4Se8+zelKA9nxhBtoE="
  "resolved" "https://registry.nlark.com/@babel/plugin-proposal-class-static-block/download/@babel/plugin-proposal-class-static-block-7.14.5.tgz?cache=0&sync_timestamp=1623280411252&other_urls=https%3A%2F%2Fregistry.nlark.com%2F%40babel%2Fplugin-proposal-class-static-block%2Fdownload%2F%40babel%2Fplugin-proposal-class-static-block-7.14.5.tgz"
  "version" "7.14.5"
  dependencies:
    "@babel/helper-create-class-features-plugin" "^7.14.5"
    "@babel/helper-plugin-utils" "^7.14.5"
    "@babel/plugin-syntax-class-static-block" "^7.14.5"

"@babel/plugin-proposal-decorators@^7.4.4":
  "integrity" "sha1-WbxN/B1mW1pnSc95j/Qil+0bLB0="
  "resolved" "https://registry.nlark.com/@babel/plugin-proposal-decorators/download/@babel/plugin-proposal-decorators-7.14.5.tgz?cache=0&sync_timestamp=1623280416062&other_urls=https%3A%2F%2Fregistry.nlark.com%2F%40babel%2Fplugin-proposal-decorators%2Fdownload%2F%40babel%2Fplugin-proposal-decorators-7.14.5.tgz"
  "version" "7.14.5"
  dependencies:
    "@babel/helper-create-class-features-plugin" "^7.14.5"
    "@babel/helper-plugin-utils" "^7.14.5"
    "@babel/plugin-syntax-decorators" "^7.14.5"

"@babel/plugin-proposal-do-expressions@^7.5.0":
  "integrity" "sha1-oV4LJiqY9zLYFYGxu19SzuHn6wc="
  "resolved" "https://registry.nlark.com/@babel/plugin-proposal-do-expressions/download/@babel/plugin-proposal-do-expressions-7.14.5.tgz"
  "version" "7.14.5"
  dependencies:
    "@babel/helper-plugin-utils" "^7.14.5"
    "@babel/plugin-syntax-do-expressions" "^7.14.5"

"@babel/plugin-proposal-dynamic-import@^7.14.5":
  "integrity" "sha1-DGYX30YcDB+P/ztHzVl3I2AQHSw="
  "resolved" "https://registry.nlark.com/@babel/plugin-proposal-dynamic-import/download/@babel/plugin-proposal-dynamic-import-7.14.5.tgz"
  "version" "7.14.5"
  dependencies:
    "@babel/helper-plugin-utils" "^7.14.5"
    "@babel/plugin-syntax-dynamic-import" "^7.8.3"

"@babel/plugin-proposal-export-default-from@^7.5.2":
  "integrity" "sha1-iTGmVgYyxlD5Ko5ZSPbnMBnW0yE="
  "resolved" "https://registry.nlark.com/@babel/plugin-proposal-export-default-from/download/@babel/plugin-proposal-export-default-from-7.14.5.tgz?cache=0&sync_timestamp=1623281221934&other_urls=https%3A%2F%2Fregistry.nlark.com%2F%40babel%2Fplugin-proposal-export-default-from%2Fdownload%2F%40babel%2Fplugin-proposal-export-default-from-7.14.5.tgz"
  "version" "7.14.5"
  dependencies:
    "@babel/helper-plugin-utils" "^7.14.5"
    "@babel/plugin-syntax-export-default-from" "^7.14.5"

"@babel/plugin-proposal-export-namespace-from@^7.14.5", "@babel/plugin-proposal-export-namespace-from@^7.5.2":
  "integrity" "sha1-260kQxDObM0IMHIWfYzqg6Uvr3Y="
  "resolved" "https://registry.nlark.com/@babel/plugin-proposal-export-namespace-from/download/@babel/plugin-proposal-export-namespace-from-7.14.5.tgz"
  "version" "7.14.5"
  dependencies:
    "@babel/helper-plugin-utils" "^7.14.5"
    "@babel/plugin-syntax-export-namespace-from" "^7.8.3"

"@babel/plugin-proposal-function-bind@^7.2.0":
  "integrity" "sha1-g7vGhDEr+CvaRux8tS3CJuiRgz8="
  "resolved" "https://registry.nlark.com/@babel/plugin-proposal-function-bind/download/@babel/plugin-proposal-function-bind-7.14.5.tgz"
  "version" "7.14.5"
  dependencies:
    "@babel/helper-plugin-utils" "^7.14.5"
    "@babel/plugin-syntax-function-bind" "^7.14.5"

"@babel/plugin-proposal-function-sent@^7.5.0":
  "integrity" "sha1-c5SzB7E84sYI/lqySzhnqlkGm2o="
  "resolved" "https://registry.nlark.com/@babel/plugin-proposal-function-sent/download/@babel/plugin-proposal-function-sent-7.14.5.tgz"
  "version" "7.14.5"
  dependencies:
    "@babel/helper-plugin-utils" "^7.14.5"
    "@babel/helper-wrap-function" "^7.14.5"
    "@babel/plugin-syntax-function-sent" "^7.14.5"

"@babel/plugin-proposal-json-strings@^7.14.5", "@babel/plugin-proposal-json-strings@^7.2.0":
  "integrity" "sha1-ON5g2zYug6PYyUSshY3fnwwiOes="
  "resolved" "https://registry.nlark.com/@babel/plugin-proposal-json-strings/download/@babel/plugin-proposal-json-strings-7.14.5.tgz?cache=0&sync_timestamp=1623280420044&other_urls=https%3A%2F%2Fregistry.nlark.com%2F%40babel%2Fplugin-proposal-json-strings%2Fdownload%2F%40babel%2Fplugin-proposal-json-strings-7.14.5.tgz"
  "version" "7.14.5"
  dependencies:
    "@babel/helper-plugin-utils" "^7.14.5"
    "@babel/plugin-syntax-json-strings" "^7.8.3"

"@babel/plugin-proposal-logical-assignment-operators@^7.14.5", "@babel/plugin-proposal-logical-assignment-operators@^7.2.0":
  "integrity" "sha1-bmIpwqmbAqspFfglceDMZGpAxzg="
  "resolved" "https://registry.nlark.com/@babel/plugin-proposal-logical-assignment-operators/download/@babel/plugin-proposal-logical-assignment-operators-7.14.5.tgz?cache=0&sync_timestamp=1623280419692&other_urls=https%3A%2F%2Fregistry.nlark.com%2F%40babel%2Fplugin-proposal-logical-assignment-operators%2Fdownload%2F%40babel%2Fplugin-proposal-logical-assignment-operators-7.14.5.tgz"
  "version" "7.14.5"
  dependencies:
    "@babel/helper-plugin-utils" "^7.14.5"
    "@babel/plugin-syntax-logical-assignment-operators" "^7.10.4"

"@babel/plugin-proposal-nullish-coalescing-operator@^7.14.5", "@babel/plugin-proposal-nullish-coalescing-operator@^7.4.4":
  "integrity" "sha1-7jhYnOAOLMWbKZ7D6kBvzToP2vY="
  "resolved" "https://registry.nlark.com/@babel/plugin-proposal-nullish-coalescing-operator/download/@babel/plugin-proposal-nullish-coalescing-operator-7.14.5.tgz?cache=0&sync_timestamp=1623280418763&other_urls=https%3A%2F%2Fregistry.nlark.com%2F%40babel%2Fplugin-proposal-nullish-coalescing-operator%2Fdownload%2F%40babel%2Fplugin-proposal-nullish-coalescing-operator-7.14.5.tgz"
  "version" "7.14.5"
  dependencies:
    "@babel/helper-plugin-utils" "^7.14.5"
    "@babel/plugin-syntax-nullish-coalescing-operator" "^7.8.3"

"@babel/plugin-proposal-numeric-separator@^7.14.5", "@babel/plugin-proposal-numeric-separator@^7.2.0":
  "integrity" "sha1-g2Mb8z2aUd8YTCECoGmsDFjAXxg="
  "resolved" "https://registry.nlark.com/@babel/plugin-proposal-numeric-separator/download/@babel/plugin-proposal-numeric-separator-7.14.5.tgz?cache=0&sync_timestamp=1623280419388&other_urls=https%3A%2F%2Fregistry.nlark.com%2F%40babel%2Fplugin-proposal-numeric-separator%2Fdownload%2F%40babel%2Fplugin-proposal-numeric-separator-7.14.5.tgz"
  "version" "7.14.5"
  dependencies:
    "@babel/helper-plugin-utils" "^7.14.5"
    "@babel/plugin-syntax-numeric-separator" "^7.10.4"

"@babel/plugin-proposal-object-rest-spread@^7.14.7", "@babel/plugin-proposal-object-rest-spread@^7.5.5":
  "integrity" "sha1-WSCis99/eQHfAgWXTAZBsT/Z02M="
  "resolved" "https://registry.nlark.com/@babel/plugin-proposal-object-rest-spread/download/@babel/plugin-proposal-object-rest-spread-7.14.7.tgz"
  "version" "7.14.7"
  dependencies:
    "@babel/compat-data" "^7.14.7"
    "@babel/helper-compilation-targets" "^7.14.5"
    "@babel/helper-plugin-utils" "^7.14.5"
    "@babel/plugin-syntax-object-rest-spread" "^7.8.3"
    "@babel/plugin-transform-parameters" "^7.14.5"

"@babel/plugin-proposal-optional-catch-binding@^7.14.5":
  "integrity" "sha1-k53W7d7/Omf997PwRLU0cmJZjDw="
  "resolved" "https://registry.nlark.com/@babel/plugin-proposal-optional-catch-binding/download/@babel/plugin-proposal-optional-catch-binding-7.14.5.tgz?cache=0&sync_timestamp=1623280419133&other_urls=https%3A%2F%2Fregistry.nlark.com%2F%40babel%2Fplugin-proposal-optional-catch-binding%2Fdownload%2F%40babel%2Fplugin-proposal-optional-catch-binding-7.14.5.tgz"
  "version" "7.14.5"
  dependencies:
    "@babel/helper-plugin-utils" "^7.14.5"
    "@babel/plugin-syntax-optional-catch-binding" "^7.8.3"

"@babel/plugin-proposal-optional-chaining@^7.14.5", "@babel/plugin-proposal-optional-chaining@^7.2.0":
  "integrity" "sha1-+oNlHmCjYOPxN5fu8AuNUZaVtgM="
  "resolved" "https://registry.nlark.com/@babel/plugin-proposal-optional-chaining/download/@babel/plugin-proposal-optional-chaining-7.14.5.tgz?cache=0&sync_timestamp=1623280397698&other_urls=https%3A%2F%2Fregistry.nlark.com%2F%40babel%2Fplugin-proposal-optional-chaining%2Fdownload%2F%40babel%2Fplugin-proposal-optional-chaining-7.14.5.tgz"
  "version" "7.14.5"
  dependencies:
    "@babel/helper-plugin-utils" "^7.14.5"
    "@babel/helper-skip-transparent-expression-wrappers" "^7.14.5"
    "@babel/plugin-syntax-optional-chaining" "^7.8.3"

"@babel/plugin-proposal-private-methods@^7.14.5":
  "integrity" "sha1-N0RklZlrKUXzD1vltg1eKqT1eS0="
  "resolved" "https://registry.nlark.com/@babel/plugin-proposal-private-methods/download/@babel/plugin-proposal-private-methods-7.14.5.tgz?cache=0&sync_timestamp=1623280416204&other_urls=https%3A%2F%2Fregistry.nlark.com%2F%40babel%2Fplugin-proposal-private-methods%2Fdownload%2F%40babel%2Fplugin-proposal-private-methods-7.14.5.tgz"
  "version" "7.14.5"
  dependencies:
    "@babel/helper-create-class-features-plugin" "^7.14.5"
    "@babel/helper-plugin-utils" "^7.14.5"

"@babel/plugin-proposal-private-property-in-object@^7.14.5":
  "integrity" "sha1-n2Wk0Ek6lAtMAfiqnT8YlKWH9jY="
  "resolved" "https://registry.nlark.com/@babel/plugin-proposal-private-property-in-object/download/@babel/plugin-proposal-private-property-in-object-7.14.5.tgz?cache=0&sync_timestamp=1623280416058&other_urls=https%3A%2F%2Fregistry.nlark.com%2F%40babel%2Fplugin-proposal-private-property-in-object%2Fdownload%2F%40babel%2Fplugin-proposal-private-property-in-object-7.14.5.tgz"
  "version" "7.14.5"
  dependencies:
    "@babel/helper-annotate-as-pure" "^7.14.5"
    "@babel/helper-create-class-features-plugin" "^7.14.5"
    "@babel/helper-plugin-utils" "^7.14.5"
    "@babel/plugin-syntax-private-property-in-object" "^7.14.5"

"@babel/plugin-proposal-throw-expressions@^7.2.0":
  "integrity" "sha1-m6f05bqkzgENbjDDeXkegbEJheU="
  "resolved" "https://registry.nlark.com/@babel/plugin-proposal-throw-expressions/download/@babel/plugin-proposal-throw-expressions-7.14.5.tgz"
  "version" "7.14.5"
  dependencies:
    "@babel/helper-plugin-utils" "^7.14.5"
    "@babel/plugin-syntax-throw-expressions" "^7.14.5"

"@babel/plugin-proposal-unicode-property-regex@^7.14.5", "@babel/plugin-proposal-unicode-property-regex@^7.4.4":
  "integrity" "sha1-D5XuDnV6XWR/N42qDsp+k/qou+g="
  "resolved" "https://registry.nlark.com/@babel/plugin-proposal-unicode-property-regex/download/@babel/plugin-proposal-unicode-property-regex-7.14.5.tgz?cache=0&sync_timestamp=1623280385924&other_urls=https%3A%2F%2Fregistry.nlark.com%2F%40babel%2Fplugin-proposal-unicode-property-regex%2Fdownload%2F%40babel%2Fplugin-proposal-unicode-property-regex-7.14.5.tgz"
  "version" "7.14.5"
  dependencies:
    "@babel/helper-create-regexp-features-plugin" "^7.14.5"
    "@babel/helper-plugin-utils" "^7.14.5"

"@babel/plugin-syntax-async-generators@^7.8.4":
  "integrity" "sha1-qYP7Gusuw/btBCohD2QOkOeG/g0="
  "resolved" "https://registry.nlark.com/@babel/plugin-syntax-async-generators/download/@babel/plugin-syntax-async-generators-7.8.4.tgz"
  "version" "7.8.4"
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.0"

"@babel/plugin-syntax-class-properties@^7.12.13":
  "integrity" "sha1-tcmHJ0xKOoK4lxR5aTGmtTVErhA="
  "resolved" "https://registry.npm.taobao.org/@babel/plugin-syntax-class-properties/download/@babel/plugin-syntax-class-properties-7.12.13.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40babel%2Fplugin-syntax-class-properties%2Fdownload%2F%40babel%2Fplugin-syntax-class-properties-7.12.13.tgz"
  "version" "7.12.13"
  dependencies:
    "@babel/helper-plugin-utils" "^7.12.13"

"@babel/plugin-syntax-class-static-block@^7.14.5":
  "integrity" "sha1-GV34mxRrS3izv4l/16JXyEZZ1AY="
  "resolved" "https://registry.nlark.com/@babel/plugin-syntax-class-static-block/download/@babel/plugin-syntax-class-static-block-7.14.5.tgz?cache=0&sync_timestamp=1623280420297&other_urls=https%3A%2F%2Fregistry.nlark.com%2F%40babel%2Fplugin-syntax-class-static-block%2Fdownload%2F%40babel%2Fplugin-syntax-class-static-block-7.14.5.tgz"
  "version" "7.14.5"
  dependencies:
    "@babel/helper-plugin-utils" "^7.14.5"

"@babel/plugin-syntax-decorators@^7.14.5":
  "integrity" "sha1-6vucDL4JyK/rlkujp7vWOUWnLyA="
  "resolved" "https://registry.nlark.com/@babel/plugin-syntax-decorators/download/@babel/plugin-syntax-decorators-7.14.5.tgz?cache=0&sync_timestamp=1623280421117&other_urls=https%3A%2F%2Fregistry.nlark.com%2F%40babel%2Fplugin-syntax-decorators%2Fdownload%2F%40babel%2Fplugin-syntax-decorators-7.14.5.tgz"
  "version" "7.14.5"
  dependencies:
    "@babel/helper-plugin-utils" "^7.14.5"

"@babel/plugin-syntax-do-expressions@^7.14.5":
  "integrity" "sha1-0yvjO7DqfBbPImXwl4ZMNjaQlUo="
  "resolved" "https://registry.nlark.com/@babel/plugin-syntax-do-expressions/download/@babel/plugin-syntax-do-expressions-7.14.5.tgz?cache=0&sync_timestamp=1623283577037&other_urls=https%3A%2F%2Fregistry.nlark.com%2F%40babel%2Fplugin-syntax-do-expressions%2Fdownload%2F%40babel%2Fplugin-syntax-do-expressions-7.14.5.tgz"
  "version" "7.14.5"
  dependencies:
    "@babel/helper-plugin-utils" "^7.14.5"

"@babel/plugin-syntax-dynamic-import@^7.2.0", "@babel/plugin-syntax-dynamic-import@^7.8.3":
  "integrity" "sha1-Yr+Ysto80h1iYVT8lu5bPLaOrLM="
  "resolved" "https://registry.nlark.com/@babel/plugin-syntax-dynamic-import/download/@babel/plugin-syntax-dynamic-import-7.8.3.tgz?cache=0&sync_timestamp=1618847125283&other_urls=https%3A%2F%2Fregistry.nlark.com%2F%40babel%2Fplugin-syntax-dynamic-import%2Fdownload%2F%40babel%2Fplugin-syntax-dynamic-import-7.8.3.tgz"
  "version" "7.8.3"
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.0"

"@babel/plugin-syntax-export-default-from@^7.14.5":
  "integrity" "sha1-zfqdQ9KyyJtvGvPoNRjoyLntDbw="
  "resolved" "https://registry.nlark.com/@babel/plugin-syntax-export-default-from/download/@babel/plugin-syntax-export-default-from-7.14.5.tgz?cache=0&sync_timestamp=1623281257589&other_urls=https%3A%2F%2Fregistry.nlark.com%2F%40babel%2Fplugin-syntax-export-default-from%2Fdownload%2F%40babel%2Fplugin-syntax-export-default-from-7.14.5.tgz"
  "version" "7.14.5"
  dependencies:
    "@babel/helper-plugin-utils" "^7.14.5"

"@babel/plugin-syntax-export-namespace-from@^7.8.3":
  "integrity" "sha1-AolkqbqA28CUyRXEh618TnpmRlo="
  "resolved" "https://registry.nlark.com/@babel/plugin-syntax-export-namespace-from/download/@babel/plugin-syntax-export-namespace-from-7.8.3.tgz"
  "version" "7.8.3"
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.3"

"@babel/plugin-syntax-function-bind@^7.14.5":
  "integrity" "sha1-63VEAU/jSfSKy+vFCE7q3eLv5X4="
  "resolved" "https://registry.nlark.com/@babel/plugin-syntax-function-bind/download/@babel/plugin-syntax-function-bind-7.14.5.tgz?cache=0&sync_timestamp=1623283576680&other_urls=https%3A%2F%2Fregistry.nlark.com%2F%40babel%2Fplugin-syntax-function-bind%2Fdownload%2F%40babel%2Fplugin-syntax-function-bind-7.14.5.tgz"
  "version" "7.14.5"
  dependencies:
    "@babel/helper-plugin-utils" "^7.14.5"

"@babel/plugin-syntax-function-sent@^7.14.5":
  "integrity" "sha1-F7tV6eU4V6+TpXAAZEwCCWLwezI="
  "resolved" "https://registry.nlark.com/@babel/plugin-syntax-function-sent/download/@babel/plugin-syntax-function-sent-7.14.5.tgz"
  "version" "7.14.5"
  dependencies:
    "@babel/helper-plugin-utils" "^7.14.5"

"@babel/plugin-syntax-import-meta@^7.2.0":
  "integrity" "sha1-7mATSMNw+jNNIge+FYd3SWUh/VE="
  "resolved" "https://registry.npm.taobao.org/@babel/plugin-syntax-import-meta/download/@babel/plugin-syntax-import-meta-7.10.4.tgz"
  "version" "7.10.4"
  dependencies:
    "@babel/helper-plugin-utils" "^7.10.4"

"@babel/plugin-syntax-json-strings@^7.8.3":
  "integrity" "sha1-AcohtmjNghjJ5kDLbdiMVBKyyWo="
  "resolved" "https://registry.nlark.com/@babel/plugin-syntax-json-strings/download/@babel/plugin-syntax-json-strings-7.8.3.tgz"
  "version" "7.8.3"
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.0"

"@babel/plugin-syntax-jsx@^7.14.5":
  "integrity" "sha1-AA4uJdhnPM5JMAUXo+2kTCY+QgE="
  "resolved" "https://registry.nlark.com/@babel/plugin-syntax-jsx/download/@babel/plugin-syntax-jsx-7.14.5.tgz?cache=0&sync_timestamp=1623280424207&other_urls=https%3A%2F%2Fregistry.nlark.com%2F%40babel%2Fplugin-syntax-jsx%2Fdownload%2F%40babel%2Fplugin-syntax-jsx-7.14.5.tgz"
  "version" "7.14.5"
  dependencies:
    "@babel/helper-plugin-utils" "^7.14.5"

"@babel/plugin-syntax-logical-assignment-operators@^7.10.4":
  "integrity" "sha1-ypHvRjA1MESLkGZSusLp/plB9pk="
  "resolved" "https://registry.nlark.com/@babel/plugin-syntax-logical-assignment-operators/download/@babel/plugin-syntax-logical-assignment-operators-7.10.4.tgz"
  "version" "7.10.4"
  dependencies:
    "@babel/helper-plugin-utils" "^7.10.4"

"@babel/plugin-syntax-nullish-coalescing-operator@^7.8.3":
  "integrity" "sha1-Fn7XA2iIYIH3S1w2xlqIwDtm0ak="
  "resolved" "https://registry.npm.taobao.org/@babel/plugin-syntax-nullish-coalescing-operator/download/@babel/plugin-syntax-nullish-coalescing-operator-7.8.3.tgz"
  "version" "7.8.3"
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.0"

"@babel/plugin-syntax-numeric-separator@^7.10.4":
  "integrity" "sha1-ubBws+M1cM2f0Hun+pHA3Te5r5c="
  "resolved" "https://registry.npm.taobao.org/@babel/plugin-syntax-numeric-separator/download/@babel/plugin-syntax-numeric-separator-7.10.4.tgz?cache=0&sync_timestamp=1593522054358&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40babel%2Fplugin-syntax-numeric-separator%2Fdownload%2F%40babel%2Fplugin-syntax-numeric-separator-7.10.4.tgz"
  "version" "7.10.4"
  dependencies:
    "@babel/helper-plugin-utils" "^7.10.4"

"@babel/plugin-syntax-object-rest-spread@^7.8.3":
  "integrity" "sha1-YOIl7cvZimQDMqLnLdPmbxr1WHE="
  "resolved" "https://registry.nlark.com/@babel/plugin-syntax-object-rest-spread/download/@babel/plugin-syntax-object-rest-spread-7.8.3.tgz"
  "version" "7.8.3"
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.0"

"@babel/plugin-syntax-optional-catch-binding@^7.8.3":
  "integrity" "sha1-YRGiZbz7Ag6579D9/X0mQCue1sE="
  "resolved" "https://registry.npm.taobao.org/@babel/plugin-syntax-optional-catch-binding/download/@babel/plugin-syntax-optional-catch-binding-7.8.3.tgz"
  "version" "7.8.3"
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.0"

"@babel/plugin-syntax-optional-chaining@^7.8.3":
  "integrity" "sha1-T2nCq5UWfgGAzVM2YT+MV4j31Io="
  "resolved" "https://registry.nlark.com/@babel/plugin-syntax-optional-chaining/download/@babel/plugin-syntax-optional-chaining-7.8.3.tgz"
  "version" "7.8.3"
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.0"

"@babel/plugin-syntax-private-property-in-object@^7.14.5":
  "integrity" "sha1-DcZnHsDqIrbpShEU+FeXDNOd4a0="
  "resolved" "https://registry.nlark.com/@babel/plugin-syntax-private-property-in-object/download/@babel/plugin-syntax-private-property-in-object-7.14.5.tgz?cache=0&sync_timestamp=1623280423879&other_urls=https%3A%2F%2Fregistry.nlark.com%2F%40babel%2Fplugin-syntax-private-property-in-object%2Fdownload%2F%40babel%2Fplugin-syntax-private-property-in-object-7.14.5.tgz"
  "version" "7.14.5"
  dependencies:
    "@babel/helper-plugin-utils" "^7.14.5"

"@babel/plugin-syntax-throw-expressions@^7.14.5":
  "integrity" "sha1-25Z4XZEx+n54aJaOind6xtPtqAE="
  "resolved" "https://registry.nlark.com/@babel/plugin-syntax-throw-expressions/download/@babel/plugin-syntax-throw-expressions-7.14.5.tgz"
  "version" "7.14.5"
  dependencies:
    "@babel/helper-plugin-utils" "^7.14.5"

"@babel/plugin-syntax-top-level-await@^7.14.5":
  "integrity" "sha1-wc/a3DWmRiQAAfBhOCR7dBw02Uw="
  "resolved" "https://registry.nlark.com/@babel/plugin-syntax-top-level-await/download/@babel/plugin-syntax-top-level-await-7.14.5.tgz?cache=0&sync_timestamp=1623280427172&other_urls=https%3A%2F%2Fregistry.nlark.com%2F%40babel%2Fplugin-syntax-top-level-await%2Fdownload%2F%40babel%2Fplugin-syntax-top-level-await-7.14.5.tgz"
  "version" "7.14.5"
  dependencies:
    "@babel/helper-plugin-utils" "^7.14.5"

"@babel/plugin-syntax-typescript@^7.14.5":
  "integrity" "sha1-uCxs5HGxZbXOQgz5KRTW+0YiVxY="
  "resolved" "https://registry.nlark.com/@babel/plugin-syntax-typescript/download/@babel/plugin-syntax-typescript-7.14.5.tgz?cache=0&sync_timestamp=1623281252012&other_urls=https%3A%2F%2Fregistry.nlark.com%2F%40babel%2Fplugin-syntax-typescript%2Fdownload%2F%40babel%2Fplugin-syntax-typescript-7.14.5.tgz"
  "version" "7.14.5"
  dependencies:
    "@babel/helper-plugin-utils" "^7.14.5"

"@babel/plugin-transform-arrow-functions@^7.14.5":
  "integrity" "sha1-9xh9lYinaN0IC/TJ/+EX6mL3hio="
  "resolved" "https://registry.nlark.com/@babel/plugin-transform-arrow-functions/download/@babel/plugin-transform-arrow-functions-7.14.5.tgz?cache=0&sync_timestamp=1623280426581&other_urls=https%3A%2F%2Fregistry.nlark.com%2F%40babel%2Fplugin-transform-arrow-functions%2Fdownload%2F%40babel%2Fplugin-transform-arrow-functions-7.14.5.tgz"
  "version" "7.14.5"
  dependencies:
    "@babel/helper-plugin-utils" "^7.14.5"

"@babel/plugin-transform-async-to-generator@^7.14.5":
  "integrity" "sha1-cseJCE2PIJSsuUVjOUPvhEPTnmc="
  "resolved" "https://registry.nlark.com/@babel/plugin-transform-async-to-generator/download/@babel/plugin-transform-async-to-generator-7.14.5.tgz?cache=0&sync_timestamp=1623280415969&other_urls=https%3A%2F%2Fregistry.nlark.com%2F%40babel%2Fplugin-transform-async-to-generator%2Fdownload%2F%40babel%2Fplugin-transform-async-to-generator-7.14.5.tgz"
  "version" "7.14.5"
  dependencies:
    "@babel/helper-module-imports" "^7.14.5"
    "@babel/helper-plugin-utils" "^7.14.5"
    "@babel/helper-remap-async-to-generator" "^7.14.5"

"@babel/plugin-transform-block-scoped-functions@^7.14.5":
  "integrity" "sha1-5IZB2ZnUvBV6Z+8zautUvET9OtQ="
  "resolved" "https://registry.nlark.com/@babel/plugin-transform-block-scoped-functions/download/@babel/plugin-transform-block-scoped-functions-7.14.5.tgz?cache=0&sync_timestamp=1623280426909&other_urls=https%3A%2F%2Fregistry.nlark.com%2F%40babel%2Fplugin-transform-block-scoped-functions%2Fdownload%2F%40babel%2Fplugin-transform-block-scoped-functions-7.14.5.tgz"
  "version" "7.14.5"
  dependencies:
    "@babel/helper-plugin-utils" "^7.14.5"

"@babel/plugin-transform-block-scoping@^7.14.5":
  "integrity" "sha1-jMY+YeUPQuB45vCb53WnXyPvmTk="
  "resolved" "https://registry.nlark.com/@babel/plugin-transform-block-scoping/download/@babel/plugin-transform-block-scoping-7.14.5.tgz?cache=0&sync_timestamp=1623280394580&other_urls=https%3A%2F%2Fregistry.nlark.com%2F%40babel%2Fplugin-transform-block-scoping%2Fdownload%2F%40babel%2Fplugin-transform-block-scoping-7.14.5.tgz"
  "version" "7.14.5"
  dependencies:
    "@babel/helper-plugin-utils" "^7.14.5"

"@babel/plugin-transform-classes@^7.14.5":
  "integrity" "sha1-DpjoIJezhVCwO0g/m1GnjeCsss8="
  "resolved" "https://registry.nlark.com/@babel/plugin-transform-classes/download/@babel/plugin-transform-classes-7.14.5.tgz?cache=0&sync_timestamp=1623280406029&other_urls=https%3A%2F%2Fregistry.nlark.com%2F%40babel%2Fplugin-transform-classes%2Fdownload%2F%40babel%2Fplugin-transform-classes-7.14.5.tgz"
  "version" "7.14.5"
  dependencies:
    "@babel/helper-annotate-as-pure" "^7.14.5"
    "@babel/helper-function-name" "^7.14.5"
    "@babel/helper-optimise-call-expression" "^7.14.5"
    "@babel/helper-plugin-utils" "^7.14.5"
    "@babel/helper-replace-supers" "^7.14.5"
    "@babel/helper-split-export-declaration" "^7.14.5"
    "globals" "^11.1.0"

"@babel/plugin-transform-computed-properties@^7.14.5":
  "integrity" "sha1-G514mHQg0RIj1BGVRhzEO5dLIE8="
  "resolved" "https://registry.nlark.com/@babel/plugin-transform-computed-properties/download/@babel/plugin-transform-computed-properties-7.14.5.tgz?cache=0&sync_timestamp=1623280393882&other_urls=https%3A%2F%2Fregistry.nlark.com%2F%40babel%2Fplugin-transform-computed-properties%2Fdownload%2F%40babel%2Fplugin-transform-computed-properties-7.14.5.tgz"
  "version" "7.14.5"
  dependencies:
    "@babel/helper-plugin-utils" "^7.14.5"

"@babel/plugin-transform-destructuring@^7.14.7":
  "integrity" "sha1-CtWO034j4iCE0QnxhSYINeVVdXY="
  "resolved" "https://registry.nlark.com/@babel/plugin-transform-destructuring/download/@babel/plugin-transform-destructuring-7.14.7.tgz"
  "version" "7.14.7"
  dependencies:
    "@babel/helper-plugin-utils" "^7.14.5"

"@babel/plugin-transform-dotall-regex@^7.14.5", "@babel/plugin-transform-dotall-regex@^7.4.4":
  "integrity" "sha1-L2v3bka9+AQ7Tn4WzyRTJim6DHo="
  "resolved" "https://registry.nlark.com/@babel/plugin-transform-dotall-regex/download/@babel/plugin-transform-dotall-regex-7.14.5.tgz?cache=0&sync_timestamp=1623280386290&other_urls=https%3A%2F%2Fregistry.nlark.com%2F%40babel%2Fplugin-transform-dotall-regex%2Fdownload%2F%40babel%2Fplugin-transform-dotall-regex-7.14.5.tgz"
  "version" "7.14.5"
  dependencies:
    "@babel/helper-create-regexp-features-plugin" "^7.14.5"
    "@babel/helper-plugin-utils" "^7.14.5"

"@babel/plugin-transform-duplicate-keys@^7.14.5":
  "integrity" "sha1-NlpIRIgb3xUB46nwJw5/D5EXeVQ="
  "resolved" "https://registry.nlark.com/@babel/plugin-transform-duplicate-keys/download/@babel/plugin-transform-duplicate-keys-7.14.5.tgz"
  "version" "7.14.5"
  dependencies:
    "@babel/helper-plugin-utils" "^7.14.5"

"@babel/plugin-transform-exponentiation-operator@^7.14.5":
  "integrity" "sha1-UVS43Wo9/m2Qkj1hckvT3uuQtJM="
  "resolved" "https://registry.nlark.com/@babel/plugin-transform-exponentiation-operator/download/@babel/plugin-transform-exponentiation-operator-7.14.5.tgz?cache=0&sync_timestamp=1623280390976&other_urls=https%3A%2F%2Fregistry.nlark.com%2F%40babel%2Fplugin-transform-exponentiation-operator%2Fdownload%2F%40babel%2Fplugin-transform-exponentiation-operator-7.14.5.tgz"
  "version" "7.14.5"
  dependencies:
    "@babel/helper-builder-binary-assignment-operator-visitor" "^7.14.5"
    "@babel/helper-plugin-utils" "^7.14.5"

"@babel/plugin-transform-for-of@^7.14.5":
  "integrity" "sha1-2uOEYT3o93wZaohpy/YCpE9/wOs="
  "resolved" "https://registry.nlark.com/@babel/plugin-transform-for-of/download/@babel/plugin-transform-for-of-7.14.5.tgz?cache=0&sync_timestamp=1623280394473&other_urls=https%3A%2F%2Fregistry.nlark.com%2F%40babel%2Fplugin-transform-for-of%2Fdownload%2F%40babel%2Fplugin-transform-for-of-7.14.5.tgz"
  "version" "7.14.5"
  dependencies:
    "@babel/helper-plugin-utils" "^7.14.5"

"@babel/plugin-transform-function-name@^7.14.5":
  "integrity" "sha1-6Bxl7LkAdG1/MYAva+0fUtkV1vI="
  "resolved" "https://registry.nlark.com/@babel/plugin-transform-function-name/download/@babel/plugin-transform-function-name-7.14.5.tgz?cache=0&sync_timestamp=1623280395990&other_urls=https%3A%2F%2Fregistry.nlark.com%2F%40babel%2Fplugin-transform-function-name%2Fdownload%2F%40babel%2Fplugin-transform-function-name-7.14.5.tgz"
  "version" "7.14.5"
  dependencies:
    "@babel/helper-function-name" "^7.14.5"
    "@babel/helper-plugin-utils" "^7.14.5"

"@babel/plugin-transform-literals@^7.14.5":
  "integrity" "sha1-QdBsf/XU0J489Fh70+zzkwxzD3g="
  "resolved" "https://registry.nlark.com/@babel/plugin-transform-literals/download/@babel/plugin-transform-literals-7.14.5.tgz?cache=0&sync_timestamp=1623280393774&other_urls=https%3A%2F%2Fregistry.nlark.com%2F%40babel%2Fplugin-transform-literals%2Fdownload%2F%40babel%2Fplugin-transform-literals-7.14.5.tgz"
  "version" "7.14.5"
  dependencies:
    "@babel/helper-plugin-utils" "^7.14.5"

"@babel/plugin-transform-member-expression-literals@^7.14.5":
  "integrity" "sha1-s5zVISor8jWmF9Mg7CtIvMCRuKc="
  "resolved" "https://registry.nlark.com/@babel/plugin-transform-member-expression-literals/download/@babel/plugin-transform-member-expression-literals-7.14.5.tgz"
  "version" "7.14.5"
  dependencies:
    "@babel/helper-plugin-utils" "^7.14.5"

"@babel/plugin-transform-modules-amd@^7.14.5":
  "integrity" "sha1-T9nOfjQRy4uDhISAtwQdgwBIWPc="
  "resolved" "https://registry.nlark.com/@babel/plugin-transform-modules-amd/download/@babel/plugin-transform-modules-amd-7.14.5.tgz?cache=0&sync_timestamp=1623280416173&other_urls=https%3A%2F%2Fregistry.nlark.com%2F%40babel%2Fplugin-transform-modules-amd%2Fdownload%2F%40babel%2Fplugin-transform-modules-amd-7.14.5.tgz"
  "version" "7.14.5"
  dependencies:
    "@babel/helper-module-transforms" "^7.14.5"
    "@babel/helper-plugin-utils" "^7.14.5"
    "babel-plugin-dynamic-import-node" "^2.3.3"

"@babel/plugin-transform-modules-commonjs@^7.14.5":
  "integrity" "sha1-eq7g6pgoPelNqYso+MNXAUKdrZc="
  "resolved" "https://registry.nlark.com/@babel/plugin-transform-modules-commonjs/download/@babel/plugin-transform-modules-commonjs-7.14.5.tgz?cache=0&sync_timestamp=1623280421563&other_urls=https%3A%2F%2Fregistry.nlark.com%2F%40babel%2Fplugin-transform-modules-commonjs%2Fdownload%2F%40babel%2Fplugin-transform-modules-commonjs-7.14.5.tgz"
  "version" "7.14.5"
  dependencies:
    "@babel/helper-module-transforms" "^7.14.5"
    "@babel/helper-plugin-utils" "^7.14.5"
    "@babel/helper-simple-access" "^7.14.5"
    "babel-plugin-dynamic-import-node" "^2.3.3"

"@babel/plugin-transform-modules-systemjs@^7.14.5":
  "integrity" "sha1-x1NC74sw3N5CldNAGq4k5lY47Sk="
  "resolved" "https://registry.nlark.com/@babel/plugin-transform-modules-systemjs/download/@babel/plugin-transform-modules-systemjs-7.14.5.tgz?cache=0&sync_timestamp=1623280420500&other_urls=https%3A%2F%2Fregistry.nlark.com%2F%40babel%2Fplugin-transform-modules-systemjs%2Fdownload%2F%40babel%2Fplugin-transform-modules-systemjs-7.14.5.tgz"
  "version" "7.14.5"
  dependencies:
    "@babel/helper-hoist-variables" "^7.14.5"
    "@babel/helper-module-transforms" "^7.14.5"
    "@babel/helper-plugin-utils" "^7.14.5"
    "@babel/helper-validator-identifier" "^7.14.5"
    "babel-plugin-dynamic-import-node" "^2.3.3"

"@babel/plugin-transform-modules-umd@^7.14.5":
  "integrity" "sha1-+2Yt/uaXzOJ0p82lJRkKeQlqpuA="
  "resolved" "https://registry.nlark.com/@babel/plugin-transform-modules-umd/download/@babel/plugin-transform-modules-umd-7.14.5.tgz?cache=0&sync_timestamp=1623280420892&other_urls=https%3A%2F%2Fregistry.nlark.com%2F%40babel%2Fplugin-transform-modules-umd%2Fdownload%2F%40babel%2Fplugin-transform-modules-umd-7.14.5.tgz"
  "version" "7.14.5"
  dependencies:
    "@babel/helper-module-transforms" "^7.14.5"
    "@babel/helper-plugin-utils" "^7.14.5"

"@babel/plugin-transform-named-capturing-groups-regex@^7.14.7":
  "integrity" "sha1-YMBokqz53yMeJWwkRkv+ywkI/U4="
  "resolved" "https://registry.nlark.com/@babel/plugin-transform-named-capturing-groups-regex/download/@babel/plugin-transform-named-capturing-groups-regex-7.14.7.tgz"
  "version" "7.14.7"
  dependencies:
    "@babel/helper-create-regexp-features-plugin" "^7.14.5"

"@babel/plugin-transform-new-target@^7.14.5":
  "integrity" "sha1-Mb2ui5JdyEB26/zSqZQBQ67X2/g="
  "resolved" "https://registry.nlark.com/@babel/plugin-transform-new-target/download/@babel/plugin-transform-new-target-7.14.5.tgz"
  "version" "7.14.5"
  dependencies:
    "@babel/helper-plugin-utils" "^7.14.5"

"@babel/plugin-transform-object-super@^7.14.5":
  "integrity" "sha1-0LX66snphZehYanPeMUn7ZNM3EU="
  "resolved" "https://registry.nlark.com/@babel/plugin-transform-object-super/download/@babel/plugin-transform-object-super-7.14.5.tgz?cache=0&sync_timestamp=1623280405923&other_urls=https%3A%2F%2Fregistry.nlark.com%2F%40babel%2Fplugin-transform-object-super%2Fdownload%2F%40babel%2Fplugin-transform-object-super-7.14.5.tgz"
  "version" "7.14.5"
  dependencies:
    "@babel/helper-plugin-utils" "^7.14.5"
    "@babel/helper-replace-supers" "^7.14.5"

"@babel/plugin-transform-parameters@^7.14.5":
  "integrity" "sha1-SWYuhqHz3cysY2On37H/ChWK/rM="
  "resolved" "https://registry.nlark.com/@babel/plugin-transform-parameters/download/@babel/plugin-transform-parameters-7.14.5.tgz"
  "version" "7.14.5"
  dependencies:
    "@babel/helper-plugin-utils" "^7.14.5"

"@babel/plugin-transform-property-literals@^7.14.5":
  "integrity" "sha1-DduqH4PbNgbxzfSEb6HftHNFizQ="
  "resolved" "https://registry.nlark.com/@babel/plugin-transform-property-literals/download/@babel/plugin-transform-property-literals-7.14.5.tgz"
  "version" "7.14.5"
  dependencies:
    "@babel/helper-plugin-utils" "^7.14.5"

"@babel/plugin-transform-react-display-name@^7.14.5":
  "integrity" "sha1-uqktFcRXBBEwGoWnTBNTSHOIW2U="
  "resolved" "https://registry.nlark.com/@babel/plugin-transform-react-display-name/download/@babel/plugin-transform-react-display-name-7.14.5.tgz?cache=0&sync_timestamp=1623281082034&other_urls=https%3A%2F%2Fregistry.nlark.com%2F%40babel%2Fplugin-transform-react-display-name%2Fdownload%2F%40babel%2Fplugin-transform-react-display-name-7.14.5.tgz"
  "version" "7.14.5"
  dependencies:
    "@babel/helper-plugin-utils" "^7.14.5"

"@babel/plugin-transform-react-jsx-development@^7.14.5":
  "integrity" "sha1-Gmxz4vftLELuvD0q1gsMdJT8ua8="
  "resolved" "https://registry.nlark.com/@babel/plugin-transform-react-jsx-development/download/@babel/plugin-transform-react-jsx-development-7.14.5.tgz"
  "version" "7.14.5"
  dependencies:
    "@babel/plugin-transform-react-jsx" "^7.14.5"

"@babel/plugin-transform-react-jsx@^7.14.5":
  "integrity" "sha1-OXSfDuHv2KG9cpFSz1948dJHpEo="
  "resolved" "https://registry.nlark.com/@babel/plugin-transform-react-jsx/download/@babel/plugin-transform-react-jsx-7.14.5.tgz?cache=0&sync_timestamp=1623281231398&other_urls=https%3A%2F%2Fregistry.nlark.com%2F%40babel%2Fplugin-transform-react-jsx%2Fdownload%2F%40babel%2Fplugin-transform-react-jsx-7.14.5.tgz"
  "version" "7.14.5"
  dependencies:
    "@babel/helper-annotate-as-pure" "^7.14.5"
    "@babel/helper-module-imports" "^7.14.5"
    "@babel/helper-plugin-utils" "^7.14.5"
    "@babel/plugin-syntax-jsx" "^7.14.5"
    "@babel/types" "^7.14.5"

"@babel/plugin-transform-react-pure-annotations@^7.14.5":
  "integrity" "sha1-GN5hK4QCHjqYAsvCEsnZ9G0NEfw="
  "resolved" "https://registry.nlark.com/@babel/plugin-transform-react-pure-annotations/download/@babel/plugin-transform-react-pure-annotations-7.14.5.tgz"
  "version" "7.14.5"
  dependencies:
    "@babel/helper-annotate-as-pure" "^7.14.5"
    "@babel/helper-plugin-utils" "^7.14.5"

"@babel/plugin-transform-regenerator@^7.14.5":
  "integrity" "sha1-lnb9VwftKPUicnxbPAqoVERAsE8="
  "resolved" "https://registry.nlark.com/@babel/plugin-transform-regenerator/download/@babel/plugin-transform-regenerator-7.14.5.tgz?cache=0&sync_timestamp=1623280296038&other_urls=https%3A%2F%2Fregistry.nlark.com%2F%40babel%2Fplugin-transform-regenerator%2Fdownload%2F%40babel%2Fplugin-transform-regenerator-7.14.5.tgz"
  "version" "7.14.5"
  dependencies:
    "regenerator-transform" "^0.14.2"

"@babel/plugin-transform-reserved-words@^7.14.5":
  "integrity" "sha1-xEWJtmHP2++NQwDcx0ad/6kvgwQ="
  "resolved" "https://registry.nlark.com/@babel/plugin-transform-reserved-words/download/@babel/plugin-transform-reserved-words-7.14.5.tgz"
  "version" "7.14.5"
  dependencies:
    "@babel/helper-plugin-utils" "^7.14.5"

"@babel/plugin-transform-shorthand-properties@^7.14.5":
  "integrity" "sha1-l/E4VfFAkzjYyty6ymcK154JGlg="
  "resolved" "https://registry.nlark.com/@babel/plugin-transform-shorthand-properties/download/@babel/plugin-transform-shorthand-properties-7.14.5.tgz"
  "version" "7.14.5"
  dependencies:
    "@babel/helper-plugin-utils" "^7.14.5"

"@babel/plugin-transform-spread@^7.14.6":
  "integrity" "sha1-a9QOV/596UqpBIUZY7VhZlL3MUQ="
  "resolved" "https://registry.nlark.com/@babel/plugin-transform-spread/download/@babel/plugin-transform-spread-7.14.6.tgz"
  "version" "7.14.6"
  dependencies:
    "@babel/helper-plugin-utils" "^7.14.5"
    "@babel/helper-skip-transparent-expression-wrappers" "^7.14.5"

"@babel/plugin-transform-sticky-regex@^7.14.5":
  "integrity" "sha1-W2F1Qmdei3dhKUOB88KMYz9Arrk="
  "resolved" "https://registry.nlark.com/@babel/plugin-transform-sticky-regex/download/@babel/plugin-transform-sticky-regex-7.14.5.tgz"
  "version" "7.14.5"
  dependencies:
    "@babel/helper-plugin-utils" "^7.14.5"

"@babel/plugin-transform-template-literals@^7.14.5":
  "integrity" "sha1-pfK8Izk32EU4hdxza92Nn/q/PZM="
  "resolved" "https://registry.nlark.com/@babel/plugin-transform-template-literals/download/@babel/plugin-transform-template-literals-7.14.5.tgz"
  "version" "7.14.5"
  dependencies:
    "@babel/helper-plugin-utils" "^7.14.5"

"@babel/plugin-transform-typeof-symbol@^7.14.5":
  "integrity" "sha1-Oa8nOemJor0pG/a1PxaYFCPUV9Q="
  "resolved" "https://registry.nlark.com/@babel/plugin-transform-typeof-symbol/download/@babel/plugin-transform-typeof-symbol-7.14.5.tgz"
  "version" "7.14.5"
  dependencies:
    "@babel/helper-plugin-utils" "^7.14.5"

"@babel/plugin-transform-typescript@^7.14.5":
  "integrity" "sha1-bpwtmNolB+vgqIOxAM3jxyed82w="
  "resolved" "https://registry.nlark.com/@babel/plugin-transform-typescript/download/@babel/plugin-transform-typescript-7.14.6.tgz?cache=0&sync_timestamp=1623708034552&other_urls=https%3A%2F%2Fregistry.nlark.com%2F%40babel%2Fplugin-transform-typescript%2Fdownload%2F%40babel%2Fplugin-transform-typescript-7.14.6.tgz"
  "version" "7.14.6"
  dependencies:
    "@babel/helper-create-class-features-plugin" "^7.14.6"
    "@babel/helper-plugin-utils" "^7.14.5"
    "@babel/plugin-syntax-typescript" "^7.14.5"

"@babel/plugin-transform-unicode-escapes@^7.14.5":
  "integrity" "sha1-nUvSpoHjxdes9PV/qeURddkdDGs="
  "resolved" "https://registry.nlark.com/@babel/plugin-transform-unicode-escapes/download/@babel/plugin-transform-unicode-escapes-7.14.5.tgz"
  "version" "7.14.5"
  dependencies:
    "@babel/helper-plugin-utils" "^7.14.5"

"@babel/plugin-transform-unicode-regex@^7.14.5":
  "integrity" "sha1-TNCbbIQl3YElXHzrP7GDbnQUOC4="
  "resolved" "https://registry.nlark.com/@babel/plugin-transform-unicode-regex/download/@babel/plugin-transform-unicode-regex-7.14.5.tgz?cache=0&sync_timestamp=1623280395968&other_urls=https%3A%2F%2Fregistry.nlark.com%2F%40babel%2Fplugin-transform-unicode-regex%2Fdownload%2F%40babel%2Fplugin-transform-unicode-regex-7.14.5.tgz"
  "version" "7.14.5"
  dependencies:
    "@babel/helper-create-regexp-features-plugin" "^7.14.5"
    "@babel/helper-plugin-utils" "^7.14.5"

"@babel/preset-env@^7.5.5":
  "integrity" "sha1-XHCyLUwtiTsD2MiGpcF0IlArkyo="
  "resolved" "https://registry.nlark.com/@babel/preset-env/download/@babel/preset-env-7.14.7.tgz"
  "version" "7.14.7"
  dependencies:
    "@babel/compat-data" "^7.14.7"
    "@babel/helper-compilation-targets" "^7.14.5"
    "@babel/helper-plugin-utils" "^7.14.5"
    "@babel/helper-validator-option" "^7.14.5"
    "@babel/plugin-bugfix-v8-spread-parameters-in-optional-chaining" "^7.14.5"
    "@babel/plugin-proposal-async-generator-functions" "^7.14.7"
    "@babel/plugin-proposal-class-properties" "^7.14.5"
    "@babel/plugin-proposal-class-static-block" "^7.14.5"
    "@babel/plugin-proposal-dynamic-import" "^7.14.5"
    "@babel/plugin-proposal-export-namespace-from" "^7.14.5"
    "@babel/plugin-proposal-json-strings" "^7.14.5"
    "@babel/plugin-proposal-logical-assignment-operators" "^7.14.5"
    "@babel/plugin-proposal-nullish-coalescing-operator" "^7.14.5"
    "@babel/plugin-proposal-numeric-separator" "^7.14.5"
    "@babel/plugin-proposal-object-rest-spread" "^7.14.7"
    "@babel/plugin-proposal-optional-catch-binding" "^7.14.5"
    "@babel/plugin-proposal-optional-chaining" "^7.14.5"
    "@babel/plugin-proposal-private-methods" "^7.14.5"
    "@babel/plugin-proposal-private-property-in-object" "^7.14.5"
    "@babel/plugin-proposal-unicode-property-regex" "^7.14.5"
    "@babel/plugin-syntax-async-generators" "^7.8.4"
    "@babel/plugin-syntax-class-properties" "^7.12.13"
    "@babel/plugin-syntax-class-static-block" "^7.14.5"
    "@babel/plugin-syntax-dynamic-import" "^7.8.3"
    "@babel/plugin-syntax-export-namespace-from" "^7.8.3"
    "@babel/plugin-syntax-json-strings" "^7.8.3"
    "@babel/plugin-syntax-logical-assignment-operators" "^7.10.4"
    "@babel/plugin-syntax-nullish-coalescing-operator" "^7.8.3"
    "@babel/plugin-syntax-numeric-separator" "^7.10.4"
    "@babel/plugin-syntax-object-rest-spread" "^7.8.3"
    "@babel/plugin-syntax-optional-catch-binding" "^7.8.3"
    "@babel/plugin-syntax-optional-chaining" "^7.8.3"
    "@babel/plugin-syntax-private-property-in-object" "^7.14.5"
    "@babel/plugin-syntax-top-level-await" "^7.14.5"
    "@babel/plugin-transform-arrow-functions" "^7.14.5"
    "@babel/plugin-transform-async-to-generator" "^7.14.5"
    "@babel/plugin-transform-block-scoped-functions" "^7.14.5"
    "@babel/plugin-transform-block-scoping" "^7.14.5"
    "@babel/plugin-transform-classes" "^7.14.5"
    "@babel/plugin-transform-computed-properties" "^7.14.5"
    "@babel/plugin-transform-destructuring" "^7.14.7"
    "@babel/plugin-transform-dotall-regex" "^7.14.5"
    "@babel/plugin-transform-duplicate-keys" "^7.14.5"
    "@babel/plugin-transform-exponentiation-operator" "^7.14.5"
    "@babel/plugin-transform-for-of" "^7.14.5"
    "@babel/plugin-transform-function-name" "^7.14.5"
    "@babel/plugin-transform-literals" "^7.14.5"
    "@babel/plugin-transform-member-expression-literals" "^7.14.5"
    "@babel/plugin-transform-modules-amd" "^7.14.5"
    "@babel/plugin-transform-modules-commonjs" "^7.14.5"
    "@babel/plugin-transform-modules-systemjs" "^7.14.5"
    "@babel/plugin-transform-modules-umd" "^7.14.5"
    "@babel/plugin-transform-named-capturing-groups-regex" "^7.14.7"
    "@babel/plugin-transform-new-target" "^7.14.5"
    "@babel/plugin-transform-object-super" "^7.14.5"
    "@babel/plugin-transform-parameters" "^7.14.5"
    "@babel/plugin-transform-property-literals" "^7.14.5"
    "@babel/plugin-transform-regenerator" "^7.14.5"
    "@babel/plugin-transform-reserved-words" "^7.14.5"
    "@babel/plugin-transform-shorthand-properties" "^7.14.5"
    "@babel/plugin-transform-spread" "^7.14.6"
    "@babel/plugin-transform-sticky-regex" "^7.14.5"
    "@babel/plugin-transform-template-literals" "^7.14.5"
    "@babel/plugin-transform-typeof-symbol" "^7.14.5"
    "@babel/plugin-transform-unicode-escapes" "^7.14.5"
    "@babel/plugin-transform-unicode-regex" "^7.14.5"
    "@babel/preset-modules" "^0.1.4"
    "@babel/types" "^7.14.5"
    "babel-plugin-polyfill-corejs2" "^0.2.2"
    "babel-plugin-polyfill-corejs3" "^0.2.2"
    "babel-plugin-polyfill-regenerator" "^0.2.2"
    "core-js-compat" "^3.15.0"
    "semver" "^6.3.0"

"@babel/preset-modules@^0.1.4":
  "integrity" "sha1-Ni8raMZihClw/bXiVP/I/BwuQV4="
  "resolved" "https://registry.nlark.com/@babel/preset-modules/download/@babel/preset-modules-0.1.4.tgz"
  "version" "0.1.4"
  dependencies:
    "@babel/helper-plugin-utils" "^7.0.0"
    "@babel/plugin-proposal-unicode-property-regex" "^7.4.4"
    "@babel/plugin-transform-dotall-regex" "^7.4.4"
    "@babel/types" "^7.4.4"
    "esutils" "^2.0.2"

"@babel/preset-react@^7.0.0":
  "integrity" "sha1-D7t2lRP4mcLFbzqIL6eWc8LUqzw="
  "resolved" "https://registry.nlark.com/@babel/preset-react/download/@babel/preset-react-7.14.5.tgz"
  "version" "7.14.5"
  dependencies:
    "@babel/helper-plugin-utils" "^7.14.5"
    "@babel/helper-validator-option" "^7.14.5"
    "@babel/plugin-transform-react-display-name" "^7.14.5"
    "@babel/plugin-transform-react-jsx" "^7.14.5"
    "@babel/plugin-transform-react-jsx-development" "^7.14.5"
    "@babel/plugin-transform-react-pure-annotations" "^7.14.5"

"@babel/preset-typescript@^7.3.3":
  "integrity" "sha1-qpjeEZz5hSt5UR8Z5/RKLTebzOA="
  "resolved" "https://registry.nlark.com/@babel/preset-typescript/download/@babel/preset-typescript-7.14.5.tgz"
  "version" "7.14.5"
  dependencies:
    "@babel/helper-plugin-utils" "^7.14.5"
    "@babel/helper-validator-option" "^7.14.5"
    "@babel/plugin-transform-typescript" "^7.14.5"

"@babel/runtime-corejs3@^7.10.2":
  "integrity" "sha1-DvKSu85AygD4dMlyTvF1oSR2Rlw="
  "resolved" "https://registry.nlark.com/@babel/runtime-corejs3/download/@babel/runtime-corejs3-7.14.7.tgz"
  "version" "7.14.7"
  dependencies:
    "core-js-pure" "^3.15.0"
    "regenerator-runtime" "^0.13.4"

"@babel/runtime@^7.0.0", "@babel/runtime@^7.1.2", "@babel/runtime@^7.10.2", "@babel/runtime@^7.11.2", "@babel/runtime@^7.12.1", "@babel/runtime@^7.5.5", "@babel/runtime@^7.8.4", "@babel/runtime@^7.9.2":
  "integrity" "sha1-U1IDvAiS78fexgvcJ7Ls9uQJBi0="
  "resolved" "https://registry.nlark.com/@babel/runtime/download/@babel/runtime-7.14.6.tgz?cache=0&sync_timestamp=1623708023742&other_urls=https%3A%2F%2Fregistry.nlark.com%2F%40babel%2Fruntime%2Fdownload%2F%40babel%2Fruntime-7.14.6.tgz"
  "version" "7.14.6"
  dependencies:
    "regenerator-runtime" "^0.13.4"

"@babel/template@^7.14.5":
  "integrity" "sha1-qbydizM1T/blWpxg0RCSAKaJdPQ="
  "resolved" "https://registry.nlark.com/@babel/template/download/@babel/template-7.14.5.tgz?cache=0&sync_timestamp=1623280386138&other_urls=https%3A%2F%2Fregistry.nlark.com%2F%40babel%2Ftemplate%2Fdownload%2F%40babel%2Ftemplate-7.14.5.tgz"
  "version" "7.14.5"
  dependencies:
    "@babel/code-frame" "^7.14.5"
    "@babel/parser" "^7.14.5"
    "@babel/types" "^7.14.5"

"@babel/traverse@^7.13.0", "@babel/traverse@^7.14.5":
  "integrity" "sha1-ZAB8l3TP3Dq9I7B4C8GKPONjF1M="
  "resolved" "https://registry.nlark.com/@babel/traverse/download/@babel/traverse-7.14.7.tgz"
  "version" "7.14.7"
  dependencies:
    "@babel/code-frame" "^7.14.5"
    "@babel/generator" "^7.14.5"
    "@babel/helper-function-name" "^7.14.5"
    "@babel/helper-hoist-variables" "^7.14.5"
    "@babel/helper-split-export-declaration" "^7.14.5"
    "@babel/parser" "^7.14.7"
    "@babel/types" "^7.14.5"
    "debug" "^4.1.0"
    "globals" "^11.1.0"

"@babel/types@^7.14.5", "@babel/types@^7.4.4":
  "integrity" "sha1-O7mXuoKaIQTO2yBonEpbgSHTg/8="
  "resolved" "https://registry.nlark.com/@babel/types/download/@babel/types-7.14.5.tgz"
  "version" "7.14.5"
  dependencies:
    "@babel/helper-validator-identifier" "^7.14.5"
    "to-fast-properties" "^2.0.0"

"@eslint/eslintrc@^0.4.2":
  "integrity" "sha1-9j0O8G9cDFfXbEq19j04NcUbAXk="
  "resolved" "https://registry.nlark.com/@eslint/eslintrc/download/@eslint/eslintrc-0.4.2.tgz?cache=0&sync_timestamp=1622844991204&other_urls=https%3A%2F%2Fregistry.nlark.com%2F%40eslint%2Feslintrc%2Fdownload%2F%40eslint%2Feslintrc-0.4.2.tgz"
  "version" "0.4.2"
  dependencies:
    "ajv" "^6.12.4"
    "debug" "^4.1.1"
    "espree" "^7.3.0"
    "globals" "^13.9.0"
    "ignore" "^4.0.6"
    "import-fresh" "^3.2.1"
    "js-yaml" "^3.13.1"
    "minimatch" "^3.0.4"
    "strip-json-comments" "^3.1.1"

"@fast-csv/format@4.3.5":
  "integrity" "sha1-kNg9G0e2qvZ75w1hGPhPPhLuH/M="
  "resolved" "https://registry.npm.taobao.org/@fast-csv/format/download/@fast-csv/format-4.3.5.tgz"
  "version" "4.3.5"
  dependencies:
    "@types/node" "^14.0.1"
    "lodash.escaperegexp" "^4.1.2"
    "lodash.isboolean" "^3.0.3"
    "lodash.isequal" "^4.5.0"
    "lodash.isfunction" "^3.0.9"
    "lodash.isnil" "^4.0.0"

"@fast-csv/parse@4.3.6":
  "integrity" "sha1-7kfQZAygKRA0x6qUA5p0TPsBkmQ="
  "resolved" "https://registry.nlark.com/@fast-csv/parse/download/@fast-csv/parse-4.3.6.tgz"
  "version" "4.3.6"
  dependencies:
    "@types/node" "^14.0.1"
    "lodash.escaperegexp" "^4.1.2"
    "lodash.groupby" "^4.6.0"
    "lodash.isfunction" "^3.0.9"
    "lodash.isnil" "^4.0.0"
    "lodash.isundefined" "^3.0.1"
    "lodash.uniq" "^4.5.0"

"@types/braft-editor@^1.9.3":
  "integrity" "sha1-VexfdnhqsAV0L66B8gCPDKTUBE8="
  "resolved" "https://registry.npm.taobao.org/@types/braft-editor/download/@types/braft-editor-1.9.3.tgz"
  "version" "1.9.3"
  dependencies:
    "@types/draft-js" "*"
    "@types/react" "*"

"@types/cropperjs@^1.1.5":
  "integrity" "sha1-RLgE7JkeX7i4eJF6xRBn92ySo3c="
  "resolved" "https://registry.nlark.com/@types/cropperjs/download/@types/cropperjs-1.3.0.tgz"
  "version" "1.3.0"
  dependencies:
    "cropperjs" "*"

"@types/draft-js@*":
  "integrity" "sha1-gRDBQ+CrQZwE90lICzE+bMyHE7s="
  "resolved" "https://registry.nlark.com/@types/draft-js/download/@types/draft-js-0.11.3.tgz"
  "version" "0.11.3"
  dependencies:
    "@types/react" "*"
    "immutable" "~3.7.4"

"@types/emoji-mart@^3.0.5":
  "integrity" "sha1-Dl1v7+KMFQYGp1qWK88cWVZDkAE="
  "resolved" "https://registry.nlark.com/@types/emoji-mart/download/@types/emoji-mart-3.0.5.tgz"
  "version" "3.0.5"
  dependencies:
    "@types/react" "*"

"@types/eslint-visitor-keys@^1.0.0":
  "integrity" "sha1-HuMNeVRMqE1o1LPNsK9PIFZj3S0="
  "resolved" "https://registry.nlark.com/@types/eslint-visitor-keys/download/@types/eslint-visitor-keys-1.0.0.tgz"
  "version" "1.0.0"

"@types/glob@^7.1.1":
  "integrity" "sha1-5rqA82t9qtLGhazZJmOC5omFwYM="
  "resolved" "https://registry.nlark.com/@types/glob/download/@types/glob-7.1.3.tgz"
  "version" "7.1.3"
  dependencies:
    "@types/minimatch" "*"
    "@types/node" "*"

"@types/history@*":
  "integrity" "sha1-STSDh5gwdXBf6PTgL7Z/farsSTQ="
  "resolved" "https://registry.nlark.com/@types/history/download/@types/history-4.7.8.tgz"
  "version" "4.7.8"

"@types/hoist-non-react-statics@^3.3.0":
  "integrity" "sha1-ESSq/lEYy1kZd66xzqrtEHDrA58="
  "resolved" "https://registry.nlark.com/@types/hoist-non-react-statics/download/@types/hoist-non-react-statics-3.3.1.tgz?cache=0&sync_timestamp=1621241339435&other_urls=https%3A%2F%2Fregistry.nlark.com%2F%40types%2Fhoist-non-react-statics%2Fdownload%2F%40types%2Fhoist-non-react-statics-3.3.1.tgz"
  "version" "3.3.1"
  dependencies:
    "@types/react" "*"
    "hoist-non-react-statics" "^3.3.0"

"@types/isomorphic-fetch@0.0.35":
  "integrity" "sha1-wcDUAtqsMkWCthhrkfiQU0DqM2E="
  "resolved" "https://registry.nlark.com/@types/isomorphic-fetch/download/@types/isomorphic-fetch-0.0.35.tgz"
  "version" "0.0.35"

"@types/json-schema@^7.0.3", "@types/json-schema@^7.0.5":
  "integrity" "sha1-mKmTUWyFnrDVxMjwmDF6nqaNua0="
  "resolved" "https://registry.nlark.com/@types/json-schema/download/@types/json-schema-7.0.7.tgz"
  "version" "7.0.7"

"@types/json5@^0.0.29":
  "integrity" "sha1-7ihweulOEdK4J7y+UnC86n8+ce4="
  "resolved" "https://registry.npm.taobao.org/@types/json5/download/@types/json5-0.0.29.tgz"
  "version" "0.0.29"

"@types/lodash@^4.14.136":
  "integrity" "sha1-DWdxHUv39MpRR+kJG4R0ebh5JdY="
  "resolved" "https://registry.nlark.com/@types/lodash/download/@types/lodash-4.14.170.tgz"
  "version" "4.14.170"

"@types/minimatch@*":
  "integrity" "sha1-8Owl2/Lw5LGGRzE6wDETTKWySyE="
  "resolved" "https://registry.nlark.com/@types/minimatch/download/@types/minimatch-3.0.4.tgz"
  "version" "3.0.4"

"@types/node@*", "@types/node@^14.0.1":
  "integrity" "sha1-IYcSJCRG/IaNDgB68ppECMd2W8A="
  "resolved" "https://registry.nlark.com/@types/node/download/@types/node-14.17.4.tgz?cache=0&sync_timestamp=1624763053899&other_urls=https%3A%2F%2Fregistry.nlark.com%2F%40types%2Fnode%2Fdownload%2F%40types%2Fnode-14.17.4.tgz"
  "version" "14.17.4"

"@types/prop-types@*":
  "integrity" "sha1-KrDV2i5YFflLC51LldHl8kOrLKc="
  "resolved" "https://registry.nlark.com/@types/prop-types/download/@types/prop-types-15.7.3.tgz"
  "version" "15.7.3"

"@types/q@^1.5.1":
  "integrity" "sha1-FZJUFOCtLNdlv+9YhC9+JqesyyQ="
  "resolved" "https://registry.nlark.com/@types/q/download/@types/q-1.5.4.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.nlark.com%2F%40types%2Fq%2Fdownload%2F%40types%2Fq-1.5.4.tgz"
  "version" "1.5.4"

"@types/react-dom@^16.8.5":
  "integrity" "sha1-WJjw7mj+IAaF5rYdPX2IKGkoFNA="
  "resolved" "https://registry.nlark.com/@types/react-dom/download/@types/react-dom-16.9.13.tgz"
  "version" "16.9.13"
  dependencies:
    "@types/react" "^16"

"@types/react-redux@^7.1.1", "@types/react-redux@^7.1.16":
  "integrity" "sha1-D70EwlAMEhBUlMg9Sj5FwITjyyE="
  "resolved" "https://registry.nlark.com/@types/react-redux/download/@types/react-redux-7.1.16.tgz?cache=0&sync_timestamp=1621242443933&other_urls=https%3A%2F%2Fregistry.nlark.com%2F%40types%2Freact-redux%2Fdownload%2F%40types%2Freact-redux-7.1.16.tgz"
  "version" "7.1.16"
  dependencies:
    "@types/hoist-non-react-statics" "^3.3.0"
    "@types/react" "*"
    "hoist-non-react-statics" "^3.3.0"
    "redux" "^4.0.0"

"@types/react-router-dom@^5.1.5":
  "integrity" "sha1-oSbZ6nYHn/u9sNkiUHPrV5ercnE="
  "resolved" "https://registry.nlark.com/@types/react-router-dom/download/@types/react-router-dom-5.1.7.tgz"
  "version" "5.1.7"
  dependencies:
    "@types/history" "*"
    "@types/react" "*"
    "@types/react-router" "*"

"@types/react-router@*", "@types/react-router@^5.1.8":
  "integrity" "sha1-wQaeDaRhf9MV44G1axi4lJDhTio="
  "resolved" "https://registry.nlark.com/@types/react-router/download/@types/react-router-5.1.15.tgz"
  "version" "5.1.15"
  dependencies:
    "@types/history" "*"
    "@types/react" "*"

"@types/react-slick@^0.23.4":
  "integrity" "sha1-5V/ceb8ZAi73em8i6dZP0PdGPMQ="
  "resolved" "https://registry.nlark.com/@types/react-slick/download/@types/react-slick-0.23.5.tgz"
  "version" "0.23.5"
  dependencies:
    "@types/react" "*"

"@types/react@*", "@types/react@^15.0.0 || ^16.0.0 || ^17.0.0 ", "@types/react@^16", "@types/react@^16.9.1":
  "integrity" "sha1-Su46sATLmEUZF8m3raPH1+Uts/4="
  "resolved" "https://registry.nlark.com/@types/react/download/@types/react-16.14.8.tgz?cache=0&sync_timestamp=1623247499569&other_urls=https%3A%2F%2Fregistry.nlark.com%2F%40types%2Freact%2Fdownload%2F%40types%2Freact-16.14.8.tgz"
  "version" "16.14.8"
  dependencies:
    "@types/prop-types" "*"
    "@types/scheduler" "*"
    "csstype" "^3.0.2"

"@types/redux-logger@^3.0.7":
  "integrity" "sha1-H7bSaRe7GYeSuxz1f+sxyuFTLF0="
  "resolved" "https://registry.nlark.com/@types/redux-logger/download/@types/redux-logger-3.0.8.tgz?cache=0&sync_timestamp=1621242514621&other_urls=https%3A%2F%2Fregistry.nlark.com%2F%40types%2Fredux-logger%2Fdownload%2F%40types%2Fredux-logger-3.0.8.tgz"
  "version" "3.0.8"
  dependencies:
    "redux" "^4.0.0"

"@types/scheduler@*":
  "integrity" "sha1-GIRSBehv8AOFF6q3oYpiprn3EnU="
  "resolved" "https://registry.nlark.com/@types/scheduler/download/@types/scheduler-0.16.1.tgz?cache=0&sync_timestamp=1621242704081&other_urls=https%3A%2F%2Fregistry.nlark.com%2F%40types%2Fscheduler%2Fdownload%2F%40types%2Fscheduler-0.16.1.tgz"
  "version" "0.16.1"

"@types/source-list-map@*":
  "integrity" "sha1-AHiDYGP/rxdBI0m7o2QIfgrALsk="
  "resolved" "https://registry.nlark.com/@types/source-list-map/download/@types/source-list-map-0.1.2.tgz?cache=0&sync_timestamp=1621243743474&other_urls=https%3A%2F%2Fregistry.nlark.com%2F%40types%2Fsource-list-map%2Fdownload%2F%40types%2Fsource-list-map-0.1.2.tgz"
  "version" "0.1.2"

"@types/tapable@^1":
  "integrity" "sha1-VFFYNC+Uno/Tv9gTIklx7N3D+sQ="
  "resolved" "https://registry.nlark.com/@types/tapable/download/@types/tapable-1.0.7.tgz?cache=0&sync_timestamp=1621243788434&other_urls=https%3A%2F%2Fregistry.nlark.com%2F%40types%2Ftapable%2Fdownload%2F%40types%2Ftapable-1.0.7.tgz"
  "version" "1.0.7"

"@types/uglify-js@*":
  "integrity" "sha1-HK2N8fsLFDxaugjeVxLqnR/3ESQ="
  "resolved" "https://registry.nlark.com/@types/uglify-js/download/@types/uglify-js-3.13.0.tgz?cache=0&sync_timestamp=1621243851839&other_urls=https%3A%2F%2Fregistry.nlark.com%2F%40types%2Fuglify-js%2Fdownload%2F%40types%2Fuglify-js-3.13.0.tgz"
  "version" "3.13.0"
  dependencies:
    "source-map" "^0.6.1"

"@types/uuid@^3.4.5":
  "integrity" "sha1-/PAZl7vJ98Ca5fkTg68HbUZllOE="
  "resolved" "https://registry.nlark.com/@types/uuid/download/@types/uuid-3.4.9.tgz?cache=0&sync_timestamp=1621243965245&other_urls=https%3A%2F%2Fregistry.nlark.com%2F%40types%2Fuuid%2Fdownload%2F%40types%2Fuuid-3.4.9.tgz"
  "version" "3.4.9"

"@types/webpack-sources@*":
  "integrity" "sha1-iIKwvWLR4M5i8YPQ0Bty5ugujBA="
  "resolved" "https://registry.nlark.com/@types/webpack-sources/download/@types/webpack-sources-2.1.0.tgz?cache=0&sync_timestamp=1621243863278&other_urls=https%3A%2F%2Fregistry.nlark.com%2F%40types%2Fwebpack-sources%2Fdownload%2F%40types%2Fwebpack-sources-2.1.0.tgz"
  "version" "2.1.0"
  dependencies:
    "@types/node" "*"
    "@types/source-list-map" "*"
    "source-map" "^0.7.3"

"@types/webpack@^4.4.31":
  "integrity" "sha1-LmbB3oIjxEA2ZGlBXFCkfZdiV3M="
  "resolved" "https://registry.nlark.com/@types/webpack/download/@types/webpack-4.41.29.tgz?cache=0&sync_timestamp=1621533733988&other_urls=https%3A%2F%2Fregistry.nlark.com%2F%40types%2Fwebpack%2Fdownload%2F%40types%2Fwebpack-4.41.29.tgz"
  "version" "4.41.29"
  dependencies:
    "@types/node" "*"
    "@types/tapable" "^1"
    "@types/uglify-js" "*"
    "@types/webpack-sources" "*"
    "anymatch" "^3.0.0"
    "source-map" "^0.6.0"

"@typescript-eslint/eslint-plugin@^3.1.0", "@typescript-eslint/eslint-plugin@^3.5.0":
  "integrity" "sha1-fgYTOKE4P1ntwgTGBYmfk9wuLI8="
  "resolved" "https://registry.nlark.com/@typescript-eslint/eslint-plugin/download/@typescript-eslint/eslint-plugin-3.10.1.tgz"
  "version" "3.10.1"
  dependencies:
    "@typescript-eslint/experimental-utils" "3.10.1"
    "debug" "^4.1.1"
    "functional-red-black-tree" "^1.0.1"
    "regexpp" "^3.0.0"
    "semver" "^7.3.2"
    "tsutils" "^3.17.1"

"@typescript-eslint/experimental-utils@3.10.1":
  "integrity" "sha1-4Xn/yBqA68ri6gTgMy+LJRNFpoY="
  "resolved" "https://registry.nlark.com/@typescript-eslint/experimental-utils/download/@typescript-eslint/experimental-utils-3.10.1.tgz"
  "version" "3.10.1"
  dependencies:
    "@types/json-schema" "^7.0.3"
    "@typescript-eslint/types" "3.10.1"
    "@typescript-eslint/typescript-estree" "3.10.1"
    "eslint-scope" "^5.0.0"
    "eslint-utils" "^2.0.0"

"@typescript-eslint/parser@^3.0.0", "@typescript-eslint/parser@^3.1.0", "@typescript-eslint/parser@^3.5.0":
  "integrity" "sha1-GIOFjoPotEJifhrG9AiSUhEVVGc="
  "resolved" "https://registry.nlark.com/@typescript-eslint/parser/download/@typescript-eslint/parser-3.10.1.tgz"
  "version" "3.10.1"
  dependencies:
    "@types/eslint-visitor-keys" "^1.0.0"
    "@typescript-eslint/experimental-utils" "3.10.1"
    "@typescript-eslint/types" "3.10.1"
    "@typescript-eslint/typescript-estree" "3.10.1"
    "eslint-visitor-keys" "^1.1.0"

"@typescript-eslint/types@3.10.1":
  "integrity" "sha1-HXRj+nwy2KI6tQioA8ov4m51hyc="
  "resolved" "https://registry.nlark.com/@typescript-eslint/types/download/@typescript-eslint/types-3.10.1.tgz?cache=0&sync_timestamp=1624900647755&other_urls=https%3A%2F%2Fregistry.nlark.com%2F%40typescript-eslint%2Ftypes%2Fdownload%2F%40typescript-eslint%2Ftypes-3.10.1.tgz"
  "version" "3.10.1"

"@typescript-eslint/typescript-estree@3.10.1":
  "integrity" "sha1-/QBhzDit1PrUUTbWVECFafNluFM="
  "resolved" "https://registry.nlark.com/@typescript-eslint/typescript-estree/download/@typescript-eslint/typescript-estree-3.10.1.tgz"
  "version" "3.10.1"
  dependencies:
    "@typescript-eslint/types" "3.10.1"
    "@typescript-eslint/visitor-keys" "3.10.1"
    "debug" "^4.1.1"
    "glob" "^7.1.6"
    "is-glob" "^4.0.1"
    "lodash" "^4.17.15"
    "semver" "^7.3.2"
    "tsutils" "^3.17.1"

"@typescript-eslint/visitor-keys@3.10.1":
  "integrity" "sha1-zUJ0dz4+tjsuhwrGAidEh+zR6TE="
  "resolved" "https://registry.nlark.com/@typescript-eslint/visitor-keys/download/@typescript-eslint/visitor-keys-3.10.1.tgz"
  "version" "3.10.1"
  dependencies:
    "eslint-visitor-keys" "^1.1.0"

"@webassemblyjs/ast@1.9.0":
  "integrity" "sha1-vYUGBLQEJFmlpBzX0zjL7Wle2WQ="
  "resolved" "https://registry.nlark.com/@webassemblyjs/ast/download/@webassemblyjs/ast-1.9.0.tgz"
  "version" "1.9.0"
  dependencies:
    "@webassemblyjs/helper-module-context" "1.9.0"
    "@webassemblyjs/helper-wasm-bytecode" "1.9.0"
    "@webassemblyjs/wast-parser" "1.9.0"

"@webassemblyjs/floating-point-hex-parser@1.9.0":
  "integrity" "sha1-PD07Jxvd/ITesA9xNEQ4MR1S/7Q="
  "resolved" "https://registry.nlark.com/@webassemblyjs/floating-point-hex-parser/download/@webassemblyjs/floating-point-hex-parser-1.9.0.tgz"
  "version" "1.9.0"

"@webassemblyjs/helper-api-error@1.9.0":
  "integrity" "sha1-ID9nbjM7lsnaLuqzzO8zxFkotqI="
  "resolved" "https://registry.nlark.com/@webassemblyjs/helper-api-error/download/@webassemblyjs/helper-api-error-1.9.0.tgz"
  "version" "1.9.0"

"@webassemblyjs/helper-buffer@1.9.0":
  "integrity" "sha1-oUQtJpxf6yP8vJ73WdrDVH8p3gA="
  "resolved" "https://registry.npm.taobao.org/@webassemblyjs/helper-buffer/download/@webassemblyjs/helper-buffer-1.9.0.tgz?cache=0&sync_timestamp=1610045496323&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40webassemblyjs%2Fhelper-buffer%2Fdownload%2F%40webassemblyjs%2Fhelper-buffer-1.9.0.tgz"
  "version" "1.9.0"

"@webassemblyjs/helper-code-frame@1.9.0":
  "integrity" "sha1-ZH+Iks0gQ6gqwMjF51w28dkVnyc="
  "resolved" "https://registry.npm.taobao.org/@webassemblyjs/helper-code-frame/download/@webassemblyjs/helper-code-frame-1.9.0.tgz"
  "version" "1.9.0"
  dependencies:
    "@webassemblyjs/wast-printer" "1.9.0"

"@webassemblyjs/helper-fsm@1.9.0":
  "integrity" "sha1-wFJWtxJEIUZx9LCOwQitY7cO3bg="
  "resolved" "https://registry.npm.taobao.org/@webassemblyjs/helper-fsm/download/@webassemblyjs/helper-fsm-1.9.0.tgz?cache=0&sync_timestamp=1610045497114&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40webassemblyjs%2Fhelper-fsm%2Fdownload%2F%40webassemblyjs%2Fhelper-fsm-1.9.0.tgz"
  "version" "1.9.0"

"@webassemblyjs/helper-module-context@1.9.0":
  "integrity" "sha1-JdiIS3aDmHGgimxvgGw5ee9xLwc="
  "resolved" "https://registry.npm.taobao.org/@webassemblyjs/helper-module-context/download/@webassemblyjs/helper-module-context-1.9.0.tgz"
  "version" "1.9.0"
  dependencies:
    "@webassemblyjs/ast" "1.9.0"

"@webassemblyjs/helper-wasm-bytecode@1.9.0":
  "integrity" "sha1-T+2L6sm4wU+MWLcNEk1UndH+V5A="
  "resolved" "https://registry.npm.taobao.org/@webassemblyjs/helper-wasm-bytecode/download/@webassemblyjs/helper-wasm-bytecode-1.9.0.tgz"
  "version" "1.9.0"

"@webassemblyjs/helper-wasm-section@1.9.0":
  "integrity" "sha1-WkE41aYpK6GLBMWuSXF+QWeWU0Y="
  "resolved" "https://registry.npm.taobao.org/@webassemblyjs/helper-wasm-section/download/@webassemblyjs/helper-wasm-section-1.9.0.tgz?cache=0&sync_timestamp=1610045503299&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40webassemblyjs%2Fhelper-wasm-section%2Fdownload%2F%40webassemblyjs%2Fhelper-wasm-section-1.9.0.tgz"
  "version" "1.9.0"
  dependencies:
    "@webassemblyjs/ast" "1.9.0"
    "@webassemblyjs/helper-buffer" "1.9.0"
    "@webassemblyjs/helper-wasm-bytecode" "1.9.0"
    "@webassemblyjs/wasm-gen" "1.9.0"

"@webassemblyjs/ieee754@1.9.0":
  "integrity" "sha1-Fceg+6roP7JhQ7us9tbfFwKtOeQ="
  "resolved" "https://registry.nlark.com/@webassemblyjs/ieee754/download/@webassemblyjs/ieee754-1.9.0.tgz"
  "version" "1.9.0"
  dependencies:
    "@xtuc/ieee754" "^1.2.0"

"@webassemblyjs/leb128@1.9.0":
  "integrity" "sha1-8Zygt2ptxVYjoJz/p2noOPoeHJU="
  "resolved" "https://registry.nlark.com/@webassemblyjs/leb128/download/@webassemblyjs/leb128-1.9.0.tgz"
  "version" "1.9.0"
  dependencies:
    "@xtuc/long" "4.2.2"

"@webassemblyjs/utf8@1.9.0":
  "integrity" "sha1-BNM7Y2945qaBMifoJAL3Y3tiKas="
  "resolved" "https://registry.npm.taobao.org/@webassemblyjs/utf8/download/@webassemblyjs/utf8-1.9.0.tgz?cache=0&sync_timestamp=1610045498791&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40webassemblyjs%2Futf8%2Fdownload%2F%40webassemblyjs%2Futf8-1.9.0.tgz"
  "version" "1.9.0"

"@webassemblyjs/wasm-edit@1.9.0":
  "integrity" "sha1-P+bXnT8PkiGDqoYALELdJWz+6c8="
  "resolved" "https://registry.nlark.com/@webassemblyjs/wasm-edit/download/@webassemblyjs/wasm-edit-1.9.0.tgz"
  "version" "1.9.0"
  dependencies:
    "@webassemblyjs/ast" "1.9.0"
    "@webassemblyjs/helper-buffer" "1.9.0"
    "@webassemblyjs/helper-wasm-bytecode" "1.9.0"
    "@webassemblyjs/helper-wasm-section" "1.9.0"
    "@webassemblyjs/wasm-gen" "1.9.0"
    "@webassemblyjs/wasm-opt" "1.9.0"
    "@webassemblyjs/wasm-parser" "1.9.0"
    "@webassemblyjs/wast-printer" "1.9.0"

"@webassemblyjs/wasm-gen@1.9.0":
  "integrity" "sha1-ULxw7Gje2OJ2OwGhQYv0NJGnpJw="
  "resolved" "https://registry.npm.taobao.org/@webassemblyjs/wasm-gen/download/@webassemblyjs/wasm-gen-1.9.0.tgz?cache=0&sync_timestamp=1610045502219&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40webassemblyjs%2Fwasm-gen%2Fdownload%2F%40webassemblyjs%2Fwasm-gen-1.9.0.tgz"
  "version" "1.9.0"
  dependencies:
    "@webassemblyjs/ast" "1.9.0"
    "@webassemblyjs/helper-wasm-bytecode" "1.9.0"
    "@webassemblyjs/ieee754" "1.9.0"
    "@webassemblyjs/leb128" "1.9.0"
    "@webassemblyjs/utf8" "1.9.0"

"@webassemblyjs/wasm-opt@1.9.0":
  "integrity" "sha1-IhEYHlsxMmRDzIES658LkChyGmE="
  "resolved" "https://registry.nlark.com/@webassemblyjs/wasm-opt/download/@webassemblyjs/wasm-opt-1.9.0.tgz"
  "version" "1.9.0"
  dependencies:
    "@webassemblyjs/ast" "1.9.0"
    "@webassemblyjs/helper-buffer" "1.9.0"
    "@webassemblyjs/wasm-gen" "1.9.0"
    "@webassemblyjs/wasm-parser" "1.9.0"

"@webassemblyjs/wasm-parser@1.9.0":
  "integrity" "sha1-nUjkSCbfSmWYKUqmyHRp1kL/9l4="
  "resolved" "https://registry.nlark.com/@webassemblyjs/wasm-parser/download/@webassemblyjs/wasm-parser-1.9.0.tgz"
  "version" "1.9.0"
  dependencies:
    "@webassemblyjs/ast" "1.9.0"
    "@webassemblyjs/helper-api-error" "1.9.0"
    "@webassemblyjs/helper-wasm-bytecode" "1.9.0"
    "@webassemblyjs/ieee754" "1.9.0"
    "@webassemblyjs/leb128" "1.9.0"
    "@webassemblyjs/utf8" "1.9.0"

"@webassemblyjs/wast-parser@1.9.0":
  "integrity" "sha1-MDERXXmsW9JhVWzsw/qQo+9FGRQ="
  "resolved" "https://registry.npm.taobao.org/@webassemblyjs/wast-parser/download/@webassemblyjs/wast-parser-1.9.0.tgz"
  "version" "1.9.0"
  dependencies:
    "@webassemblyjs/ast" "1.9.0"
    "@webassemblyjs/floating-point-hex-parser" "1.9.0"
    "@webassemblyjs/helper-api-error" "1.9.0"
    "@webassemblyjs/helper-code-frame" "1.9.0"
    "@webassemblyjs/helper-fsm" "1.9.0"
    "@xtuc/long" "4.2.2"

"@webassemblyjs/wast-printer@1.9.0":
  "integrity" "sha1-STXVTIX+9jewDOn1I3dFHQDUeJk="
  "resolved" "https://registry.nlark.com/@webassemblyjs/wast-printer/download/@webassemblyjs/wast-printer-1.9.0.tgz"
  "version" "1.9.0"
  dependencies:
    "@webassemblyjs/ast" "1.9.0"
    "@webassemblyjs/wast-parser" "1.9.0"
    "@xtuc/long" "4.2.2"

"@xtuc/ieee754@^1.2.0":
  "integrity" "sha1-7vAUoxRa5Hehy8AM0eVSM23Ot5A="
  "resolved" "https://registry.nlark.com/@xtuc/ieee754/download/@xtuc/ieee754-1.2.0.tgz"
  "version" "1.2.0"

"@xtuc/long@4.2.2":
  "integrity" "sha1-0pHGpOl5ibXGHZrPOWrk/hM6cY0="
  "resolved" "https://registry.npm.taobao.org/@xtuc/long/download/@xtuc/long-4.2.2.tgz"
  "version" "4.2.2"

"abbrev@1":
  "integrity" "sha1-+PLIh60Qv2f2NPAFtph/7TF5qsg="
  "resolved" "https://registry.npm.taobao.org/abbrev/download/abbrev-1.1.1.tgz"
  "version" "1.1.1"

"accepts@~1.3.4", "accepts@~1.3.5", "accepts@~1.3.7":
  "integrity" "sha1-UxvHJlF6OytB+FACHGzBXqq1B80="
  "resolved" "https://registry.npm.taobao.org/accepts/download/accepts-1.3.7.tgz"
  "version" "1.3.7"
  dependencies:
    "mime-types" "~2.1.24"
    "negotiator" "0.6.2"

"acorn-jsx@^5.3.1":
  "integrity" "sha1-/IZh4Rt6wVOcR9v+oucrOvNNJns="
  "resolved" "https://registry.npm.taobao.org/acorn-jsx/download/acorn-jsx-5.3.1.tgz?cache=0&sync_timestamp=1599546317194&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Facorn-jsx%2Fdownload%2Facorn-jsx-5.3.1.tgz"
  "version" "5.3.1"

"acorn@^6.0.0 || ^7.0.0 || ^8.0.0", "acorn@^7.4.0":
  "integrity" "sha1-/q7SVZc9LndVW4PbwIhRpsY1IPo="
  "resolved" "https://registry.nlark.com/acorn/download/acorn-7.4.1.tgz"
  "version" "7.4.1"

"acorn@^6.4.1":
  "integrity" "sha1-NYZv1xBSjpLeEM8GAWSY5H454eY="
  "resolved" "https://registry.nlark.com/acorn/download/acorn-6.4.2.tgz"
  "version" "6.4.2"

"add-dom-event-listener@^1.1.0":
  "integrity" "sha1-apLbOg3Qq8JU4JXA8dwUrLuq4xA="
  "resolved" "https://registry.nlark.com/add-dom-event-listener/download/add-dom-event-listener-1.1.0.tgz"
  "version" "1.1.0"
  dependencies:
    "object-assign" "4.x"

"aggregate-error@^3.0.0":
  "integrity" "sha1-kmcP9Q9TWb23o+DUDQ7DDFc3aHo="
  "resolved" "https://registry.npm.taobao.org/aggregate-error/download/aggregate-error-3.1.0.tgz?cache=0&sync_timestamp=1618681553608&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Faggregate-error%2Fdownload%2Faggregate-error-3.1.0.tgz"
  "version" "3.1.0"
  dependencies:
    "clean-stack" "^2.0.0"
    "indent-string" "^4.0.0"

"ajv-errors@^1.0.0":
  "integrity" "sha1-81mGrOuRr63sQQL72FAUlQzvpk0="
  "resolved" "https://registry.nlark.com/ajv-errors/download/ajv-errors-1.0.1.tgz"
  "version" "1.0.1"

"ajv-keywords@^3.1.0", "ajv-keywords@^3.4.1", "ajv-keywords@^3.5.2":
  "integrity" "sha1-MfKdpatuANHC0yms97WSlhTVAU0="
  "resolved" "https://registry.nlark.com/ajv-keywords/download/ajv-keywords-3.5.2.tgz"
  "version" "3.5.2"

"ajv@^6.1.0", "ajv@^6.10.0", "ajv@^6.10.2", "ajv@^6.12.3", "ajv@^6.12.4", "ajv@^6.9.1", "ajv@>=5.0.0":
  "integrity" "sha1-uvWmLoArB9l3A0WG+MO69a3ybfQ="
  "resolved" "https://registry.nlark.com/ajv/download/ajv-6.12.6.tgz"
  "version" "6.12.6"
  dependencies:
    "fast-deep-equal" "^3.1.1"
    "fast-json-stable-stringify" "^2.0.0"
    "json-schema-traverse" "^0.4.1"
    "uri-js" "^4.2.2"

"ajv@^8.0.1":
  "integrity" "sha1-YMxF2cRqR32A2SxIB22XLDQuVyA="
  "resolved" "https://registry.nlark.com/ajv/download/ajv-8.6.0.tgz"
  "version" "8.6.0"
  dependencies:
    "fast-deep-equal" "^3.1.1"
    "json-schema-traverse" "^1.0.0"
    "require-from-string" "^2.0.2"
    "uri-js" "^4.2.2"

"alphanum-sort@^1.0.0":
  "integrity" "sha1-l6ERlkmyEa0zaR2fn0hqjsn74KM="
  "resolved" "https://registry.npm.taobao.org/alphanum-sort/download/alphanum-sort-1.0.2.tgz"
  "version" "1.0.2"

"amdefine@>=0.0.4":
  "integrity" "sha1-SlKCrBZHKek2Gbz9OtFR+BfOkfU="
  "resolved" "https://registry.npm.taobao.org/amdefine/download/amdefine-1.0.1.tgz"
  "version" "1.0.1"

"ansi-colors@^3.0.0":
  "integrity" "sha1-46PaS/uubIapwoViXeEkojQCb78="
  "resolved" "https://registry.nlark.com/ansi-colors/download/ansi-colors-3.2.4.tgz"
  "version" "3.2.4"

"ansi-colors@^4.1.1":
  "integrity" "sha1-y7muJWv3UK8eqzRPIpqif+lLo0g="
  "resolved" "https://registry.nlark.com/ansi-colors/download/ansi-colors-4.1.1.tgz"
  "version" "4.1.1"

"ansi-html@0.0.7":
  "integrity" "sha1-gTWEAhliqenm/QOflA0S9WynhZ4="
  "resolved" "https://registry.npm.taobao.org/ansi-html/download/ansi-html-0.0.7.tgz"
  "version" "0.0.7"

"ansi-regex@^2.0.0":
  "integrity" "sha1-w7M6te42DYbg5ijwRorn7yfWVN8="
  "resolved" "https://registry.npm.taobao.org/ansi-regex/download/ansi-regex-2.1.1.tgz?cache=0&sync_timestamp=1618553044693&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fansi-regex%2Fdownload%2Fansi-regex-2.1.1.tgz"
  "version" "2.1.1"

"ansi-regex@^3.0.0":
  "integrity" "sha1-7QMXwyIGT3lGbAKWa922Bas32Zg="
  "resolved" "https://registry.npm.taobao.org/ansi-regex/download/ansi-regex-3.0.0.tgz?cache=0&sync_timestamp=1618553044693&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fansi-regex%2Fdownload%2Fansi-regex-3.0.0.tgz"
  "version" "3.0.0"

"ansi-regex@^4.1.0":
  "integrity" "sha1-i5+PCM8ay4Q3Vqg5yox+MWjFGZc="
  "resolved" "https://registry.npm.taobao.org/ansi-regex/download/ansi-regex-4.1.0.tgz?cache=0&sync_timestamp=1618553044693&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fansi-regex%2Fdownload%2Fansi-regex-4.1.0.tgz"
  "version" "4.1.0"

"ansi-regex@^5.0.0":
  "integrity" "sha1-OIU59VF5vzkznIGvMKZU1p+Hy3U="
  "resolved" "https://registry.npm.taobao.org/ansi-regex/download/ansi-regex-5.0.0.tgz?cache=0&sync_timestamp=1618553044693&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fansi-regex%2Fdownload%2Fansi-regex-5.0.0.tgz"
  "version" "5.0.0"

"ansi-styles@^2.2.1":
  "integrity" "sha1-tDLdM1i2NM914eRmQ2gkBTPB3b4="
  "resolved" "https://registry.nlark.com/ansi-styles/download/ansi-styles-2.2.1.tgz?cache=0&sync_timestamp=1618995588464&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fansi-styles%2Fdownload%2Fansi-styles-2.2.1.tgz"
  "version" "2.2.1"

"ansi-styles@^3.2.0", "ansi-styles@^3.2.1":
  "integrity" "sha1-QfuyAkPlCxK+DwS43tvwdSDOhB0="
  "resolved" "https://registry.nlark.com/ansi-styles/download/ansi-styles-3.2.1.tgz?cache=0&sync_timestamp=1618995588464&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fansi-styles%2Fdownload%2Fansi-styles-3.2.1.tgz"
  "version" "3.2.1"
  dependencies:
    "color-convert" "^1.9.0"

"ansi-styles@^4.0.0", "ansi-styles@^4.1.0":
  "integrity" "sha512-zbB9rCJAT1rbjiVDb2hqKFHNYLxgtk8NURxZ3IZwD3F6NtxbXZQCnnSi1Lkx+IDohdPlFp222wVALIheZJQSEg=="
  "resolved" "https://registry.npmmirror.com/ansi-styles/-/ansi-styles-4.3.0.tgz"
  "version" "4.3.0"
  dependencies:
    "color-convert" "^2.0.1"

"antd-dayjs-webpack-plugin@0.0.8":
  "integrity" "sha1-UlthAQ30jwnSpzi+84j2lx2UpzA="
  "resolved" "https://registry.npm.taobao.org/antd-dayjs-webpack-plugin/download/antd-dayjs-webpack-plugin-0.0.8.tgz"
  "version" "0.0.8"
  dependencies:
    "dayjs" "*"

"antd@^3.26.3":
  "integrity" "sha1-BGdHVn0l44GxspjDpcW5cO1xag0="
  "resolved" "https://registry.nlark.com/antd/download/antd-3.26.3.tgz?cache=0&sync_timestamp=1624946614894&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fantd%2Fdownload%2Fantd-3.26.3.tgz"
  "version" "3.26.3"
  dependencies:
    "@ant-design/create-react-context" "^0.2.4"
    "@ant-design/icons" "~2.1.1"
    "@ant-design/icons-react" "~2.0.1"
    "@types/react-slick" "^0.23.4"
    "array-tree-filter" "^2.1.0"
    "babel-runtime" "6.x"
    "classnames" "~2.2.6"
    "copy-to-clipboard" "^3.2.0"
    "css-animation" "^1.5.0"
    "dom-closest" "^0.2.0"
    "enquire.js" "^2.1.6"
    "lodash" "^4.17.13"
    "moment" "^2.24.0"
    "omit.js" "^1.0.2"
    "prop-types" "^15.7.2"
    "raf" "^3.4.1"
    "rc-animate" "^2.10.2"
    "rc-calendar" "~9.15.7"
    "rc-cascader" "~0.17.4"
    "rc-checkbox" "~2.1.6"
    "rc-collapse" "~1.11.3"
    "rc-dialog" "~7.6.0"
    "rc-drawer" "~3.1.1"
    "rc-dropdown" "~2.4.1"
    "rc-editor-mention" "^1.1.13"
    "rc-form" "^2.4.10"
    "rc-input-number" "~4.5.0"
    "rc-mentions" "~0.4.0"
    "rc-menu" "~7.5.1"
    "rc-notification" "~3.3.1"
    "rc-pagination" "~1.20.11"
    "rc-progress" "~2.5.0"
    "rc-rate" "~2.5.0"
    "rc-resize-observer" "^0.1.0"
    "rc-select" "~9.2.0"
    "rc-slider" "~8.7.1"
    "rc-steps" "~3.5.0"
    "rc-switch" "~1.9.0"
    "rc-table" "~6.10.5"
    "rc-tabs" "~9.7.0"
    "rc-time-picker" "~3.7.1"
    "rc-tooltip" "~3.7.3"
    "rc-tree" "~2.1.0"
    "rc-tree-select" "~2.9.1"
    "rc-trigger" "^2.6.2"
    "rc-upload" "~2.9.1"
    "rc-util" "^4.16.1"
    "react-lazy-load" "^3.0.13"
    "react-lifecycles-compat" "^3.0.4"
    "react-slick" "~0.25.2"
    "resize-observer-polyfill" "^1.5.1"
    "shallowequal" "^1.1.0"
    "warning" "~4.0.3"

"anymatch@^2.0.0":
  "integrity" "sha1-vLJLTzeTTZqnrBe0ra+J58du8us="
  "resolved" "https://registry.nlark.com/anymatch/download/anymatch-2.0.0.tgz"
  "version" "2.0.0"
  dependencies:
    "micromatch" "^3.1.4"
    "normalize-path" "^2.1.1"

"anymatch@^3.0.0", "anymatch@~3.1.2":
  "integrity" "sha1-wFV8CWrzLxBhmPT04qODU343hxY="
  "resolved" "https://registry.nlark.com/anymatch/download/anymatch-3.1.2.tgz"
  "version" "3.1.2"
  dependencies:
    "normalize-path" "^3.0.0"
    "picomatch" "^2.0.4"

"aproba@^1.0.3", "aproba@^1.1.1":
  "integrity" "sha1-aALmJk79GMeQobDVF/DyYnvyyUo="
  "resolved" "https://registry.nlark.com/aproba/download/aproba-1.2.0.tgz"
  "version" "1.2.0"

"archiver-utils@^2.1.0":
  "integrity" "sha1-6KRg6UtpPD49oYKgmMpihbqSSeI="
  "resolved" "https://registry.nlark.com/archiver-utils/download/archiver-utils-2.1.0.tgz"
  "version" "2.1.0"
  dependencies:
    "glob" "^7.1.4"
    "graceful-fs" "^4.2.0"
    "lazystream" "^1.0.0"
    "lodash.defaults" "^4.2.0"
    "lodash.difference" "^4.5.0"
    "lodash.flatten" "^4.4.0"
    "lodash.isplainobject" "^4.0.6"
    "lodash.union" "^4.6.0"
    "normalize-path" "^3.0.0"
    "readable-stream" "^2.0.0"

"archiver@^5.0.0":
  "integrity" "sha1-3T4JdiRIF0HfYmJnVk992GQKRbo="
  "resolved" "https://registry.nlark.com/archiver/download/archiver-5.3.0.tgz"
  "version" "5.3.0"
  dependencies:
    "archiver-utils" "^2.1.0"
    "async" "^3.2.0"
    "buffer-crc32" "^0.2.1"
    "readable-stream" "^3.6.0"
    "readdir-glob" "^1.0.0"
    "tar-stream" "^2.2.0"
    "zip-stream" "^4.1.0"

"are-we-there-yet@~1.1.2":
  "integrity" "sha1-SzXClE8GKov82mZBB2A1D+nd/CE="
  "resolved" "https://registry.npm.taobao.org/are-we-there-yet/download/are-we-there-yet-1.1.5.tgz"
  "version" "1.1.5"
  dependencies:
    "delegates" "^1.0.0"
    "readable-stream" "^2.0.6"

"argparse@^1.0.7":
  "integrity" "sha1-vNZ5HqWuCXJeF+WtmIE0zUCz2RE="
  "resolved" "https://registry.nlark.com/argparse/download/argparse-1.0.10.tgz"
  "version" "1.0.10"
  dependencies:
    "sprintf-js" "~1.0.2"

"aria-query@^4.2.2":
  "integrity" "sha1-DSymyazrVriXfp/tau1+FbvS+Ds="
  "resolved" "https://registry.npm.taobao.org/aria-query/download/aria-query-4.2.2.tgz"
  "version" "4.2.2"
  dependencies:
    "@babel/runtime" "^7.10.2"
    "@babel/runtime-corejs3" "^7.10.2"

"arr-diff@^4.0.0":
  "integrity" "sha1-1kYQdP6/7HHn4VI1dhoyml3HxSA="
  "resolved" "https://registry.nlark.com/arr-diff/download/arr-diff-4.0.0.tgz?cache=0&sync_timestamp=1618847029174&other_urls=https%3A%2F%2Fregistry.nlark.com%2Farr-diff%2Fdownload%2Farr-diff-4.0.0.tgz"
  "version" "4.0.0"

"arr-flatten@^1.1.0":
  "integrity" "sha1-NgSLv/TntH4TZkQxbJlmnqWukfE="
  "resolved" "https://registry.nlark.com/arr-flatten/download/arr-flatten-1.1.0.tgz"
  "version" "1.1.0"

"arr-union@^3.1.0":
  "integrity" "sha1-45sJrqne+Gao8gbiiK9jkZuuOcQ="
  "resolved" "https://registry.nlark.com/arr-union/download/arr-union-3.1.0.tgz"
  "version" "3.1.0"

"array-find-index@^1.0.1":
  "integrity" "sha1-3wEKoSh+Fku9pvlyOwqWoexBh6E="
  "resolved" "https://registry.nlark.com/array-find-index/download/array-find-index-1.0.2.tgz"
  "version" "1.0.2"

"array-flatten@^2.1.0":
  "integrity" "sha1-JO+AoowaiTYX4hSbDG0NeIKTsJk="
  "resolved" "https://registry.npm.taobao.org/array-flatten/download/array-flatten-2.1.2.tgz"
  "version" "2.1.2"

"array-flatten@1.1.1":
  "integrity" "sha1-ml9pkFGx5wczKPKgCJaLZOopVdI="
  "resolved" "https://registry.npm.taobao.org/array-flatten/download/array-flatten-1.1.1.tgz"
  "version" "1.1.1"

"array-includes@^3.1.1", "array-includes@^3.1.2", "array-includes@^3.1.3":
  "integrity" "sha1-x/YZs4KtKvr1Mmzd/cCvxhr3aQo="
  "resolved" "https://registry.npm.taobao.org/array-includes/download/array-includes-3.1.3.tgz"
  "version" "3.1.3"
  dependencies:
    "call-bind" "^1.0.2"
    "define-properties" "^1.1.3"
    "es-abstract" "^1.18.0-next.2"
    "get-intrinsic" "^1.1.1"
    "is-string" "^1.0.5"

"array-tree-filter@^2.1.0":
  "integrity" "sha1-hzrAD+yDdJ8lWsjdCDgUtPYykZA="
  "resolved" "https://registry.nlark.com/array-tree-filter/download/array-tree-filter-2.1.0.tgz"
  "version" "2.1.0"

"array-union@^1.0.1":
  "integrity" "sha1-mjRBDk9OPaI96jdb5b5w8kd47Dk="
  "resolved" "https://registry.npm.taobao.org/array-union/download/array-union-1.0.2.tgz?cache=0&sync_timestamp=1614624262896&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Farray-union%2Fdownload%2Farray-union-1.0.2.tgz"
  "version" "1.0.2"
  dependencies:
    "array-uniq" "^1.0.1"

"array-uniq@^1.0.1":
  "integrity" "sha1-r2rId6Jcx/dOBYiUdThY39sk/bY="
  "resolved" "https://registry.nlark.com/array-uniq/download/array-uniq-1.0.3.tgz?cache=0&sync_timestamp=1620042045402&other_urls=https%3A%2F%2Fregistry.nlark.com%2Farray-uniq%2Fdownload%2Farray-uniq-1.0.3.tgz"
  "version" "1.0.3"

"array-unique@^0.3.2":
  "integrity" "sha1-qJS3XUvE9s1nnvMkSp/Y9Gri1Cg="
  "resolved" "https://registry.nlark.com/array-unique/download/array-unique-0.3.2.tgz"
  "version" "0.3.2"

"array.prototype.flat@^1.2.4":
  "integrity" "sha1-bvY4tDMSvUAbTGGZ/ex+LcnpoSM="
  "resolved" "https://registry.npm.taobao.org/array.prototype.flat/download/array.prototype.flat-1.2.4.tgz?cache=0&sync_timestamp=1605688971975&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Farray.prototype.flat%2Fdownload%2Farray.prototype.flat-1.2.4.tgz"
  "version" "1.2.4"
  dependencies:
    "call-bind" "^1.0.0"
    "define-properties" "^1.1.3"
    "es-abstract" "^1.18.0-next.1"

"array.prototype.flatmap@^1.2.4":
  "integrity" "sha1-lM/UfMFVbsB0fZf3x3OMWBIgBMk="
  "resolved" "https://registry.npm.taobao.org/array.prototype.flatmap/download/array.prototype.flatmap-1.2.4.tgz"
  "version" "1.2.4"
  dependencies:
    "call-bind" "^1.0.0"
    "define-properties" "^1.1.3"
    "es-abstract" "^1.18.0-next.1"
    "function-bind" "^1.1.1"

"asap@~2.0.3":
  "integrity" "sha1-5QNHYR1+aQlDIIu9r+vLwvuGbUY="
  "resolved" "https://registry.nlark.com/asap/download/asap-2.0.6.tgz"
  "version" "2.0.6"

"asn1.js@^5.2.0":
  "integrity" "sha1-EamAuE67kXgc41sP3C7ilON4Pwc="
  "resolved" "https://registry.npm.taobao.org/asn1.js/download/asn1.js-5.4.1.tgz"
  "version" "5.4.1"
  dependencies:
    "bn.js" "^4.0.0"
    "inherits" "^2.0.1"
    "minimalistic-assert" "^1.0.0"
    "safer-buffer" "^2.1.0"

"asn1@~0.2.3":
  "integrity" "sha1-jSR136tVO7M+d7VOWeiAu4ziMTY="
  "resolved" "https://registry.npm.taobao.org/asn1/download/asn1-0.2.4.tgz"
  "version" "0.2.4"
  dependencies:
    "safer-buffer" "~2.1.0"

"assert-plus@^1.0.0", "assert-plus@1.0.0":
  "integrity" "sha1-8S4PPF13sLHN2RRpQuTpbB5N1SU="
  "resolved" "https://registry.npm.taobao.org/assert-plus/download/assert-plus-1.0.0.tgz"
  "version" "1.0.0"

"assert@^1.1.1":
  "integrity" "sha1-VcEJqvbgrv2z3EtxJAxwv1dLGOs="
  "resolved" "https://registry.nlark.com/assert/download/assert-1.5.0.tgz"
  "version" "1.5.0"
  dependencies:
    "object-assign" "^4.1.1"
    "util" "0.10.3"

"assign-symbols@^1.0.0":
  "integrity" "sha1-WWZ/QfrdTyDMvCu5a41Pf3jsA2c="
  "resolved" "https://registry.npm.taobao.org/assign-symbols/download/assign-symbols-1.0.0.tgz"
  "version" "1.0.0"

"ast-types-flow@^0.0.7":
  "integrity" "sha1-9wtzXGvKGlycItmCw+Oef+ujva0="
  "resolved" "https://registry.npm.taobao.org/ast-types-flow/download/ast-types-flow-0.0.7.tgz"
  "version" "0.0.7"

"astral-regex@^2.0.0":
  "integrity" "sha1-SDFDxWeu7UeFdZwIZXhtx319LjE="
  "resolved" "https://registry.npm.taobao.org/astral-regex/download/astral-regex-2.0.0.tgz"
  "version" "2.0.0"

"async-each@^1.0.1":
  "integrity" "sha1-tyfb+H12UWAvBvTUrDh/R9kbDL8="
  "resolved" "https://registry.nlark.com/async-each/download/async-each-1.0.3.tgz"
  "version" "1.0.3"

"async-foreach@^0.1.3":
  "integrity" "sha1-NhIfhFwFeBct5Bmpfb6x0W7DRUI="
  "resolved" "https://registry.npm.taobao.org/async-foreach/download/async-foreach-0.1.3.tgz"
  "version" "0.1.3"

"async-limiter@~1.0.0":
  "integrity" "sha1-3TeelPDbgxCwgpH51kwyCXZmF/0="
  "resolved" "https://registry.nlark.com/async-limiter/download/async-limiter-1.0.1.tgz"
  "version" "1.0.1"

"async-validator@~1.11.3":
  "integrity" "sha1-nUPPSe9rt2vlRCOI0Z+5puR1l+o="
  "resolved" "https://registry.nlark.com/async-validator/download/async-validator-1.11.5.tgz"
  "version" "1.11.5"

"async@^2.5.0":
  "integrity" "sha1-1yYl4jRKNlbjo61Pp0n6gymdgv8="
  "resolved" "https://registry.npm.taobao.org/async/download/async-2.6.3.tgz"
  "version" "2.6.3"
  dependencies:
    "lodash" "^4.17.14"

"async@^2.6.2":
  "integrity" "sha1-1yYl4jRKNlbjo61Pp0n6gymdgv8="
  "resolved" "https://registry.npm.taobao.org/async/download/async-2.6.3.tgz"
  "version" "2.6.3"
  dependencies:
    "lodash" "^4.17.14"

"async@^3.2.0":
  "integrity" "sha1-s6JoXF67ZB094C0WEALGD8n4VyA="
  "resolved" "https://registry.npm.taobao.org/async/download/async-3.2.0.tgz"
  "version" "3.2.0"

"async@1.5.0":
  "integrity" "sha1-J5ZkJyNXOFlWVjP8YnRES+4vjOM="
  "resolved" "https://registry.npm.taobao.org/async/download/async-1.5.0.tgz"
  "version" "1.5.0"

"asynckit@^0.4.0":
  "integrity" "sha1-x57Zf380y48robyXkLzDZkdLS3k="
  "resolved" "https://registry.npm.taobao.org/asynckit/download/asynckit-0.4.0.tgz"
  "version" "0.4.0"

"atob@^2.1.2":
  "integrity" "sha1-bZUX654DDSQ2ZmZR6GvZ9vE1M8k="
  "resolved" "https://registry.nlark.com/atob/download/atob-2.1.2.tgz"
  "version" "2.1.2"

"aws-sign2@~0.7.0":
  "integrity" "sha1-tG6JCTSpWR8tL2+G1+ap8bP+dqg="
  "resolved" "https://registry.nlark.com/aws-sign2/download/aws-sign2-0.7.0.tgz"
  "version" "0.7.0"

"aws4@^1.8.0":
  "integrity" "sha1-1h9G2DslGSUOJ4Ta9bCUeai0HFk="
  "resolved" "https://registry.nlark.com/aws4/download/aws4-1.11.0.tgz"
  "version" "1.11.0"

"axe-core@^4.0.2":
  "integrity" "sha1-Kjr8My8AMbQvYC9KPeA8IRypj3I="
  "resolved" "https://registry.nlark.com/axe-core/download/axe-core-4.2.3.tgz?cache=0&sync_timestamp=1624905722927&other_urls=https%3A%2F%2Fregistry.nlark.com%2Faxe-core%2Fdownload%2Faxe-core-4.2.3.tgz"
  "version" "4.2.3"

"axobject-query@^2.2.0":
  "integrity" "sha1-lD1H4QwLcEqkInXiDt83ImSJib4="
  "resolved" "https://registry.npm.taobao.org/axobject-query/download/axobject-query-2.2.0.tgz"
  "version" "2.2.0"

"babel-loader@^8.0.6":
  "integrity" "sha1-k2POhMEMmkDmx1N0jhRBtgyKC4E="
  "resolved" "https://registry.nlark.com/babel-loader/download/babel-loader-8.2.2.tgz?cache=0&sync_timestamp=1618847034310&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fbabel-loader%2Fdownload%2Fbabel-loader-8.2.2.tgz"
  "version" "8.2.2"
  dependencies:
    "find-cache-dir" "^3.3.1"
    "loader-utils" "^1.4.0"
    "make-dir" "^3.1.0"
    "schema-utils" "^2.6.5"

"babel-plugin-dynamic-import-node@^2.3.3":
  "integrity" "sha1-hP2hnJduxcbe/vV/lCez3vZuF6M="
  "resolved" "https://registry.nlark.com/babel-plugin-dynamic-import-node/download/babel-plugin-dynamic-import-node-2.3.3.tgz?cache=0&sync_timestamp=1618846958717&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fbabel-plugin-dynamic-import-node%2Fdownload%2Fbabel-plugin-dynamic-import-node-2.3.3.tgz"
  "version" "2.3.3"
  dependencies:
    "object.assign" "^4.1.0"

"babel-plugin-import@^1.12.0":
  "integrity" "sha1-nbu6fRrHK9QSkXqDDUReAJQdJtc="
  "resolved" "https://registry.nlark.com/babel-plugin-import/download/babel-plugin-import-1.13.3.tgz"
  "version" "1.13.3"
  dependencies:
    "@babel/helper-module-imports" "^7.0.0"
    "@babel/runtime" "^7.0.0"

"babel-plugin-polyfill-corejs2@^0.2.2":
  "integrity" "sha1-6RJHheb9lPlLYYp5VOVpMFO/Uyc="
  "resolved" "https://registry.nlark.com/babel-plugin-polyfill-corejs2/download/babel-plugin-polyfill-corejs2-0.2.2.tgz?cache=0&sync_timestamp=1622023904181&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fbabel-plugin-polyfill-corejs2%2Fdownload%2Fbabel-plugin-polyfill-corejs2-0.2.2.tgz"
  "version" "0.2.2"
  dependencies:
    "@babel/compat-data" "^7.13.11"
    "@babel/helper-define-polyfill-provider" "^0.2.2"
    "semver" "^6.1.1"

"babel-plugin-polyfill-corejs3@^0.2.2":
  "integrity" "sha1-cq3WjPCKi/E5um5t/AsdUECY5Xs="
  "resolved" "https://registry.nlark.com/babel-plugin-polyfill-corejs3/download/babel-plugin-polyfill-corejs3-0.2.3.tgz?cache=0&sync_timestamp=1623879605583&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fbabel-plugin-polyfill-corejs3%2Fdownload%2Fbabel-plugin-polyfill-corejs3-0.2.3.tgz"
  "version" "0.2.3"
  dependencies:
    "@babel/helper-define-polyfill-provider" "^0.2.2"
    "core-js-compat" "^3.14.0"

"babel-plugin-polyfill-regenerator@^0.2.2":
  "integrity" "sha1-sxDI1kKsraNIwfo7Pmzg6FG+4Hc="
  "resolved" "https://registry.nlark.com/babel-plugin-polyfill-regenerator/download/babel-plugin-polyfill-regenerator-0.2.2.tgz?cache=0&sync_timestamp=1622023907940&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fbabel-plugin-polyfill-regenerator%2Fdownload%2Fbabel-plugin-polyfill-regenerator-0.2.2.tgz"
  "version" "0.2.2"
  dependencies:
    "@babel/helper-define-polyfill-provider" "^0.2.2"

"babel-runtime@^6.23.0", "babel-runtime@^6.26.0", "babel-runtime@6.x":
  "integrity" "sha1-llxwWGaOgrVde/4E/yM3vItWR/4="
  "resolved" "https://registry.npm.taobao.org/babel-runtime/download/babel-runtime-6.26.0.tgz"
  "version" "6.26.0"
  dependencies:
    "core-js" "^2.4.0"
    "regenerator-runtime" "^0.11.0"

"balanced-match@^1.0.0":
  "integrity" "sha1-6D46fj8wCzTLnYf2FfoMvzV2kO4="
  "resolved" "https://registry.npm.taobao.org/balanced-match/download/balanced-match-1.0.2.tgz?cache=0&sync_timestamp=1617714233441&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fbalanced-match%2Fdownload%2Fbalanced-match-1.0.2.tgz"
  "version" "1.0.2"

"base@^0.11.1":
  "integrity" "sha1-e95c7RRbbVUakNuH+DxVi060io8="
  "resolved" "https://registry.npm.taobao.org/base/download/base-0.11.2.tgz"
  "version" "0.11.2"
  dependencies:
    "cache-base" "^1.0.1"
    "class-utils" "^0.3.5"
    "component-emitter" "^1.2.1"
    "define-property" "^1.0.0"
    "isobject" "^3.0.1"
    "mixin-deep" "^1.2.0"
    "pascalcase" "^0.1.1"

"base64-js@^1.0.2", "base64-js@^1.3.1":
  "integrity" "sha1-GxtEAWClv3rUC2UPCVljSBkDkwo="
  "resolved" "https://registry.nlark.com/base64-js/download/base64-js-1.5.1.tgz"
  "version" "1.5.1"

"batch@0.6.1":
  "integrity" "sha1-3DQxT05nkxgJP8dgJyUl+UvyXBY="
  "resolved" "https://registry.nlark.com/batch/download/batch-0.6.1.tgz"
  "version" "0.6.1"

"bcrypt-pbkdf@^1.0.0":
  "integrity" "sha1-pDAdOJtqQ/m2f/PKEaP2Y342Dp4="
  "resolved" "https://registry.npm.taobao.org/bcrypt-pbkdf/download/bcrypt-pbkdf-1.0.2.tgz"
  "version" "1.0.2"
  dependencies:
    "tweetnacl" "^0.14.3"

"big-integer@^1.6.17":
  "integrity" "sha1-j9iL0WMsukocjD49cVnwi7lbS54="
  "resolved" "https://registry.npm.taobao.org/big-integer/download/big-integer-1.6.48.tgz"
  "version" "1.6.48"

"big.js@^3.1.3":
  "integrity" "sha1-pfwpi4G54Nyi5FiCR4S2XFK6WI4="
  "resolved" "https://registry.nlark.com/big.js/download/big.js-3.2.0.tgz?cache=0&sync_timestamp=1620132748267&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fbig.js%2Fdownload%2Fbig.js-3.2.0.tgz"
  "version" "3.2.0"

"big.js@^5.2.2":
  "integrity" "sha1-ZfCvOC9Xi83HQr2cKB6cstd2gyg="
  "resolved" "https://registry.nlark.com/big.js/download/big.js-5.2.2.tgz?cache=0&sync_timestamp=1620132748267&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fbig.js%2Fdownload%2Fbig.js-5.2.2.tgz"
  "version" "5.2.2"

"binary-extensions@^1.0.0":
  "integrity" "sha1-WYr+VHVbKGilMw0q/51Ou1Mgm2U="
  "resolved" "https://registry.nlark.com/binary-extensions/download/binary-extensions-1.13.1.tgz"
  "version" "1.13.1"

"binary-extensions@^2.0.0":
  "integrity" "sha1-dfUC7q+f/eQvyYgpZFvk6na9ni0="
  "resolved" "https://registry.nlark.com/binary-extensions/download/binary-extensions-2.2.0.tgz"
  "version" "2.2.0"

"binary@~0.3.0":
  "integrity" "sha1-n2BVO8XOjDOG87VTz/R0Yq3sqnk="
  "resolved" "https://registry.npm.taobao.org/binary/download/binary-0.3.0.tgz"
  "version" "0.3.0"
  dependencies:
    "buffers" "~0.1.1"
    "chainsaw" "~0.1.0"

"bl@^4.0.3":
  "integrity" "sha1-RRU1JkGCvsL7vIOmKrmM8R2fezo="
  "resolved" "https://registry.nlark.com/bl/download/bl-4.1.0.tgz"
  "version" "4.1.0"
  dependencies:
    "buffer" "^5.5.0"
    "inherits" "^2.0.4"
    "readable-stream" "^3.4.0"

"block-stream@*":
  "integrity" "sha1-E+v+d4oDIFz+A3UUgeu0szAMEmo="
  "resolved" "https://registry.npm.taobao.org/block-stream/download/block-stream-0.0.9.tgz"
  "version" "0.0.9"
  dependencies:
    "inherits" "~2.0.0"

"bluebird@^3.5.5":
  "integrity" "sha1-nyKcFb4nJFT/qXOs4NvueaGww28="
  "resolved" "https://registry.nlark.com/bluebird/download/bluebird-3.7.2.tgz"
  "version" "3.7.2"

"bluebird@~3.4.1":
  "integrity" "sha1-9y12C+Cbf3bQjtj66Ysomo0F+rM="
  "resolved" "https://registry.nlark.com/bluebird/download/bluebird-3.4.7.tgz"
  "version" "3.4.7"

"bn.js@^4.0.0":
  "integrity" "sha1-d1s/J477uXGO7HNh9IP7Nvu/6og="
  "resolved" "https://registry.npm.taobao.org/bn.js/download/bn.js-4.12.0.tgz"
  "version" "4.12.0"

"bn.js@^4.1.0":
  "integrity" "sha1-d1s/J477uXGO7HNh9IP7Nvu/6og="
  "resolved" "https://registry.npm.taobao.org/bn.js/download/bn.js-4.12.0.tgz"
  "version" "4.12.0"

"bn.js@^4.11.9":
  "integrity" "sha1-d1s/J477uXGO7HNh9IP7Nvu/6og="
  "resolved" "https://registry.npm.taobao.org/bn.js/download/bn.js-4.12.0.tgz"
  "version" "4.12.0"

"bn.js@^5.0.0", "bn.js@^5.1.1":
  "integrity" "sha1-NYhgZ0OWxpl3canQUfzBtX1K4AI="
  "resolved" "https://registry.npm.taobao.org/bn.js/download/bn.js-5.2.0.tgz"
  "version" "5.2.0"

"body-parser@1.19.0":
  "integrity" "sha1-lrJwnlfJxOCab9Zqj9l5hE9p8Io="
  "resolved" "https://registry.npm.taobao.org/body-parser/download/body-parser-1.19.0.tgz"
  "version" "1.19.0"
  dependencies:
    "bytes" "3.1.0"
    "content-type" "~1.0.4"
    "debug" "2.6.9"
    "depd" "~1.1.2"
    "http-errors" "1.7.2"
    "iconv-lite" "0.4.24"
    "on-finished" "~2.3.0"
    "qs" "6.7.0"
    "raw-body" "2.4.0"
    "type-is" "~1.6.17"

"bonjour@^3.5.0":
  "integrity" "sha1-jokKGD2O6aI5OzhExpGkK897yfU="
  "resolved" "https://registry.npm.taobao.org/bonjour/download/bonjour-3.5.0.tgz"
  "version" "3.5.0"
  dependencies:
    "array-flatten" "^2.1.0"
    "deep-equal" "^1.0.1"
    "dns-equal" "^1.0.0"
    "dns-txt" "^2.0.2"
    "multicast-dns" "^6.0.1"
    "multicast-dns-service-types" "^1.1.0"

"boolbase@^1.0.0", "boolbase@~1.0.0":
  "integrity" "sha1-aN/1++YMUes3cl6p4+0xDcwed24="
  "resolved" "https://registry.nlark.com/boolbase/download/boolbase-1.0.0.tgz"
  "version" "1.0.0"

"brace-expansion@^1.1.7":
  "integrity" "sha1-PH/L9SnYcibz0vUrlm/1Jx60Qd0="
  "resolved" "https://registry.nlark.com/brace-expansion/download/brace-expansion-1.1.11.tgz"
  "version" "1.1.11"
  dependencies:
    "balanced-match" "^1.0.0"
    "concat-map" "0.0.1"

"braces@^2.3.1", "braces@^2.3.2":
  "integrity" "sha1-WXn9PxTNUxVl5fot8av/8d+u5yk="
  "resolved" "https://registry.npm.taobao.org/braces/download/braces-2.3.2.tgz"
  "version" "2.3.2"
  dependencies:
    "arr-flatten" "^1.1.0"
    "array-unique" "^0.3.2"
    "extend-shallow" "^2.0.1"
    "fill-range" "^4.0.0"
    "isobject" "^3.0.1"
    "repeat-element" "^1.1.2"
    "snapdragon" "^0.8.1"
    "snapdragon-node" "^2.0.1"
    "split-string" "^3.0.2"
    "to-regex" "^3.0.1"

"braces@~3.0.2":
  "integrity" "sha1-NFThpGLujVmeI23zNs2epPiv4Qc="
  "resolved" "https://registry.npm.taobao.org/braces/download/braces-3.0.2.tgz"
  "version" "3.0.2"
  dependencies:
    "fill-range" "^7.0.1"

"braft-convert@^2.1.4", "braft-convert@^2.3.0":
  "integrity" "sha1-J9WQUTbDNJA9CDt6I1KnIEVieIg="
  "resolved" "https://registry.npm.taobao.org/braft-convert/download/braft-convert-2.3.0.tgz"
  "version" "2.3.0"
  dependencies:
    "draft-convert" "^2.0.0"
    "draft-js" "^0.10.3"

"braft-editor@^2.3.7":
  "integrity" "sha1-/SuOI+pxGRAWV5oe2CMdFq2PW0o="
  "resolved" "https://registry.npm.taobao.org/braft-editor/download/braft-editor-2.3.9.tgz"
  "version" "2.3.9"
  dependencies:
    "@babel/runtime" "^7.0.0"
    "braft-convert" "^2.3.0"
    "braft-finder" "^0.0.19"
    "braft-utils" "^3.0.8"
    "draft-convert" "^2.0.0"
    "draft-js" "^0.10.3"
    "draft-js-multidecorators" "^1.0.0"
    "draftjs-utils" "^0.9.4"
    "immutable" "~3.7.4"

"braft-finder@^0.0.19":
  "integrity" "sha1-wyTYJSbtNHapPehsybQH9OGIvI0="
  "resolved" "https://registry.npm.taobao.org/braft-finder/download/braft-finder-0.0.19.tgz"
  "version" "0.0.19"

"braft-utils@^3.0.8":
  "integrity" "sha1-K3Vc4dg5fZa2J7Z2f3TQfyVynYU="
  "resolved" "https://registry.npm.taobao.org/braft-utils/download/braft-utils-3.0.12.tgz"
  "version" "3.0.12"

"brorand@^1.0.1", "brorand@^1.1.0":
  "integrity" "sha1-EsJe/kCkXjwyPrhnWgoM5XsiNx8="
  "resolved" "https://registry.npm.taobao.org/brorand/download/brorand-1.1.0.tgz"
  "version" "1.1.0"

"browserify-aes@^1.0.0", "browserify-aes@^1.0.4":
  "integrity" "sha1-Mmc0ZC9APavDADIJhTu3CtQo70g="
  "resolved" "https://registry.nlark.com/browserify-aes/download/browserify-aes-1.2.0.tgz"
  "version" "1.2.0"
  dependencies:
    "buffer-xor" "^1.0.3"
    "cipher-base" "^1.0.0"
    "create-hash" "^1.1.0"
    "evp_bytestokey" "^1.0.3"
    "inherits" "^2.0.1"
    "safe-buffer" "^5.0.1"

"browserify-cipher@^1.0.0":
  "integrity" "sha1-jWR0wbhwv9q807z8wZNKEOlPFfA="
  "resolved" "https://registry.nlark.com/browserify-cipher/download/browserify-cipher-1.0.1.tgz"
  "version" "1.0.1"
  dependencies:
    "browserify-aes" "^1.0.4"
    "browserify-des" "^1.0.0"
    "evp_bytestokey" "^1.0.0"

"browserify-des@^1.0.0":
  "integrity" "sha1-OvTx9Zg5QDVy8cZiBDdfen9wPpw="
  "resolved" "https://registry.nlark.com/browserify-des/download/browserify-des-1.0.2.tgz"
  "version" "1.0.2"
  dependencies:
    "cipher-base" "^1.0.1"
    "des.js" "^1.0.0"
    "inherits" "^2.0.1"
    "safe-buffer" "^5.1.2"

"browserify-rsa@^4.0.0", "browserify-rsa@^4.0.1":
  "integrity" "sha1-sv0Gtbda4pf3zi3GUfkY9b4VjI0="
  "resolved" "https://registry.nlark.com/browserify-rsa/download/browserify-rsa-4.1.0.tgz"
  "version" "4.1.0"
  dependencies:
    "bn.js" "^5.0.0"
    "randombytes" "^2.0.1"

"browserify-sign@^4.0.0":
  "integrity" "sha1-6vSt1G3VS+O7OzbAzxWrvrp5VsM="
  "resolved" "https://registry.npm.taobao.org/browserify-sign/download/browserify-sign-4.2.1.tgz?cache=0&sync_timestamp=1596557839950&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fbrowserify-sign%2Fdownload%2Fbrowserify-sign-4.2.1.tgz"
  "version" "4.2.1"
  dependencies:
    "bn.js" "^5.1.1"
    "browserify-rsa" "^4.0.1"
    "create-hash" "^1.2.0"
    "create-hmac" "^1.1.7"
    "elliptic" "^6.5.3"
    "inherits" "^2.0.4"
    "parse-asn1" "^5.1.5"
    "readable-stream" "^3.6.0"
    "safe-buffer" "^5.2.0"

"browserify-zlib@^0.2.0":
  "integrity" "sha1-KGlFnZqjviRf6P4sofRuLn9U1z8="
  "resolved" "https://registry.npm.taobao.org/browserify-zlib/download/browserify-zlib-0.2.0.tgz"
  "version" "0.2.0"
  dependencies:
    "pako" "~1.0.5"

"browserslist@^4.0.0", "browserslist@^4.16.6":
  "integrity" "sha1-15ASd6WojlVO0wWxg+ybDAj2b6I="
  "resolved" "https://registry.nlark.com/browserslist/download/browserslist-4.16.6.tgz?cache=0&sync_timestamp=1619789072079&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fbrowserslist%2Fdownload%2Fbrowserslist-4.16.6.tgz"
  "version" "4.16.6"
  dependencies:
    "caniuse-lite" "^1.0.30001219"
    "colorette" "^1.2.2"
    "electron-to-chromium" "^1.3.723"
    "escalade" "^3.1.1"
    "node-releases" "^1.1.71"

"buffer-crc32@^0.2.1", "buffer-crc32@^0.2.13":
  "integrity" "sha1-DTM+PwDqxQqhRUq9MO+MKl2ackI="
  "resolved" "https://registry.npm.taobao.org/buffer-crc32/download/buffer-crc32-0.2.13.tgz"
  "version" "0.2.13"

"buffer-from@^1.0.0":
  "integrity" "sha1-MnE7wCj3XAL9txDXx7zsHyxgcO8="
  "resolved" "https://registry.nlark.com/buffer-from/download/buffer-from-1.1.1.tgz"
  "version" "1.1.1"

"buffer-indexof-polyfill@~1.0.0":
  "integrity" "sha1-0nMhNcWZnGSyd/z5savjSYJUcpw="
  "resolved" "https://registry.npm.taobao.org/buffer-indexof-polyfill/download/buffer-indexof-polyfill-1.0.2.tgz?cache=0&sync_timestamp=1599616618576&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fbuffer-indexof-polyfill%2Fdownload%2Fbuffer-indexof-polyfill-1.0.2.tgz"
  "version" "1.0.2"

"buffer-indexof@^1.0.0":
  "integrity" "sha1-Uvq8xqYG0aADAoAmSO9o9jnaJow="
  "resolved" "https://registry.npm.taobao.org/buffer-indexof/download/buffer-indexof-1.1.1.tgz"
  "version" "1.1.1"

"buffer-xor@^1.0.3":
  "integrity" "sha1-JuYe0UIvtw3ULm42cp7VHYVf6Nk="
  "resolved" "https://registry.nlark.com/buffer-xor/download/buffer-xor-1.0.3.tgz"
  "version" "1.0.3"

"buffer@^4.3.0":
  "integrity" "sha1-Iw6tNEACmIZEhBqwJEr4xEu+Pvg="
  "resolved" "https://registry.nlark.com/buffer/download/buffer-4.9.2.tgz?cache=0&sync_timestamp=1618846959596&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fbuffer%2Fdownload%2Fbuffer-4.9.2.tgz"
  "version" "4.9.2"
  dependencies:
    "base64-js" "^1.0.2"
    "ieee754" "^1.1.4"
    "isarray" "^1.0.0"

"buffer@^5.5.0":
  "integrity" "sha1-umLnwTEzBTWCGXFghRqPZI6Z7tA="
  "resolved" "https://registry.nlark.com/buffer/download/buffer-5.7.1.tgz?cache=0&sync_timestamp=1618846959596&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fbuffer%2Fdownload%2Fbuffer-5.7.1.tgz"
  "version" "5.7.1"
  dependencies:
    "base64-js" "^1.3.1"
    "ieee754" "^1.1.13"

"buffers@~0.1.1":
  "integrity" "sha1-skV5w77U1tOWru5tmorn9Ugqt7s="
  "resolved" "https://registry.npm.taobao.org/buffers/download/buffers-0.1.1.tgz"
  "version" "0.1.1"

"builtin-status-codes@^3.0.0":
  "integrity" "sha1-hZgoeOIbmOHGZCXgPQF0eI9Wnug="
  "resolved" "https://registry.nlark.com/builtin-status-codes/download/builtin-status-codes-3.0.0.tgz"
  "version" "3.0.0"

"bytes@3.0.0":
  "integrity" "sha1-0ygVQE1olpn4Wk6k+odV3ROpYEg="
  "resolved" "https://registry.npm.taobao.org/bytes/download/bytes-3.0.0.tgz"
  "version" "3.0.0"

"bytes@3.1.0":
  "integrity" "sha1-9s95M6Ng4FiPqf3oVlHNx/gF0fY="
  "resolved" "https://registry.npm.taobao.org/bytes/download/bytes-3.1.0.tgz"
  "version" "3.1.0"

"cacache@^12.0.2":
  "integrity" "sha1-ZovL0QWutfHZL+JVcOyVJcj6pAw="
  "resolved" "https://registry.nlark.com/cacache/download/cacache-12.0.4.tgz?cache=0&sync_timestamp=1621949584977&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fcacache%2Fdownload%2Fcacache-12.0.4.tgz"
  "version" "12.0.4"
  dependencies:
    "bluebird" "^3.5.5"
    "chownr" "^1.1.1"
    "figgy-pudding" "^3.5.1"
    "glob" "^7.1.4"
    "graceful-fs" "^4.1.15"
    "infer-owner" "^1.0.3"
    "lru-cache" "^5.1.1"
    "mississippi" "^3.0.0"
    "mkdirp" "^0.5.1"
    "move-concurrently" "^1.0.1"
    "promise-inflight" "^1.0.1"
    "rimraf" "^2.6.3"
    "ssri" "^6.0.1"
    "unique-filename" "^1.1.1"
    "y18n" "^4.0.0"

"cacache@^12.0.3":
  "integrity" "sha1-ZovL0QWutfHZL+JVcOyVJcj6pAw="
  "resolved" "https://registry.nlark.com/cacache/download/cacache-12.0.4.tgz?cache=0&sync_timestamp=1621949584977&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fcacache%2Fdownload%2Fcacache-12.0.4.tgz"
  "version" "12.0.4"
  dependencies:
    "bluebird" "^3.5.5"
    "chownr" "^1.1.1"
    "figgy-pudding" "^3.5.1"
    "glob" "^7.1.4"
    "graceful-fs" "^4.1.15"
    "infer-owner" "^1.0.3"
    "lru-cache" "^5.1.1"
    "mississippi" "^3.0.0"
    "mkdirp" "^0.5.1"
    "move-concurrently" "^1.0.1"
    "promise-inflight" "^1.0.1"
    "rimraf" "^2.6.3"
    "ssri" "^6.0.1"
    "unique-filename" "^1.1.1"
    "y18n" "^4.0.0"

"cacache@^13.0.1":
  "integrity" "sha1-qAAMIWlwiQgvhSh6GuxuOCAkpxw="
  "resolved" "https://registry.nlark.com/cacache/download/cacache-13.0.1.tgz?cache=0&sync_timestamp=1621949584977&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fcacache%2Fdownload%2Fcacache-13.0.1.tgz"
  "version" "13.0.1"
  dependencies:
    "chownr" "^1.1.2"
    "figgy-pudding" "^3.5.1"
    "fs-minipass" "^2.0.0"
    "glob" "^7.1.4"
    "graceful-fs" "^4.2.2"
    "infer-owner" "^1.0.4"
    "lru-cache" "^5.1.1"
    "minipass" "^3.0.0"
    "minipass-collect" "^1.0.2"
    "minipass-flush" "^1.0.5"
    "minipass-pipeline" "^1.2.2"
    "mkdirp" "^0.5.1"
    "move-concurrently" "^1.0.1"
    "p-map" "^3.0.0"
    "promise-inflight" "^1.0.1"
    "rimraf" "^2.7.1"
    "ssri" "^7.0.0"
    "unique-filename" "^1.1.1"

"cache-base@^1.0.1":
  "integrity" "sha1-Cn9GQWgxyLZi7jb+TnxZ129marI="
  "resolved" "https://registry.npm.taobao.org/cache-base/download/cache-base-1.0.1.tgz"
  "version" "1.0.1"
  dependencies:
    "collection-visit" "^1.0.0"
    "component-emitter" "^1.2.1"
    "get-value" "^2.0.6"
    "has-value" "^1.0.0"
    "isobject" "^3.0.1"
    "set-value" "^2.0.0"
    "to-object-path" "^0.3.0"
    "union-value" "^1.0.0"
    "unset-value" "^1.0.0"

"call-bind@^1.0.0", "call-bind@^1.0.2":
  "integrity" "sha1-sdTonmiBGcPJqQOtMKuy9qkZvjw="
  "resolved" "https://registry.npm.taobao.org/call-bind/download/call-bind-1.0.2.tgz?cache=0&sync_timestamp=1610403020286&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fcall-bind%2Fdownload%2Fcall-bind-1.0.2.tgz"
  "version" "1.0.2"
  dependencies:
    "function-bind" "^1.1.1"
    "get-intrinsic" "^1.0.2"

"caller-callsite@^2.0.0":
  "integrity" "sha1-hH4PzgoiN1CpoCfFSzNzGtMVQTQ="
  "resolved" "https://registry.nlark.com/caller-callsite/download/caller-callsite-2.0.0.tgz"
  "version" "2.0.0"
  dependencies:
    "callsites" "^2.0.0"

"caller-path@^2.0.0":
  "integrity" "sha1-Ro+DBE42mrIBD6xfBs7uFbsssfQ="
  "resolved" "https://registry.nlark.com/caller-path/download/caller-path-2.0.0.tgz"
  "version" "2.0.0"
  dependencies:
    "caller-callsite" "^2.0.0"

"callsites@^2.0.0":
  "integrity" "sha1-BuuE8A7qQT2oav/vrL/7Ngk7PFA="
  "resolved" "https://registry.nlark.com/callsites/download/callsites-2.0.0.tgz"
  "version" "2.0.0"

"callsites@^3.0.0":
  "integrity" "sha1-s2MKvYlDQy9Us/BRkjjjPNffL3M="
  "resolved" "https://registry.nlark.com/callsites/download/callsites-3.1.0.tgz"
  "version" "3.1.0"

"camel-case@3.0.x":
  "integrity" "sha1-yjw2iKTpzzpM2nd9xNy8cTJJz3M="
  "resolved" "https://registry.nlark.com/camel-case/download/camel-case-3.0.0.tgz"
  "version" "3.0.0"
  dependencies:
    "no-case" "^2.2.0"
    "upper-case" "^1.1.1"

"camelcase-keys@^2.0.0":
  "integrity" "sha1-MIvur/3ygRkFHvodkyITyRuPkuc="
  "resolved" "https://registry.nlark.com/camelcase-keys/download/camelcase-keys-2.1.0.tgz?cache=0&sync_timestamp=1624609060222&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fcamelcase-keys%2Fdownload%2Fcamelcase-keys-2.1.0.tgz"
  "version" "2.1.0"
  dependencies:
    "camelcase" "^2.0.0"
    "map-obj" "^1.0.0"

"camelcase@^2.0.0":
  "integrity" "sha1-fB0W1nmhu+WcoCys7PsBHiAfWh8="
  "resolved" "https://registry.npm.taobao.org/camelcase/download/camelcase-2.1.1.tgz"
  "version" "2.1.1"

"camelcase@^5.0.0", "camelcase@^5.3.1":
  "integrity" "sha1-48mzFWnhBoEd8kL3FXJaH0xJQyA="
  "resolved" "https://registry.npm.taobao.org/camelcase/download/camelcase-5.3.1.tgz"
  "version" "5.3.1"

"caniuse-api@^3.0.0":
  "integrity" "sha1-Xk2Q4idJYdRikZl99Znj7QCO5MA="
  "resolved" "https://registry.npm.taobao.org/caniuse-api/download/caniuse-api-3.0.0.tgz"
  "version" "3.0.0"
  dependencies:
    "browserslist" "^4.0.0"
    "caniuse-lite" "^1.0.0"
    "lodash.memoize" "^4.1.2"
    "lodash.uniq" "^4.5.0"

"caniuse-lite@^1.0.0", "caniuse-lite@^1.0.30001219":
  "integrity" "sha1-zT+uR+s9dpFpK0BlaNej5bI8dZg="
  "resolved" "https://registry.nlark.com/caniuse-lite/download/caniuse-lite-1.0.30001241.tgz"
  "version" "1.0.30001241"

"caseless@~0.12.0":
  "integrity" "sha1-G2gcIf+EAzyCZUMJBolCDRhxUdw="
  "resolved" "https://registry.nlark.com/caseless/download/caseless-0.12.0.tgz"
  "version" "0.12.0"

"chainsaw@~0.1.0":
  "integrity" "sha1-XqtQsor+WAdNDVgpE4iCi15fvJg="
  "resolved" "https://registry.npm.taobao.org/chainsaw/download/chainsaw-0.1.0.tgz"
  "version" "0.1.0"
  dependencies:
    "traverse" ">=0.3.0 <0.4"

"chalk@^1.1.1":
  "integrity" "sha1-qBFcVeSnAv5NFQq9OHKCKn4J/Jg="
  "resolved" "https://registry.nlark.com/chalk/download/chalk-1.1.3.tgz"
  "version" "1.1.3"
  dependencies:
    "ansi-styles" "^2.2.1"
    "escape-string-regexp" "^1.0.2"
    "has-ansi" "^2.0.0"
    "strip-ansi" "^3.0.0"
    "supports-color" "^2.0.0"

"chalk@^1.1.3":
  "integrity" "sha1-qBFcVeSnAv5NFQq9OHKCKn4J/Jg="
  "resolved" "https://registry.nlark.com/chalk/download/chalk-1.1.3.tgz"
  "version" "1.1.3"
  dependencies:
    "ansi-styles" "^2.2.1"
    "escape-string-regexp" "^1.0.2"
    "has-ansi" "^2.0.0"
    "strip-ansi" "^3.0.0"
    "supports-color" "^2.0.0"

"chalk@^2.0.0", "chalk@^2.4.1", "chalk@^2.4.2":
  "integrity" "sha1-zUJUFnelQzPPVBpJEIwUMrRMlCQ="
  "resolved" "https://registry.nlark.com/chalk/download/chalk-2.4.2.tgz"
  "version" "2.4.2"
  dependencies:
    "ansi-styles" "^3.2.1"
    "escape-string-regexp" "^1.0.5"
    "supports-color" "^5.3.0"

"chalk@^4.0.0":
  "integrity" "sha1-yAs/qyi/Y3HmhjMl7uZ+YYt35q0="
  "resolved" "https://registry.nlark.com/chalk/download/chalk-4.1.1.tgz"
  "version" "4.1.1"
  dependencies:
    "ansi-styles" "^4.1.0"
    "supports-color" "^7.1.0"

"chalk@^4.1.0":
  "integrity" "sha512-oKnbhFyRIXpUuez8iBMmyEa4nbj4IOQyuhc/wy9kY7/WVPcwIO9VA668Pu8RkO7+0G76SLROeyw9CpQ061i4mA=="
  "resolved" "https://registry.npmmirror.com/chalk/-/chalk-4.1.2.tgz"
  "version" "4.1.2"
  dependencies:
    "ansi-styles" "^4.1.0"
    "supports-color" "^7.1.0"

"chokidar@^2.1.8":
  "integrity" "sha1-gEs6e2qZNYw8XGHnHYco8EHP+Rc="
  "resolved" "https://registry.nlark.com/chokidar/download/chokidar-2.1.8.tgz"
  "version" "2.1.8"
  dependencies:
    "anymatch" "^2.0.0"
    "async-each" "^1.0.1"
    "braces" "^2.3.2"
    "glob-parent" "^3.1.0"
    "inherits" "^2.0.3"
    "is-binary-path" "^1.0.0"
    "is-glob" "^4.0.0"
    "normalize-path" "^3.0.0"
    "path-is-absolute" "^1.0.0"
    "readdirp" "^2.2.1"
    "upath" "^1.1.1"
  optionalDependencies:
    "fsevents" "^1.2.7"

"chokidar@^3.4.1":
  "integrity" "sha1-26OXb8rbAW9m/TZQIdkWANAcHnU="
  "resolved" "https://registry.nlark.com/chokidar/download/chokidar-3.5.2.tgz"
  "version" "3.5.2"
  dependencies:
    "anymatch" "~3.1.2"
    "braces" "~3.0.2"
    "glob-parent" "~5.1.2"
    "is-binary-path" "~2.1.0"
    "is-glob" "~4.0.1"
    "normalize-path" "~3.0.0"
    "readdirp" "~3.6.0"
  optionalDependencies:
    "fsevents" "~2.3.2"

"chownr@^1.1.1", "chownr@^1.1.2":
  "integrity" "sha1-b8nXtC0ypYNZYzdmbn0ICE2izGs="
  "resolved" "https://registry.npm.taobao.org/chownr/download/chownr-1.1.4.tgz"
  "version" "1.1.4"

"chrome-trace-event@^1.0.2":
  "integrity" "sha1-EBXs7UdB4V0GZkqVfbv1DQQeJqw="
  "resolved" "https://registry.nlark.com/chrome-trace-event/download/chrome-trace-event-1.0.3.tgz"
  "version" "1.0.3"

"cipher-base@^1.0.0", "cipher-base@^1.0.1", "cipher-base@^1.0.3":
  "integrity" "sha1-h2Dk7MJy9MNjUy+SbYdKriwTl94="
  "resolved" "https://registry.nlark.com/cipher-base/download/cipher-base-1.0.4.tgz"
  "version" "1.0.4"
  dependencies:
    "inherits" "^2.0.1"
    "safe-buffer" "^5.0.1"

"class-utils@^0.3.5":
  "integrity" "sha1-+TNprouafOAv1B+q0MqDAzGQxGM="
  "resolved" "https://registry.nlark.com/class-utils/download/class-utils-0.3.6.tgz"
  "version" "0.3.6"
  dependencies:
    "arr-union" "^3.1.0"
    "define-property" "^0.2.5"
    "isobject" "^3.0.0"
    "static-extend" "^0.1.1"

"classnames@^2.2.0", "classnames@^2.2.1", "classnames@^2.2.3", "classnames@^2.2.5", "classnames@^2.2.6", "classnames@~2.2.6", "classnames@2.x":
  "integrity" "sha1-Q5Nb/90pHzJtrQogUwmzjQD2UM4="
  "resolved" "https://registry.npm.taobao.org/classnames/download/classnames-2.2.6.tgz"
  "version" "2.2.6"

"clean-css@4.2.x":
  "integrity" "sha1-UHtd59l7SO5T2ErbAWD/YhY4D3g="
  "resolved" "https://registry.nlark.com/clean-css/download/clean-css-4.2.3.tgz"
  "version" "4.2.3"
  dependencies:
    "source-map" "~0.6.0"

"clean-stack@^2.0.0":
  "integrity" "sha1-7oRy27Ep5yezHooQpCfe6d/kAIs="
  "resolved" "https://registry.nlark.com/clean-stack/download/clean-stack-2.2.0.tgz?cache=0&sync_timestamp=1621915044030&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fclean-stack%2Fdownload%2Fclean-stack-2.2.0.tgz"
  "version" "2.2.0"

"clean-webpack-plugin@^3.0.0":
  "integrity" "sha1-qZ2Ow0wcYopFQVZ6p7RXRGRgxis="
  "resolved" "https://registry.npm.taobao.org/clean-webpack-plugin/download/clean-webpack-plugin-3.0.0.tgz"
  "version" "3.0.0"
  dependencies:
    "@types/webpack" "^4.4.31"
    "del" "^4.1.1"

"clipboard@^2.0.6":
  "integrity" "sha1-/8bBA90pZ6gwBfP2GXaqRlWkzbo="
  "resolved" "https://registry.npm.taobao.org/clipboard/download/clipboard-2.0.8.tgz?cache=0&sync_timestamp=1615410040723&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fclipboard%2Fdownload%2Fclipboard-2.0.8.tgz"
  "version" "2.0.8"
  dependencies:
    "good-listener" "^1.2.2"
    "select" "^1.1.2"
    "tiny-emitter" "^2.0.0"

"cliui@^5.0.0":
  "integrity" "sha1-3u/P2y6AB4SqNPRvoI4GhRx7u8U="
  "resolved" "https://registry.nlark.com/cliui/download/cliui-5.0.0.tgz"
  "version" "5.0.0"
  dependencies:
    "string-width" "^3.1.0"
    "strip-ansi" "^5.2.0"
    "wrap-ansi" "^5.1.0"

"cliui@^7.0.2":
  "integrity" "sha512-OcRE68cOsVMXp1Yvonl/fzkQOyjLSu/8bhPDfQt0e0/Eb283TKP20Fs2MqoPsr9SwA595rRCA+QMzYc9nBP+JQ=="
  "resolved" "https://registry.npmmirror.com/cliui/-/cliui-7.0.4.tgz"
  "version" "7.0.4"
  dependencies:
    "string-width" "^4.2.0"
    "strip-ansi" "^6.0.0"
    "wrap-ansi" "^7.0.0"

"clone-deep@^4.0.1":
  "integrity" "sha1-wZ/Zvbv4WUK0/ZechNz31fB8I4c="
  "resolved" "https://registry.nlark.com/clone-deep/download/clone-deep-4.0.1.tgz"
  "version" "4.0.1"
  dependencies:
    "is-plain-object" "^2.0.4"
    "kind-of" "^6.0.2"
    "shallow-clone" "^3.0.0"

"clone@^2.1.1":
  "integrity" "sha1-G39Ln1kfHo+DZwQBYANFoCiHQ18="
  "resolved" "https://registry.nlark.com/clone/download/clone-2.1.2.tgz"
  "version" "2.1.2"

"coa@^2.0.2":
  "integrity" "sha1-Q/bCEVG07yv1cYfbDXPeIp4+fsM="
  "resolved" "https://registry.nlark.com/coa/download/coa-2.0.2.tgz"
  "version" "2.0.2"
  dependencies:
    "@types/q" "^1.5.1"
    "chalk" "^2.4.1"
    "q" "^1.1.2"

"code-point-at@^1.0.0":
  "integrity" "sha1-DQcLTQQ6W+ozovGkDi7bPZpMz3c="
  "resolved" "https://registry.nlark.com/code-point-at/download/code-point-at-1.1.0.tgz"
  "version" "1.1.0"

"collection-visit@^1.0.0":
  "integrity" "sha1-S8A3PBZLwykbTTaMgpzxqApZ3KA="
  "resolved" "https://registry.npm.taobao.org/collection-visit/download/collection-visit-1.0.0.tgz"
  "version" "1.0.0"
  dependencies:
    "map-visit" "^1.0.0"
    "object-visit" "^1.0.0"

"color-convert@^1.9.0", "color-convert@^1.9.1":
  "integrity" "sha1-u3GFBpDh8TZWfeYp0tVHHe2kweg="
  "resolved" "https://registry.nlark.com/color-convert/download/color-convert-1.9.3.tgz"
  "version" "1.9.3"
  dependencies:
    "color-name" "1.1.3"

"color-convert@^2.0.1":
  "integrity" "sha512-RRECPsj7iu/xb5oKYcsFHSppFNnsj/52OVTRKb4zP5onXwVF3zVmmToNcOfGC+CRDpfK/U584fMg38ZHCaElKQ=="
  "resolved" "https://registry.npmmirror.com/color-convert/-/color-convert-2.0.1.tgz"
  "version" "2.0.1"
  dependencies:
    "color-name" "~1.1.4"

"color-name@^1.0.0", "color-name@1.1.3":
  "integrity" "sha1-p9BVi9icQveV3UIyj3QIMcpTvCU="
  "resolved" "https://registry.npm.taobao.org/color-name/download/color-name-1.1.3.tgz"
  "version" "1.1.3"

"color-name@~1.1.4":
  "integrity" "sha512-dOy+3AuW3a2wNbZHIuMZpTcgjGuLU/uBL/ubcZF9OXbDo8ff4O8yVp5Bf0efS8uEoYo5q4Fx7dY9OgQGXgAsQA=="
  "resolved" "https://registry.npmmirror.com/color-name/-/color-name-1.1.4.tgz"
  "version" "1.1.4"

"color-string@^1.5.4":
  "integrity" "sha1-ZUdKjw50OWJfPSemoZ2J/EUiMBQ="
  "resolved" "https://registry.nlark.com/color-string/download/color-string-1.5.5.tgz"
  "version" "1.5.5"
  dependencies:
    "color-name" "^1.0.0"
    "simple-swizzle" "^0.2.2"

"color@^3.0.0":
  "integrity" "sha1-ymf7TnuX1hHc3jns7tQiBn2RWW4="
  "resolved" "https://registry.npm.taobao.org/color/download/color-3.1.3.tgz?cache=0&sync_timestamp=1602228725017&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fcolor%2Fdownload%2Fcolor-3.1.3.tgz"
  "version" "3.1.3"
  dependencies:
    "color-convert" "^1.9.1"
    "color-string" "^1.5.4"

"colorette@^1.2.2":
  "integrity" "sha1-y8x51emcrqLb8Q6zom/Ys+as+pQ="
  "resolved" "https://registry.nlark.com/colorette/download/colorette-1.2.2.tgz?cache=0&sync_timestamp=1618846981554&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fcolorette%2Fdownload%2Fcolorette-1.2.2.tgz"
  "version" "1.2.2"

"combined-stream@^1.0.6", "combined-stream@~1.0.6":
  "integrity" "sha1-w9RaizT9cwYxoRCoolIGgrMdWn8="
  "resolved" "https://registry.nlark.com/combined-stream/download/combined-stream-1.0.8.tgz"
  "version" "1.0.8"
  dependencies:
    "delayed-stream" "~1.0.0"

"commander@^2.20.0":
  "integrity" "sha1-/UhehMA+tIgcIHIrpIA16FMa6zM="
  "resolved" "https://registry.nlark.com/commander/download/commander-2.20.3.tgz?cache=0&sync_timestamp=1624609539421&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fcommander%2Fdownload%2Fcommander-2.20.3.tgz"
  "version" "2.20.3"

"commander@~2.19.0":
  "integrity" "sha1-9hmKqE5bg8RgVLlN3tv+1e6f8So="
  "resolved" "https://registry.nlark.com/commander/download/commander-2.19.0.tgz?cache=0&sync_timestamp=1624609539421&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fcommander%2Fdownload%2Fcommander-2.19.0.tgz"
  "version" "2.19.0"

"commander@2.17.x":
  "integrity" "sha1-vXerfebelCBc6sxy8XFtKfIKd78="
  "resolved" "https://registry.nlark.com/commander/download/commander-2.17.1.tgz?cache=0&sync_timestamp=1624609539421&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fcommander%2Fdownload%2Fcommander-2.17.1.tgz"
  "version" "2.17.1"

"commondir@^1.0.1":
  "integrity" "sha1-3dgA2gxmEnOTzKWVDqloo6rxJTs="
  "resolved" "https://registry.nlark.com/commondir/download/commondir-1.0.1.tgz"
  "version" "1.0.1"

"component-classes@^1.2.5", "component-classes@^1.2.6", "component-classes@1.x":
  "integrity" "sha1-xkI5TDYYpNiwuJGe/Mu9kw5c1pE="
  "resolved" "https://registry.npm.taobao.org/component-classes/download/component-classes-1.2.6.tgz"
  "version" "1.2.6"
  dependencies:
    "component-indexof" "0.0.3"

"component-emitter@^1.2.1":
  "integrity" "sha1-FuQHD7qK4ptnnyIVhT7hgasuq8A="
  "resolved" "https://registry.npm.taobao.org/component-emitter/download/component-emitter-1.3.0.tgz"
  "version" "1.3.0"

"component-indexof@0.0.3":
  "integrity" "sha1-EdCRMSI5648yyPJa6csAL/6NPCQ="
  "resolved" "https://registry.npm.taobao.org/component-indexof/download/component-indexof-0.0.3.tgz"
  "version" "0.0.3"

"compress-commons@^4.1.0":
  "integrity" "sha1-3yoJp+0XRHZCutEKhcyaGeXEKn0="
  "resolved" "https://registry.nlark.com/compress-commons/download/compress-commons-4.1.1.tgz?cache=0&sync_timestamp=1622399262423&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fcompress-commons%2Fdownload%2Fcompress-commons-4.1.1.tgz"
  "version" "4.1.1"
  dependencies:
    "buffer-crc32" "^0.2.13"
    "crc32-stream" "^4.0.2"
    "normalize-path" "^3.0.0"
    "readable-stream" "^3.6.0"

"compressible@~2.0.16":
  "integrity" "sha1-r1PMprBw1MPAdQ+9dyhqbXzEb7o="
  "resolved" "https://registry.npm.taobao.org/compressible/download/compressible-2.0.18.tgz"
  "version" "2.0.18"
  dependencies:
    "mime-db" ">= 1.43.0 < 2"

"compression-webpack-plugin@^3.0.0":
  "integrity" "sha1-n1EBcqe1+uWq07ZwZS6L15l67so="
  "resolved" "https://registry.nlark.com/compression-webpack-plugin/download/compression-webpack-plugin-3.1.0.tgz?cache=0&sync_timestamp=1624626650884&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fcompression-webpack-plugin%2Fdownload%2Fcompression-webpack-plugin-3.1.0.tgz"
  "version" "3.1.0"
  dependencies:
    "cacache" "^13.0.1"
    "find-cache-dir" "^3.0.0"
    "neo-async" "^2.5.0"
    "schema-utils" "^2.6.1"
    "serialize-javascript" "^2.1.2"
    "webpack-sources" "^1.0.1"

"compression@^1.7.4":
  "integrity" "sha1-lVI+/xcMpXwpoMpB5v4TH0Hlu48="
  "resolved" "https://registry.nlark.com/compression/download/compression-1.7.4.tgz"
  "version" "1.7.4"
  dependencies:
    "accepts" "~1.3.5"
    "bytes" "3.0.0"
    "compressible" "~2.0.16"
    "debug" "2.6.9"
    "on-headers" "~1.0.2"
    "safe-buffer" "5.1.2"
    "vary" "~1.1.2"

"concat-map@0.0.1":
  "integrity" "sha1-2Klr13/Wjfd5OnMDajug1UBdR3s="
  "resolved" "https://registry.npm.taobao.org/concat-map/download/concat-map-0.0.1.tgz"
  "version" "0.0.1"

"concat-stream@^1.5.0":
  "integrity" "sha1-kEvfGUzTEi/Gdcd/xKw9T/D9GjQ="
  "resolved" "https://registry.npm.taobao.org/concat-stream/download/concat-stream-1.6.2.tgz"
  "version" "1.6.2"
  dependencies:
    "buffer-from" "^1.0.0"
    "inherits" "^2.0.3"
    "readable-stream" "^2.2.2"
    "typedarray" "^0.0.6"

"concurrently@^7.0.0":
  "integrity" "sha512-WKM7PUsI8wyXpF80H+zjHP32fsgsHNQfPLw/e70Z5dYkV7hF+rf8q3D+ScWJIEr57CpkO3OWBko6hwhQLPR8Pw=="
  "resolved" "https://registry.npmmirror.com/concurrently/-/concurrently-7.0.0.tgz"
  "version" "7.0.0"
  dependencies:
    "chalk" "^4.1.0"
    "date-fns" "^2.16.1"
    "lodash" "^4.17.21"
    "rxjs" "^6.6.3"
    "spawn-command" "^0.0.2-1"
    "supports-color" "^8.1.0"
    "tree-kill" "^1.2.2"
    "yargs" "^16.2.0"

"confusing-browser-globals@^1.0.10":
  "integrity" "sha1-MNHn89G4grJexJM9HRraw1PSClk="
  "resolved" "https://registry.nlark.com/confusing-browser-globals/download/confusing-browser-globals-1.0.10.tgz"
  "version" "1.0.10"

"connect-history-api-fallback@^1.6.0":
  "integrity" "sha1-izIIk1kwjRERFdgcrT/Oq4iPl7w="
  "resolved" "https://registry.npm.taobao.org/connect-history-api-fallback/download/connect-history-api-fallback-1.6.0.tgz"
  "version" "1.6.0"

"console-browserify@^1.1.0":
  "integrity" "sha1-ZwY871fOts9Jk6KrOlWECujEkzY="
  "resolved" "https://registry.npm.taobao.org/console-browserify/download/console-browserify-1.2.0.tgz"
  "version" "1.2.0"

"console-control-strings@^1.0.0", "console-control-strings@~1.1.0":
  "integrity" "sha1-PXz0Rk22RG6mRL9LOVB/mFEAjo4="
  "resolved" "https://registry.nlark.com/console-control-strings/download/console-control-strings-1.1.0.tgz"
  "version" "1.1.0"

"constants-browserify@^1.0.0":
  "integrity" "sha1-wguW2MYXdIqvHBYCF2DNJ/y4y3U="
  "resolved" "https://registry.nlark.com/constants-browserify/download/constants-browserify-1.0.0.tgz"
  "version" "1.0.0"

"content-disposition@0.5.3":
  "integrity" "sha1-4TDK9+cnkIfFYWwgB9BIVpiYT70="
  "resolved" "https://registry.nlark.com/content-disposition/download/content-disposition-0.5.3.tgz"
  "version" "0.5.3"
  dependencies:
    "safe-buffer" "5.1.2"

"content-type@~1.0.4":
  "integrity" "sha1-4TjMdeBAxyexlm/l5fjJruJW/js="
  "resolved" "https://registry.nlark.com/content-type/download/content-type-1.0.4.tgz"
  "version" "1.0.4"

"convert-source-map@^1.7.0":
  "integrity" "sha1-8zc8MtIbTXgN2ABFFGhPt5HKQ2k="
  "resolved" "https://registry.nlark.com/convert-source-map/download/convert-source-map-1.8.0.tgz?cache=0&sync_timestamp=1624045420970&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fconvert-source-map%2Fdownload%2Fconvert-source-map-1.8.0.tgz"
  "version" "1.8.0"
  dependencies:
    "safe-buffer" "~5.1.1"

"cookie-signature@1.0.6":
  "integrity" "sha1-4wOogrNCzD7oylE6eZmXNNqzriw="
  "resolved" "https://registry.nlark.com/cookie-signature/download/cookie-signature-1.0.6.tgz"
  "version" "1.0.6"

"cookie@0.4.0":
  "integrity" "sha1-vrQ35wIrO21JAZ0IhmUwPr6cFLo="
  "resolved" "https://registry.npm.taobao.org/cookie/download/cookie-0.4.0.tgz"
  "version" "0.4.0"

"copy-anything@^2.0.1":
  "integrity" "sha1-hCQHugJGaw34RIGbvjuuu+XUXYc="
  "resolved" "https://registry.nlark.com/copy-anything/download/copy-anything-2.0.3.tgz"
  "version" "2.0.3"
  dependencies:
    "is-what" "^3.12.0"

"copy-concurrently@^1.0.0":
  "integrity" "sha1-kilzmMrjSTf8r9bsgTnBgFHwteA="
  "resolved" "https://registry.npm.taobao.org/copy-concurrently/download/copy-concurrently-1.0.5.tgz"
  "version" "1.0.5"
  dependencies:
    "aproba" "^1.1.1"
    "fs-write-stream-atomic" "^1.0.8"
    "iferr" "^0.1.5"
    "mkdirp" "^0.5.1"
    "rimraf" "^2.5.4"
    "run-queue" "^1.0.0"

"copy-descriptor@^0.1.0":
  "integrity" "sha1-Z29us8OZl8LuGsOpJP1hJHSPV40="
  "resolved" "https://registry.npm.taobao.org/copy-descriptor/download/copy-descriptor-0.1.1.tgz"
  "version" "0.1.1"

"copy-to-clipboard@^3.2.0":
  "integrity" "sha1-EVqhqZmP+rYZb5MHatbaO5E2Yq4="
  "resolved" "https://registry.npm.taobao.org/copy-to-clipboard/download/copy-to-clipboard-3.3.1.tgz"
  "version" "3.3.1"
  dependencies:
    "toggle-selection" "^1.0.6"

"copy-webpack-plugin@^5.0.4":
  "integrity" "sha1-ioieHcr6bJHGzUvhrRWPHTgjuuI="
  "resolved" "https://registry.nlark.com/copy-webpack-plugin/download/copy-webpack-plugin-5.1.2.tgz?cache=0&sync_timestamp=1624628458516&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fcopy-webpack-plugin%2Fdownload%2Fcopy-webpack-plugin-5.1.2.tgz"
  "version" "5.1.2"
  dependencies:
    "cacache" "^12.0.3"
    "find-cache-dir" "^2.1.0"
    "glob-parent" "^3.1.0"
    "globby" "^7.1.1"
    "is-glob" "^4.0.1"
    "loader-utils" "^1.2.3"
    "minimatch" "^3.0.4"
    "normalize-path" "^3.0.0"
    "p-limit" "^2.2.1"
    "schema-utils" "^1.0.0"
    "serialize-javascript" "^4.0.0"
    "webpack-log" "^2.0.0"

"core-js-compat@^3.14.0", "core-js-compat@^3.15.0":
  "integrity" "sha1-Gv4jNxbTfuAhlW7wl1lAcbK1hac="
  "resolved" "https://registry.nlark.com/core-js-compat/download/core-js-compat-3.15.1.tgz"
  "version" "3.15.1"
  dependencies:
    "browserslist" "^4.16.6"
    "semver" "7.0.0"

"core-js-pure@^3.15.0":
  "integrity" "sha1-g1b2V2+iqo5Uym/gJiCWj/AQ7tc="
  "resolved" "https://registry.nlark.com/core-js-pure/download/core-js-pure-3.15.1.tgz"
  "version" "3.15.1"

"core-js@^1.0.0":
  "integrity" "sha1-ZSKUwUZR2yj6k70tX/KYOk8IxjY="
  "resolved" "https://registry.nlark.com/core-js/download/core-js-1.2.7.tgz"
  "version" "1.2.7"

"core-js@^2.4.0":
  "integrity" "sha1-2TM9+nsGXjR8xWgiGdb2kIWcwuw="
  "resolved" "https://registry.nlark.com/core-js/download/core-js-2.6.12.tgz?cache=0&sync_timestamp=1624966012065&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fcore-js%2Fdownload%2Fcore-js-2.6.12.tgz"
  "version" "2.6.12"

"core-js@^3.2.0":
  "integrity" "sha1-bAiriKvfVlRQRcz1/YH0f0B+fxo="
  "resolved" "https://registry.nlark.com/core-js/download/core-js-3.15.1.tgz"
  "version" "3.15.1"

"core-util-is@~1.0.0", "core-util-is@1.0.2":
  "integrity" "sha1-tf1UIgqivFq1eqtxQMlAdUUDwac="
  "resolved" "https://registry.npm.taobao.org/core-util-is/download/core-util-is-1.0.2.tgz"
  "version" "1.0.2"

"cosmiconfig@^5.0.0":
  "integrity" "sha1-BA9yaAnFked6F8CjYmykW08Wixo="
  "resolved" "https://registry.npm.taobao.org/cosmiconfig/download/cosmiconfig-5.2.1.tgz?cache=0&sync_timestamp=1596310773001&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fcosmiconfig%2Fdownload%2Fcosmiconfig-5.2.1.tgz"
  "version" "5.2.1"
  dependencies:
    "import-fresh" "^2.0.0"
    "is-directory" "^0.3.1"
    "js-yaml" "^3.13.1"
    "parse-json" "^4.0.0"

"crc-32@^1.2.0":
  "integrity" "sha1-yy224puIUI4y2d0OwWk+e0Ghggg="
  "resolved" "https://registry.npm.taobao.org/crc-32/download/crc-32-1.2.0.tgz"
  "version" "1.2.0"
  dependencies:
    "exit-on-epipe" "~1.0.1"
    "printj" "~1.1.0"

"crc32-stream@^4.0.2":
  "integrity" "sha1-ySKtIrODlavp04cPAvqBNO1wkAc="
  "resolved" "https://registry.npm.taobao.org/crc32-stream/download/crc32-stream-4.0.2.tgz"
  "version" "4.0.2"
  dependencies:
    "crc-32" "^1.2.0"
    "readable-stream" "^3.4.0"

"create-ecdh@^4.0.0":
  "integrity" "sha1-1uf0v/pmc2CFoHYv06YyaE2rzE4="
  "resolved" "https://registry.npm.taobao.org/create-ecdh/download/create-ecdh-4.0.4.tgz?cache=0&sync_timestamp=1596557450797&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fcreate-ecdh%2Fdownload%2Fcreate-ecdh-4.0.4.tgz"
  "version" "4.0.4"
  dependencies:
    "bn.js" "^4.1.0"
    "elliptic" "^6.5.3"

"create-hash@^1.1.0", "create-hash@^1.1.2", "create-hash@^1.2.0":
  "integrity" "sha1-iJB4rxGmN1a8+1m9IhmWvjqe8ZY="
  "resolved" "https://registry.nlark.com/create-hash/download/create-hash-1.2.0.tgz"
  "version" "1.2.0"
  dependencies:
    "cipher-base" "^1.0.1"
    "inherits" "^2.0.1"
    "md5.js" "^1.3.4"
    "ripemd160" "^2.0.1"
    "sha.js" "^2.4.0"

"create-hmac@^1.1.0", "create-hmac@^1.1.4", "create-hmac@^1.1.7":
  "integrity" "sha1-aRcMeLOrlXFHsriwRXLkfq0iQ/8="
  "resolved" "https://registry.npm.taobao.org/create-hmac/download/create-hmac-1.1.7.tgz"
  "version" "1.1.7"
  dependencies:
    "cipher-base" "^1.0.3"
    "create-hash" "^1.1.0"
    "inherits" "^2.0.1"
    "ripemd160" "^2.0.0"
    "safe-buffer" "^5.0.1"
    "sha.js" "^2.4.8"

"create-react-class@^15.5.3":
  "integrity" "sha1-dJnXyi5pu1HRP69ZvQTwxlodbB4="
  "resolved" "https://registry.npm.taobao.org/create-react-class/download/create-react-class-15.7.0.tgz?cache=0&sync_timestamp=1602801795820&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fcreate-react-class%2Fdownload%2Fcreate-react-class-15.7.0.tgz"
  "version" "15.7.0"
  dependencies:
    "loose-envify" "^1.3.1"
    "object-assign" "^4.1.1"

"cropperjs@*", "cropperjs@^1.5.5":
  "integrity" "sha1-2cDbK/uMDXadUXOej5FrvEThD1A="
  "resolved" "https://registry.nlark.com/cropperjs/download/cropperjs-1.5.12.tgz?cache=0&sync_timestamp=1623485718941&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fcropperjs%2Fdownload%2Fcropperjs-1.5.12.tgz"
  "version" "1.5.12"

"cross-env@^5.2.1":
  "integrity" "sha1-ssdsHKet1m3IdNEXmEZglPVRs00="
  "resolved" "https://registry.nlark.com/cross-env/download/cross-env-5.2.1.tgz"
  "version" "5.2.1"
  dependencies:
    "cross-spawn" "^6.0.5"

"cross-spawn@^3.0.0":
  "integrity" "sha1-ElYDfsufDF9549bvE14wdwGEuYI="
  "resolved" "https://registry.npm.taobao.org/cross-spawn/download/cross-spawn-3.0.1.tgz?cache=0&sync_timestamp=1606748073153&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fcross-spawn%2Fdownload%2Fcross-spawn-3.0.1.tgz"
  "version" "3.0.1"
  dependencies:
    "lru-cache" "^4.0.1"
    "which" "^1.2.9"

"cross-spawn@^6.0.0", "cross-spawn@^6.0.5":
  "integrity" "sha1-Sl7Hxk364iw6FBJNus3uhG2Ay8Q="
  "resolved" "https://registry.npm.taobao.org/cross-spawn/download/cross-spawn-6.0.5.tgz?cache=0&sync_timestamp=1606748073153&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fcross-spawn%2Fdownload%2Fcross-spawn-6.0.5.tgz"
  "version" "6.0.5"
  dependencies:
    "nice-try" "^1.0.4"
    "path-key" "^2.0.1"
    "semver" "^5.5.0"
    "shebang-command" "^1.2.0"
    "which" "^1.2.9"

"cross-spawn@^7.0.2":
  "integrity" "sha1-9zqFudXUHQRVUcF34ogtSshXKKY="
  "resolved" "https://registry.npm.taobao.org/cross-spawn/download/cross-spawn-7.0.3.tgz?cache=0&sync_timestamp=1606748073153&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fcross-spawn%2Fdownload%2Fcross-spawn-7.0.3.tgz"
  "version" "7.0.3"
  dependencies:
    "path-key" "^3.1.0"
    "shebang-command" "^2.0.0"
    "which" "^2.0.1"

"crypto-browserify@^3.11.0":
  "integrity" "sha1-OWz58xN/A+S45TLFj2mCVOAPgOw="
  "resolved" "https://registry.npm.taobao.org/crypto-browserify/download/crypto-browserify-3.12.0.tgz"
  "version" "3.12.0"
  dependencies:
    "browserify-cipher" "^1.0.0"
    "browserify-sign" "^4.0.0"
    "create-ecdh" "^4.0.0"
    "create-hash" "^1.1.0"
    "create-hmac" "^1.1.0"
    "diffie-hellman" "^5.0.0"
    "inherits" "^2.0.1"
    "pbkdf2" "^3.0.3"
    "public-encrypt" "^4.0.0"
    "randombytes" "^2.0.0"
    "randomfill" "^1.0.3"

"css-animation@^1.3.2", "css-animation@^1.5.0", "css-animation@1.x":
  "integrity" "sha1-FiBko7DVH5WLf/N7PW1N4Y4XA54="
  "resolved" "https://registry.nlark.com/css-animation/download/css-animation-1.6.1.tgz"
  "version" "1.6.1"
  dependencies:
    "babel-runtime" "6.x"
    "component-classes" "^1.2.5"

"css-color-names@^0.0.4", "css-color-names@0.0.4":
  "integrity" "sha1-gIrcLnnPhHOAabZGyyDsJ762KeA="
  "resolved" "https://registry.nlark.com/css-color-names/download/css-color-names-0.0.4.tgz?cache=0&sync_timestamp=1618846942982&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fcss-color-names%2Fdownload%2Fcss-color-names-0.0.4.tgz"
  "version" "0.0.4"

"css-declaration-sorter@^4.0.1":
  "integrity" "sha1-wZiUD2OnbX42wecQGLABchBUyyI="
  "resolved" "https://registry.nlark.com/css-declaration-sorter/download/css-declaration-sorter-4.0.1.tgz?cache=0&sync_timestamp=1620754698059&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fcss-declaration-sorter%2Fdownload%2Fcss-declaration-sorter-4.0.1.tgz"
  "version" "4.0.1"
  dependencies:
    "postcss" "^7.0.1"
    "timsort" "^0.3.0"

"css-loader@^3.2.0":
  "integrity" "sha1-Lkssfm4tJ/jI8o9hv/zS5ske9kU="
  "resolved" "https://registry.nlark.com/css-loader/download/css-loader-3.6.0.tgz"
  "version" "3.6.0"
  dependencies:
    "camelcase" "^5.3.1"
    "cssesc" "^3.0.0"
    "icss-utils" "^4.1.1"
    "loader-utils" "^1.2.3"
    "normalize-path" "^3.0.0"
    "postcss" "^7.0.32"
    "postcss-modules-extract-imports" "^2.0.0"
    "postcss-modules-local-by-default" "^3.0.2"
    "postcss-modules-scope" "^2.2.0"
    "postcss-modules-values" "^3.0.0"
    "postcss-value-parser" "^4.1.0"
    "schema-utils" "^2.7.0"
    "semver" "^6.3.0"

"css-select-base-adapter@^0.1.1":
  "integrity" "sha1-Oy/0lyzDYquIVhUHqVQIoUMhNdc="
  "resolved" "https://registry.npm.taobao.org/css-select-base-adapter/download/css-select-base-adapter-0.1.1.tgz"
  "version" "0.1.1"

"css-select@^2.0.0":
  "integrity" "sha1-ajRlM1ZjWTSoG6ymjQJVQyEF2+8="
  "resolved" "https://registry.nlark.com/css-select/download/css-select-2.1.0.tgz?cache=0&sync_timestamp=1622994276976&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fcss-select%2Fdownload%2Fcss-select-2.1.0.tgz"
  "version" "2.1.0"
  dependencies:
    "boolbase" "^1.0.0"
    "css-what" "^3.2.1"
    "domutils" "^1.7.0"
    "nth-check" "^1.0.2"

"css-select@^4.1.3":
  "integrity" "sha1-pwRA9wMX8maRGK10/xBeZYSccGc="
  "resolved" "https://registry.nlark.com/css-select/download/css-select-4.1.3.tgz?cache=0&sync_timestamp=1622994276976&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fcss-select%2Fdownload%2Fcss-select-4.1.3.tgz"
  "version" "4.1.3"
  dependencies:
    "boolbase" "^1.0.0"
    "css-what" "^5.0.0"
    "domhandler" "^4.2.0"
    "domutils" "^2.6.0"
    "nth-check" "^2.0.0"

"css-tree@^1.1.2":
  "integrity" "sha1-60hw+2/XcHMn7JXC/yqwm16NuR0="
  "resolved" "https://registry.nlark.com/css-tree/download/css-tree-1.1.3.tgz"
  "version" "1.1.3"
  dependencies:
    "mdn-data" "2.0.14"
    "source-map" "^0.6.1"

"css-tree@1.0.0-alpha.37":
  "integrity" "sha1-mL69YsTB2flg7DQM+fdSLjBwmiI="
  "resolved" "https://registry.nlark.com/css-tree/download/css-tree-1.0.0-alpha.37.tgz"
  "version" "1.0.0-alpha.37"
  dependencies:
    "mdn-data" "2.0.4"
    "source-map" "^0.6.1"

"css-what@^3.2.1":
  "integrity" "sha1-6nAm/LAXd+295SEk4h8yfnrpUOQ="
  "resolved" "https://registry.nlark.com/css-what/download/css-what-3.4.2.tgz?cache=0&sync_timestamp=1622227760882&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fcss-what%2Fdownload%2Fcss-what-3.4.2.tgz"
  "version" "3.4.2"

"css-what@^5.0.0":
  "integrity" "sha1-PvqCATH0ZpqKwkCPnDLnx96fTK0="
  "resolved" "https://registry.nlark.com/css-what/download/css-what-5.0.1.tgz?cache=0&sync_timestamp=1622227760882&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fcss-what%2Fdownload%2Fcss-what-5.0.1.tgz"
  "version" "5.0.1"

"cssesc@^3.0.0":
  "integrity" "sha1-N3QZGZA7hoVl4cCep0dEXNGJg+4="
  "resolved" "https://registry.nlark.com/cssesc/download/cssesc-3.0.0.tgz"
  "version" "3.0.0"

"cssnano-preset-default@^4.0.8":
  "integrity" "sha1-kgYisfwelaNOiDggPxOXpQTy0/8="
  "resolved" "https://registry.nlark.com/cssnano-preset-default/download/cssnano-preset-default-4.0.8.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fcssnano-preset-default%2Fdownload%2Fcssnano-preset-default-4.0.8.tgz"
  "version" "4.0.8"
  dependencies:
    "css-declaration-sorter" "^4.0.1"
    "cssnano-util-raw-cache" "^4.0.1"
    "postcss" "^7.0.0"
    "postcss-calc" "^7.0.1"
    "postcss-colormin" "^4.0.3"
    "postcss-convert-values" "^4.0.1"
    "postcss-discard-comments" "^4.0.2"
    "postcss-discard-duplicates" "^4.0.2"
    "postcss-discard-empty" "^4.0.1"
    "postcss-discard-overridden" "^4.0.1"
    "postcss-merge-longhand" "^4.0.11"
    "postcss-merge-rules" "^4.0.3"
    "postcss-minify-font-values" "^4.0.2"
    "postcss-minify-gradients" "^4.0.2"
    "postcss-minify-params" "^4.0.2"
    "postcss-minify-selectors" "^4.0.2"
    "postcss-normalize-charset" "^4.0.1"
    "postcss-normalize-display-values" "^4.0.2"
    "postcss-normalize-positions" "^4.0.2"
    "postcss-normalize-repeat-style" "^4.0.2"
    "postcss-normalize-string" "^4.0.2"
    "postcss-normalize-timing-functions" "^4.0.2"
    "postcss-normalize-unicode" "^4.0.1"
    "postcss-normalize-url" "^4.0.1"
    "postcss-normalize-whitespace" "^4.0.2"
    "postcss-ordered-values" "^4.1.2"
    "postcss-reduce-initial" "^4.0.3"
    "postcss-reduce-transforms" "^4.0.2"
    "postcss-svgo" "^4.0.3"
    "postcss-unique-selectors" "^4.0.1"

"cssnano-util-get-arguments@^4.0.0":
  "integrity" "sha1-7ToIKZ8h11dBsg87gfGU7UnMFQ8="
  "resolved" "https://registry.npm.taobao.org/cssnano-util-get-arguments/download/cssnano-util-get-arguments-4.0.0.tgz"
  "version" "4.0.0"

"cssnano-util-get-match@^4.0.0":
  "integrity" "sha1-wOTKB/U4a7F+xeUiULT1lhNlFW0="
  "resolved" "https://registry.nlark.com/cssnano-util-get-match/download/cssnano-util-get-match-4.0.0.tgz"
  "version" "4.0.0"

"cssnano-util-raw-cache@^4.0.1":
  "integrity" "sha1-sm1f1fcqEd/np4RvtMZyYPlr8oI="
  "resolved" "https://registry.npm.taobao.org/cssnano-util-raw-cache/download/cssnano-util-raw-cache-4.0.1.tgz"
  "version" "4.0.1"
  dependencies:
    "postcss" "^7.0.0"

"cssnano-util-same-parent@^4.0.0":
  "integrity" "sha1-V0CC+yhZ0ttDOFWDXZqEVuoYu/M="
  "resolved" "https://registry.npm.taobao.org/cssnano-util-same-parent/download/cssnano-util-same-parent-4.0.1.tgz"
  "version" "4.0.1"

"cssnano@^4.1.10":
  "integrity" "sha1-x7X1uB2iacsf2YLLlgwSAJEMmpk="
  "resolved" "https://registry.nlark.com/cssnano/download/cssnano-4.1.11.tgz"
  "version" "4.1.11"
  dependencies:
    "cosmiconfig" "^5.0.0"
    "cssnano-preset-default" "^4.0.8"
    "is-resolvable" "^1.0.0"
    "postcss" "^7.0.0"

"csso@^4.0.2":
  "integrity" "sha1-6jpWE0bo3J9UbW/r7dUBh884lSk="
  "resolved" "https://registry.nlark.com/csso/download/csso-4.2.0.tgz"
  "version" "4.2.0"
  dependencies:
    "css-tree" "^1.1.2"

"csstype@^3.0.2":
  "integrity" "sha1-0iZqeScp+yJ80hb7Vy9Dco4a00A="
  "resolved" "https://registry.nlark.com/csstype/download/csstype-3.0.8.tgz"
  "version" "3.0.8"

"currently-unhandled@^0.4.1":
  "integrity" "sha1-mI3zP+qxke95mmE2nddsF635V+o="
  "resolved" "https://registry.nlark.com/currently-unhandled/download/currently-unhandled-0.4.1.tgz?cache=0&sync_timestamp=1618847033604&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fcurrently-unhandled%2Fdownload%2Fcurrently-unhandled-0.4.1.tgz"
  "version" "0.4.1"
  dependencies:
    "array-find-index" "^1.0.1"

"cyclist@^1.0.1":
  "integrity" "sha1-WW6WmP0MgOEgOMK4LW6xs1tiJNk="
  "resolved" "https://registry.nlark.com/cyclist/download/cyclist-1.0.1.tgz"
  "version" "1.0.1"

"damerau-levenshtein@^1.0.6":
  "integrity" "sha1-ZDaAA1EqGmmSWTdBoJqdMag29V0="
  "resolved" "https://registry.nlark.com/damerau-levenshtein/download/damerau-levenshtein-1.0.7.tgz"
  "version" "1.0.7"

"dashdash@^1.12.0":
  "integrity" "sha1-hTz6D3y+L+1d4gMmuN1YEDX24vA="
  "resolved" "https://registry.npm.taobao.org/dashdash/download/dashdash-1.14.1.tgz?cache=0&sync_timestamp=1601073647826&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fdashdash%2Fdownload%2Fdashdash-1.14.1.tgz"
  "version" "1.14.1"
  dependencies:
    "assert-plus" "^1.0.0"

"date-fns@^2.16.1":
  "integrity" "sha512-8d35hViGYx/QH0icHYCeLmsLmMUheMmTyV9Fcm6gvNwdw31yXXH+O85sOBJ+OLnLQMKZowvpKb6FgMIQjcpvQw=="
  "resolved" "https://registry.npmmirror.com/date-fns/-/date-fns-2.28.0.tgz"
  "version" "2.28.0"

"dayjs@*", "dayjs@^1.8.34":
  "integrity" "sha1-VgDfRUj8JFOz8WPrsqu+llzPuYY="
  "resolved" "https://registry.nlark.com/dayjs/download/dayjs-1.10.5.tgz?cache=0&sync_timestamp=1622012271727&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fdayjs%2Fdownload%2Fdayjs-1.10.5.tgz"
  "version" "1.10.5"

"debug@^2.2.0":
  "integrity" "sha1-XRKFFd8TT/Mn6QpMk/Tgd6U2NB8="
  "resolved" "https://registry.npm.taobao.org/debug/download/debug-2.6.9.tgz?cache=0&sync_timestamp=1614330710870&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fdebug%2Fdownload%2Fdebug-2.6.9.tgz"
  "version" "2.6.9"
  dependencies:
    "ms" "2.0.0"

"debug@^2.3.3":
  "integrity" "sha1-XRKFFd8TT/Mn6QpMk/Tgd6U2NB8="
  "resolved" "https://registry.npm.taobao.org/debug/download/debug-2.6.9.tgz?cache=0&sync_timestamp=1614330710870&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fdebug%2Fdownload%2Fdebug-2.6.9.tgz"
  "version" "2.6.9"
  dependencies:
    "ms" "2.0.0"

"debug@^2.6.9":
  "integrity" "sha1-XRKFFd8TT/Mn6QpMk/Tgd6U2NB8="
  "resolved" "https://registry.npm.taobao.org/debug/download/debug-2.6.9.tgz?cache=0&sync_timestamp=1614330710870&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fdebug%2Fdownload%2Fdebug-2.6.9.tgz"
  "version" "2.6.9"
  dependencies:
    "ms" "2.0.0"

"debug@^3.1.1":
  "integrity" "sha1-clgLfpFF+zm2Z2+cXl+xALk0F5o="
  "resolved" "https://registry.npm.taobao.org/debug/download/debug-3.2.7.tgz?cache=0&sync_timestamp=1614330710870&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fdebug%2Fdownload%2Fdebug-3.2.7.tgz"
  "version" "3.2.7"
  dependencies:
    "ms" "^2.1.1"

"debug@^3.2.6":
  "integrity" "sha1-clgLfpFF+zm2Z2+cXl+xALk0F5o="
  "resolved" "https://registry.npm.taobao.org/debug/download/debug-3.2.7.tgz?cache=0&sync_timestamp=1614330710870&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fdebug%2Fdownload%2Fdebug-3.2.7.tgz"
  "version" "3.2.7"
  dependencies:
    "ms" "^2.1.1"

"debug@^3.2.7":
  "integrity" "sha1-clgLfpFF+zm2Z2+cXl+xALk0F5o="
  "resolved" "https://registry.npm.taobao.org/debug/download/debug-3.2.7.tgz?cache=0&sync_timestamp=1614330710870&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fdebug%2Fdownload%2Fdebug-3.2.7.tgz"
  "version" "3.2.7"
  dependencies:
    "ms" "^2.1.1"

"debug@^4.0.1", "debug@^4.1.0", "debug@^4.1.1":
  "integrity" "sha1-8NIpxQXgxtjEmsVT0bE9wYP2su4="
  "resolved" "https://registry.npm.taobao.org/debug/download/debug-4.3.1.tgz?cache=0&sync_timestamp=1614330710870&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fdebug%2Fdownload%2Fdebug-4.3.1.tgz"
  "version" "4.3.1"
  dependencies:
    "ms" "2.1.2"

"debug@2.6.9":
  "integrity" "sha1-XRKFFd8TT/Mn6QpMk/Tgd6U2NB8="
  "resolved" "https://registry.npm.taobao.org/debug/download/debug-2.6.9.tgz?cache=0&sync_timestamp=1614330710870&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fdebug%2Fdownload%2Fdebug-2.6.9.tgz"
  "version" "2.6.9"
  dependencies:
    "ms" "2.0.0"

"decamelize@^1.1.2", "decamelize@^1.2.0":
  "integrity" "sha1-9lNNFRSCabIDUue+4m9QH5oZEpA="
  "resolved" "https://registry.nlark.com/decamelize/download/decamelize-1.2.0.tgz"
  "version" "1.2.0"

"decode-uri-component@^0.2.0":
  "integrity" "sha1-6zkTMzRYd1y4TNGh+uBiEGu4dUU="
  "resolved" "https://registry.nlark.com/decode-uri-component/download/decode-uri-component-0.2.0.tgz"
  "version" "0.2.0"

"deep-diff@^0.3.5":
  "integrity" "sha1-wB3mPvsO7JeYgB1Ax+Da4ltYLIQ="
  "resolved" "https://registry.npm.taobao.org/deep-diff/download/deep-diff-0.3.8.tgz"
  "version" "0.3.8"

"deep-equal@^1.0.1":
  "integrity" "sha1-tcmMlCzv+vfLBR4k4UNKJaLmB2o="
  "resolved" "https://registry.npm.taobao.org/deep-equal/download/deep-equal-1.1.1.tgz?cache=0&sync_timestamp=1606860754950&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fdeep-equal%2Fdownload%2Fdeep-equal-1.1.1.tgz"
  "version" "1.1.1"
  dependencies:
    "is-arguments" "^1.0.4"
    "is-date-object" "^1.0.1"
    "is-regex" "^1.0.4"
    "object-is" "^1.0.1"
    "object-keys" "^1.1.1"
    "regexp.prototype.flags" "^1.2.0"

"deep-is@^0.1.3":
  "integrity" "sha1-s2nW+128E+7PUk+RsHD+7cNXzzQ="
  "resolved" "https://registry.nlark.com/deep-is/download/deep-is-0.1.3.tgz"
  "version" "0.1.3"

"default-gateway@^4.2.0":
  "integrity" "sha1-FnEEx1AMIRX23WmwpTa7jtcgVSs="
  "resolved" "https://registry.npm.taobao.org/default-gateway/download/default-gateway-4.2.0.tgz?cache=0&sync_timestamp=1610365857779&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fdefault-gateway%2Fdownload%2Fdefault-gateway-4.2.0.tgz"
  "version" "4.2.0"
  dependencies:
    "execa" "^1.0.0"
    "ip-regex" "^2.1.0"

"define-properties@^1.1.2", "define-properties@^1.1.3":
  "integrity" "sha1-z4jabL7ib+bbcJT2HYcMvYTO6fE="
  "resolved" "https://registry.npm.taobao.org/define-properties/download/define-properties-1.1.3.tgz"
  "version" "1.1.3"
  dependencies:
    "object-keys" "^1.0.12"

"define-property@^0.2.5":
  "integrity" "sha1-w1se+RjsPJkPmlvFe+BKrOxcgRY="
  "resolved" "https://registry.nlark.com/define-property/download/define-property-0.2.5.tgz"
  "version" "0.2.5"
  dependencies:
    "is-descriptor" "^0.1.0"

"define-property@^1.0.0":
  "integrity" "sha1-dp66rz9KY6rTr56NMEybvnm/sOY="
  "resolved" "https://registry.nlark.com/define-property/download/define-property-1.0.0.tgz"
  "version" "1.0.0"
  dependencies:
    "is-descriptor" "^1.0.0"

"define-property@^2.0.2":
  "integrity" "sha1-1Flono1lS6d+AqgX+HENcCyxbp0="
  "resolved" "https://registry.nlark.com/define-property/download/define-property-2.0.2.tgz"
  "version" "2.0.2"
  dependencies:
    "is-descriptor" "^1.0.2"
    "isobject" "^3.0.1"

"del@^4.1.1":
  "integrity" "sha1-no8RciLqRKMf86FWwEm5kFKp8LQ="
  "resolved" "https://registry.npm.taobao.org/del/download/del-4.1.1.tgz"
  "version" "4.1.1"
  dependencies:
    "@types/glob" "^7.1.1"
    "globby" "^6.1.0"
    "is-path-cwd" "^2.0.0"
    "is-path-in-cwd" "^2.0.0"
    "p-map" "^2.0.0"
    "pify" "^4.0.1"
    "rimraf" "^2.6.3"

"delayed-stream@~1.0.0":
  "integrity" "sha1-3zrhmayt+31ECqrgsp4icrJOxhk="
  "resolved" "https://registry.nlark.com/delayed-stream/download/delayed-stream-1.0.0.tgz"
  "version" "1.0.0"

"delegate@^3.1.2":
  "integrity" "sha1-tmtxwxWFIuirV0T3INjKDCr1kWY="
  "resolved" "https://registry.nlark.com/delegate/download/delegate-3.2.0.tgz"
  "version" "3.2.0"

"delegates@^1.0.0":
  "integrity" "sha1-hMbhWbgZBP3KWaDvRM2HDTElD5o="
  "resolved" "https://registry.npm.taobao.org/delegates/download/delegates-1.0.0.tgz"
  "version" "1.0.0"

"depd@~1.1.2":
  "integrity" "sha1-m81S4UwJd2PnSbJ0xDRu0uVgtak="
  "resolved" "https://registry.nlark.com/depd/download/depd-1.1.2.tgz"
  "version" "1.1.2"

"des.js@^1.0.0":
  "integrity" "sha1-U4IULhvcU/hdhtU+X0qn3rkeCEM="
  "resolved" "https://registry.npm.taobao.org/des.js/download/des.js-1.0.1.tgz"
  "version" "1.0.1"
  dependencies:
    "inherits" "^2.0.1"
    "minimalistic-assert" "^1.0.0"

"destroy@~1.0.4":
  "integrity" "sha1-l4hXRCxEdJ5CBmE+N5RiBYJqvYA="
  "resolved" "https://registry.nlark.com/destroy/download/destroy-1.0.4.tgz"
  "version" "1.0.4"

"detect-file@^1.0.0":
  "integrity" "sha1-8NZtA2cqglyxtzvbP+YjEMjlUrc="
  "resolved" "https://registry.npm.taobao.org/detect-file/download/detect-file-1.0.0.tgz"
  "version" "1.0.0"

"detect-node@^2.0.4":
  "integrity" "sha1-yccHdaScPQO8LAbZpzvlUPl4+LE="
  "resolved" "https://registry.nlark.com/detect-node/download/detect-node-2.1.0.tgz?cache=0&sync_timestamp=1621146902208&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fdetect-node%2Fdownload%2Fdetect-node-2.1.0.tgz"
  "version" "2.1.0"

"diffie-hellman@^5.0.0":
  "integrity" "sha1-QOjumPVaIUlgcUaSHGPhrl89KHU="
  "resolved" "https://registry.nlark.com/diffie-hellman/download/diffie-hellman-5.0.3.tgz"
  "version" "5.0.3"
  dependencies:
    "bn.js" "^4.1.0"
    "miller-rabin" "^4.0.0"
    "randombytes" "^2.0.0"

"dir-glob@^2.0.0":
  "integrity" "sha1-+gnwaUFTyJGLGLoN6vrpR2n8UMQ="
  "resolved" "https://registry.npm.taobao.org/dir-glob/download/dir-glob-2.2.2.tgz"
  "version" "2.2.2"
  dependencies:
    "path-type" "^3.0.0"

"dns-equal@^1.0.0":
  "integrity" "sha1-s55/HabrCnW6nBcySzR1PEfgZU0="
  "resolved" "https://registry.npm.taobao.org/dns-equal/download/dns-equal-1.0.0.tgz"
  "version" "1.0.0"

"dns-packet@^1.3.1":
  "integrity" "sha1-40VQZYJKJQe6iGxVqJljuxB97G8="
  "resolved" "https://registry.nlark.com/dns-packet/download/dns-packet-1.3.4.tgz"
  "version" "1.3.4"
  dependencies:
    "ip" "^1.1.0"
    "safe-buffer" "^5.0.1"

"dns-txt@^2.0.2":
  "integrity" "sha1-uR2Ab10nGI5Ks+fRB9iBocxGQrY="
  "resolved" "https://registry.nlark.com/dns-txt/download/dns-txt-2.0.2.tgz"
  "version" "2.0.2"
  dependencies:
    "buffer-indexof" "^1.0.0"

"doctrine@^2.1.0":
  "integrity" "sha1-XNAfwQFiG0LEzX9dGmYkNxbT850="
  "resolved" "https://registry.npm.taobao.org/doctrine/download/doctrine-2.1.0.tgz"
  "version" "2.1.0"
  dependencies:
    "esutils" "^2.0.2"

"doctrine@^3.0.0":
  "integrity" "sha1-rd6+rXKmV023g2OdyHoSF3OXOWE="
  "resolved" "https://registry.npm.taobao.org/doctrine/download/doctrine-3.0.0.tgz"
  "version" "3.0.0"
  dependencies:
    "esutils" "^2.0.2"

"dom-align@^1.7.0":
  "integrity" "sha1-D4Fk69DJwhsMeQMQSTzYVYkqzUs="
  "resolved" "https://registry.nlark.com/dom-align/download/dom-align-1.12.2.tgz"
  "version" "1.12.2"

"dom-closest@^0.2.0":
  "integrity" "sha1-69n5HRvyLo1vR3h2u80+yQIWwM8="
  "resolved" "https://registry.npm.taobao.org/dom-closest/download/dom-closest-0.2.0.tgz"
  "version" "0.2.0"
  dependencies:
    "dom-matches" ">=1.0.1"

"dom-converter@^0.2.0":
  "integrity" "sha1-ZyGp2u4uKTaClVtq/kFncWJ7t2g="
  "resolved" "https://registry.npm.taobao.org/dom-converter/download/dom-converter-0.2.0.tgz"
  "version" "0.2.0"
  dependencies:
    "utila" "~0.4"

"dom-matches@>=1.0.1":
  "integrity" "sha1-0nKLQWqHUzmA6wibhI0lPPI6dYw="
  "resolved" "https://registry.nlark.com/dom-matches/download/dom-matches-2.0.0.tgz"
  "version" "2.0.0"

"dom-scroll-into-view@^1.2.0":
  "integrity" "sha1-6PNnMt0ImwIBqI14Fdw/iObWbH4="
  "resolved" "https://registry.npm.taobao.org/dom-scroll-into-view/download/dom-scroll-into-view-1.2.1.tgz"
  "version" "1.2.1"

"dom-scroll-into-view@^1.2.1":
  "integrity" "sha1-6PNnMt0ImwIBqI14Fdw/iObWbH4="
  "resolved" "https://registry.npm.taobao.org/dom-scroll-into-view/download/dom-scroll-into-view-1.2.1.tgz"
  "version" "1.2.1"

"dom-scroll-into-view@^2.0.1":
  "integrity" "sha1-DezIUigB/Y0/HGujVadNOCxfmJs="
  "resolved" "https://registry.npm.taobao.org/dom-scroll-into-view/download/dom-scroll-into-view-2.0.1.tgz"
  "version" "2.0.1"

"dom-scroll-into-view@1.x":
  "integrity" "sha1-6PNnMt0ImwIBqI14Fdw/iObWbH4="
  "resolved" "https://registry.npm.taobao.org/dom-scroll-into-view/download/dom-scroll-into-view-1.2.1.tgz"
  "version" "1.2.1"

"dom-serializer@^1.0.1":
  "integrity" "sha1-YgZDfTLO767HFhgDIwx6ILwbTZE="
  "resolved" "https://registry.nlark.com/dom-serializer/download/dom-serializer-1.3.2.tgz?cache=0&sync_timestamp=1621256918158&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fdom-serializer%2Fdownload%2Fdom-serializer-1.3.2.tgz"
  "version" "1.3.2"
  dependencies:
    "domelementtype" "^2.0.1"
    "domhandler" "^4.2.0"
    "entities" "^2.0.0"

"dom-serializer@0":
  "integrity" "sha1-GvuB9TNxcXXUeGVd68XjMtn5u1E="
  "resolved" "https://registry.nlark.com/dom-serializer/download/dom-serializer-0.2.2.tgz?cache=0&sync_timestamp=1621256918158&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fdom-serializer%2Fdownload%2Fdom-serializer-0.2.2.tgz"
  "version" "0.2.2"
  dependencies:
    "domelementtype" "^2.0.1"
    "entities" "^2.0.0"

"dom-walk@^0.1.0":
  "integrity" "sha1-DFSL7wSPTR8qlySQAiNgYNqj/YQ="
  "resolved" "https://registry.npm.taobao.org/dom-walk/download/dom-walk-0.1.2.tgz"
  "version" "0.1.2"

"domain-browser@^1.1.1":
  "integrity" "sha1-PTH1AZGmdJ3RN1p/Ui6CPULlTto="
  "resolved" "https://registry.npm.taobao.org/domain-browser/download/domain-browser-1.2.0.tgz"
  "version" "1.2.0"

"domelementtype@^2.0.1", "domelementtype@^2.2.0":
  "integrity" "sha1-mgtsJ4LtahxzI9QiZxg9+b2LHVc="
  "resolved" "https://registry.nlark.com/domelementtype/download/domelementtype-2.2.0.tgz"
  "version" "2.2.0"

"domelementtype@1":
  "integrity" "sha1-0EjESzew0Qp/Kj1f7j9DM9eQSB8="
  "resolved" "https://registry.nlark.com/domelementtype/download/domelementtype-1.3.1.tgz"
  "version" "1.3.1"

"domhandler@^4.0.0", "domhandler@^4.2.0":
  "integrity" "sha1-+XaKXwNL5gqJonwuTQ9066DYsFk="
  "resolved" "https://registry.nlark.com/domhandler/download/domhandler-4.2.0.tgz"
  "version" "4.2.0"
  dependencies:
    "domelementtype" "^2.2.0"

"domutils@^1.7.0":
  "integrity" "sha1-Vuo0HoNOBuZ0ivehyyXaZ+qfjCo="
  "resolved" "https://registry.nlark.com/domutils/download/domutils-1.7.0.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fdomutils%2Fdownload%2Fdomutils-1.7.0.tgz"
  "version" "1.7.0"
  dependencies:
    "dom-serializer" "0"
    "domelementtype" "1"

"domutils@^2.5.2", "domutils@^2.6.0":
  "integrity" "sha1-jrrwxB66/PVbC3LsMcVjI3EsVEI="
  "resolved" "https://registry.nlark.com/domutils/download/domutils-2.7.0.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fdomutils%2Fdownload%2Fdomutils-2.7.0.tgz"
  "version" "2.7.0"
  dependencies:
    "dom-serializer" "^1.0.1"
    "domelementtype" "^2.2.0"
    "domhandler" "^4.2.0"

"dot-prop@^5.2.0":
  "integrity" "sha1-kMzOcIzZzYLMTcjD3dmr3VWyDog="
  "resolved" "https://registry.npm.taobao.org/dot-prop/download/dot-prop-5.3.0.tgz?cache=0&sync_timestamp=1605778245785&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fdot-prop%2Fdownload%2Fdot-prop-5.3.0.tgz"
  "version" "5.3.0"
  dependencies:
    "is-obj" "^2.0.0"

"draft-convert@^2.0.0":
  "integrity" "sha1-CXlxUax+2cj3iY0Ra6nzaGmZe98="
  "resolved" "https://registry.nlark.com/draft-convert/download/draft-convert-2.1.11.tgz"
  "version" "2.1.11"
  dependencies:
    "@babel/runtime" "^7.5.5"
    "immutable" "~3.7.4"
    "invariant" "^2.2.1"

"draft-js-multidecorators@^1.0.0":
  "integrity" "sha1-bEvo17eN0rlm7lHubMF5ubU15hI="
  "resolved" "https://registry.npm.taobao.org/draft-js-multidecorators/download/draft-js-multidecorators-1.0.0.tgz"
  "version" "1.0.0"
  dependencies:
    "immutable" "*"

"draft-js@^0.10.0", "draft-js@^0.10.3", "draft-js@^0.10.5", "draft-js@^0.10.x", "draft-js@>=0.7.0", "draft-js@~0.10.0":
  "integrity" "sha1-v6m+sBj+BTPbsI1mdcNxprCPp0I="
  "resolved" "https://registry.npm.taobao.org/draft-js/download/draft-js-0.10.5.tgz?cache=0&sync_timestamp=1597680829415&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fdraft-js%2Fdownload%2Fdraft-js-0.10.5.tgz"
  "version" "0.10.5"
  dependencies:
    "fbjs" "^0.8.15"
    "immutable" "~3.7.4"
    "object-assign" "^4.1.0"

"draftjs-utils@^0.9.4":
  "integrity" "sha1-l2xhqhM9u7/t1lrh3WYn17mMbwg="
  "resolved" "https://registry.npm.taobao.org/draftjs-utils/download/draftjs-utils-0.9.4.tgz"
  "version" "0.9.4"

"duplexer2@~0.1.4":
  "integrity" "sha1-ixLauHjA1p4+eJEFFmKjL8a93ME="
  "resolved" "https://registry.npm.taobao.org/duplexer2/download/duplexer2-0.1.4.tgz"
  "version" "0.1.4"
  dependencies:
    "readable-stream" "^2.0.2"

"duplexify@^3.4.2", "duplexify@^3.6.0":
  "integrity" "sha1-Kk31MX9sz9kfhtb9JdjYoQO4gwk="
  "resolved" "https://registry.nlark.com/duplexify/download/duplexify-3.7.1.tgz"
  "version" "3.7.1"
  dependencies:
    "end-of-stream" "^1.0.0"
    "inherits" "^2.0.1"
    "readable-stream" "^2.0.0"
    "stream-shift" "^1.0.0"

"ecc-jsbn@~0.1.1":
  "integrity" "sha1-OoOpBOVDUyh4dMVkt1SThoSamMk="
  "resolved" "https://registry.nlark.com/ecc-jsbn/download/ecc-jsbn-0.1.2.tgz"
  "version" "0.1.2"
  dependencies:
    "jsbn" "~0.1.0"
    "safer-buffer" "^2.1.0"

"ee-first@1.1.1":
  "integrity" "sha1-WQxhFWsK4vTwJVcyoViyZrxWsh0="
  "resolved" "https://registry.npm.taobao.org/ee-first/download/ee-first-1.1.1.tgz"
  "version" "1.1.1"

"electron-to-chromium@^1.3.723":
  "integrity" "sha1-ahdIurjtlJhDkeyL6Kfn7B/j2EM="
  "resolved" "https://registry.nlark.com/electron-to-chromium/download/electron-to-chromium-1.3.761.tgz?cache=0&sync_timestamp=1624961030041&other_urls=https%3A%2F%2Fregistry.nlark.com%2Felectron-to-chromium%2Fdownload%2Felectron-to-chromium-1.3.761.tgz"
  "version" "1.3.761"

"elliptic@^6.5.3":
  "integrity" "sha1-2jfOvTHnmhNn6UG1ku0fvr1Yq7s="
  "resolved" "https://registry.nlark.com/elliptic/download/elliptic-6.5.4.tgz"
  "version" "6.5.4"
  dependencies:
    "bn.js" "^4.11.9"
    "brorand" "^1.1.0"
    "hash.js" "^1.0.0"
    "hmac-drbg" "^1.0.1"
    "inherits" "^2.0.4"
    "minimalistic-assert" "^1.0.1"
    "minimalistic-crypto-utils" "^1.0.1"

"emoji-mart@^3.0.1":
  "integrity" "sha1-nOhnBuAq6gUGNF+YRkgUpmLKVMY="
  "resolved" "https://registry.npm.taobao.org/emoji-mart/download/emoji-mart-3.0.1.tgz"
  "version" "3.0.1"
  dependencies:
    "@babel/runtime" "^7.0.0"
    "prop-types" "^15.6.0"

"emoji-regex@^7.0.1":
  "integrity" "sha1-kzoEBShgyF6DwSJHnEdIqOTHIVY="
  "resolved" "https://registry.nlark.com/emoji-regex/download/emoji-regex-7.0.3.tgz"
  "version" "7.0.3"

"emoji-regex@^8.0.0":
  "integrity" "sha1-6Bj9ac5cz8tARZT4QpY79TFkzDc="
  "resolved" "https://registry.nlark.com/emoji-regex/download/emoji-regex-8.0.0.tgz"
  "version" "8.0.0"

"emoji-regex@^9.0.0":
  "integrity" "sha1-hAyIA7DYBH9P8M+WMXazLU7z7XI="
  "resolved" "https://registry.nlark.com/emoji-regex/download/emoji-regex-9.2.2.tgz"
  "version" "9.2.2"

"emojis-list@^2.0.0":
  "integrity" "sha1-TapNnbAPmBmIDHn6RXrlsJof04k="
  "resolved" "https://registry.nlark.com/emojis-list/download/emojis-list-2.1.0.tgz"
  "version" "2.1.0"

"emojis-list@^3.0.0":
  "integrity" "sha1-VXBmIEatKeLpFucariYKvf9Pang="
  "resolved" "https://registry.nlark.com/emojis-list/download/emojis-list-3.0.0.tgz"
  "version" "3.0.0"

"encodeurl@~1.0.2":
  "integrity" "sha1-rT/0yG7C0CkyL1oCw6mmBslbP1k="
  "resolved" "https://registry.nlark.com/encodeurl/download/encodeurl-1.0.2.tgz"
  "version" "1.0.2"

"encoding@^0.1.11":
  "integrity" "sha1-VldK/deR9UqOmyeFwFgqLSYhD6k="
  "resolved" "https://registry.npm.taobao.org/encoding/download/encoding-0.1.13.tgz"
  "version" "0.1.13"
  dependencies:
    "iconv-lite" "^0.6.2"

"end-of-stream@^1.0.0", "end-of-stream@^1.1.0", "end-of-stream@^1.4.1":
  "integrity" "sha1-WuZKX0UFe682JuwU2gyl5LJDHrA="
  "resolved" "https://registry.nlark.com/end-of-stream/download/end-of-stream-1.4.4.tgz"
  "version" "1.4.4"
  dependencies:
    "once" "^1.4.0"

"enhanced-resolve@^4.1.1", "enhanced-resolve@^4.5.0":
  "integrity" "sha1-Lzz9hNvjtIfxjy2y7x4GSlccpew="
  "resolved" "https://registry.nlark.com/enhanced-resolve/download/enhanced-resolve-4.5.0.tgz?cache=0&sync_timestamp=1620663214002&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fenhanced-resolve%2Fdownload%2Fenhanced-resolve-4.5.0.tgz"
  "version" "4.5.0"
  dependencies:
    "graceful-fs" "^4.1.2"
    "memory-fs" "^0.5.0"
    "tapable" "^1.0.0"

"enquire.js@^2.1.6":
  "integrity" "sha1-PoeAybi4NQhMP2DhZtvDwqPImBQ="
  "resolved" "https://registry.npm.taobao.org/enquire.js/download/enquire.js-2.1.6.tgz"
  "version" "2.1.6"

"enquirer@^2.3.5":
  "integrity" "sha1-Kn/l3WNKHkElqXXsmU/1RW3Dc00="
  "resolved" "https://registry.npm.taobao.org/enquirer/download/enquirer-2.3.6.tgz"
  "version" "2.3.6"
  dependencies:
    "ansi-colors" "^4.1.1"

"entities@^2.0.0":
  "integrity" "sha1-CY3JDruD2N/6CJ1VJWs1HTTE2lU="
  "resolved" "https://registry.nlark.com/entities/download/entities-2.2.0.tgz"
  "version" "2.2.0"

"errno@^0.1.1", "errno@^0.1.3", "errno@~0.1.7":
  "integrity" "sha1-i7Ppx9Rjvkl2/4iPdrSAnrwugR8="
  "resolved" "https://registry.nlark.com/errno/download/errno-0.1.8.tgz"
  "version" "0.1.8"
  dependencies:
    "prr" "~1.0.1"

"error-ex@^1.2.0", "error-ex@^1.3.1":
  "integrity" "sha1-tKxAZIEH/c3PriQvQovqihTU8b8="
  "resolved" "https://registry.npm.taobao.org/error-ex/download/error-ex-1.3.2.tgz"
  "version" "1.3.2"
  dependencies:
    "is-arrayish" "^0.2.1"

"error-stack-parser@^2.0.0":
  "integrity" "sha1-WpmnB716TFinl5AtSNgoA+3mqtg="
  "resolved" "https://registry.npm.taobao.org/error-stack-parser/download/error-stack-parser-2.0.6.tgz"
  "version" "2.0.6"
  dependencies:
    "stackframe" "^1.1.1"

"es-abstract@^1.18.0-next.1", "es-abstract@^1.18.0-next.2", "es-abstract@^1.18.2":
  "integrity" "sha1-JcTDOAonqiA8RLK2hbupTaMbY+A="
  "resolved" "https://registry.nlark.com/es-abstract/download/es-abstract-1.18.3.tgz?cache=0&sync_timestamp=1622157901871&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fes-abstract%2Fdownload%2Fes-abstract-1.18.3.tgz"
  "version" "1.18.3"
  dependencies:
    "call-bind" "^1.0.2"
    "es-to-primitive" "^1.2.1"
    "function-bind" "^1.1.1"
    "get-intrinsic" "^1.1.1"
    "has" "^1.0.3"
    "has-symbols" "^1.0.2"
    "is-callable" "^1.2.3"
    "is-negative-zero" "^2.0.1"
    "is-regex" "^1.1.3"
    "is-string" "^1.0.6"
    "object-inspect" "^1.10.3"
    "object-keys" "^1.1.1"
    "object.assign" "^4.1.2"
    "string.prototype.trimend" "^1.0.4"
    "string.prototype.trimstart" "^1.0.4"
    "unbox-primitive" "^1.0.1"

"es-to-primitive@^1.2.1":
  "integrity" "sha1-5VzUyc3BiLzvsDs2bHNjI/xciYo="
  "resolved" "https://registry.npm.taobao.org/es-to-primitive/download/es-to-primitive-1.2.1.tgz"
  "version" "1.2.1"
  dependencies:
    "is-callable" "^1.1.4"
    "is-date-object" "^1.0.1"
    "is-symbol" "^1.0.2"

"escalade@^3.1.1":
  "integrity" "sha1-2M/ccACWXFoBdLSoLqpcBVJ0LkA="
  "resolved" "https://registry.npm.taobao.org/escalade/download/escalade-3.1.1.tgz"
  "version" "3.1.1"

"escape-html@~1.0.3":
  "integrity" "sha1-Aljq5NPQwJdN4cFpGI7wBR0dGYg="
  "resolved" "https://registry.npm.taobao.org/escape-html/download/escape-html-1.0.3.tgz"
  "version" "1.0.3"

"escape-string-regexp@^1.0.2", "escape-string-regexp@^1.0.5":
  "integrity" "sha1-G2HAViGQqN/2rjuyzwIAyhMLhtQ="
  "resolved" "https://registry.nlark.com/escape-string-regexp/download/escape-string-regexp-1.0.5.tgz"
  "version" "1.0.5"

"escape-string-regexp@^4.0.0":
  "integrity" "sha1-FLqDpdNz49MR5a/KKc9b+tllvzQ="
  "resolved" "https://registry.nlark.com/escape-string-regexp/download/escape-string-regexp-4.0.0.tgz"
  "version" "4.0.0"

"eslint-config-airbnb-base@^14.1.0", "eslint-config-airbnb-base@^14.2.1":
  "integrity" "sha1-ii6zhFXcWjElUBk7MZza7vBCzR4="
  "resolved" "https://registry.npm.taobao.org/eslint-config-airbnb-base/download/eslint-config-airbnb-base-14.2.1.tgz"
  "version" "14.2.1"
  dependencies:
    "confusing-browser-globals" "^1.0.10"
    "object.assign" "^4.1.2"
    "object.entries" "^1.1.2"

"eslint-config-airbnb-typescript@^8.0.2":
  "integrity" "sha1-RlsXsLH6zcyk/iOlQmvCereysvI="
  "resolved" "https://registry.npm.taobao.org/eslint-config-airbnb-typescript/download/eslint-config-airbnb-typescript-8.0.2.tgz?cache=0&sync_timestamp=1612595943465&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Feslint-config-airbnb-typescript%2Fdownload%2Feslint-config-airbnb-typescript-8.0.2.tgz"
  "version" "8.0.2"
  dependencies:
    "@typescript-eslint/parser" "^3.1.0"
    "eslint-config-airbnb" "^18.1.0"
    "eslint-config-airbnb-base" "^14.1.0"

"eslint-config-airbnb@^18.1.0":
  "integrity" "sha1-t/4rQvn4Fz6CW3PIAUtZLkScmNk="
  "resolved" "https://registry.npm.taobao.org/eslint-config-airbnb/download/eslint-config-airbnb-18.2.1.tgz"
  "version" "18.2.1"
  dependencies:
    "eslint-config-airbnb-base" "^14.2.1"
    "object.assign" "^4.1.2"
    "object.entries" "^1.1.2"

"eslint-config-prettier@^6.11.0":
  "integrity" "sha1-f5P2y31FqS8VN6cOzAY2bhrG/tk="
  "resolved" "https://registry.nlark.com/eslint-config-prettier/download/eslint-config-prettier-6.15.0.tgz?cache=0&sync_timestamp=1619270475218&other_urls=https%3A%2F%2Fregistry.nlark.com%2Feslint-config-prettier%2Fdownload%2Feslint-config-prettier-6.15.0.tgz"
  "version" "6.15.0"
  dependencies:
    "get-stdin" "^6.0.0"

"eslint-import-resolver-node@^0.3.4":
  "integrity" "sha1-hf+oGULCUBLYIxCW3fZ5wDBCxxc="
  "resolved" "https://registry.npm.taobao.org/eslint-import-resolver-node/download/eslint-import-resolver-node-0.3.4.tgz"
  "version" "0.3.4"
  dependencies:
    "debug" "^2.6.9"
    "resolve" "^1.13.1"

"eslint-module-utils@^2.6.1":
  "integrity" "sha1-tRvh5HPdDeHF6mOOIkKcJJDqgjM="
  "resolved" "https://registry.nlark.com/eslint-module-utils/download/eslint-module-utils-2.6.1.tgz?cache=0&sync_timestamp=1620972121953&other_urls=https%3A%2F%2Fregistry.nlark.com%2Feslint-module-utils%2Fdownload%2Feslint-module-utils-2.6.1.tgz"
  "version" "2.6.1"
  dependencies:
    "debug" "^3.2.7"
    "pkg-dir" "^2.0.0"

"eslint-plugin-import@^2.22.0", "eslint-plugin-import@^2.22.1":
  "integrity" "sha1-jc6x7Wtz5G5Q7JpbskEbZF59PZc="
  "resolved" "https://registry.nlark.com/eslint-plugin-import/download/eslint-plugin-import-2.23.4.tgz"
  "version" "2.23.4"
  dependencies:
    "array-includes" "^3.1.3"
    "array.prototype.flat" "^1.2.4"
    "debug" "^2.6.9"
    "doctrine" "^2.1.0"
    "eslint-import-resolver-node" "^0.3.4"
    "eslint-module-utils" "^2.6.1"
    "find-up" "^2.0.0"
    "has" "^1.0.3"
    "is-core-module" "^2.4.0"
    "minimatch" "^3.0.4"
    "object.values" "^1.1.3"
    "pkg-up" "^2.0.0"
    "read-pkg-up" "^3.0.0"
    "resolve" "^1.20.0"
    "tsconfig-paths" "^3.9.0"

"eslint-plugin-jsx-a11y@^6.3.1", "eslint-plugin-jsx-a11y@^6.4.1":
  "integrity" "sha1-othMqkl1aUL0Lx/6uQAkNjkXGP0="
  "resolved" "https://registry.npm.taobao.org/eslint-plugin-jsx-a11y/download/eslint-plugin-jsx-a11y-6.4.1.tgz?cache=0&sync_timestamp=1603729445122&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Feslint-plugin-jsx-a11y%2Fdownload%2Feslint-plugin-jsx-a11y-6.4.1.tgz"
  "version" "6.4.1"
  dependencies:
    "@babel/runtime" "^7.11.2"
    "aria-query" "^4.2.2"
    "array-includes" "^3.1.1"
    "ast-types-flow" "^0.0.7"
    "axe-core" "^4.0.2"
    "axobject-query" "^2.2.0"
    "damerau-levenshtein" "^1.0.6"
    "emoji-regex" "^9.0.0"
    "has" "^1.0.3"
    "jsx-ast-utils" "^3.1.0"
    "language-tags" "^1.0.5"

"eslint-plugin-prettier@^3.1.4":
  "integrity" "sha1-zbrTvx29Kxd+mCVzf+Y7R2oI8Mc="
  "resolved" "https://registry.npm.taobao.org/eslint-plugin-prettier/download/eslint-plugin-prettier-3.4.0.tgz"
  "version" "3.4.0"
  dependencies:
    "prettier-linter-helpers" "^1.0.0"

"eslint-plugin-react-hooks@^4 || ^3 || ^2.3.0 || ^1.7.0", "eslint-plugin-react-hooks@^4.0.4":
  "integrity" "sha1-jCKcJo1GiVYzTJQ7tF/IYCgPVVY="
  "resolved" "https://registry.nlark.com/eslint-plugin-react-hooks/download/eslint-plugin-react-hooks-4.2.0.tgz"
  "version" "4.2.0"

"eslint-plugin-react@^7.20.2", "eslint-plugin-react@^7.21.5":
  "integrity" "sha1-6t7fo1Gm82tJCqF/T6mxToQrnrQ="
  "resolved" "https://registry.nlark.com/eslint-plugin-react/download/eslint-plugin-react-7.24.0.tgz"
  "version" "7.24.0"
  dependencies:
    "array-includes" "^3.1.3"
    "array.prototype.flatmap" "^1.2.4"
    "doctrine" "^2.1.0"
    "has" "^1.0.3"
    "jsx-ast-utils" "^2.4.1 || ^3.0.0"
    "minimatch" "^3.0.4"
    "object.entries" "^1.1.4"
    "object.fromentries" "^2.0.4"
    "object.values" "^1.1.4"
    "prop-types" "^15.7.2"
    "resolve" "^2.0.0-next.3"
    "string.prototype.matchall" "^4.0.5"

"eslint-scope@^4.0.3":
  "integrity" "sha1-ygODMxD2iJoyZHgaqC5j65z+eEg="
  "resolved" "https://registry.nlark.com/eslint-scope/download/eslint-scope-4.0.3.tgz?cache=0&sync_timestamp=1618908284969&other_urls=https%3A%2F%2Fregistry.nlark.com%2Feslint-scope%2Fdownload%2Feslint-scope-4.0.3.tgz"
  "version" "4.0.3"
  dependencies:
    "esrecurse" "^4.1.0"
    "estraverse" "^4.1.1"

"eslint-scope@^5.0.0", "eslint-scope@^5.1.1":
  "integrity" "sha1-54blmmbLkrP2wfsNUIqrF0hI9Iw="
  "resolved" "https://registry.nlark.com/eslint-scope/download/eslint-scope-5.1.1.tgz?cache=0&sync_timestamp=1618908284969&other_urls=https%3A%2F%2Fregistry.nlark.com%2Feslint-scope%2Fdownload%2Feslint-scope-5.1.1.tgz"
  "version" "5.1.1"
  dependencies:
    "esrecurse" "^4.3.0"
    "estraverse" "^4.1.1"

"eslint-utils@^2.0.0", "eslint-utils@^2.1.0":
  "integrity" "sha1-0t5eA0JOcH3BDHQGjd7a5wh0Gyc="
  "resolved" "https://registry.nlark.com/eslint-utils/download/eslint-utils-2.1.0.tgz?cache=0&sync_timestamp=1620975524854&other_urls=https%3A%2F%2Fregistry.nlark.com%2Feslint-utils%2Fdownload%2Feslint-utils-2.1.0.tgz"
  "version" "2.1.0"
  dependencies:
    "eslint-visitor-keys" "^1.1.0"

"eslint-visitor-keys@^1.1.0", "eslint-visitor-keys@^1.3.0":
  "integrity" "sha1-MOvR73wv3/AcOk8VEESvJfqwUj4="
  "resolved" "https://registry.nlark.com/eslint-visitor-keys/download/eslint-visitor-keys-1.3.0.tgz?cache=0&sync_timestamp=1624559054225&other_urls=https%3A%2F%2Fregistry.nlark.com%2Feslint-visitor-keys%2Fdownload%2Feslint-visitor-keys-1.3.0.tgz"
  "version" "1.3.0"

"eslint-visitor-keys@^2.0.0":
  "integrity" "sha1-9lMoJZMFknOSyTjtROsKXJsr0wM="
  "resolved" "https://registry.nlark.com/eslint-visitor-keys/download/eslint-visitor-keys-2.1.0.tgz?cache=0&sync_timestamp=1624559054225&other_urls=https%3A%2F%2Fregistry.nlark.com%2Feslint-visitor-keys%2Fdownload%2Feslint-visitor-keys-2.1.0.tgz"
  "version" "2.1.0"

"eslint@*", "eslint@^2 || ^3 || ^4 || ^5 || ^6 || ^7.2.0", "eslint@^3 || ^4 || ^5 || ^6 || ^7", "eslint@^3.0.0 || ^4.0.0 || ^5.0.0 || ^6.0.0 || ^7.0.0", "eslint@^5.0.0 || ^6.0.0 || ^7.0.0", "eslint@^5.16.0 || ^6.8.0 || ^7.2.0", "eslint@^7.3.1", "eslint@>=3.14.1", "eslint@>=5.0.0":
  "integrity" "sha1-7ip2SPLnKUheTQvWOD7B3qvIs8A="
  "resolved" "https://registry.nlark.com/eslint/download/eslint-7.29.0.tgz"
  "version" "7.29.0"
  dependencies:
    "@babel/code-frame" "7.12.11"
    "@eslint/eslintrc" "^0.4.2"
    "ajv" "^6.10.0"
    "chalk" "^4.0.0"
    "cross-spawn" "^7.0.2"
    "debug" "^4.0.1"
    "doctrine" "^3.0.0"
    "enquirer" "^2.3.5"
    "escape-string-regexp" "^4.0.0"
    "eslint-scope" "^5.1.1"
    "eslint-utils" "^2.1.0"
    "eslint-visitor-keys" "^2.0.0"
    "espree" "^7.3.1"
    "esquery" "^1.4.0"
    "esutils" "^2.0.2"
    "fast-deep-equal" "^3.1.3"
    "file-entry-cache" "^6.0.1"
    "functional-red-black-tree" "^1.0.1"
    "glob-parent" "^5.1.2"
    "globals" "^13.6.0"
    "ignore" "^4.0.6"
    "import-fresh" "^3.0.0"
    "imurmurhash" "^0.1.4"
    "is-glob" "^4.0.0"
    "js-yaml" "^3.13.1"
    "json-stable-stringify-without-jsonify" "^1.0.1"
    "levn" "^0.4.1"
    "lodash.merge" "^4.6.2"
    "minimatch" "^3.0.4"
    "natural-compare" "^1.4.0"
    "optionator" "^0.9.1"
    "progress" "^2.0.0"
    "regexpp" "^3.1.0"
    "semver" "^7.2.1"
    "strip-ansi" "^6.0.0"
    "strip-json-comments" "^3.1.0"
    "table" "^6.0.9"
    "text-table" "^0.2.0"
    "v8-compile-cache" "^2.0.3"

"espree@^7.3.0", "espree@^7.3.1":
  "integrity" "sha1-8t8zC3Usb1UBn4vYm3ZgA5wbu7Y="
  "resolved" "https://registry.nlark.com/espree/download/espree-7.3.1.tgz"
  "version" "7.3.1"
  dependencies:
    "acorn" "^7.4.0"
    "acorn-jsx" "^5.3.1"
    "eslint-visitor-keys" "^1.3.0"

"esprima@^4.0.0":
  "integrity" "sha1-E7BM2z5sXRnfkatph6hpVhmwqnE="
  "resolved" "https://registry.nlark.com/esprima/download/esprima-4.0.1.tgz"
  "version" "4.0.1"

"esquery@^1.4.0":
  "integrity" "sha1-IUj/w4uC6McFff7UhCWz5h8PJKU="
  "resolved" "https://registry.nlark.com/esquery/download/esquery-1.4.0.tgz"
  "version" "1.4.0"
  dependencies:
    "estraverse" "^5.1.0"

"esrecurse@^4.1.0", "esrecurse@^4.3.0":
  "integrity" "sha1-eteWTWeauyi+5yzsY3WLHF0smSE="
  "resolved" "https://registry.npm.taobao.org/esrecurse/download/esrecurse-4.3.0.tgz"
  "version" "4.3.0"
  dependencies:
    "estraverse" "^5.2.0"

"estraverse@^4.1.1":
  "integrity" "sha1-OYrT88WiSUi+dyXoPRGn3ijNvR0="
  "resolved" "https://registry.npm.taobao.org/estraverse/download/estraverse-4.3.0.tgz?cache=0&sync_timestamp=1596643087695&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Festraverse%2Fdownload%2Festraverse-4.3.0.tgz"
  "version" "4.3.0"

"estraverse@^5.1.0":
  "integrity" "sha1-MH30JUfmzHMk088DwVXVzbjFOIA="
  "resolved" "https://registry.npm.taobao.org/estraverse/download/estraverse-5.2.0.tgz?cache=0&sync_timestamp=1596643087695&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Festraverse%2Fdownload%2Festraverse-5.2.0.tgz"
  "version" "5.2.0"

"estraverse@^5.2.0":
  "integrity" "sha1-MH30JUfmzHMk088DwVXVzbjFOIA="
  "resolved" "https://registry.npm.taobao.org/estraverse/download/estraverse-5.2.0.tgz?cache=0&sync_timestamp=1596643087695&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Festraverse%2Fdownload%2Festraverse-5.2.0.tgz"
  "version" "5.2.0"

"esutils@^2.0.2":
  "integrity" "sha1-dNLrTeC42hKTcRkQ1Qd1ubcQ72Q="
  "resolved" "https://registry.npm.taobao.org/esutils/download/esutils-2.0.3.tgz"
  "version" "2.0.3"

"etag@~1.8.1":
  "integrity" "sha1-Qa4u62XvpiJorr/qg6x9eSmbCIc="
  "resolved" "https://registry.npm.taobao.org/etag/download/etag-1.8.1.tgz"
  "version" "1.8.1"

"eventemitter3@^4.0.0":
  "integrity" "sha1-Lem2j2Uo1WRO9cWVJqG0oHMGFp8="
  "resolved" "https://registry.npm.taobao.org/eventemitter3/download/eventemitter3-4.0.7.tgz?cache=0&sync_timestamp=1598517819668&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Feventemitter3%2Fdownload%2Feventemitter3-4.0.7.tgz"
  "version" "4.0.7"

"eventlistener@0.0.1":
  "integrity" "sha1-7Suqu4UiJ68rz4iRUscsY8pTLrg="
  "resolved" "https://registry.npm.taobao.org/eventlistener/download/eventlistener-0.0.1.tgz"
  "version" "0.0.1"

"events@^3.0.0":
  "integrity" "sha1-Mala0Kkk4tLEGagTrrLE6HjqdAA="
  "resolved" "https://registry.npm.taobao.org/events/download/events-3.3.0.tgz?cache=0&sync_timestamp=1614444838320&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fevents%2Fdownload%2Fevents-3.3.0.tgz"
  "version" "3.3.0"

"eventsource@^1.0.7":
  "integrity" "sha1-AOjKfJIQnpSw3fMtrGd9hBAoz68="
  "resolved" "https://registry.nlark.com/eventsource/download/eventsource-1.1.0.tgz"
  "version" "1.1.0"
  dependencies:
    "original" "^1.0.0"

"evp_bytestokey@^1.0.0", "evp_bytestokey@^1.0.3":
  "integrity" "sha1-f8vbGY3HGVlDLv4ThCaE4FJaywI="
  "resolved" "https://registry.nlark.com/evp_bytestokey/download/evp_bytestokey-1.0.3.tgz"
  "version" "1.0.3"
  dependencies:
    "md5.js" "^1.3.4"
    "safe-buffer" "^5.1.1"

"exceljs@^4.2.1":
  "integrity" "sha1-SddLq/yudPYbz8jplk8P7KCE2Rs="
  "resolved" "https://registry.npm.taobao.org/exceljs/download/exceljs-4.2.1.tgz"
  "version" "4.2.1"
  dependencies:
    "archiver" "^5.0.0"
    "dayjs" "^1.8.34"
    "fast-csv" "^4.3.1"
    "jszip" "^3.5.0"
    "readable-stream" "^3.6.0"
    "saxes" "^5.0.1"
    "tmp" "^0.2.0"
    "unzipper" "^0.10.11"
    "uuid" "^8.3.0"

"execa@^1.0.0":
  "integrity" "sha1-xiNqW7TfbW8V6I5/AXeYIWdJ3dg="
  "resolved" "https://registry.nlark.com/execa/download/execa-1.0.0.tgz"
  "version" "1.0.0"
  dependencies:
    "cross-spawn" "^6.0.0"
    "get-stream" "^4.0.0"
    "is-stream" "^1.1.0"
    "npm-run-path" "^2.0.0"
    "p-finally" "^1.0.0"
    "signal-exit" "^3.0.0"
    "strip-eof" "^1.0.0"

"exit-on-epipe@~1.0.1":
  "integrity" "sha1-C92S6H1ShdJn2qgXHQ6wYVlolpI="
  "resolved" "https://registry.npm.taobao.org/exit-on-epipe/download/exit-on-epipe-1.0.1.tgz"
  "version" "1.0.1"

"expand-brackets@^2.1.4":
  "integrity" "sha1-t3c14xXOMPa27/D4OwQVGiJEliI="
  "resolved" "https://registry.npm.taobao.org/expand-brackets/download/expand-brackets-2.1.4.tgz"
  "version" "2.1.4"
  dependencies:
    "debug" "^2.3.3"
    "define-property" "^0.2.5"
    "extend-shallow" "^2.0.1"
    "posix-character-classes" "^0.1.0"
    "regex-not" "^1.0.0"
    "snapdragon" "^0.8.1"
    "to-regex" "^3.0.1"

"expand-tilde@^2.0.0", "expand-tilde@^2.0.2":
  "integrity" "sha1-l+gBqgUt8CRU3kawK/YhZCzchQI="
  "resolved" "https://registry.npm.taobao.org/expand-tilde/download/expand-tilde-2.0.2.tgz"
  "version" "2.0.2"
  dependencies:
    "homedir-polyfill" "^1.0.1"

"express@^4.17.1":
  "integrity" "sha1-RJH8OGBc9R+GKdOcK10Cb5ikwTQ="
  "resolved" "https://registry.nlark.com/express/download/express-4.17.1.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fexpress%2Fdownload%2Fexpress-4.17.1.tgz"
  "version" "4.17.1"
  dependencies:
    "accepts" "~1.3.7"
    "array-flatten" "1.1.1"
    "body-parser" "1.19.0"
    "content-disposition" "0.5.3"
    "content-type" "~1.0.4"
    "cookie" "0.4.0"
    "cookie-signature" "1.0.6"
    "debug" "2.6.9"
    "depd" "~1.1.2"
    "encodeurl" "~1.0.2"
    "escape-html" "~1.0.3"
    "etag" "~1.8.1"
    "finalhandler" "~1.1.2"
    "fresh" "0.5.2"
    "merge-descriptors" "1.0.1"
    "methods" "~1.1.2"
    "on-finished" "~2.3.0"
    "parseurl" "~1.3.3"
    "path-to-regexp" "0.1.7"
    "proxy-addr" "~2.0.5"
    "qs" "6.7.0"
    "range-parser" "~1.2.1"
    "safe-buffer" "5.1.2"
    "send" "0.17.1"
    "serve-static" "1.14.1"
    "setprototypeof" "1.1.1"
    "statuses" "~1.5.0"
    "type-is" "~1.6.18"
    "utils-merge" "1.0.1"
    "vary" "~1.1.2"

"extend-shallow@^2.0.1":
  "integrity" "sha1-Ua99YUrZqfYQ6huvu5idaxxWiQ8="
  "resolved" "https://registry.nlark.com/extend-shallow/download/extend-shallow-2.0.1.tgz"
  "version" "2.0.1"
  dependencies:
    "is-extendable" "^0.1.0"

"extend-shallow@^3.0.0", "extend-shallow@^3.0.2":
  "integrity" "sha1-Jqcarwc7OfshJxcnRhMcJwQCjbg="
  "resolved" "https://registry.nlark.com/extend-shallow/download/extend-shallow-3.0.2.tgz"
  "version" "3.0.2"
  dependencies:
    "assign-symbols" "^1.0.0"
    "is-extendable" "^1.0.1"

"extend@~3.0.2":
  "integrity" "sha1-+LETa0Bx+9jrFAr/hYsQGewpFfo="
  "resolved" "https://registry.nlark.com/extend/download/extend-3.0.2.tgz"
  "version" "3.0.2"

"extglob@^2.0.4":
  "integrity" "sha1-rQD+TcYSqSMuhxhxHcXLWrAoVUM="
  "resolved" "https://registry.nlark.com/extglob/download/extglob-2.0.4.tgz"
  "version" "2.0.4"
  dependencies:
    "array-unique" "^0.3.2"
    "define-property" "^1.0.0"
    "expand-brackets" "^2.1.4"
    "extend-shallow" "^2.0.1"
    "fragment-cache" "^0.2.1"
    "regex-not" "^1.0.0"
    "snapdragon" "^0.8.1"
    "to-regex" "^3.0.1"

"extsprintf@^1.2.0", "extsprintf@1.3.0":
  "integrity" "sha1-lpGEQOMEGnpBT4xS48V06zw+HgU="
  "resolved" "https://registry.nlark.com/extsprintf/download/extsprintf-1.3.0.tgz"
  "version" "1.3.0"

"fast-csv@^4.3.1":
  "integrity" "sha1-cDSb3Y/k1msRMNjJGCC2SiG8SmM="
  "resolved" "https://registry.npm.taobao.org/fast-csv/download/fast-csv-4.3.6.tgz"
  "version" "4.3.6"
  dependencies:
    "@fast-csv/format" "4.3.5"
    "@fast-csv/parse" "4.3.6"

"fast-deep-equal@^3.1.1", "fast-deep-equal@^3.1.3":
  "integrity" "sha1-On1WtVnWy8PrUSMlJE5hmmXGxSU="
  "resolved" "https://registry.nlark.com/fast-deep-equal/download/fast-deep-equal-3.1.3.tgz"
  "version" "3.1.3"

"fast-diff@^1.1.2":
  "integrity" "sha1-c+4RmC2Gyq95WYKNUZz+kn+sXwM="
  "resolved" "https://registry.npm.taobao.org/fast-diff/download/fast-diff-1.2.0.tgz"
  "version" "1.2.0"

"fast-json-stable-stringify@^2.0.0":
  "integrity" "sha1-h0v2nG9ATCtdmcSBNBOZ/VWJJjM="
  "resolved" "https://registry.npm.taobao.org/fast-json-stable-stringify/download/fast-json-stable-stringify-2.1.0.tgz"
  "version" "2.1.0"

"fast-levenshtein@^2.0.6":
  "integrity" "sha1-PYpcZog6FqMMqGQ+hR8Zuqd5eRc="
  "resolved" "https://registry.npm.taobao.org/fast-levenshtein/download/fast-levenshtein-2.0.6.tgz"
  "version" "2.0.6"

"faye-websocket@^0.11.3":
  "integrity" "sha1-fw2Sdc/dhqHJY9yLZfzEUe3Lsdo="
  "resolved" "https://registry.nlark.com/faye-websocket/download/faye-websocket-0.11.4.tgz"
  "version" "0.11.4"
  dependencies:
    "websocket-driver" ">=0.5.1"

"fbjs@^0.8.15":
  "integrity" "sha1-xNWY6taUkRJlPWWIsBpc3Nn5D90="
  "resolved" "https://registry.npm.taobao.org/fbjs/download/fbjs-0.8.17.tgz?cache=0&sync_timestamp=1602048886093&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Ffbjs%2Fdownload%2Ffbjs-0.8.17.tgz"
  "version" "0.8.17"
  dependencies:
    "core-js" "^1.0.0"
    "isomorphic-fetch" "^2.1.1"
    "loose-envify" "^1.0.0"
    "object-assign" "^4.1.0"
    "promise" "^7.1.1"
    "setimmediate" "^1.0.5"
    "ua-parser-js" "^0.7.18"

"figgy-pudding@^3.5.1":
  "integrity" "sha1-tO7oFIq7Adzx0aw0Nn1Z4S+mHW4="
  "resolved" "https://registry.nlark.com/figgy-pudding/download/figgy-pudding-3.5.2.tgz"
  "version" "3.5.2"

"file-entry-cache@^6.0.1":
  "integrity" "sha1-IRst2WWcsDlLBz5zI6w8kz1SICc="
  "resolved" "https://registry.nlark.com/file-entry-cache/download/file-entry-cache-6.0.1.tgz"
  "version" "6.0.1"
  dependencies:
    "flat-cache" "^3.0.4"

"fill-range@^4.0.0":
  "integrity" "sha1-1USBHUKPmOsGpj3EAtJAPDKMOPc="
  "resolved" "https://registry.npm.taobao.org/fill-range/download/fill-range-4.0.0.tgz"
  "version" "4.0.0"
  dependencies:
    "extend-shallow" "^2.0.1"
    "is-number" "^3.0.0"
    "repeat-string" "^1.6.1"
    "to-regex-range" "^2.1.0"

"fill-range@^7.0.1":
  "integrity" "sha1-GRmmp8df44ssfHflGYU12prN2kA="
  "resolved" "https://registry.npm.taobao.org/fill-range/download/fill-range-7.0.1.tgz"
  "version" "7.0.1"
  dependencies:
    "to-regex-range" "^5.0.1"

"finalhandler@~1.1.2":
  "integrity" "sha1-t+fQAP/RGTjQ/bBTUG9uur6fWH0="
  "resolved" "https://registry.npm.taobao.org/finalhandler/download/finalhandler-1.1.2.tgz"
  "version" "1.1.2"
  dependencies:
    "debug" "2.6.9"
    "encodeurl" "~1.0.2"
    "escape-html" "~1.0.3"
    "on-finished" "~2.3.0"
    "parseurl" "~1.3.3"
    "statuses" "~1.5.0"
    "unpipe" "~1.0.0"

"find-cache-dir@^2.1.0":
  "integrity" "sha1-jQ+UzRP+Q8bHwmGg2GEVypGMBfc="
  "resolved" "https://registry.nlark.com/find-cache-dir/download/find-cache-dir-2.1.0.tgz"
  "version" "2.1.0"
  dependencies:
    "commondir" "^1.0.1"
    "make-dir" "^2.0.0"
    "pkg-dir" "^3.0.0"

"find-cache-dir@^3.0.0", "find-cache-dir@^3.3.1":
  "integrity" "sha1-ibM/rUpGcNqpT4Vff74x1thP6IA="
  "resolved" "https://registry.nlark.com/find-cache-dir/download/find-cache-dir-3.3.1.tgz"
  "version" "3.3.1"
  dependencies:
    "commondir" "^1.0.1"
    "make-dir" "^3.0.2"
    "pkg-dir" "^4.1.0"

"find-up@^1.0.0":
  "integrity" "sha1-ay6YIrGizgpgq2TWEOzK1TyyTQ8="
  "resolved" "https://registry.npm.taobao.org/find-up/download/find-up-1.1.2.tgz"
  "version" "1.1.2"
  dependencies:
    "path-exists" "^2.0.0"
    "pinkie-promise" "^2.0.0"

"find-up@^2.0.0":
  "integrity" "sha1-RdG35QbHF93UgndaK3eSCjwMV6c="
  "resolved" "https://registry.npm.taobao.org/find-up/download/find-up-2.1.0.tgz"
  "version" "2.1.0"
  dependencies:
    "locate-path" "^2.0.0"

"find-up@^2.1.0":
  "integrity" "sha1-RdG35QbHF93UgndaK3eSCjwMV6c="
  "resolved" "https://registry.npm.taobao.org/find-up/download/find-up-2.1.0.tgz"
  "version" "2.1.0"
  dependencies:
    "locate-path" "^2.0.0"

"find-up@^3.0.0":
  "integrity" "sha1-SRafHXmTQwZG2mHsxa41XCHJe3M="
  "resolved" "https://registry.npm.taobao.org/find-up/download/find-up-3.0.0.tgz"
  "version" "3.0.0"
  dependencies:
    "locate-path" "^3.0.0"

"find-up@^4.0.0":
  "integrity" "sha1-l6/n1s3AvFkoWEt8jXsW6KmqXRk="
  "resolved" "https://registry.npm.taobao.org/find-up/download/find-up-4.1.0.tgz"
  "version" "4.1.0"
  dependencies:
    "locate-path" "^5.0.0"
    "path-exists" "^4.0.0"

"findup-sync@^3.0.0":
  "integrity" "sha1-F7EI+e5RLft6XH88iyfqnhqcCNE="
  "resolved" "https://registry.npm.taobao.org/findup-sync/download/findup-sync-3.0.0.tgz"
  "version" "3.0.0"
  dependencies:
    "detect-file" "^1.0.0"
    "is-glob" "^4.0.0"
    "micromatch" "^3.0.4"
    "resolve-dir" "^1.0.1"

"flat-cache@^3.0.4":
  "integrity" "sha1-YbAzgwKy/p+Vfcwy/CqH8cMEixE="
  "resolved" "https://registry.npm.taobao.org/flat-cache/download/flat-cache-3.0.4.tgz"
  "version" "3.0.4"
  dependencies:
    "flatted" "^3.1.0"
    "rimraf" "^3.0.2"

"flatted@^3.1.0":
  "integrity" "sha1-xLSJ6ACW2d8d/JfHmHGup8YXxGk="
  "resolved" "https://registry.npm.taobao.org/flatted/download/flatted-3.1.1.tgz?cache=0&sync_timestamp=1611059462226&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fflatted%2Fdownload%2Fflatted-3.1.1.tgz"
  "version" "3.1.1"

"flush-write-stream@^1.0.0":
  "integrity" "sha1-jdfYc6G6vCB9lOrQwuDkQnbr8ug="
  "resolved" "https://registry.npm.taobao.org/flush-write-stream/download/flush-write-stream-1.1.1.tgz"
  "version" "1.1.1"
  dependencies:
    "inherits" "^2.0.3"
    "readable-stream" "^2.3.6"

"follow-redirects@^1.0.0":
  "integrity" "sha1-2RFN7Qoc/dM04WTmZirQK/2R/0M="
  "resolved" "https://registry.nlark.com/follow-redirects/download/follow-redirects-1.14.1.tgz"
  "version" "1.14.1"

"for-in@^1.0.2":
  "integrity" "sha1-gQaNKVqBQuwKxybG4iAMMPttXoA="
  "resolved" "https://registry.nlark.com/for-in/download/for-in-1.0.2.tgz"
  "version" "1.0.2"

"forever-agent@~0.6.1":
  "integrity" "sha1-+8cfDEGt6zf5bFd60e1C2P2sypE="
  "resolved" "https://registry.nlark.com/forever-agent/download/forever-agent-0.6.1.tgz"
  "version" "0.6.1"

"form-data@~2.3.2":
  "integrity" "sha1-3M5SwF9kTymManq5Nr1yTO/786Y="
  "resolved" "https://registry.nlark.com/form-data/download/form-data-2.3.3.tgz"
  "version" "2.3.3"
  dependencies:
    "asynckit" "^0.4.0"
    "combined-stream" "^1.0.6"
    "mime-types" "^2.1.12"

"forwarded@0.2.0":
  "integrity" "sha1-ImmTZCiq1MFcfr6XeahL8LKoGBE="
  "resolved" "https://registry.nlark.com/forwarded/download/forwarded-0.2.0.tgz?cache=0&sync_timestamp=1622503508967&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fforwarded%2Fdownload%2Fforwarded-0.2.0.tgz"
  "version" "0.2.0"

"fragment-cache@^0.2.1":
  "integrity" "sha1-QpD60n8T6Jvn8zeZxrxaCr//DRk="
  "resolved" "https://registry.nlark.com/fragment-cache/download/fragment-cache-0.2.1.tgz"
  "version" "0.2.1"
  dependencies:
    "map-cache" "^0.2.2"

"fresh@0.5.2":
  "integrity" "sha1-PYyt2Q2XZWn6g1qx+OSyOhBWBac="
  "resolved" "https://registry.npm.taobao.org/fresh/download/fresh-0.5.2.tgz"
  "version" "0.5.2"

"friendly-errors-webpack-plugin@^1.7.0":
  "integrity" "sha1-78hsu4FiJFZYYaG+ep2E0Kr+oTY="
  "resolved" "https://registry.nlark.com/friendly-errors-webpack-plugin/download/friendly-errors-webpack-plugin-1.7.0.tgz"
  "version" "1.7.0"
  dependencies:
    "chalk" "^1.1.3"
    "error-stack-parser" "^2.0.0"
    "string-width" "^2.0.0"

"from2@^2.1.0":
  "integrity" "sha1-i/tVAr3kpNNs/e6gB/zKIdfjgq8="
  "resolved" "https://registry.nlark.com/from2/download/from2-2.3.0.tgz"
  "version" "2.3.0"
  dependencies:
    "inherits" "^2.0.1"
    "readable-stream" "^2.0.0"

"fs-constants@^1.0.0":
  "integrity" "sha1-a+Dem+mYzhavivwkSXue6bfM2a0="
  "resolved" "https://registry.npm.taobao.org/fs-constants/download/fs-constants-1.0.0.tgz"
  "version" "1.0.0"

"fs-minipass@^2.0.0":
  "integrity" "sha1-f1A2/b8SxjwWkZDL5BmchSJx+fs="
  "resolved" "https://registry.npm.taobao.org/fs-minipass/download/fs-minipass-2.1.0.tgz"
  "version" "2.1.0"
  dependencies:
    "minipass" "^3.0.0"

"fs-write-stream-atomic@^1.0.8":
  "integrity" "sha1-tH31NJPvkR33VzHnCp3tAYnbQMk="
  "resolved" "https://registry.npm.taobao.org/fs-write-stream-atomic/download/fs-write-stream-atomic-1.0.10.tgz"
  "version" "1.0.10"
  dependencies:
    "graceful-fs" "^4.1.2"
    "iferr" "^0.1.5"
    "imurmurhash" "^0.1.4"
    "readable-stream" "1 || 2"

"fs.realpath@^1.0.0":
  "integrity" "sha1-FQStJSMVjKpA20onh8sBQRmU6k8="
  "resolved" "https://registry.nlark.com/fs.realpath/download/fs.realpath-1.0.0.tgz"
  "version" "1.0.0"

"fstream@^1.0.0", "fstream@^1.0.12":
  "integrity" "sha1-Touo7i1Ivk99DeUFRVVI6uWTIEU="
  "resolved" "https://registry.npm.taobao.org/fstream/download/fstream-1.0.12.tgz"
  "version" "1.0.12"
  dependencies:
    "graceful-fs" "^4.1.2"
    "inherits" "~2.0.0"
    "mkdirp" ">=0.5 0"
    "rimraf" "2"

"function-bind@^1.1.1":
  "integrity" "sha1-pWiZ0+o8m6uHS7l3O3xe3pL0iV0="
  "resolved" "https://registry.nlark.com/function-bind/download/function-bind-1.1.1.tgz"
  "version" "1.1.1"

"functional-red-black-tree@^1.0.1":
  "integrity" "sha1-GwqzvVU7Kg1jmdKcDj6gslIHgyc="
  "resolved" "https://registry.npm.taobao.org/functional-red-black-tree/download/functional-red-black-tree-1.0.1.tgz?cache=0&sync_timestamp=1577806294691&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Ffunctional-red-black-tree%2Fdownload%2Ffunctional-red-black-tree-1.0.1.tgz"
  "version" "1.0.1"

"gauge@~2.7.3":
  "integrity" "sha1-LANAXHU4w51+s3sxcCLjJfsBi/c="
  "resolved" "https://registry.npm.taobao.org/gauge/download/gauge-2.7.4.tgz"
  "version" "2.7.4"
  dependencies:
    "aproba" "^1.0.3"
    "console-control-strings" "^1.0.0"
    "has-unicode" "^2.0.0"
    "object-assign" "^4.1.0"
    "signal-exit" "^3.0.0"
    "string-width" "^1.0.1"
    "strip-ansi" "^3.0.1"
    "wide-align" "^1.1.0"

"gaze@^1.0.0":
  "integrity" "sha1-xEFzPhO5J6yMD/C0w7Az8ogSkko="
  "resolved" "https://registry.npm.taobao.org/gaze/download/gaze-1.1.3.tgz"
  "version" "1.1.3"
  dependencies:
    "globule" "^1.0.0"

"gensync@^1.0.0-beta.2":
  "integrity" "sha1-MqbudsPX9S1GsrGuXZP+qFgKJeA="
  "resolved" "https://registry.nlark.com/gensync/download/gensync-1.0.0-beta.2.tgz"
  "version" "1.0.0-beta.2"

"get-caller-file@^2.0.1", "get-caller-file@^2.0.5":
  "integrity" "sha1-T5RBKoLbMvNuOwuXQfipf+sDH34="
  "resolved" "https://registry.npm.taobao.org/get-caller-file/download/get-caller-file-2.0.5.tgz"
  "version" "2.0.5"

"get-intrinsic@^1.0.2", "get-intrinsic@^1.1.0", "get-intrinsic@^1.1.1":
  "integrity" "sha1-FfWfN2+FXERpY5SPDSTNNje0q8Y="
  "resolved" "https://registry.npm.taobao.org/get-intrinsic/download/get-intrinsic-1.1.1.tgz"
  "version" "1.1.1"
  dependencies:
    "function-bind" "^1.1.1"
    "has" "^1.0.3"
    "has-symbols" "^1.0.1"

"get-stdin@^4.0.1":
  "integrity" "sha1-uWjGsKBDhDJJAui/Gl3zJXmkUP4="
  "resolved" "https://registry.npm.taobao.org/get-stdin/download/get-stdin-4.0.1.tgz?cache=0&sync_timestamp=1618557719783&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fget-stdin%2Fdownload%2Fget-stdin-4.0.1.tgz"
  "version" "4.0.1"

"get-stdin@^6.0.0":
  "integrity" "sha1-ngm/cSs2CrkiXoEgSPcf3pyJZXs="
  "resolved" "https://registry.npm.taobao.org/get-stdin/download/get-stdin-6.0.0.tgz?cache=0&sync_timestamp=1618557719783&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fget-stdin%2Fdownload%2Fget-stdin-6.0.0.tgz"
  "version" "6.0.0"

"get-stream@^4.0.0":
  "integrity" "sha1-wbJVV189wh1Zv8ec09K0axw6VLU="
  "resolved" "https://registry.npm.taobao.org/get-stream/download/get-stream-4.1.0.tgz"
  "version" "4.1.0"
  dependencies:
    "pump" "^3.0.0"

"get-value@^2.0.3", "get-value@^2.0.6":
  "integrity" "sha1-3BXKHGcjh8p2vTesCjlbogQqLCg="
  "resolved" "https://registry.npm.taobao.org/get-value/download/get-value-2.0.6.tgz"
  "version" "2.0.6"

"getpass@^0.1.1":
  "integrity" "sha1-Xv+OPmhNVprkyysSgmBOi6YhSfo="
  "resolved" "https://registry.nlark.com/getpass/download/getpass-0.1.7.tgz"
  "version" "0.1.7"
  dependencies:
    "assert-plus" "^1.0.0"

"glob-parent@^3.1.0":
  "integrity" "sha1-nmr2KZ2NO9K9QEMIMr0RPfkGxa4="
  "resolved" "https://registry.nlark.com/glob-parent/download/glob-parent-3.1.0.tgz"
  "version" "3.1.0"
  dependencies:
    "is-glob" "^3.1.0"
    "path-dirname" "^1.0.0"

"glob-parent@^5.1.2":
  "integrity" "sha1-hpgyxYA0/mikCTwX3BXoNA2EAcQ="
  "resolved" "https://registry.nlark.com/glob-parent/download/glob-parent-5.1.2.tgz"
  "version" "5.1.2"
  dependencies:
    "is-glob" "^4.0.1"

"glob-parent@~5.1.2":
  "integrity" "sha1-hpgyxYA0/mikCTwX3BXoNA2EAcQ="
  "resolved" "https://registry.nlark.com/glob-parent/download/glob-parent-5.1.2.tgz"
  "version" "5.1.2"
  dependencies:
    "is-glob" "^4.0.1"

"glob@^7.0.0", "glob@^7.0.3", "glob@^7.1.2", "glob@^7.1.3", "glob@^7.1.4", "glob@^7.1.6", "glob@~7.1.1":
  "integrity" "sha1-Oxk+kjPwHULQs/eClLvutBj5SpA="
  "resolved" "https://registry.nlark.com/glob/download/glob-7.1.7.tgz?cache=0&sync_timestamp=1620337382269&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fglob%2Fdownload%2Fglob-7.1.7.tgz"
  "version" "7.1.7"
  dependencies:
    "fs.realpath" "^1.0.0"
    "inflight" "^1.0.4"
    "inherits" "2"
    "minimatch" "^3.0.4"
    "once" "^1.3.0"
    "path-is-absolute" "^1.0.0"

"global-modules@^1.0.0":
  "integrity" "sha1-bXcPDrUjrHgWTXK15xqIdyZcw+o="
  "resolved" "https://registry.nlark.com/global-modules/download/global-modules-1.0.0.tgz"
  "version" "1.0.0"
  dependencies:
    "global-prefix" "^1.0.1"
    "is-windows" "^1.0.1"
    "resolve-dir" "^1.0.0"

"global-modules@^2.0.0":
  "integrity" "sha1-mXYFrSNF8n9RU5vqJldEISFcd4A="
  "resolved" "https://registry.nlark.com/global-modules/download/global-modules-2.0.0.tgz"
  "version" "2.0.0"
  dependencies:
    "global-prefix" "^3.0.0"

"global-prefix@^1.0.1":
  "integrity" "sha1-2/dDxsFJklk8ZVVoy2btMsASLr4="
  "resolved" "https://registry.nlark.com/global-prefix/download/global-prefix-1.0.2.tgz"
  "version" "1.0.2"
  dependencies:
    "expand-tilde" "^2.0.2"
    "homedir-polyfill" "^1.0.1"
    "ini" "^1.3.4"
    "is-windows" "^1.0.1"
    "which" "^1.2.14"

"global-prefix@^3.0.0":
  "integrity" "sha1-/IX3MGTfafUEIfR/iD/luRO6m5c="
  "resolved" "https://registry.nlark.com/global-prefix/download/global-prefix-3.0.0.tgz"
  "version" "3.0.0"
  dependencies:
    "ini" "^1.3.5"
    "kind-of" "^6.0.2"
    "which" "^1.3.1"

"global@^4.3.0":
  "integrity" "sha1-PnsQUXkAajI+1xqvyj6cV6XMZAY="
  "resolved" "https://registry.npm.taobao.org/global/download/global-4.4.0.tgz"
  "version" "4.4.0"
  dependencies:
    "min-document" "^2.19.0"
    "process" "^0.11.10"

"globals@^11.1.0":
  "integrity" "sha1-q4eVM4hooLq9hSV1gBjCp+uVxC4="
  "resolved" "https://registry.nlark.com/globals/download/globals-11.12.0.tgz"
  "version" "11.12.0"

"globals@^13.6.0":
  "integrity" "sha1-S/K/Y1szShc/sdr3xeayGOzcBss="
  "resolved" "https://registry.nlark.com/globals/download/globals-13.9.0.tgz"
  "version" "13.9.0"
  dependencies:
    "type-fest" "^0.20.2"

"globals@^13.9.0":
  "integrity" "sha1-S/K/Y1szShc/sdr3xeayGOzcBss="
  "resolved" "https://registry.nlark.com/globals/download/globals-13.9.0.tgz"
  "version" "13.9.0"
  dependencies:
    "type-fest" "^0.20.2"

"globby@^6.1.0":
  "integrity" "sha1-9abXDoOV4hyFj7BInWTfAkJNUGw="
  "resolved" "https://registry.nlark.com/globby/download/globby-6.1.0.tgz?cache=0&sync_timestamp=1623850192942&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fglobby%2Fdownload%2Fglobby-6.1.0.tgz"
  "version" "6.1.0"
  dependencies:
    "array-union" "^1.0.1"
    "glob" "^7.0.3"
    "object-assign" "^4.0.1"
    "pify" "^2.0.0"
    "pinkie-promise" "^2.0.0"

"globby@^7.1.1":
  "integrity" "sha1-+yzP+UAfhgCUXfral0QMypcrhoA="
  "resolved" "https://registry.nlark.com/globby/download/globby-7.1.1.tgz?cache=0&sync_timestamp=1623850192942&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fglobby%2Fdownload%2Fglobby-7.1.1.tgz"
  "version" "7.1.1"
  dependencies:
    "array-union" "^1.0.1"
    "dir-glob" "^2.0.0"
    "glob" "^7.1.2"
    "ignore" "^3.3.5"
    "pify" "^3.0.0"
    "slash" "^1.0.0"

"globule@^1.0.0":
  "integrity" "sha1-2L3Z6eTu+PluJFmZpd7n612FKcQ="
  "resolved" "https://registry.nlark.com/globule/download/globule-1.3.2.tgz"
  "version" "1.3.2"
  dependencies:
    "glob" "~7.1.1"
    "lodash" "~4.17.10"
    "minimatch" "~3.0.2"

"good-listener@^1.2.2":
  "integrity" "sha1-1TswzfkxPf+33JoNR3CWqm0UXFA="
  "resolved" "https://registry.nlark.com/good-listener/download/good-listener-1.2.2.tgz"
  "version" "1.2.2"
  dependencies:
    "delegate" "^3.1.2"

"graceful-fs@^4.1.11", "graceful-fs@^4.1.15", "graceful-fs@^4.1.2", "graceful-fs@^4.2.0", "graceful-fs@^4.2.2":
  "integrity" "sha1-/wQLKwhTsjw9MQJ1I3BvGIXXa+4="
  "resolved" "https://registry.nlark.com/graceful-fs/download/graceful-fs-4.2.6.tgz"
  "version" "4.2.6"

"gud@^1.0.0":
  "integrity" "sha1-pIlYGxfmpwvsqavjrlfeekmYUsA="
  "resolved" "https://registry.npm.taobao.org/gud/download/gud-1.0.0.tgz"
  "version" "1.0.0"

"hammerjs@^2.0.8":
  "integrity" "sha1-BO93hiz/K7edMPdpIJWTAiK/YPE="
  "resolved" "https://registry.npm.taobao.org/hammerjs/download/hammerjs-2.0.8.tgz"
  "version" "2.0.8"

"handle-thing@^2.0.0":
  "integrity" "sha1-hX95zjWVgMNA1DCBzGSJcNC7I04="
  "resolved" "https://registry.nlark.com/handle-thing/download/handle-thing-2.0.1.tgz"
  "version" "2.0.1"

"happypack@^5.0.1":
  "integrity" "sha1-hQg5Qm1iBaUgv5E+liNJ++UjoHw="
  "resolved" "https://registry.npm.taobao.org/happypack/download/happypack-5.0.1.tgz"
  "version" "5.0.1"
  dependencies:
    "async" "1.5.0"
    "json-stringify-safe" "5.0.1"
    "loader-utils" "1.1.0"
    "serialize-error" "^2.1.0"

"har-schema@^2.0.0":
  "integrity" "sha1-qUwiJOvKwEeCoNkDVSHyRzW37JI="
  "resolved" "https://registry.npm.taobao.org/har-schema/download/har-schema-2.0.0.tgz"
  "version" "2.0.0"

"har-validator@~5.1.3":
  "integrity" "sha1-HwgDufjLIMD6E4It8ezds2veHv0="
  "resolved" "https://registry.nlark.com/har-validator/download/har-validator-5.1.5.tgz"
  "version" "5.1.5"
  dependencies:
    "ajv" "^6.12.3"
    "har-schema" "^2.0.0"

"has-ansi@^2.0.0":
  "integrity" "sha1-NPUEnOHs3ysGSa8+8k5F7TVBbZE="
  "resolved" "https://registry.nlark.com/has-ansi/download/has-ansi-2.0.0.tgz"
  "version" "2.0.0"
  dependencies:
    "ansi-regex" "^2.0.0"

"has-bigints@^1.0.1":
  "integrity" "sha1-ZP5qywIGc+O3jbA1pa9pqp0HsRM="
  "resolved" "https://registry.nlark.com/has-bigints/download/has-bigints-1.0.1.tgz"
  "version" "1.0.1"

"has-flag@^3.0.0":
  "integrity" "sha1-tdRU3CGZriJWmfNGfloH87lVuv0="
  "resolved" "https://registry.npm.taobao.org/has-flag/download/has-flag-3.0.0.tgz"
  "version" "3.0.0"

"has-flag@^4.0.0":
  "integrity" "sha512-EykJT/Q1KjTWctppgIAgfSO0tKVuZUjhgMr17kqTumMl6Afv3EISleU7qZUzoXDFTAHTDC4NOoG/ZxU3EvlMPQ=="
  "resolved" "https://registry.npmmirror.com/has-flag/-/has-flag-4.0.0.tgz"
  "version" "4.0.0"

"has-symbols@^1.0.1", "has-symbols@^1.0.2":
  "integrity" "sha1-Fl0wcMADCXUqEjakeTMeOsVvFCM="
  "resolved" "https://registry.npm.taobao.org/has-symbols/download/has-symbols-1.0.2.tgz?cache=0&sync_timestamp=1614443557459&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fhas-symbols%2Fdownload%2Fhas-symbols-1.0.2.tgz"
  "version" "1.0.2"

"has-unicode@^2.0.0":
  "integrity" "sha1-4Ob+aijPUROIVeCG0Wkedx3iqLk="
  "resolved" "https://registry.npm.taobao.org/has-unicode/download/has-unicode-2.0.1.tgz"
  "version" "2.0.1"

"has-value@^0.3.1":
  "integrity" "sha1-ex9YutpiyoJ+wKIHgCVlSEWZXh8="
  "resolved" "https://registry.nlark.com/has-value/download/has-value-0.3.1.tgz"
  "version" "0.3.1"
  dependencies:
    "get-value" "^2.0.3"
    "has-values" "^0.1.4"
    "isobject" "^2.0.0"

"has-value@^1.0.0":
  "integrity" "sha1-GLKB2lhbHFxR3vJMkw7SmgvmsXc="
  "resolved" "https://registry.nlark.com/has-value/download/has-value-1.0.0.tgz"
  "version" "1.0.0"
  dependencies:
    "get-value" "^2.0.6"
    "has-values" "^1.0.0"
    "isobject" "^3.0.0"

"has-values@^0.1.4":
  "integrity" "sha1-bWHeldkd/Km5oCCJrThL/49it3E="
  "resolved" "https://registry.nlark.com/has-values/download/has-values-0.1.4.tgz"
  "version" "0.1.4"

"has-values@^1.0.0":
  "integrity" "sha1-lbC2P+whRmGab+V/51Yo1aOe/k8="
  "resolved" "https://registry.nlark.com/has-values/download/has-values-1.0.0.tgz"
  "version" "1.0.0"
  dependencies:
    "is-number" "^3.0.0"
    "kind-of" "^4.0.0"

"has@^1.0.0", "has@^1.0.3":
  "integrity" "sha1-ci18v8H2qoJB8W3YFOAR4fQeh5Y="
  "resolved" "https://registry.nlark.com/has/download/has-1.0.3.tgz"
  "version" "1.0.3"
  dependencies:
    "function-bind" "^1.1.1"

"hash-base@^3.0.0":
  "integrity" "sha1-VcOB2eBuHSmXqIO0o/3f5/DTrzM="
  "resolved" "https://registry.npm.taobao.org/hash-base/download/hash-base-3.1.0.tgz"
  "version" "3.1.0"
  dependencies:
    "inherits" "^2.0.4"
    "readable-stream" "^3.6.0"
    "safe-buffer" "^5.2.0"

"hash.js@^1.0.0", "hash.js@^1.0.3":
  "integrity" "sha1-C6vKU46NTuSg+JiNaIZlN6ADz0I="
  "resolved" "https://registry.npm.taobao.org/hash.js/download/hash.js-1.1.7.tgz"
  "version" "1.1.7"
  dependencies:
    "inherits" "^2.0.3"
    "minimalistic-assert" "^1.0.1"

"he@1.2.x":
  "integrity" "sha1-hK5l+n6vsWX922FWauFLrwVmTw8="
  "resolved" "https://registry.nlark.com/he/download/he-1.2.0.tgz"
  "version" "1.2.0"

"hex-color-regex@^1.1.0":
  "integrity" "sha1-TAb8y0YC/iYCs8k9+C1+fb8aio4="
  "resolved" "https://registry.npm.taobao.org/hex-color-regex/download/hex-color-regex-1.1.0.tgz"
  "version" "1.1.0"

"history@^4.9.0":
  "integrity" "sha1-MzcaZeOoOyZ0NOKz87G0xYqtTPM="
  "resolved" "https://registry.nlark.com/history/download/history-4.10.1.tgz"
  "version" "4.10.1"
  dependencies:
    "@babel/runtime" "^7.1.2"
    "loose-envify" "^1.2.0"
    "resolve-pathname" "^3.0.0"
    "tiny-invariant" "^1.0.2"
    "tiny-warning" "^1.0.0"
    "value-equal" "^1.0.1"

"hmac-drbg@^1.0.1":
  "integrity" "sha1-0nRXAQJabHdabFRXk+1QL8DGSaE="
  "resolved" "https://registry.npm.taobao.org/hmac-drbg/download/hmac-drbg-1.0.1.tgz"
  "version" "1.0.1"
  dependencies:
    "hash.js" "^1.0.3"
    "minimalistic-assert" "^1.0.0"
    "minimalistic-crypto-utils" "^1.0.1"

"hoist-non-react-statics@^2.3.1":
  "integrity" "sha1-xZA89AnA39kI84jmGdhrnBF0y0c="
  "resolved" "https://registry.nlark.com/hoist-non-react-statics/download/hoist-non-react-statics-2.5.5.tgz?cache=0&sync_timestamp=1618847090932&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fhoist-non-react-statics%2Fdownload%2Fhoist-non-react-statics-2.5.5.tgz"
  "version" "2.5.5"

"hoist-non-react-statics@^3.1.0", "hoist-non-react-statics@^3.3.0", "hoist-non-react-statics@^3.3.2":
  "integrity" "sha1-7OCsr3HWLClpwuxZ/v9CpLGoW0U="
  "resolved" "https://registry.nlark.com/hoist-non-react-statics/download/hoist-non-react-statics-3.3.2.tgz?cache=0&sync_timestamp=1618847090932&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fhoist-non-react-statics%2Fdownload%2Fhoist-non-react-statics-3.3.2.tgz"
  "version" "3.3.2"
  dependencies:
    "react-is" "^16.7.0"

"homedir-polyfill@^1.0.1":
  "integrity" "sha1-dDKYzvTlrz4ZQWH7rcwhUdOgWOg="
  "resolved" "https://registry.nlark.com/homedir-polyfill/download/homedir-polyfill-1.0.3.tgz"
  "version" "1.0.3"
  dependencies:
    "parse-passwd" "^1.0.0"

"hosted-git-info@^2.1.4":
  "integrity" "sha1-3/wL+aIcAiCQkPKqaUKeFBTa8/k="
  "resolved" "https://registry.nlark.com/hosted-git-info/download/hosted-git-info-2.8.9.tgz"
  "version" "2.8.9"

"hpack.js@^2.1.6":
  "integrity" "sha1-h3dMCUnlE/QuhFdbPEVoH63ioLI="
  "resolved" "https://registry.npm.taobao.org/hpack.js/download/hpack.js-2.1.6.tgz"
  "version" "2.1.6"
  dependencies:
    "inherits" "^2.0.1"
    "obuf" "^1.0.0"
    "readable-stream" "^2.0.1"
    "wbuf" "^1.1.0"

"hsl-regex@^1.0.0":
  "integrity" "sha1-1JMwx4ntgZ4nakwNJy3/owsY/m4="
  "resolved" "https://registry.nlark.com/hsl-regex/download/hsl-regex-1.0.0.tgz"
  "version" "1.0.0"

"hsla-regex@^1.0.0":
  "integrity" "sha1-wc56MWjIxmFAM6S194d/OyJfnDg="
  "resolved" "https://registry.npm.taobao.org/hsla-regex/download/hsla-regex-1.0.0.tgz"
  "version" "1.0.0"

"html-entities@^1.3.1":
  "integrity" "sha1-z70bAdKvr5rcobEK59/6uYxx0tw="
  "resolved" "https://registry.nlark.com/html-entities/download/html-entities-1.4.0.tgz"
  "version" "1.4.0"

"html-minifier@^3.2.3":
  "integrity" "sha1-0AQOBUcw41TbAIRjWTGUAVIS0gw="
  "resolved" "https://registry.npm.taobao.org/html-minifier/download/html-minifier-3.5.21.tgz"
  "version" "3.5.21"
  dependencies:
    "camel-case" "3.0.x"
    "clean-css" "4.2.x"
    "commander" "2.17.x"
    "he" "1.2.x"
    "param-case" "2.1.x"
    "relateurl" "0.2.x"
    "uglify-js" "3.4.x"

"html-webpack-plugin@^3.2.0":
  "integrity" "sha1-sBq71yOsqqeze2r0SS69oD2d03s="
  "resolved" "https://registry.nlark.com/html-webpack-plugin/download/html-webpack-plugin-3.2.0.tgz"
  "version" "3.2.0"
  dependencies:
    "html-minifier" "^3.2.3"
    "loader-utils" "^0.2.16"
    "lodash" "^4.17.3"
    "pretty-error" "^2.0.2"
    "tapable" "^1.0.0"
    "toposort" "^1.0.0"
    "util.promisify" "1.0.0"

"htmlparser2@^6.1.0":
  "integrity" "sha1-xNditsM3GgXb5l6UrkOp+EX7j7c="
  "resolved" "https://registry.nlark.com/htmlparser2/download/htmlparser2-6.1.0.tgz"
  "version" "6.1.0"
  dependencies:
    "domelementtype" "^2.0.1"
    "domhandler" "^4.0.0"
    "domutils" "^2.5.2"
    "entities" "^2.0.0"

"http-deceiver@^1.2.7":
  "integrity" "sha1-+nFolEq5pRnTN8sL7HKE3D5yPYc="
  "resolved" "https://registry.npm.taobao.org/http-deceiver/download/http-deceiver-1.2.7.tgz"
  "version" "1.2.7"

"http-errors@~1.6.2":
  "integrity" "sha1-i1VoC7S+KDoLW/TqLjhYC+HZMg0="
  "resolved" "https://registry.npm.taobao.org/http-errors/download/http-errors-1.6.3.tgz?cache=0&sync_timestamp=1593407634112&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fhttp-errors%2Fdownload%2Fhttp-errors-1.6.3.tgz"
  "version" "1.6.3"
  dependencies:
    "depd" "~1.1.2"
    "inherits" "2.0.3"
    "setprototypeof" "1.1.0"
    "statuses" ">= 1.4.0 < 2"

"http-errors@~1.7.2", "http-errors@1.7.2":
  "integrity" "sha1-T1ApzxMjnzEDblsuVSkrz7zIXI8="
  "resolved" "https://registry.npm.taobao.org/http-errors/download/http-errors-1.7.2.tgz?cache=0&sync_timestamp=1593407634112&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fhttp-errors%2Fdownload%2Fhttp-errors-1.7.2.tgz"
  "version" "1.7.2"
  dependencies:
    "depd" "~1.1.2"
    "inherits" "2.0.3"
    "setprototypeof" "1.1.1"
    "statuses" ">= 1.5.0 < 2"
    "toidentifier" "1.0.0"

"http-parser-js@>=0.5.1":
  "integrity" "sha1-AdJwnHnUFpi7AdTezF6dpOSgM9k="
  "resolved" "https://registry.npm.taobao.org/http-parser-js/download/http-parser-js-0.5.3.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fhttp-parser-js%2Fdownload%2Fhttp-parser-js-0.5.3.tgz"
  "version" "0.5.3"

"http-proxy-middleware@0.19.1":
  "integrity" "sha1-GDx9xKoUeRUDBkmMIQza+WCApDo="
  "resolved" "https://registry.nlark.com/http-proxy-middleware/download/http-proxy-middleware-0.19.1.tgz"
  "version" "0.19.1"
  dependencies:
    "http-proxy" "^1.17.0"
    "is-glob" "^4.0.0"
    "lodash" "^4.17.11"
    "micromatch" "^3.1.10"

"http-proxy@^1.17.0":
  "integrity" "sha1-QBVB8FNIhLv5UmAzTnL4juOXZUk="
  "resolved" "https://registry.npm.taobao.org/http-proxy/download/http-proxy-1.18.1.tgz"
  "version" "1.18.1"
  dependencies:
    "eventemitter3" "^4.0.0"
    "follow-redirects" "^1.0.0"
    "requires-port" "^1.0.0"

"http-signature@~1.2.0":
  "integrity" "sha1-muzZJRFHcvPZW2WmCruPfBj7rOE="
  "resolved" "https://registry.npm.taobao.org/http-signature/download/http-signature-1.2.0.tgz?cache=0&sync_timestamp=1600868555829&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fhttp-signature%2Fdownload%2Fhttp-signature-1.2.0.tgz"
  "version" "1.2.0"
  dependencies:
    "assert-plus" "^1.0.0"
    "jsprim" "^1.2.2"
    "sshpk" "^1.7.0"

"https-browserify@^1.0.0":
  "integrity" "sha1-7AbBDgo0wPL68Zn3/X/Hj//QPHM="
  "resolved" "https://registry.nlark.com/https-browserify/download/https-browserify-1.0.0.tgz"
  "version" "1.0.0"

"iconv-lite@^0.6.2":
  "integrity" "sha1-pS+AvzjaGVLrXGgXkHGYcaGnJQE="
  "resolved" "https://registry.nlark.com/iconv-lite/download/iconv-lite-0.6.3.tgz?cache=0&sync_timestamp=1621826342262&other_urls=https%3A%2F%2Fregistry.nlark.com%2Ficonv-lite%2Fdownload%2Ficonv-lite-0.6.3.tgz"
  "version" "0.6.3"
  dependencies:
    "safer-buffer" ">= 2.1.2 < 3.0.0"

"iconv-lite@0.4.24":
  "integrity" "sha1-ICK0sl+93CHS9SSXSkdKr+czkIs="
  "resolved" "https://registry.nlark.com/iconv-lite/download/iconv-lite-0.4.24.tgz?cache=0&sync_timestamp=1621826342262&other_urls=https%3A%2F%2Fregistry.nlark.com%2Ficonv-lite%2Fdownload%2Ficonv-lite-0.4.24.tgz"
  "version" "0.4.24"
  dependencies:
    "safer-buffer" ">= 2.1.2 < 3"

"icss-utils@^4.0.0", "icss-utils@^4.1.1":
  "integrity" "sha1-IRcLU3ie4nRHwvR91oMIFAP5pGc="
  "resolved" "https://registry.nlark.com/icss-utils/download/icss-utils-4.1.1.tgz"
  "version" "4.1.1"
  dependencies:
    "postcss" "^7.0.14"

"ieee754@^1.1.13", "ieee754@^1.1.4":
  "integrity" "sha1-jrehCmP/8l0VpXsAFYbRd9Gw01I="
  "resolved" "https://registry.nlark.com/ieee754/download/ieee754-1.2.1.tgz"
  "version" "1.2.1"

"iferr@^0.1.5":
  "integrity" "sha1-xg7taebY/bazEEofy8ocGS3FtQE="
  "resolved" "https://registry.nlark.com/iferr/download/iferr-0.1.5.tgz"
  "version" "0.1.5"

"ignore@^3.3.5":
  "integrity" "sha1-Cpf7h2mG6AgcYxFg+PnziRV/AEM="
  "resolved" "https://registry.nlark.com/ignore/download/ignore-3.3.10.tgz"
  "version" "3.3.10"

"ignore@^4.0.6":
  "integrity" "sha1-dQ49tYYgh7RzfrrIIH/9HvJ7Jfw="
  "resolved" "https://registry.nlark.com/ignore/download/ignore-4.0.6.tgz"
  "version" "4.0.6"

"image-size@~0.5.0":
  "integrity" "sha1-Cd/Uq50g4p6xw+gLiZA3jfnjy5w="
  "resolved" "https://registry.npm.taobao.org/image-size/download/image-size-0.5.5.tgz?cache=0&sync_timestamp=1618424661730&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fimage-size%2Fdownload%2Fimage-size-0.5.5.tgz"
  "version" "0.5.5"

"immediate@~3.0.5":
  "integrity" "sha1-nbHb0Pr43m++D13V5Wu2BigN5ps="
  "resolved" "https://registry.nlark.com/immediate/download/immediate-3.0.6.tgz"
  "version" "3.0.6"

"immutable@*", "immutable@^4.0.0-rc.12", "immutable@~3.7.4", "immutable@3.x.x || 4.x.x":
  "integrity" "sha1-ylmn5MGa6Nm/dKl73w9uLypdAhc="
  "resolved" "https://registry.npm.taobao.org/immutable/download/immutable-4.0.0-rc.12.tgz"
  "version" "4.0.0-rc.12"

"immutable@^3.7.4":
  "integrity" "sha1-wkOZUUVbs5kT2vKBN28VMOEErfM="
  "resolved" "https://registry.nlark.com/immutable/download/immutable-3.8.2.tgz"
  "version" "3.8.2"

"import-fresh@^2.0.0":
  "integrity" "sha1-2BNVwVYS04bGH53dOSLUMEgipUY="
  "resolved" "https://registry.npm.taobao.org/import-fresh/download/import-fresh-2.0.0.tgz?cache=0&sync_timestamp=1608469579940&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fimport-fresh%2Fdownload%2Fimport-fresh-2.0.0.tgz"
  "version" "2.0.0"
  dependencies:
    "caller-path" "^2.0.0"
    "resolve-from" "^3.0.0"

"import-fresh@^3.0.0", "import-fresh@^3.2.1":
  "integrity" "sha1-NxYsJfy566oublPVtNiM4X2eDCs="
  "resolved" "https://registry.npm.taobao.org/import-fresh/download/import-fresh-3.3.0.tgz?cache=0&sync_timestamp=1608469579940&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fimport-fresh%2Fdownload%2Fimport-fresh-3.3.0.tgz"
  "version" "3.3.0"
  dependencies:
    "parent-module" "^1.0.0"
    "resolve-from" "^4.0.0"

"import-local@^2.0.0":
  "integrity" "sha1-VQcL44pZk88Y72236WH1vuXFoJ0="
  "resolved" "https://registry.nlark.com/import-local/download/import-local-2.0.0.tgz"
  "version" "2.0.0"
  dependencies:
    "pkg-dir" "^3.0.0"
    "resolve-cwd" "^2.0.0"

"imurmurhash@^0.1.4":
  "integrity" "sha1-khi5srkoojixPcT7a21XbyMUU+o="
  "resolved" "https://registry.nlark.com/imurmurhash/download/imurmurhash-0.1.4.tgz"
  "version" "0.1.4"

"in-publish@^2.0.0":
  "integrity" "sha1-lIsaU1yAMFYc6lIvc/ePS+NX4Aw="
  "resolved" "https://registry.npm.taobao.org/in-publish/download/in-publish-2.0.1.tgz"
  "version" "2.0.1"

"indent-string@^2.1.0":
  "integrity" "sha1-ji1INIdCEhtKghi3oTfppSBJ3IA="
  "resolved" "https://registry.nlark.com/indent-string/download/indent-string-2.1.0.tgz?cache=0&sync_timestamp=1618847271946&other_urls=https%3A%2F%2Fregistry.nlark.com%2Findent-string%2Fdownload%2Findent-string-2.1.0.tgz"
  "version" "2.1.0"
  dependencies:
    "repeating" "^2.0.0"

"indent-string@^4.0.0":
  "integrity" "sha1-Yk+PRJfWGbLZdoUx1Y9BIoVNclE="
  "resolved" "https://registry.nlark.com/indent-string/download/indent-string-4.0.0.tgz?cache=0&sync_timestamp=1618847271946&other_urls=https%3A%2F%2Fregistry.nlark.com%2Findent-string%2Fdownload%2Findent-string-4.0.0.tgz"
  "version" "4.0.0"

"indexes-of@^1.0.1":
  "integrity" "sha1-8w9xbI4r00bHtn0985FVZqfAVgc="
  "resolved" "https://registry.npm.taobao.org/indexes-of/download/indexes-of-1.0.1.tgz"
  "version" "1.0.1"

"infer-owner@^1.0.3", "infer-owner@^1.0.4":
  "integrity" "sha1-xM78qo5RBRwqQLos6KPScpWvlGc="
  "resolved" "https://registry.nlark.com/infer-owner/download/infer-owner-1.0.4.tgz"
  "version" "1.0.4"

"inflight@^1.0.4":
  "integrity" "sha1-Sb1jMdfQLQwJvJEKEHW6gWW1bfk="
  "resolved" "https://registry.npm.taobao.org/inflight/download/inflight-1.0.6.tgz"
  "version" "1.0.6"
  dependencies:
    "once" "^1.3.0"
    "wrappy" "1"

"inherits@^2.0.1", "inherits@^2.0.3", "inherits@^2.0.4", "inherits@~2.0.0", "inherits@~2.0.1", "inherits@~2.0.3", "inherits@2":
  "integrity" "sha1-D6LGT5MpF8NDOg3tVTY6rjdBa3w="
  "resolved" "https://registry.nlark.com/inherits/download/inherits-2.0.4.tgz"
  "version" "2.0.4"

"inherits@2.0.1":
  "integrity" "sha1-sX0I0ya0Qj5Wjv9xn5GwscvfafE="
  "resolved" "https://registry.nlark.com/inherits/download/inherits-2.0.1.tgz"
  "version" "2.0.1"

"inherits@2.0.3":
  "integrity" "sha1-Yzwsg+PaQqUC9SRmAiSA9CCCYd4="
  "resolved" "https://registry.nlark.com/inherits/download/inherits-2.0.3.tgz"
  "version" "2.0.3"

"ini@^1.3.4", "ini@^1.3.5":
  "integrity" "sha1-op2kJbSIBvNHZ6Tvzjlyaa8oQyw="
  "resolved" "https://registry.nlark.com/ini/download/ini-1.3.8.tgz"
  "version" "1.3.8"

"internal-ip@^4.3.0":
  "integrity" "sha1-hFRSuq2dLKO2nGNaE3rLmg2tCQc="
  "resolved" "https://registry.npm.taobao.org/internal-ip/download/internal-ip-4.3.0.tgz"
  "version" "4.3.0"
  dependencies:
    "default-gateway" "^4.2.0"
    "ipaddr.js" "^1.9.0"

"internal-slot@^1.0.3":
  "integrity" "sha1-c0fjB97uovqsKsYgXUvH00ln9Zw="
  "resolved" "https://registry.nlark.com/internal-slot/download/internal-slot-1.0.3.tgz"
  "version" "1.0.3"
  dependencies:
    "get-intrinsic" "^1.1.0"
    "has" "^1.0.3"
    "side-channel" "^1.0.4"

"interpret@^1.4.0":
  "integrity" "sha1-Zlq4vE2iendKQFhOgS4+D6RbGh4="
  "resolved" "https://registry.nlark.com/interpret/download/interpret-1.4.0.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.nlark.com%2Finterpret%2Fdownload%2Finterpret-1.4.0.tgz"
  "version" "1.4.0"

"invariant@^2.2.1":
  "integrity" "sha1-YQ88ksk1nOHbYW5TgAjSP/NRWOY="
  "resolved" "https://registry.nlark.com/invariant/download/invariant-2.2.4.tgz"
  "version" "2.2.4"
  dependencies:
    "loose-envify" "^1.0.0"

"ip-regex@^2.1.0":
  "integrity" "sha1-+ni/XS5pE8kRzp+BnuUUa7bYROk="
  "resolved" "https://registry.nlark.com/ip-regex/download/ip-regex-2.1.0.tgz?cache=0&sync_timestamp=1618846943469&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fip-regex%2Fdownload%2Fip-regex-2.1.0.tgz"
  "version" "2.1.0"

"ip@^1.1.0", "ip@^1.1.5":
  "integrity" "sha1-vd7XARQpCCjAoDnnLvJfWq7ENUo="
  "resolved" "https://registry.nlark.com/ip/download/ip-1.1.5.tgz"
  "version" "1.1.5"

"ipaddr.js@^1.9.0", "ipaddr.js@1.9.1":
  "integrity" "sha1-v/OFQ+64mEglB5/zoqjmy9RngbM="
  "resolved" "https://registry.nlark.com/ipaddr.js/download/ipaddr.js-1.9.1.tgz"
  "version" "1.9.1"

"is-absolute-url@^2.0.0":
  "integrity" "sha1-UFMN+4T8yap9vnhS6Do3uTufKqY="
  "resolved" "https://registry.npm.taobao.org/is-absolute-url/download/is-absolute-url-2.1.0.tgz"
  "version" "2.1.0"

"is-absolute-url@^3.0.3":
  "integrity" "sha1-lsaiK2ojkpsR6gr7GDbDatSl1pg="
  "resolved" "https://registry.npm.taobao.org/is-absolute-url/download/is-absolute-url-3.0.3.tgz"
  "version" "3.0.3"

"is-accessor-descriptor@^0.1.6":
  "integrity" "sha1-qeEss66Nh2cn7u84Q/igiXtcmNY="
  "resolved" "https://registry.nlark.com/is-accessor-descriptor/download/is-accessor-descriptor-0.1.6.tgz"
  "version" "0.1.6"
  dependencies:
    "kind-of" "^3.0.2"

"is-accessor-descriptor@^1.0.0":
  "integrity" "sha1-FpwvbT3x+ZJhgHI2XJsOofaHhlY="
  "resolved" "https://registry.nlark.com/is-accessor-descriptor/download/is-accessor-descriptor-1.0.0.tgz"
  "version" "1.0.0"
  dependencies:
    "kind-of" "^6.0.0"

"is-arguments@^1.0.4":
  "integrity" "sha1-YjUwMd++4HzrNGVqa95Z7+yujdk="
  "resolved" "https://registry.npm.taobao.org/is-arguments/download/is-arguments-1.1.0.tgz"
  "version" "1.1.0"
  dependencies:
    "call-bind" "^1.0.0"

"is-arrayish@^0.2.1":
  "integrity" "sha1-d8mYQFJ6qOyxqLppe4BkWnqSap0="
  "resolved" "https://registry.npm.taobao.org/is-arrayish/download/is-arrayish-0.2.1.tgz"
  "version" "0.2.1"

"is-arrayish@^0.3.1":
  "integrity" "sha1-RXSirlb3qyBolvtDHq7tBm/fjwM="
  "resolved" "https://registry.npm.taobao.org/is-arrayish/download/is-arrayish-0.3.2.tgz"
  "version" "0.3.2"

"is-bigint@^1.0.1":
  "integrity" "sha1-/7OBRCUDI1rSReqJ5Fs9v/BA7lo="
  "resolved" "https://registry.nlark.com/is-bigint/download/is-bigint-1.0.2.tgz"
  "version" "1.0.2"

"is-binary-path@^1.0.0":
  "integrity" "sha1-dfFmQrSA8YenEcgUFh/TpKdlWJg="
  "resolved" "https://registry.nlark.com/is-binary-path/download/is-binary-path-1.0.1.tgz"
  "version" "1.0.1"
  dependencies:
    "binary-extensions" "^1.0.0"

"is-binary-path@~2.1.0":
  "integrity" "sha1-6h9/O4DwZCNug0cPhsCcJU+0Wwk="
  "resolved" "https://registry.nlark.com/is-binary-path/download/is-binary-path-2.1.0.tgz"
  "version" "2.1.0"
  dependencies:
    "binary-extensions" "^2.0.0"

"is-boolean-object@^1.1.0":
  "integrity" "sha1-PAh48DXLghIo01DS4eNnGXFqPeg="
  "resolved" "https://registry.nlark.com/is-boolean-object/download/is-boolean-object-1.1.1.tgz"
  "version" "1.1.1"
  dependencies:
    "call-bind" "^1.0.2"

"is-buffer@^1.1.5":
  "integrity" "sha1-76ouqdqg16suoTqXsritUf776L4="
  "resolved" "https://registry.npm.taobao.org/is-buffer/download/is-buffer-1.1.6.tgz?cache=0&sync_timestamp=1604429876103&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fis-buffer%2Fdownload%2Fis-buffer-1.1.6.tgz"
  "version" "1.1.6"

"is-callable@^1.1.4", "is-callable@^1.2.3":
  "integrity" "sha1-ix4FALc6HXbHBIdjbzaOUZ3o244="
  "resolved" "https://registry.npm.taobao.org/is-callable/download/is-callable-1.2.3.tgz?cache=0&sync_timestamp=1612133072647&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fis-callable%2Fdownload%2Fis-callable-1.2.3.tgz"
  "version" "1.2.3"

"is-color-stop@^1.0.0":
  "integrity" "sha1-z/9HGu5N1cnhWFmPvhKWe1za00U="
  "resolved" "https://registry.npm.taobao.org/is-color-stop/download/is-color-stop-1.1.0.tgz"
  "version" "1.1.0"
  dependencies:
    "css-color-names" "^0.0.4"
    "hex-color-regex" "^1.1.0"
    "hsl-regex" "^1.0.0"
    "hsla-regex" "^1.0.0"
    "rgb-regex" "^1.0.1"
    "rgba-regex" "^1.0.0"

"is-core-module@^2.2.0", "is-core-module@^2.4.0":
  "integrity" "sha1-jp/I4VAnsBFBgCbpjw5vTYYwXME="
  "resolved" "https://registry.nlark.com/is-core-module/download/is-core-module-2.4.0.tgz"
  "version" "2.4.0"
  dependencies:
    "has" "^1.0.3"

"is-data-descriptor@^0.1.4":
  "integrity" "sha1-C17mSDiOLIYCgueT8YVv7D8wG1Y="
  "resolved" "https://registry.nlark.com/is-data-descriptor/download/is-data-descriptor-0.1.4.tgz"
  "version" "0.1.4"
  dependencies:
    "kind-of" "^3.0.2"

"is-data-descriptor@^1.0.0":
  "integrity" "sha1-2Eh2Mh0Oet0DmQQGq7u9NrqSaMc="
  "resolved" "https://registry.nlark.com/is-data-descriptor/download/is-data-descriptor-1.0.0.tgz"
  "version" "1.0.0"
  dependencies:
    "kind-of" "^6.0.0"

"is-date-object@^1.0.1":
  "integrity" "sha1-VQz8wDr62gXuo90wmBx7CVUfc+U="
  "resolved" "https://registry.nlark.com/is-date-object/download/is-date-object-1.0.4.tgz"
  "version" "1.0.4"

"is-descriptor@^0.1.0":
  "integrity" "sha1-Nm2CQN3kh8pRgjsaufB6EKeCUco="
  "resolved" "https://registry.nlark.com/is-descriptor/download/is-descriptor-0.1.6.tgz"
  "version" "0.1.6"
  dependencies:
    "is-accessor-descriptor" "^0.1.6"
    "is-data-descriptor" "^0.1.4"
    "kind-of" "^5.0.0"

"is-descriptor@^1.0.0":
  "integrity" "sha1-OxWXRqZmBLBPjIFSS6NlxfFNhuw="
  "resolved" "https://registry.nlark.com/is-descriptor/download/is-descriptor-1.0.2.tgz"
  "version" "1.0.2"
  dependencies:
    "is-accessor-descriptor" "^1.0.0"
    "is-data-descriptor" "^1.0.0"
    "kind-of" "^6.0.2"

"is-descriptor@^1.0.2":
  "integrity" "sha1-OxWXRqZmBLBPjIFSS6NlxfFNhuw="
  "resolved" "https://registry.nlark.com/is-descriptor/download/is-descriptor-1.0.2.tgz"
  "version" "1.0.2"
  dependencies:
    "is-accessor-descriptor" "^1.0.0"
    "is-data-descriptor" "^1.0.0"
    "kind-of" "^6.0.2"

"is-directory@^0.3.1":
  "integrity" "sha1-YTObbyR1/Hcv2cnYP1yFddwVSuE="
  "resolved" "https://registry.npm.taobao.org/is-directory/download/is-directory-0.3.1.tgz"
  "version" "0.3.1"

"is-extendable@^0.1.0", "is-extendable@^0.1.1":
  "integrity" "sha1-YrEQ4omkcUGOPsNqYX1HLjAd/Ik="
  "resolved" "https://registry.npm.taobao.org/is-extendable/download/is-extendable-0.1.1.tgz"
  "version" "0.1.1"

"is-extendable@^1.0.1":
  "integrity" "sha1-p0cPnkJnM9gb2B4RVSZOOjUHyrQ="
  "resolved" "https://registry.npm.taobao.org/is-extendable/download/is-extendable-1.0.1.tgz"
  "version" "1.0.1"
  dependencies:
    "is-plain-object" "^2.0.4"

"is-extglob@^2.1.0", "is-extglob@^2.1.1":
  "integrity" "sha1-qIwCU1eR8C7TfHahueqXc8gz+MI="
  "resolved" "https://registry.npm.taobao.org/is-extglob/download/is-extglob-2.1.1.tgz"
  "version" "2.1.1"

"is-finite@^1.0.0":
  "integrity" "sha1-kEE1x3+0LAZB1qobzbxNqo2ggvM="
  "resolved" "https://registry.nlark.com/is-finite/download/is-finite-1.1.0.tgz"
  "version" "1.1.0"

"is-fullwidth-code-point@^1.0.0":
  "integrity" "sha1-754xOG8DGn8NZDr4L95QxFfvAMs="
  "resolved" "https://registry.nlark.com/is-fullwidth-code-point/download/is-fullwidth-code-point-1.0.0.tgz"
  "version" "1.0.0"
  dependencies:
    "number-is-nan" "^1.0.0"

"is-fullwidth-code-point@^2.0.0":
  "integrity" "sha1-o7MKXE8ZkYMWeqq5O+764937ZU8="
  "resolved" "https://registry.nlark.com/is-fullwidth-code-point/download/is-fullwidth-code-point-2.0.0.tgz"
  "version" "2.0.0"

"is-fullwidth-code-point@^3.0.0":
  "integrity" "sha1-8Rb4Bk/pCz94RKOJl8C3UFEmnx0="
  "resolved" "https://registry.nlark.com/is-fullwidth-code-point/download/is-fullwidth-code-point-3.0.0.tgz"
  "version" "3.0.0"

"is-glob@^3.1.0":
  "integrity" "sha1-e6WuJCF4BKxwcHuWkiVnSGzD6Eo="
  "resolved" "https://registry.nlark.com/is-glob/download/is-glob-3.1.0.tgz"
  "version" "3.1.0"
  dependencies:
    "is-extglob" "^2.1.0"

"is-glob@^4.0.0", "is-glob@^4.0.1", "is-glob@~4.0.1":
  "integrity" "sha1-dWfb6fL14kZ7x3q4PEopSCQHpdw="
  "resolved" "https://registry.nlark.com/is-glob/download/is-glob-4.0.1.tgz"
  "version" "4.0.1"
  dependencies:
    "is-extglob" "^2.1.1"

"is-negative-zero@^2.0.1":
  "integrity" "sha1-PedGwY3aIxkkGlNnWQjY92bxHCQ="
  "resolved" "https://registry.npm.taobao.org/is-negative-zero/download/is-negative-zero-2.0.1.tgz?cache=0&sync_timestamp=1607123422635&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fis-negative-zero%2Fdownload%2Fis-negative-zero-2.0.1.tgz"
  "version" "2.0.1"

"is-number-object@^1.0.4":
  "integrity" "sha1-bt+u7XlQz/Ga/tzp+/yp7m3Sies="
  "resolved" "https://registry.nlark.com/is-number-object/download/is-number-object-1.0.5.tgz"
  "version" "1.0.5"

"is-number@^3.0.0":
  "integrity" "sha1-JP1iAaR4LPUFYcgQJ2r8fRLXEZU="
  "resolved" "https://registry.npm.taobao.org/is-number/download/is-number-3.0.0.tgz"
  "version" "3.0.0"
  dependencies:
    "kind-of" "^3.0.2"

"is-number@^7.0.0":
  "integrity" "sha1-dTU0W4lnNNX4DE0GxQlVUnoU8Ss="
  "resolved" "https://registry.npm.taobao.org/is-number/download/is-number-7.0.0.tgz"
  "version" "7.0.0"

"is-obj@^2.0.0":
  "integrity" "sha1-Rz+wXZc3BeP9liBUUBjKjiLvSYI="
  "resolved" "https://registry.nlark.com/is-obj/download/is-obj-2.0.0.tgz"
  "version" "2.0.0"

"is-path-cwd@^2.0.0":
  "integrity" "sha1-Z9Q7gmZKe1GR/ZEZEn6zAASKn9s="
  "resolved" "https://registry.npm.taobao.org/is-path-cwd/download/is-path-cwd-2.2.0.tgz"
  "version" "2.2.0"

"is-path-in-cwd@^2.0.0":
  "integrity" "sha1-v+Lcomxp85cmWkAJljYCk1oFOss="
  "resolved" "https://registry.nlark.com/is-path-in-cwd/download/is-path-in-cwd-2.1.0.tgz"
  "version" "2.1.0"
  dependencies:
    "is-path-inside" "^2.1.0"

"is-path-inside@^2.1.0":
  "integrity" "sha1-fJgQWH1lmkDSe8201WFuqwWUlLI="
  "resolved" "https://registry.nlark.com/is-path-inside/download/is-path-inside-2.1.0.tgz?cache=0&sync_timestamp=1620046845369&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fis-path-inside%2Fdownload%2Fis-path-inside-2.1.0.tgz"
  "version" "2.1.0"
  dependencies:
    "path-is-inside" "^1.0.2"

"is-plain-obj@^1.0.0":
  "integrity" "sha1-caUMhCnfync8kqOQpKA7OfzVHT4="
  "resolved" "https://registry.nlark.com/is-plain-obj/download/is-plain-obj-1.1.0.tgz"
  "version" "1.1.0"

"is-plain-object@^2.0.3", "is-plain-object@^2.0.4":
  "integrity" "sha1-LBY7P6+xtgbZ0Xko8FwqHDjgdnc="
  "resolved" "https://registry.npm.taobao.org/is-plain-object/download/is-plain-object-2.0.4.tgz"
  "version" "2.0.4"
  dependencies:
    "isobject" "^3.0.1"

"is-regex@^1.0.4", "is-regex@^1.1.3":
  "integrity" "sha1-0Cn5r/ZEi5Prvj8z2scVEf3L758="
  "resolved" "https://registry.nlark.com/is-regex/download/is-regex-1.1.3.tgz?cache=0&sync_timestamp=1620452285370&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fis-regex%2Fdownload%2Fis-regex-1.1.3.tgz"
  "version" "1.1.3"
  dependencies:
    "call-bind" "^1.0.2"
    "has-symbols" "^1.0.2"

"is-resolvable@^1.0.0":
  "integrity" "sha1-+xj4fOH+uSUWnJpAfBkxijIG7Yg="
  "resolved" "https://registry.npm.taobao.org/is-resolvable/download/is-resolvable-1.1.0.tgz"
  "version" "1.1.0"

"is-stream@^1.0.1", "is-stream@^1.1.0":
  "integrity" "sha1-EtSj3U5o4Lec6428hBc66A2RykQ="
  "resolved" "https://registry.npm.taobao.org/is-stream/download/is-stream-1.1.0.tgz"
  "version" "1.1.0"

"is-string@^1.0.5", "is-string@^1.0.6":
  "integrity" "sha1-P+XVmS+w2TQE8yWE1LAXmnG1Sl8="
  "resolved" "https://registry.nlark.com/is-string/download/is-string-1.0.6.tgz?cache=0&sync_timestamp=1620448217105&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fis-string%2Fdownload%2Fis-string-1.0.6.tgz"
  "version" "1.0.6"

"is-symbol@^1.0.2", "is-symbol@^1.0.3":
  "integrity" "sha1-ptrJO2NbBjymhyI23oiRClevE5w="
  "resolved" "https://registry.nlark.com/is-symbol/download/is-symbol-1.0.4.tgz?cache=0&sync_timestamp=1620501182675&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fis-symbol%2Fdownload%2Fis-symbol-1.0.4.tgz"
  "version" "1.0.4"
  dependencies:
    "has-symbols" "^1.0.2"

"is-typedarray@~1.0.0":
  "integrity" "sha1-5HnICFjfDBsR3dppQPlgEfzaSpo="
  "resolved" "https://registry.npm.taobao.org/is-typedarray/download/is-typedarray-1.0.0.tgz"
  "version" "1.0.0"

"is-utf8@^0.2.0":
  "integrity" "sha1-Sw2hRCEE0bM2NA6AeX6GXPOffXI="
  "resolved" "https://registry.nlark.com/is-utf8/download/is-utf8-0.2.1.tgz"
  "version" "0.2.1"

"is-what@^3.12.0":
  "integrity" "sha1-4SIvRt3ahd6tD9HJ3xMXYOd3VcE="
  "resolved" "https://registry.nlark.com/is-what/download/is-what-3.14.1.tgz"
  "version" "3.14.1"

"is-windows@^1.0.1", "is-windows@^1.0.2":
  "integrity" "sha1-0YUOuXkezRjmGCzhKjDzlmNLsZ0="
  "resolved" "https://registry.npm.taobao.org/is-windows/download/is-windows-1.0.2.tgz"
  "version" "1.0.2"

"is-wsl@^1.1.0":
  "integrity" "sha1-HxbkqiKwTRM2tmGIpmrzxgDDpm0="
  "resolved" "https://registry.npm.taobao.org/is-wsl/download/is-wsl-1.1.0.tgz?cache=0&sync_timestamp=1592843177178&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fis-wsl%2Fdownload%2Fis-wsl-1.1.0.tgz"
  "version" "1.1.0"

"isarray@^1.0.0", "isarray@~1.0.0", "isarray@1.0.0":
  "integrity" "sha1-u5NdSFgsuhaMBoNJV6VKPgcSTxE="
  "resolved" "https://registry.npm.taobao.org/isarray/download/isarray-1.0.0.tgz"
  "version" "1.0.0"

"isarray@0.0.1":
  "integrity" "sha1-ihis/Kmo9Bd+Cav8YDiTmwXR7t8="
  "resolved" "https://registry.npm.taobao.org/isarray/download/isarray-0.0.1.tgz"
  "version" "0.0.1"

"isexe@^2.0.0":
  "integrity" "sha1-6PvzdNxVb/iUehDcsFctYz8s+hA="
  "resolved" "https://registry.npm.taobao.org/isexe/download/isexe-2.0.0.tgz"
  "version" "2.0.0"

"isobject@^2.0.0":
  "integrity" "sha1-8GVWEJaj8dou9GJy+BXIQNh+DIk="
  "resolved" "https://registry.nlark.com/isobject/download/isobject-2.1.0.tgz"
  "version" "2.1.0"
  dependencies:
    "isarray" "1.0.0"

"isobject@^3.0.0", "isobject@^3.0.1":
  "integrity" "sha1-TkMekrEalzFjaqH5yNHMvP2reN8="
  "resolved" "https://registry.nlark.com/isobject/download/isobject-3.0.1.tgz"
  "version" "3.0.1"

"isomorphic-fetch@^2.1.1", "isomorphic-fetch@^2.2.1":
  "integrity" "sha1-YRrhrPFPXoH3KVB0coGf6XM1WKk="
  "resolved" "https://registry.npm.taobao.org/isomorphic-fetch/download/isomorphic-fetch-2.2.1.tgz?cache=0&sync_timestamp=1600844260369&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fisomorphic-fetch%2Fdownload%2Fisomorphic-fetch-2.2.1.tgz"
  "version" "2.2.1"
  dependencies:
    "node-fetch" "^1.0.1"
    "whatwg-fetch" ">=0.10.0"

"isstream@~0.1.2":
  "integrity" "sha1-R+Y/evVa+m+S4VAOaQ64uFKcCZo="
  "resolved" "https://registry.nlark.com/isstream/download/isstream-0.1.2.tgz"
  "version" "0.1.2"

"js-base64@^2.1.8":
  "integrity" "sha1-9OaGxd4eofhn28rT1G2WlCjfmMQ="
  "resolved" "https://registry.nlark.com/js-base64/download/js-base64-2.6.4.tgz?cache=0&sync_timestamp=1621703447855&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fjs-base64%2Fdownload%2Fjs-base64-2.6.4.tgz"
  "version" "2.6.4"

"js-base64@^3.5.2":
  "integrity" "sha1-VVquOYt0aUtAN68filpiCdFw774="
  "resolved" "https://registry.nlark.com/js-base64/download/js-base64-3.6.1.tgz?cache=0&sync_timestamp=1621703447855&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fjs-base64%2Fdownload%2Fjs-base64-3.6.1.tgz"
  "version" "3.6.1"

"js-sha256@^0.9.0":
  "integrity" "sha1-C4msFmWD6R75EjZEvTxTNM6dCWY="
  "resolved" "https://registry.npm.taobao.org/js-sha256/download/js-sha256-0.9.0.tgz"
  "version" "0.9.0"

"js-tokens@^3.0.0 || ^4.0.0", "js-tokens@^4.0.0":
  "integrity" "sha1-GSA/tZmR35jjoocFDUZHzerzJJk="
  "resolved" "https://registry.nlark.com/js-tokens/download/js-tokens-4.0.0.tgz"
  "version" "4.0.0"

"js-yaml@^3.13.1":
  "integrity" "sha1-2ugS/bOCX6MGYJqHFzg8UMNqBTc="
  "resolved" "https://registry.npm.taobao.org/js-yaml/download/js-yaml-3.14.1.tgz?cache=0&sync_timestamp=1618435151523&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fjs-yaml%2Fdownload%2Fjs-yaml-3.14.1.tgz"
  "version" "3.14.1"
  dependencies:
    "argparse" "^1.0.7"
    "esprima" "^4.0.0"

"jsbn@~0.1.0":
  "integrity" "sha1-peZUwuWi3rXyAdls77yoDA7y9RM="
  "resolved" "https://registry.nlark.com/jsbn/download/jsbn-0.1.1.tgz"
  "version" "0.1.1"

"jsesc@^2.5.1":
  "integrity" "sha1-gFZNLkg9rPbo7yCWUKZ98/DCg6Q="
  "resolved" "https://registry.nlark.com/jsesc/download/jsesc-2.5.2.tgz"
  "version" "2.5.2"

"jsesc@~0.5.0":
  "integrity" "sha1-597mbjXW/Bb3EP6R1c9p9w8IkR0="
  "resolved" "https://registry.nlark.com/jsesc/download/jsesc-0.5.0.tgz"
  "version" "0.5.0"

"json-parse-better-errors@^1.0.1", "json-parse-better-errors@^1.0.2":
  "integrity" "sha1-u4Z8+zRQ5pEHwTHRxRS6s9yLyqk="
  "resolved" "https://registry.nlark.com/json-parse-better-errors/download/json-parse-better-errors-1.0.2.tgz"
  "version" "1.0.2"

"json-schema-traverse@^0.4.1":
  "integrity" "sha1-afaofZUTq4u4/mO9sJecRI5oRmA="
  "resolved" "https://registry.npm.taobao.org/json-schema-traverse/download/json-schema-traverse-0.4.1.tgz"
  "version" "0.4.1"

"json-schema-traverse@^1.0.0":
  "integrity" "sha1-rnvLNlard6c7pcSb9lTzjmtoYOI="
  "resolved" "https://registry.npm.taobao.org/json-schema-traverse/download/json-schema-traverse-1.0.0.tgz"
  "version" "1.0.0"

"json-schema@0.2.3":
  "integrity" "sha1-tIDIkuWaLwWVTOcnvT8qTogvnhM="
  "resolved" "https://registry.nlark.com/json-schema/download/json-schema-0.2.3.tgz"
  "version" "0.2.3"

"json-stable-stringify-without-jsonify@^1.0.1":
  "integrity" "sha1-nbe1lJatPzz+8wp1FC0tkwrXJlE="
  "resolved" "https://registry.nlark.com/json-stable-stringify-without-jsonify/download/json-stable-stringify-without-jsonify-1.0.1.tgz"
  "version" "1.0.1"

"json-stringify-safe@~5.0.1", "json-stringify-safe@5.0.1":
  "integrity" "sha1-Epai1Y/UXxmg9s4B1lcB4sc1tus="
  "resolved" "https://registry.nlark.com/json-stringify-safe/download/json-stringify-safe-5.0.1.tgz"
  "version" "5.0.1"

"json2mq@^0.2.0":
  "integrity" "sha1-tje9O6nqvhIsg+lyBIOusQ0skEo="
  "resolved" "https://registry.npm.taobao.org/json2mq/download/json2mq-0.2.0.tgz"
  "version" "0.2.0"
  dependencies:
    "string-convert" "^0.2.0"

"json3@^3.3.3":
  "integrity" "sha1-f8EON1/FrkLEcFpcwKpvYr4wW4E="
  "resolved" "https://registry.npm.taobao.org/json3/download/json3-3.3.3.tgz"
  "version" "3.3.3"

"json5@^0.5.0":
  "integrity" "sha1-Hq3nrMASA0rYTiOWdn6tn6VJWCE="
  "resolved" "https://registry.npm.taobao.org/json5/download/json5-0.5.1.tgz"
  "version" "0.5.1"

"json5@^1.0.1":
  "integrity" "sha1-d5+wAYYE+oVOrL9iUhgNg1Q+Pb4="
  "resolved" "https://registry.npm.taobao.org/json5/download/json5-1.0.1.tgz"
  "version" "1.0.1"
  dependencies:
    "minimist" "^1.2.0"

"json5@^2.1.2":
  "integrity" "sha1-Lf7+cgxrpSXZ69kJlQ8FFTFsiaM="
  "resolved" "https://registry.npm.taobao.org/json5/download/json5-2.2.0.tgz"
  "version" "2.2.0"
  dependencies:
    "minimist" "^1.2.5"

"jsprim@^1.2.2":
  "integrity" "sha1-MT5mvB5cwG5Di8G3SZwuXFastqI="
  "resolved" "https://registry.npm.taobao.org/jsprim/download/jsprim-1.4.1.tgz"
  "version" "1.4.1"
  dependencies:
    "assert-plus" "1.0.0"
    "extsprintf" "1.3.0"
    "json-schema" "0.2.3"
    "verror" "1.10.0"

"jsx-ast-utils@^2.4.1 || ^3.0.0", "jsx-ast-utils@^3.1.0":
  "integrity" "sha1-QRCNLOxAjDRTwbvopKrp4eK9j4I="
  "resolved" "https://registry.nlark.com/jsx-ast-utils/download/jsx-ast-utils-3.2.0.tgz"
  "version" "3.2.0"
  dependencies:
    "array-includes" "^3.1.2"
    "object.assign" "^4.1.2"

"jszip@^3.5.0":
  "integrity" "sha1-g5tygS4/l4GcwTrEE0/87ZXdavk="
  "resolved" "https://registry.nlark.com/jszip/download/jszip-3.6.0.tgz?cache=0&sync_timestamp=1618908792302&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fjszip%2Fdownload%2Fjszip-3.6.0.tgz"
  "version" "3.6.0"
  dependencies:
    "lie" "~3.3.0"
    "pako" "~1.0.2"
    "readable-stream" "~2.3.6"
    "set-immediate-shim" "~1.0.1"

"killable@^1.0.1":
  "integrity" "sha1-TIzkQRh6Bhx0dPuHygjipjgZSJI="
  "resolved" "https://registry.npm.taobao.org/killable/download/killable-1.0.1.tgz"
  "version" "1.0.1"

"kind-of@^3.0.2":
  "integrity" "sha1-MeohpzS6ubuw8yRm2JOupR5KPGQ="
  "resolved" "https://registry.npm.taobao.org/kind-of/download/kind-of-3.2.2.tgz"
  "version" "3.2.2"
  dependencies:
    "is-buffer" "^1.1.5"

"kind-of@^3.0.3":
  "integrity" "sha1-MeohpzS6ubuw8yRm2JOupR5KPGQ="
  "resolved" "https://registry.npm.taobao.org/kind-of/download/kind-of-3.2.2.tgz"
  "version" "3.2.2"
  dependencies:
    "is-buffer" "^1.1.5"

"kind-of@^3.2.0":
  "integrity" "sha1-MeohpzS6ubuw8yRm2JOupR5KPGQ="
  "resolved" "https://registry.npm.taobao.org/kind-of/download/kind-of-3.2.2.tgz"
  "version" "3.2.2"
  dependencies:
    "is-buffer" "^1.1.5"

"kind-of@^4.0.0":
  "integrity" "sha1-IIE989cSkosgc3hpGkUGb65y3Vc="
  "resolved" "https://registry.npm.taobao.org/kind-of/download/kind-of-4.0.0.tgz"
  "version" "4.0.0"
  dependencies:
    "is-buffer" "^1.1.5"

"kind-of@^5.0.0":
  "integrity" "sha1-cpyR4thXt6QZofmqZWhcTDP1hF0="
  "resolved" "https://registry.npm.taobao.org/kind-of/download/kind-of-5.1.0.tgz"
  "version" "5.1.0"

"kind-of@^6.0.0", "kind-of@^6.0.2":
  "integrity" "sha1-B8BQNKbDSfoG4k+jWqdttFgM5N0="
  "resolved" "https://registry.npm.taobao.org/kind-of/download/kind-of-6.0.3.tgz"
  "version" "6.0.3"

"language-subtag-registry@~0.3.2":
  "integrity" "sha1-BKwhi+pG8EywOQhGAsbanniN1Fo="
  "resolved" "https://registry.npm.taobao.org/language-subtag-registry/download/language-subtag-registry-0.3.21.tgz"
  "version" "0.3.21"

"language-tags@^1.0.5":
  "integrity" "sha1-0yHbxNowuovzAk4ED6XBRmH5GTo="
  "resolved" "https://registry.npm.taobao.org/language-tags/download/language-tags-1.0.5.tgz"
  "version" "1.0.5"
  dependencies:
    "language-subtag-registry" "~0.3.2"

"last-call-webpack-plugin@^3.0.0":
  "integrity" "sha1-l0LfDhDjz0blwDgcLekNOnotdVU="
  "resolved" "https://registry.npm.taobao.org/last-call-webpack-plugin/download/last-call-webpack-plugin-3.0.0.tgz"
  "version" "3.0.0"
  dependencies:
    "lodash" "^4.17.5"
    "webpack-sources" "^1.1.0"

"lazystream@^1.0.0":
  "integrity" "sha1-9plf4PggOS9hOWvolGJAe7dxaOQ="
  "resolved" "https://registry.npm.taobao.org/lazystream/download/lazystream-1.0.0.tgz"
  "version" "1.0.0"
  dependencies:
    "readable-stream" "^2.0.5"

"less-loader@^5.0.0":
  "integrity" "sha1-SY3eOmxsT4h0WO6e0/CGoSrRtGY="
  "resolved" "https://registry.nlark.com/less-loader/download/less-loader-5.0.0.tgz?cache=0&sync_timestamp=1623934820657&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fless-loader%2Fdownload%2Fless-loader-5.0.0.tgz"
  "version" "5.0.0"
  dependencies:
    "clone" "^2.1.1"
    "loader-utils" "^1.1.0"
    "pify" "^4.0.1"

"less@^2.3.1 || ^3.0.0", "less@^3.9.0":
  "integrity" "sha1-DryR0qDpwMZzW4PUlrCrBYMHeQk="
  "resolved" "https://registry.npm.taobao.org/less/download/less-3.13.1.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fless%2Fdownload%2Fless-3.13.1.tgz"
  "version" "3.13.1"
  dependencies:
    "copy-anything" "^2.0.1"
    "tslib" "^1.10.0"
  optionalDependencies:
    "errno" "^0.1.1"
    "graceful-fs" "^4.1.2"
    "image-size" "~0.5.0"
    "make-dir" "^2.1.0"
    "mime" "^1.4.1"
    "native-request" "^1.0.5"
    "source-map" "~0.6.0"

"levn@^0.4.1":
  "integrity" "sha1-rkViwAdHO5MqYgDUAyaN0v/8at4="
  "resolved" "https://registry.npm.taobao.org/levn/download/levn-0.4.1.tgz"
  "version" "0.4.1"
  dependencies:
    "prelude-ls" "^1.2.1"
    "type-check" "~0.4.0"

"lie@~3.3.0":
  "integrity" "sha1-3Pgt7lRfRgdNryAMfBxaCOD0D2o="
  "resolved" "https://registry.npm.taobao.org/lie/download/lie-3.3.0.tgz"
  "version" "3.3.0"
  dependencies:
    "immediate" "~3.0.5"

"listenercount@~1.0.1":
  "integrity" "sha1-hMinKrWcRyUyFIDJdeZQg0LnCTc="
  "resolved" "https://registry.npm.taobao.org/listenercount/download/listenercount-1.0.1.tgz"
  "version" "1.0.1"

"load-json-file@^1.0.0":
  "integrity" "sha1-lWkFcI1YtLq0wiYbBPWfMcmTdMA="
  "resolved" "https://registry.npm.taobao.org/load-json-file/download/load-json-file-1.1.0.tgz"
  "version" "1.1.0"
  dependencies:
    "graceful-fs" "^4.1.2"
    "parse-json" "^2.2.0"
    "pify" "^2.0.0"
    "pinkie-promise" "^2.0.0"
    "strip-bom" "^2.0.0"

"load-json-file@^4.0.0":
  "integrity" "sha1-L19Fq5HjMhYjT9U62rZo607AmTs="
  "resolved" "https://registry.npm.taobao.org/load-json-file/download/load-json-file-4.0.0.tgz"
  "version" "4.0.0"
  dependencies:
    "graceful-fs" "^4.1.2"
    "parse-json" "^4.0.0"
    "pify" "^3.0.0"
    "strip-bom" "^3.0.0"

"loader-runner@^2.4.0":
  "integrity" "sha1-7UcGa/5TTX6ExMe5mYwqdWB9k1c="
  "resolved" "https://registry.nlark.com/loader-runner/download/loader-runner-2.4.0.tgz"
  "version" "2.4.0"

"loader-utils@^0.2.16":
  "integrity" "sha1-+G5jdNQyBabmxg6RlvF8Apm/s0g="
  "resolved" "https://registry.npm.taobao.org/loader-utils/download/loader-utils-0.2.17.tgz"
  "version" "0.2.17"
  dependencies:
    "big.js" "^3.1.3"
    "emojis-list" "^2.0.0"
    "json5" "^0.5.0"
    "object-assign" "^4.0.1"

"loader-utils@^1.0.1", "loader-utils@^1.1.0", "loader-utils@^1.2.3", "loader-utils@^1.4.0":
  "integrity" "sha1-xXm140yzSxp07cbB+za/o3HVphM="
  "resolved" "https://registry.npm.taobao.org/loader-utils/download/loader-utils-1.4.0.tgz"
  "version" "1.4.0"
  dependencies:
    "big.js" "^5.2.2"
    "emojis-list" "^3.0.0"
    "json5" "^1.0.1"

"loader-utils@^2.0.0":
  "integrity" "sha1-5MrOW4FtQloWa18JfhDNErNgZLA="
  "resolved" "https://registry.npm.taobao.org/loader-utils/download/loader-utils-2.0.0.tgz"
  "version" "2.0.0"
  dependencies:
    "big.js" "^5.2.2"
    "emojis-list" "^3.0.0"
    "json5" "^2.1.2"

"loader-utils@1.1.0":
  "integrity" "sha1-yYrvSIvM7aL/teLeZG1qdUQp9c0="
  "resolved" "https://registry.npm.taobao.org/loader-utils/download/loader-utils-1.1.0.tgz"
  "version" "1.1.0"
  dependencies:
    "big.js" "^3.1.3"
    "emojis-list" "^2.0.0"
    "json5" "^0.5.0"

"locate-path@^2.0.0":
  "integrity" "sha1-K1aLJl7slExtnA3pw9u7ygNUzY4="
  "resolved" "https://registry.npm.taobao.org/locate-path/download/locate-path-2.0.0.tgz"
  "version" "2.0.0"
  dependencies:
    "p-locate" "^2.0.0"
    "path-exists" "^3.0.0"

"locate-path@^3.0.0":
  "integrity" "sha1-2+w7OrdZdYBxtY/ln8QYca8hQA4="
  "resolved" "https://registry.npm.taobao.org/locate-path/download/locate-path-3.0.0.tgz"
  "version" "3.0.0"
  dependencies:
    "p-locate" "^3.0.0"
    "path-exists" "^3.0.0"

"locate-path@^5.0.0":
  "integrity" "sha1-Gvujlq/WdqbUJQTQpno6frn2KqA="
  "resolved" "https://registry.npm.taobao.org/locate-path/download/locate-path-5.0.0.tgz"
  "version" "5.0.0"
  dependencies:
    "p-locate" "^4.1.0"

"lodash.clonedeep@^4.5.0":
  "integrity" "sha1-4j8/nE+Pvd6HJSnBBxhXoIblzO8="
  "resolved" "https://registry.nlark.com/lodash.clonedeep/download/lodash.clonedeep-4.5.0.tgz"
  "version" "4.5.0"

"lodash.debounce@^4.0.0", "lodash.debounce@^4.0.8":
  "integrity" "sha1-gteb/zCmfEAF/9XiUVMArZyk168="
  "resolved" "https://registry.npm.taobao.org/lodash.debounce/download/lodash.debounce-4.0.8.tgz"
  "version" "4.0.8"

"lodash.defaults@^4.2.0":
  "integrity" "sha1-0JF4cW/+pN3p5ft7N/bwgCJ0WAw="
  "resolved" "https://registry.npm.taobao.org/lodash.defaults/download/lodash.defaults-4.2.0.tgz"
  "version" "4.2.0"

"lodash.difference@^4.5.0":
  "integrity" "sha1-nMtOUF1Ia5FlE0V3KIWi3yf9AXw="
  "resolved" "https://registry.npm.taobao.org/lodash.difference/download/lodash.difference-4.5.0.tgz"
  "version" "4.5.0"

"lodash.escaperegexp@^4.1.2":
  "integrity" "sha1-ZHYsSGGAglGKw99Mz11YhtriA0c="
  "resolved" "https://registry.npm.taobao.org/lodash.escaperegexp/download/lodash.escaperegexp-4.1.2.tgz"
  "version" "4.1.2"

"lodash.flatten@^4.4.0":
  "integrity" "sha1-8xwiIlqWMtK7+OSt2+8kCqdlph8="
  "resolved" "https://registry.npm.taobao.org/lodash.flatten/download/lodash.flatten-4.4.0.tgz"
  "version" "4.4.0"

"lodash.groupby@^4.6.0":
  "integrity" "sha1-Cwih3PaDl8OXhVwyOXg4Mt90A9E="
  "resolved" "https://registry.nlark.com/lodash.groupby/download/lodash.groupby-4.6.0.tgz"
  "version" "4.6.0"

"lodash.isboolean@^3.0.3":
  "integrity" "sha1-bC4XHbKiV82WgC/UOwGyDV9YcPY="
  "resolved" "https://registry.npm.taobao.org/lodash.isboolean/download/lodash.isboolean-3.0.3.tgz"
  "version" "3.0.3"

"lodash.isequal@^4.5.0":
  "integrity" "sha1-QVxEePK8wwEgwizhDtMib30+GOA="
  "resolved" "https://registry.nlark.com/lodash.isequal/download/lodash.isequal-4.5.0.tgz"
  "version" "4.5.0"

"lodash.isfunction@^3.0.9":
  "integrity" "sha1-Bt4l302zJ6yTGYHRvbBn5a9o0FE="
  "resolved" "https://registry.npm.taobao.org/lodash.isfunction/download/lodash.isfunction-3.0.9.tgz"
  "version" "3.0.9"

"lodash.isnil@^4.0.0":
  "integrity" "sha1-SeKM1VkBNFjIFMVHnTxmOiG/qmw="
  "resolved" "https://registry.npm.taobao.org/lodash.isnil/download/lodash.isnil-4.0.0.tgz"
  "version" "4.0.0"

"lodash.isplainobject@^4.0.6":
  "integrity" "sha1-fFJqUtibRcRcxpC4gWO+BJf1UMs="
  "resolved" "https://registry.npm.taobao.org/lodash.isplainobject/download/lodash.isplainobject-4.0.6.tgz"
  "version" "4.0.6"

"lodash.isundefined@^3.0.1":
  "integrity" "sha1-I+89lTVWUgOmbO/VuDD4SJEa+0g="
  "resolved" "https://registry.npm.taobao.org/lodash.isundefined/download/lodash.isundefined-3.0.1.tgz"
  "version" "3.0.1"

"lodash.memoize@^4.1.2":
  "integrity" "sha1-vMbEmkKihA7Zl/Mj6tpezRguC/4="
  "resolved" "https://registry.nlark.com/lodash.memoize/download/lodash.memoize-4.1.2.tgz"
  "version" "4.1.2"

"lodash.merge@^4.6.2":
  "integrity" "sha1-VYqlO0O2YeGSWgr9+japoQhf5Xo="
  "resolved" "https://registry.npm.taobao.org/lodash.merge/download/lodash.merge-4.6.2.tgz"
  "version" "4.6.2"

"lodash.throttle@^4.0.0":
  "integrity" "sha1-wj6RtxAkKscMN/HhzaknTMOb8vQ="
  "resolved" "https://registry.nlark.com/lodash.throttle/download/lodash.throttle-4.1.1.tgz"
  "version" "4.1.1"

"lodash.truncate@^4.4.2":
  "integrity" "sha1-WjUNoLERO4N+z//VgSy+WNbq4ZM="
  "resolved" "https://registry.nlark.com/lodash.truncate/download/lodash.truncate-4.4.2.tgz"
  "version" "4.4.2"

"lodash.union@^4.6.0":
  "integrity" "sha1-SLtQiECfFvGCFmZkHETdGqrjzYg="
  "resolved" "https://registry.npm.taobao.org/lodash.union/download/lodash.union-4.6.0.tgz"
  "version" "4.6.0"

"lodash.uniq@^4.5.0":
  "integrity" "sha1-0CJTc662Uq3BvILklFM5qEJ1R3M="
  "resolved" "https://registry.npm.taobao.org/lodash.uniq/download/lodash.uniq-4.5.0.tgz"
  "version" "4.5.0"

"lodash@^4.0.0", "lodash@^4.16.5", "lodash@^4.17.11", "lodash@^4.17.13", "lodash@^4.17.14", "lodash@^4.17.15", "lodash@^4.17.20", "lodash@^4.17.21", "lodash@^4.17.3", "lodash@^4.17.4", "lodash@^4.17.5", "lodash@~4.17.10":
  "integrity" "sha1-Z5WRxWTDv/quhFTPCz3zcMPWkRw="
  "resolved" "https://registry.nlark.com/lodash/download/lodash-4.17.21.tgz?cache=0&sync_timestamp=1618847150612&other_urls=https%3A%2F%2Fregistry.nlark.com%2Flodash%2Fdownload%2Flodash-4.17.21.tgz"
  "version" "4.17.21"

"loglevel@^1.6.8":
  "integrity" "sha1-AF/eL15uRwaPk1/yhXPhJe9y8Zc="
  "resolved" "https://registry.nlark.com/loglevel/download/loglevel-1.7.1.tgz"
  "version" "1.7.1"

"loose-envify@^1.0.0", "loose-envify@^1.1.0", "loose-envify@^1.2.0", "loose-envify@^1.3.1", "loose-envify@^1.4.0":
  "integrity" "sha1-ce5R+nvkyuwaY4OffmgtgTLTDK8="
  "resolved" "https://registry.npm.taobao.org/loose-envify/download/loose-envify-1.4.0.tgz"
  "version" "1.4.0"
  dependencies:
    "js-tokens" "^3.0.0 || ^4.0.0"

"loud-rejection@^1.0.0":
  "integrity" "sha1-W0b4AUft7leIcPCG0Eghz5mOVR8="
  "resolved" "https://registry.npm.taobao.org/loud-rejection/download/loud-rejection-1.6.0.tgz"
  "version" "1.6.0"
  dependencies:
    "currently-unhandled" "^0.4.1"
    "signal-exit" "^3.0.0"

"lower-case@^1.1.1":
  "integrity" "sha1-miyr0bno4K6ZOkv31YdcOcQujqw="
  "resolved" "https://registry.npm.taobao.org/lower-case/download/lower-case-1.1.4.tgz?cache=0&sync_timestamp=1606867514181&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Flower-case%2Fdownload%2Flower-case-1.1.4.tgz"
  "version" "1.1.4"

"lru-cache@^4.0.1":
  "integrity" "sha1-i75Q6oW+1ZvJ4z3KuCNe6bz0Q80="
  "resolved" "https://registry.npm.taobao.org/lru-cache/download/lru-cache-4.1.5.tgz"
  "version" "4.1.5"
  dependencies:
    "pseudomap" "^1.0.2"
    "yallist" "^2.1.2"

"lru-cache@^5.1.1":
  "integrity" "sha1-HaJ+ZxAnGUdpXa9oSOhH8B2EuSA="
  "resolved" "https://registry.npm.taobao.org/lru-cache/download/lru-cache-5.1.1.tgz"
  "version" "5.1.1"
  dependencies:
    "yallist" "^3.0.2"

"lru-cache@^6.0.0":
  "integrity" "sha1-bW/mVw69lqr5D8rR2vo7JWbbOpQ="
  "resolved" "https://registry.npm.taobao.org/lru-cache/download/lru-cache-6.0.0.tgz"
  "version" "6.0.0"
  dependencies:
    "yallist" "^4.0.0"

"make-dir@^2.0.0":
  "integrity" "sha1-XwMQ4YuL6JjMBwCSlaMK5B6R5vU="
  "resolved" "https://registry.nlark.com/make-dir/download/make-dir-2.1.0.tgz"
  "version" "2.1.0"
  dependencies:
    "pify" "^4.0.1"
    "semver" "^5.6.0"

"make-dir@^2.1.0":
  "integrity" "sha1-XwMQ4YuL6JjMBwCSlaMK5B6R5vU="
  "resolved" "https://registry.nlark.com/make-dir/download/make-dir-2.1.0.tgz"
  "version" "2.1.0"
  dependencies:
    "pify" "^4.0.1"
    "semver" "^5.6.0"

"make-dir@^3.0.2", "make-dir@^3.1.0":
  "integrity" "sha1-QV6WcEazp/HRhSd9hKpYIDcmoT8="
  "resolved" "https://registry.nlark.com/make-dir/download/make-dir-3.1.0.tgz"
  "version" "3.1.0"
  dependencies:
    "semver" "^6.0.0"

"map-cache@^0.2.2":
  "integrity" "sha1-wyq9C9ZSXZsFFkW7TyasXcmKDb8="
  "resolved" "https://registry.npm.taobao.org/map-cache/download/map-cache-0.2.2.tgz"
  "version" "0.2.2"

"map-obj@^1.0.0", "map-obj@^1.0.1":
  "integrity" "sha1-2TPOuSBdgr3PSIb2dCvcK03qFG0="
  "resolved" "https://registry.npm.taobao.org/map-obj/download/map-obj-1.0.1.tgz?cache=0&sync_timestamp=1617771341569&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fmap-obj%2Fdownload%2Fmap-obj-1.0.1.tgz"
  "version" "1.0.1"

"map-visit@^1.0.0":
  "integrity" "sha1-7Nyo8TFE5mDxtb1B8S80edmN+48="
  "resolved" "https://registry.npm.taobao.org/map-visit/download/map-visit-1.0.0.tgz"
  "version" "1.0.0"
  dependencies:
    "object-visit" "^1.0.0"

"md5.js@^1.3.4":
  "integrity" "sha1-tdB7jjIW4+J81yjXL3DR5qNCAF8="
  "resolved" "https://registry.npm.taobao.org/md5.js/download/md5.js-1.3.5.tgz"
  "version" "1.3.5"
  dependencies:
    "hash-base" "^3.0.0"
    "inherits" "^2.0.1"
    "safe-buffer" "^5.1.2"

"mdn-data@2.0.14":
  "integrity" "sha1-cRP8QoGRfWPOKbQ0RvcB5owlulA="
  "resolved" "https://registry.nlark.com/mdn-data/download/mdn-data-2.0.14.tgz"
  "version" "2.0.14"

"mdn-data@2.0.4":
  "integrity" "sha1-aZs8OKxvHXKAkaZGULZdOIUC/Vs="
  "resolved" "https://registry.nlark.com/mdn-data/download/mdn-data-2.0.4.tgz"
  "version" "2.0.4"

"media-typer@0.3.0":
  "integrity" "sha1-hxDXrwqmJvj/+hzgAWhUUmMlV0g="
  "resolved" "https://registry.npm.taobao.org/media-typer/download/media-typer-0.3.0.tgz"
  "version" "0.3.0"

"memory-fs@^0.4.1":
  "integrity" "sha1-OpoguEYlI+RHz7x+i7gO1me/xVI="
  "resolved" "https://registry.npm.taobao.org/memory-fs/download/memory-fs-0.4.1.tgz"
  "version" "0.4.1"
  dependencies:
    "errno" "^0.1.3"
    "readable-stream" "^2.0.1"

"memory-fs@^0.5.0":
  "integrity" "sha1-MkwBKIuIZSlm0WHbd4OHIIRajjw="
  "resolved" "https://registry.npm.taobao.org/memory-fs/download/memory-fs-0.5.0.tgz"
  "version" "0.5.0"
  dependencies:
    "errno" "^0.1.3"
    "readable-stream" "^2.0.1"

"meow@^3.7.0":
  "integrity" "sha1-cstmi0JSKCkKu/qFaJJYcwioAfs="
  "resolved" "https://registry.nlark.com/meow/download/meow-3.7.0.tgz?cache=0&sync_timestamp=1623137159863&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fmeow%2Fdownload%2Fmeow-3.7.0.tgz"
  "version" "3.7.0"
  dependencies:
    "camelcase-keys" "^2.0.0"
    "decamelize" "^1.1.2"
    "loud-rejection" "^1.0.0"
    "map-obj" "^1.0.1"
    "minimist" "^1.1.3"
    "normalize-package-data" "^2.3.4"
    "object-assign" "^4.0.1"
    "read-pkg-up" "^1.0.1"
    "redent" "^1.0.0"
    "trim-newlines" "^1.0.0"

"merge-descriptors@1.0.1":
  "integrity" "sha1-sAqqVW3YtEVoFQ7J0blT8/kMu2E="
  "resolved" "https://registry.nlark.com/merge-descriptors/download/merge-descriptors-1.0.1.tgz"
  "version" "1.0.1"

"methods@~1.1.2":
  "integrity" "sha1-VSmk1nZUE07cxSZmVoNbD4Ua/O4="
  "resolved" "https://registry.nlark.com/methods/download/methods-1.1.2.tgz"
  "version" "1.1.2"

"micromatch@^3.0.4", "micromatch@^3.1.10", "micromatch@^3.1.4":
  "integrity" "sha1-cIWbyVyYQJUvNZoGij/En57PrCM="
  "resolved" "https://registry.nlark.com/micromatch/download/micromatch-3.1.10.tgz"
  "version" "3.1.10"
  dependencies:
    "arr-diff" "^4.0.0"
    "array-unique" "^0.3.2"
    "braces" "^2.3.1"
    "define-property" "^2.0.2"
    "extend-shallow" "^3.0.2"
    "extglob" "^2.0.4"
    "fragment-cache" "^0.2.1"
    "kind-of" "^6.0.2"
    "nanomatch" "^1.2.9"
    "object.pick" "^1.3.0"
    "regex-not" "^1.0.0"
    "snapdragon" "^0.8.1"
    "to-regex" "^3.0.2"

"miller-rabin@^4.0.0":
  "integrity" "sha1-8IA1HIZbDcViqEYpZtqlNUPHik0="
  "resolved" "https://registry.npm.taobao.org/miller-rabin/download/miller-rabin-4.0.1.tgz"
  "version" "4.0.1"
  dependencies:
    "bn.js" "^4.0.0"
    "brorand" "^1.0.1"

"mime-db@>= 1.43.0 < 2", "mime-db@1.48.0":
  "integrity" "sha1-41sxBF3X6to6qtU37YijOvvvLR0="
  "resolved" "https://registry.nlark.com/mime-db/download/mime-db-1.48.0.tgz?cache=0&sync_timestamp=1622433556078&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fmime-db%2Fdownload%2Fmime-db-1.48.0.tgz"
  "version" "1.48.0"

"mime-types@^2.1.12", "mime-types@~2.1.17", "mime-types@~2.1.19", "mime-types@~2.1.24":
  "integrity" "sha1-oA12t0MXxh+cLbIhi46fjpxcnms="
  "resolved" "https://registry.nlark.com/mime-types/download/mime-types-2.1.31.tgz?cache=0&sync_timestamp=1622569304088&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fmime-types%2Fdownload%2Fmime-types-2.1.31.tgz"
  "version" "2.1.31"
  dependencies:
    "mime-db" "1.48.0"

"mime@^1.4.1", "mime@1.6.0":
  "integrity" "sha1-Ms2eXGRVO9WNGaVor0Uqz/BJgbE="
  "resolved" "https://registry.npm.taobao.org/mime/download/mime-1.6.0.tgz"
  "version" "1.6.0"

"mime@^2.4.4":
  "integrity" "sha1-bj3GzCuVEGQ4MOXxnVy3U9pe6r4="
  "resolved" "https://registry.npm.taobao.org/mime/download/mime-2.5.2.tgz"
  "version" "2.5.2"

"min-document@^2.19.0":
  "integrity" "sha1-e9KC4/WELtKVu3SM3Z8f+iyCRoU="
  "resolved" "https://registry.npm.taobao.org/min-document/download/min-document-2.19.0.tgz"
  "version" "2.19.0"
  dependencies:
    "dom-walk" "^0.1.0"

"mini-create-react-context@^0.4.0":
  "integrity" "sha1-ByFxVhv9ySLaCKYMIZekl8wtHV4="
  "resolved" "https://registry.nlark.com/mini-create-react-context/download/mini-create-react-context-0.4.1.tgz"
  "version" "0.4.1"
  dependencies:
    "@babel/runtime" "^7.12.1"
    "tiny-warning" "^1.0.3"

"mini-css-extract-plugin@^0.8.0":
  "integrity" "sha1-qHXhab6yfIivd92WJ3HJ7tw9oWE="
  "resolved" "https://registry.nlark.com/mini-css-extract-plugin/download/mini-css-extract-plugin-0.8.2.tgz"
  "version" "0.8.2"
  dependencies:
    "loader-utils" "^1.1.0"
    "normalize-url" "1.9.1"
    "schema-utils" "^1.0.0"
    "webpack-sources" "^1.1.0"

"mini-store@^2.0.0":
  "integrity" "sha1-CEPASNaULOVePnixtn/AYwIrVIg="
  "resolved" "https://registry.npm.taobao.org/mini-store/download/mini-store-2.0.0.tgz?cache=0&sync_timestamp=1596178831497&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fmini-store%2Fdownload%2Fmini-store-2.0.0.tgz"
  "version" "2.0.0"
  dependencies:
    "hoist-non-react-statics" "^2.3.1"
    "prop-types" "^15.6.0"
    "react-lifecycles-compat" "^3.0.4"
    "shallowequal" "^1.0.2"

"minimalistic-assert@^1.0.0", "minimalistic-assert@^1.0.1":
  "integrity" "sha1-LhlN4ERibUoQ5/f7wAznPoPk1cc="
  "resolved" "https://registry.nlark.com/minimalistic-assert/download/minimalistic-assert-1.0.1.tgz"
  "version" "1.0.1"

"minimalistic-crypto-utils@^1.0.1":
  "integrity" "sha1-9sAMHAsIIkblxNmd+4x8CDsrWCo="
  "resolved" "https://registry.nlark.com/minimalistic-crypto-utils/download/minimalistic-crypto-utils-1.0.1.tgz"
  "version" "1.0.1"

"minimatch@^3.0.4", "minimatch@~3.0.2":
  "integrity" "sha1-UWbihkV/AzBgZL5Ul+jbsMPTIIM="
  "resolved" "https://registry.npm.taobao.org/minimatch/download/minimatch-3.0.4.tgz"
  "version" "3.0.4"
  dependencies:
    "brace-expansion" "^1.1.7"

"minimist@^1.1.3", "minimist@^1.2.0", "minimist@^1.2.5":
  "integrity" "sha1-Z9ZgFLZqaoqqDAg8X9WN9OTpdgI="
  "resolved" "https://registry.npm.taobao.org/minimist/download/minimist-1.2.5.tgz"
  "version" "1.2.5"

"minipass-collect@^1.0.2":
  "integrity" "sha1-IrgTv3Rdxu26JXa5QAIq1u3Ixhc="
  "resolved" "https://registry.nlark.com/minipass-collect/download/minipass-collect-1.0.2.tgz"
  "version" "1.0.2"
  dependencies:
    "minipass" "^3.0.0"

"minipass-flush@^1.0.5":
  "integrity" "sha1-gucTXX6JpQ/+ZGEKeHlTxMTLs3M="
  "resolved" "https://registry.nlark.com/minipass-flush/download/minipass-flush-1.0.5.tgz"
  "version" "1.0.5"
  dependencies:
    "minipass" "^3.0.0"

"minipass-pipeline@^1.2.2":
  "integrity" "sha1-aEcveXEcCEZXwGfFxq2Tzd6oIUw="
  "resolved" "https://registry.npm.taobao.org/minipass-pipeline/download/minipass-pipeline-1.2.4.tgz?cache=0&sync_timestamp=1595998554107&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fminipass-pipeline%2Fdownload%2Fminipass-pipeline-1.2.4.tgz"
  "version" "1.2.4"
  dependencies:
    "minipass" "^3.0.0"

"minipass@^3.0.0", "minipass@^3.1.1":
  "integrity" "sha1-fUL/HzljVILhX5zbUxhN7r1YFf0="
  "resolved" "https://registry.npm.taobao.org/minipass/download/minipass-3.1.3.tgz"
  "version" "3.1.3"
  dependencies:
    "yallist" "^4.0.0"

"mississippi@^3.0.0":
  "integrity" "sha1-6goykfl+C16HdrNj1fChLZTGcCI="
  "resolved" "https://registry.npm.taobao.org/mississippi/download/mississippi-3.0.0.tgz"
  "version" "3.0.0"
  dependencies:
    "concat-stream" "^1.5.0"
    "duplexify" "^3.4.2"
    "end-of-stream" "^1.1.0"
    "flush-write-stream" "^1.0.0"
    "from2" "^2.1.0"
    "parallel-transform" "^1.1.0"
    "pump" "^3.0.0"
    "pumpify" "^1.3.3"
    "stream-each" "^1.1.0"
    "through2" "^2.0.0"

"mixin-deep@^1.2.0":
  "integrity" "sha1-ESC0PcNZp4Xc5ltVuC4lfM9HlWY="
  "resolved" "https://registry.nlark.com/mixin-deep/download/mixin-deep-1.3.2.tgz"
  "version" "1.3.2"
  dependencies:
    "for-in" "^1.0.2"
    "is-extendable" "^1.0.1"

"mkdirp@^0.5.0", "mkdirp@^0.5.1", "mkdirp@^0.5.3", "mkdirp@^0.5.5", "mkdirp@>=0.5 0", "mkdirp@~0.5.1":
  "integrity" "sha1-2Rzv1i0UNsoPQWIOJRKI1CAJne8="
  "resolved" "https://registry.npm.taobao.org/mkdirp/download/mkdirp-0.5.5.tgz"
  "version" "0.5.5"
  dependencies:
    "minimist" "^1.2.5"

"moment@^2.24.0", "moment@2.x":
  "integrity" "sha1-sr52n6MZQL6e7qZGnAdeNQBvo9M="
  "resolved" "https://registry.npm.taobao.org/moment/download/moment-2.29.1.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fmoment%2Fdownload%2Fmoment-2.29.1.tgz"
  "version" "2.29.1"

"move-concurrently@^1.0.1":
  "integrity" "sha1-viwAX9oy4LKa8fBdfEszIUxwH5I="
  "resolved" "https://registry.nlark.com/move-concurrently/download/move-concurrently-1.0.1.tgz"
  "version" "1.0.1"
  dependencies:
    "aproba" "^1.1.1"
    "copy-concurrently" "^1.0.0"
    "fs-write-stream-atomic" "^1.0.8"
    "mkdirp" "^0.5.1"
    "rimraf" "^2.5.4"
    "run-queue" "^1.0.3"

"ms@^2.1.1", "ms@2.1.2":
  "integrity" "sha1-0J0fNXtEP0kzgqjrPM0YOHKuYAk="
  "resolved" "https://registry.npm.taobao.org/ms/download/ms-2.1.2.tgz?cache=0&sync_timestamp=1607433899126&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fms%2Fdownload%2Fms-2.1.2.tgz"
  "version" "2.1.2"

"ms@2.0.0":
  "integrity" "sha1-VgiurfwAvmwpAd9fmGF4jeDVl8g="
  "resolved" "https://registry.npm.taobao.org/ms/download/ms-2.0.0.tgz?cache=0&sync_timestamp=1607433899126&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fms%2Fdownload%2Fms-2.0.0.tgz"
  "version" "2.0.0"

"ms@2.1.1":
  "integrity" "sha1-MKWGTrPrsKZvLr5tcnrwagnYbgo="
  "resolved" "https://registry.npm.taobao.org/ms/download/ms-2.1.1.tgz?cache=0&sync_timestamp=1607433899126&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fms%2Fdownload%2Fms-2.1.1.tgz"
  "version" "2.1.1"

"multicast-dns-service-types@^1.1.0":
  "integrity" "sha1-iZ8R2WhuXgXLkbNdXw5jt3PPyQE="
  "resolved" "https://registry.nlark.com/multicast-dns-service-types/download/multicast-dns-service-types-1.1.0.tgz"
  "version" "1.1.0"

"multicast-dns@^6.0.1":
  "integrity" "sha1-oOx72QVcQoL3kMPIL04o2zsxsik="
  "resolved" "https://registry.nlark.com/multicast-dns/download/multicast-dns-6.2.3.tgz?cache=0&sync_timestamp=1621890647706&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fmulticast-dns%2Fdownload%2Fmulticast-dns-6.2.3.tgz"
  "version" "6.2.3"
  dependencies:
    "dns-packet" "^1.3.1"
    "thunky" "^1.0.2"

"mutationobserver-shim@^0.3.2":
  "integrity" "sha1-i/YzsMCwKRoRByVe0ywTCIqMW/M="
  "resolved" "https://registry.nlark.com/mutationobserver-shim/download/mutationobserver-shim-0.3.7.tgz"
  "version" "0.3.7"

"nan@^2.13.2":
  "integrity" "sha1-9TdkAGlRaPTMaUrJOT0MlYXu6hk="
  "resolved" "https://registry.npm.taobao.org/nan/download/nan-2.14.2.tgz"
  "version" "2.14.2"

"nanomatch@^1.2.9":
  "integrity" "sha1-uHqKpPwN6P5r6IiVs4mD/yZb0Rk="
  "resolved" "https://registry.npm.taobao.org/nanomatch/download/nanomatch-1.2.13.tgz"
  "version" "1.2.13"
  dependencies:
    "arr-diff" "^4.0.0"
    "array-unique" "^0.3.2"
    "define-property" "^2.0.2"
    "extend-shallow" "^3.0.2"
    "fragment-cache" "^0.2.1"
    "is-windows" "^1.0.2"
    "kind-of" "^6.0.2"
    "object.pick" "^1.3.0"
    "regex-not" "^1.0.0"
    "snapdragon" "^0.8.1"
    "to-regex" "^3.0.1"

"native-request@^1.0.5":
  "integrity" "sha1-j2a/YG4PfqJ8DlmV6y9dA+M65vs="
  "resolved" "https://registry.npm.taobao.org/native-request/download/native-request-1.0.8.tgz?cache=0&sync_timestamp=1603412804149&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fnative-request%2Fdownload%2Fnative-request-1.0.8.tgz"
  "version" "1.0.8"

"natural-compare@^1.4.0":
  "integrity" "sha1-Sr6/7tdUHywnrPspvbvRXI1bpPc="
  "resolved" "https://registry.npm.taobao.org/natural-compare/download/natural-compare-1.4.0.tgz"
  "version" "1.4.0"

"negotiator@0.6.2":
  "integrity" "sha1-/qz3zPUlp3rpY0Q2pkiD/+yjRvs="
  "resolved" "https://registry.npm.taobao.org/negotiator/download/negotiator-0.6.2.tgz"
  "version" "0.6.2"

"neo-async@^2.5.0", "neo-async@^2.6.1":
  "integrity" "sha1-tKr7k+OustgXTKU88WOrfXMIMF8="
  "resolved" "https://registry.npm.taobao.org/neo-async/download/neo-async-2.6.2.tgz"
  "version" "2.6.2"

"nice-try@^1.0.4":
  "integrity" "sha1-ozeKdpbOfSI+iPybdkvX7xCJ42Y="
  "resolved" "https://registry.nlark.com/nice-try/download/nice-try-1.0.5.tgz"
  "version" "1.0.5"

"no-case@^2.2.0":
  "integrity" "sha1-YLgTOWvjmz8SiKTB7V0efSi0ZKw="
  "resolved" "https://registry.nlark.com/no-case/download/no-case-2.3.2.tgz"
  "version" "2.3.2"
  dependencies:
    "lower-case" "^1.1.1"

"node-fetch@^1.0.1":
  "integrity" "sha1-mA9vcthSEaU0fGsrwYxbhMPrR+8="
  "resolved" "https://registry.nlark.com/node-fetch/download/node-fetch-1.7.3.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fnode-fetch%2Fdownload%2Fnode-fetch-1.7.3.tgz"
  "version" "1.7.3"
  dependencies:
    "encoding" "^0.1.11"
    "is-stream" "^1.0.1"

"node-forge@^0.10.0":
  "integrity" "sha1-Mt6ir7Ppkm8C7lzoeUkCaRpna/M="
  "resolved" "https://registry.npm.taobao.org/node-forge/download/node-forge-0.10.0.tgz?cache=0&sync_timestamp=1599010726129&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fnode-forge%2Fdownload%2Fnode-forge-0.10.0.tgz"
  "version" "0.10.0"

"node-gyp@^3.8.0":
  "integrity" "sha1-VAMEJhwzDoDQ1e3OJTpoyzlkIYw="
  "resolved" "https://registry.nlark.com/node-gyp/download/node-gyp-3.8.0.tgz?cache=0&sync_timestamp=1622168102506&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fnode-gyp%2Fdownload%2Fnode-gyp-3.8.0.tgz"
  "version" "3.8.0"
  dependencies:
    "fstream" "^1.0.0"
    "glob" "^7.0.3"
    "graceful-fs" "^4.1.2"
    "mkdirp" "^0.5.0"
    "nopt" "2 || 3"
    "npmlog" "0 || 1 || 2 || 3 || 4"
    "osenv" "0"
    "request" "^2.87.0"
    "rimraf" "2"
    "semver" "~5.3.0"
    "tar" "^2.0.0"
    "which" "1"

"node-libs-browser@^2.2.1":
  "integrity" "sha1-tk9RPRgzhiX5A0bSew0jXmMfZCU="
  "resolved" "https://registry.nlark.com/node-libs-browser/download/node-libs-browser-2.2.1.tgz"
  "version" "2.2.1"
  dependencies:
    "assert" "^1.1.1"
    "browserify-zlib" "^0.2.0"
    "buffer" "^4.3.0"
    "console-browserify" "^1.1.0"
    "constants-browserify" "^1.0.0"
    "crypto-browserify" "^3.11.0"
    "domain-browser" "^1.1.1"
    "events" "^3.0.0"
    "https-browserify" "^1.0.0"
    "os-browserify" "^0.3.0"
    "path-browserify" "0.0.1"
    "process" "^0.11.10"
    "punycode" "^1.2.4"
    "querystring-es3" "^0.2.0"
    "readable-stream" "^2.3.3"
    "stream-browserify" "^2.0.1"
    "stream-http" "^2.7.2"
    "string_decoder" "^1.0.0"
    "timers-browserify" "^2.0.4"
    "tty-browserify" "0.0.0"
    "url" "^0.11.0"
    "util" "^0.11.0"
    "vm-browserify" "^1.0.1"

"node-releases@^1.1.71":
  "integrity" "sha1-3U6B3dUnf/hGuAtSu0DEnt96eyA="
  "resolved" "https://registry.nlark.com/node-releases/download/node-releases-1.1.73.tgz"
  "version" "1.1.73"

"node-sass@^4.12.0":
  "integrity" "sha1-mch+wu+3BH7WOPtMnbfzpC4iF7U="
  "resolved" "https://registry.nlark.com/node-sass/download/node-sass-4.14.1.tgz"
  "version" "4.14.1"
  dependencies:
    "async-foreach" "^0.1.3"
    "chalk" "^1.1.1"
    "cross-spawn" "^3.0.0"
    "gaze" "^1.0.0"
    "get-stdin" "^4.0.1"
    "glob" "^7.0.3"
    "in-publish" "^2.0.0"
    "lodash" "^4.17.15"
    "meow" "^3.7.0"
    "mkdirp" "^0.5.1"
    "nan" "^2.13.2"
    "node-gyp" "^3.8.0"
    "npmlog" "^4.0.0"
    "request" "^2.88.0"
    "sass-graph" "2.2.5"
    "stdout-stream" "^1.4.0"
    "true-case-path" "^1.0.2"

"nopt@2 || 3":
  "integrity" "sha1-xkZdvwirzU2zWTF/eaxopkayj/k="
  "resolved" "https://registry.npm.taobao.org/nopt/download/nopt-3.0.6.tgz?cache=0&sync_timestamp=1597649892953&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fnopt%2Fdownload%2Fnopt-3.0.6.tgz"
  "version" "3.0.6"
  dependencies:
    "abbrev" "1"

"normalize-package-data@^2.3.2", "normalize-package-data@^2.3.4":
  "integrity" "sha1-5m2xg4sgDB38IzIl0SyzZSDiNKg="
  "resolved" "https://registry.nlark.com/normalize-package-data/download/normalize-package-data-2.5.0.tgz"
  "version" "2.5.0"
  dependencies:
    "hosted-git-info" "^2.1.4"
    "resolve" "^1.10.0"
    "semver" "2 || 3 || 4 || 5"
    "validate-npm-package-license" "^3.0.1"

"normalize-path@^2.1.1":
  "integrity" "sha1-GrKLVW4Zg2Oowab35vogE3/mrtk="
  "resolved" "https://registry.nlark.com/normalize-path/download/normalize-path-2.1.1.tgz"
  "version" "2.1.1"
  dependencies:
    "remove-trailing-separator" "^1.0.1"

"normalize-path@^3.0.0", "normalize-path@~3.0.0":
  "integrity" "sha1-Dc1p/yOhybEf0JeDFmRKA4ghamU="
  "resolved" "https://registry.nlark.com/normalize-path/download/normalize-path-3.0.0.tgz"
  "version" "3.0.0"

"normalize-url@^3.0.0":
  "integrity" "sha1-suHE3E98bVd0PfczpPWXjRhlBVk="
  "resolved" "https://registry.nlark.com/normalize-url/download/normalize-url-3.3.0.tgz?cache=0&sync_timestamp=1624945908746&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fnormalize-url%2Fdownload%2Fnormalize-url-3.3.0.tgz"
  "version" "3.3.0"

"normalize-url@1.9.1":
  "integrity" "sha1-LMDWazHqIwNkWENuNiDYWVTGbDw="
  "resolved" "https://registry.nlark.com/normalize-url/download/normalize-url-1.9.1.tgz?cache=0&sync_timestamp=1624945908746&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fnormalize-url%2Fdownload%2Fnormalize-url-1.9.1.tgz"
  "version" "1.9.1"
  dependencies:
    "object-assign" "^4.0.1"
    "prepend-http" "^1.0.0"
    "query-string" "^4.1.0"
    "sort-keys" "^1.0.0"

"npm-run-path@^2.0.0":
  "integrity" "sha1-NakjLfo11wZ7TLLd8jV7GHFTbF8="
  "resolved" "https://registry.nlark.com/npm-run-path/download/npm-run-path-2.0.2.tgz"
  "version" "2.0.2"
  dependencies:
    "path-key" "^2.0.0"

"npmlog@^4.0.0", "npmlog@0 || 1 || 2 || 3 || 4":
  "integrity" "sha1-CKfyqL9zRgR3mp76StXMcXq7lUs="
  "resolved" "https://registry.nlark.com/npmlog/download/npmlog-4.1.2.tgz"
  "version" "4.1.2"
  dependencies:
    "are-we-there-yet" "~1.1.2"
    "console-control-strings" "~1.1.0"
    "gauge" "~2.7.3"
    "set-blocking" "~2.0.0"

"nth-check@^1.0.2":
  "integrity" "sha1-sr0pXDfj3VijvwcAN2Zjuk2c8Fw="
  "resolved" "https://registry.nlark.com/nth-check/download/nth-check-1.0.2.tgz"
  "version" "1.0.2"
  dependencies:
    "boolbase" "~1.0.0"

"nth-check@^2.0.0":
  "integrity" "sha1-G7T22scAcvwxPoyc0UF7UHTAoSU="
  "resolved" "https://registry.nlark.com/nth-check/download/nth-check-2.0.0.tgz"
  "version" "2.0.0"
  dependencies:
    "boolbase" "^1.0.0"

"number-is-nan@^1.0.0":
  "integrity" "sha1-CXtgK1NCKlIsGvuHkDGDNpQaAR0="
  "resolved" "https://registry.npm.taobao.org/number-is-nan/download/number-is-nan-1.0.1.tgz"
  "version" "1.0.1"

"oauth-sign@~0.9.0":
  "integrity" "sha1-R6ewFrqmi1+g7PPe4IqFxnmsZFU="
  "resolved" "https://registry.npm.taobao.org/oauth-sign/download/oauth-sign-0.9.0.tgz"
  "version" "0.9.0"

"object-assign@^4.0.1", "object-assign@^4.1.0", "object-assign@^4.1.1", "object-assign@4.x":
  "integrity" "sha1-IQmtx5ZYh8/AXLvUQsrIv7s2CGM="
  "resolved" "https://registry.nlark.com/object-assign/download/object-assign-4.1.1.tgz?cache=0&sync_timestamp=1618847240432&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fobject-assign%2Fdownload%2Fobject-assign-4.1.1.tgz"
  "version" "4.1.1"

"object-copy@^0.1.0":
  "integrity" "sha1-fn2Fi3gb18mRpBupde04EnVOmYw="
  "resolved" "https://registry.nlark.com/object-copy/download/object-copy-0.1.0.tgz"
  "version" "0.1.0"
  dependencies:
    "copy-descriptor" "^0.1.0"
    "define-property" "^0.2.5"
    "kind-of" "^3.0.3"

"object-inspect@^1.10.3", "object-inspect@^1.9.0":
  "integrity" "sha1-wqp9LQn1DJk3VwT3oK3yTFeC02k="
  "resolved" "https://registry.nlark.com/object-inspect/download/object-inspect-1.10.3.tgz"
  "version" "1.10.3"

"object-is@^1.0.1":
  "integrity" "sha1-ud7qpfx/GEag+uzc7sE45XePU6w="
  "resolved" "https://registry.nlark.com/object-is/download/object-is-1.1.5.tgz"
  "version" "1.1.5"
  dependencies:
    "call-bind" "^1.0.2"
    "define-properties" "^1.1.3"

"object-keys@^1.0.12", "object-keys@^1.1.1":
  "integrity" "sha1-HEfyct8nfzsdrwYWd9nILiMixg4="
  "resolved" "https://registry.npm.taobao.org/object-keys/download/object-keys-1.1.1.tgz"
  "version" "1.1.1"

"object-visit@^1.0.0":
  "integrity" "sha1-95xEk68MU3e1n+OdOV5BBC3QRbs="
  "resolved" "https://registry.npm.taobao.org/object-visit/download/object-visit-1.0.1.tgz"
  "version" "1.0.1"
  dependencies:
    "isobject" "^3.0.0"

"object.assign@^4.0.1", "object.assign@^4.1.0", "object.assign@^4.1.2":
  "integrity" "sha1-DtVKNC7Os3s4/3brgxoOeIy2OUA="
  "resolved" "https://registry.nlark.com/object.assign/download/object.assign-4.1.2.tgz"
  "version" "4.1.2"
  dependencies:
    "call-bind" "^1.0.0"
    "define-properties" "^1.1.3"
    "has-symbols" "^1.0.1"
    "object-keys" "^1.1.1"

"object.entries@^1.1.2", "object.entries@^1.1.4":
  "integrity" "sha1-Q8z5pQvF/VtknUWrGlefJOCIyv0="
  "resolved" "https://registry.nlark.com/object.entries/download/object.entries-1.1.4.tgz"
  "version" "1.1.4"
  dependencies:
    "call-bind" "^1.0.2"
    "define-properties" "^1.1.3"
    "es-abstract" "^1.18.2"

"object.fromentries@^2.0.4":
  "integrity" "sha1-JuG6XEVxxcbwiQzvRHMGZFahILg="
  "resolved" "https://registry.nlark.com/object.fromentries/download/object.fromentries-2.0.4.tgz"
  "version" "2.0.4"
  dependencies:
    "call-bind" "^1.0.2"
    "define-properties" "^1.1.3"
    "es-abstract" "^1.18.0-next.2"
    "has" "^1.0.3"

"object.getownpropertydescriptors@^2.0.3":
  "integrity" "sha1-G9Y66s8NXS0vMbXjk7A6fGAaI/c="
  "resolved" "https://registry.npm.taobao.org/object.getownpropertydescriptors/download/object.getownpropertydescriptors-2.1.2.tgz?cache=0&sync_timestamp=1613862016164&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fobject.getownpropertydescriptors%2Fdownload%2Fobject.getownpropertydescriptors-2.1.2.tgz"
  "version" "2.1.2"
  dependencies:
    "call-bind" "^1.0.2"
    "define-properties" "^1.1.3"
    "es-abstract" "^1.18.0-next.2"

"object.pick@^1.3.0":
  "integrity" "sha1-h6EKxMFpS9Lhy/U1kaZhQftd10c="
  "resolved" "https://registry.nlark.com/object.pick/download/object.pick-1.3.0.tgz"
  "version" "1.3.0"
  dependencies:
    "isobject" "^3.0.1"

"object.values@^1.1.0", "object.values@^1.1.3", "object.values@^1.1.4":
  "integrity" "sha1-DSc3YoM+gWtpOmN9MAc+cFFTWzA="
  "resolved" "https://registry.nlark.com/object.values/download/object.values-1.1.4.tgz"
  "version" "1.1.4"
  dependencies:
    "call-bind" "^1.0.2"
    "define-properties" "^1.1.3"
    "es-abstract" "^1.18.2"

"obuf@^1.0.0", "obuf@^1.1.2":
  "integrity" "sha1-Cb6jND1BhZ69RGKS0RydTbYZCE4="
  "resolved" "https://registry.npm.taobao.org/obuf/download/obuf-1.1.2.tgz"
  "version" "1.1.2"

"omit.js@^1.0.2":
  "integrity" "sha1-kaFPDrqEBm36AVvzDkdMR/MLyFg="
  "resolved" "https://registry.npm.taobao.org/omit.js/download/omit.js-1.0.2.tgz"
  "version" "1.0.2"
  dependencies:
    "babel-runtime" "^6.23.0"

"on-finished@~2.3.0":
  "integrity" "sha1-IPEzZIGwg811M3mSoWlxqi2QaUc="
  "resolved" "https://registry.npm.taobao.org/on-finished/download/on-finished-2.3.0.tgz"
  "version" "2.3.0"
  dependencies:
    "ee-first" "1.1.1"

"on-headers@~1.0.2":
  "integrity" "sha1-dysK5qqlJcOZ5Imt+tkMQD6zwo8="
  "resolved" "https://registry.nlark.com/on-headers/download/on-headers-1.0.2.tgz"
  "version" "1.0.2"

"once@^1.3.0", "once@^1.3.1", "once@^1.4.0":
  "integrity" "sha1-WDsap3WWHUsROsF9nFC6753Xa9E="
  "resolved" "https://registry.nlark.com/once/download/once-1.4.0.tgz"
  "version" "1.4.0"
  dependencies:
    "wrappy" "1"

"opn@^5.5.0":
  "integrity" "sha1-/HFk+rVtI1kExRw7J9pnWMo7m/w="
  "resolved" "https://registry.npm.taobao.org/opn/download/opn-5.5.0.tgz"
  "version" "5.5.0"
  dependencies:
    "is-wsl" "^1.1.0"

"optimize-css-assets-webpack-plugin@^5.0.3":
  "integrity" "sha1-y8zc9abvYdT4zHjPCDpnRG5fQCo="
  "resolved" "https://registry.nlark.com/optimize-css-assets-webpack-plugin/download/optimize-css-assets-webpack-plugin-5.0.8.tgz"
  "version" "5.0.8"
  dependencies:
    "cssnano" "^4.1.10"
    "last-call-webpack-plugin" "^3.0.0"

"optionator@^0.9.1":
  "integrity" "sha1-TyNqY3Pa4FZqbUPhMmZ09QwpFJk="
  "resolved" "https://registry.npm.taobao.org/optionator/download/optionator-0.9.1.tgz"
  "version" "0.9.1"
  dependencies:
    "deep-is" "^0.1.3"
    "fast-levenshtein" "^2.0.6"
    "levn" "^0.4.1"
    "prelude-ls" "^1.2.1"
    "type-check" "^0.4.0"
    "word-wrap" "^1.2.3"

"original@^1.0.0":
  "integrity" "sha1-5EKmHP/hxf0gpl8yYcJmY7MD8l8="
  "resolved" "https://registry.nlark.com/original/download/original-1.0.2.tgz"
  "version" "1.0.2"
  dependencies:
    "url-parse" "^1.4.3"

"os-browserify@^0.3.0":
  "integrity" "sha1-hUNzx/XCMVkU/Jv8a9gjj92h7Cc="
  "resolved" "https://registry.nlark.com/os-browserify/download/os-browserify-0.3.0.tgz"
  "version" "0.3.0"

"os-homedir@^1.0.0":
  "integrity" "sha1-/7xJiDNuDoM94MFox+8VISGqf7M="
  "resolved" "https://registry.npm.taobao.org/os-homedir/download/os-homedir-1.0.2.tgz"
  "version" "1.0.2"

"os-tmpdir@^1.0.0":
  "integrity" "sha1-u+Z0BseaqFxc/sdm/lc0VV36EnQ="
  "resolved" "https://registry.nlark.com/os-tmpdir/download/os-tmpdir-1.0.2.tgz"
  "version" "1.0.2"

"osenv@0":
  "integrity" "sha1-hc36+uso6Gd/QW4odZK18/SepBA="
  "resolved" "https://registry.npm.taobao.org/osenv/download/osenv-0.1.5.tgz"
  "version" "0.1.5"
  dependencies:
    "os-homedir" "^1.0.0"
    "os-tmpdir" "^1.0.0"

"p-finally@^1.0.0":
  "integrity" "sha1-P7z7FbiZpEEjs0ttzBi3JDNqLK4="
  "resolved" "https://registry.npm.taobao.org/p-finally/download/p-finally-1.0.0.tgz?cache=0&sync_timestamp=1617947695861&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fp-finally%2Fdownload%2Fp-finally-1.0.0.tgz"
  "version" "1.0.0"

"p-limit@^1.1.0":
  "integrity" "sha1-uGvV8MJWkJEcdZD8v8IBDVSzzLg="
  "resolved" "https://registry.nlark.com/p-limit/download/p-limit-1.3.0.tgz"
  "version" "1.3.0"
  dependencies:
    "p-try" "^1.0.0"

"p-limit@^2.0.0", "p-limit@^2.2.0", "p-limit@^2.2.1":
  "integrity" "sha1-PdM8ZHohT9//2DWTPrCG2g3CHbE="
  "resolved" "https://registry.nlark.com/p-limit/download/p-limit-2.3.0.tgz"
  "version" "2.3.0"
  dependencies:
    "p-try" "^2.0.0"

"p-locate@^2.0.0":
  "integrity" "sha1-IKAQOyIqcMj9OcwuWAaA893l7EM="
  "resolved" "https://registry.nlark.com/p-locate/download/p-locate-2.0.0.tgz"
  "version" "2.0.0"
  dependencies:
    "p-limit" "^1.1.0"

"p-locate@^3.0.0":
  "integrity" "sha1-Mi1poFwCZLJZl9n0DNiokasAZKQ="
  "resolved" "https://registry.nlark.com/p-locate/download/p-locate-3.0.0.tgz"
  "version" "3.0.0"
  dependencies:
    "p-limit" "^2.0.0"

"p-locate@^4.1.0":
  "integrity" "sha1-o0KLtwiLOmApL2aRkni3wpetTwc="
  "resolved" "https://registry.nlark.com/p-locate/download/p-locate-4.1.0.tgz"
  "version" "4.1.0"
  dependencies:
    "p-limit" "^2.2.0"

"p-map@^2.0.0":
  "integrity" "sha1-MQko/u+cnsxltosXaTAYpmXOoXU="
  "resolved" "https://registry.nlark.com/p-map/download/p-map-2.1.0.tgz"
  "version" "2.1.0"

"p-map@^3.0.0":
  "integrity" "sha1-1wTZr4orpoTiYA2aIVmD1BQal50="
  "resolved" "https://registry.nlark.com/p-map/download/p-map-3.0.0.tgz"
  "version" "3.0.0"
  dependencies:
    "aggregate-error" "^3.0.0"

"p-retry@^3.0.1":
  "integrity" "sha1-MWtMiJPiyNwc+okfQGxLQivr8yg="
  "resolved" "https://registry.nlark.com/p-retry/download/p-retry-3.0.1.tgz?cache=0&sync_timestamp=1624534411422&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fp-retry%2Fdownload%2Fp-retry-3.0.1.tgz"
  "version" "3.0.1"
  dependencies:
    "retry" "^0.12.0"

"p-try@^1.0.0":
  "integrity" "sha1-y8ec26+P1CKOE/Yh8rGiN8GyB7M="
  "resolved" "https://registry.nlark.com/p-try/download/p-try-1.0.0.tgz"
  "version" "1.0.0"

"p-try@^2.0.0":
  "integrity" "sha1-yyhoVA4xPWHeWPr741zpAE1VQOY="
  "resolved" "https://registry.nlark.com/p-try/download/p-try-2.2.0.tgz"
  "version" "2.2.0"

"pako@~1.0.2", "pako@~1.0.5":
  "integrity" "sha1-bJWZ00DVTf05RjgCUqNXBaa5kr8="
  "resolved" "https://registry.nlark.com/pako/download/pako-1.0.11.tgz"
  "version" "1.0.11"

"parallel-transform@^1.1.0":
  "integrity" "sha1-kEnKN9bLIYLDsdLHIL6U0UpYFPw="
  "resolved" "https://registry.npm.taobao.org/parallel-transform/download/parallel-transform-1.2.0.tgz"
  "version" "1.2.0"
  dependencies:
    "cyclist" "^1.0.1"
    "inherits" "^2.0.3"
    "readable-stream" "^2.1.5"

"param-case@2.1.x":
  "integrity" "sha1-35T9jPZTHs915r75oIWPvHK+Ikc="
  "resolved" "https://registry.nlark.com/param-case/download/param-case-2.1.1.tgz"
  "version" "2.1.1"
  dependencies:
    "no-case" "^2.2.0"

"parent-module@^1.0.0":
  "integrity" "sha1-aR0nCeeMefrjoVZiJFLQB2LKqqI="
  "resolved" "https://registry.npm.taobao.org/parent-module/download/parent-module-1.0.1.tgz"
  "version" "1.0.1"
  dependencies:
    "callsites" "^3.0.0"

"parse-asn1@^5.0.0", "parse-asn1@^5.1.5":
  "integrity" "sha1-OFCAo+wTy2KmLTlAnLPoiETNrtQ="
  "resolved" "https://registry.npm.taobao.org/parse-asn1/download/parse-asn1-5.1.6.tgz?cache=0&sync_timestamp=1597165710136&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fparse-asn1%2Fdownload%2Fparse-asn1-5.1.6.tgz"
  "version" "5.1.6"
  dependencies:
    "asn1.js" "^5.2.0"
    "browserify-aes" "^1.0.0"
    "evp_bytestokey" "^1.0.0"
    "pbkdf2" "^3.0.3"
    "safe-buffer" "^5.1.1"

"parse-json@^2.2.0":
  "integrity" "sha1-9ID0BDTvgHQfhGkJn43qGPVaTck="
  "resolved" "https://registry.nlark.com/parse-json/download/parse-json-2.2.0.tgz"
  "version" "2.2.0"
  dependencies:
    "error-ex" "^1.2.0"

"parse-json@^4.0.0":
  "integrity" "sha1-vjX1Qlvh9/bHRxhPmKeIy5lHfuA="
  "resolved" "https://registry.nlark.com/parse-json/download/parse-json-4.0.0.tgz"
  "version" "4.0.0"
  dependencies:
    "error-ex" "^1.3.1"
    "json-parse-better-errors" "^1.0.1"

"parse-passwd@^1.0.0":
  "integrity" "sha1-bVuTSkVpk7I9N/QKOC1vFmao5cY="
  "resolved" "https://registry.nlark.com/parse-passwd/download/parse-passwd-1.0.0.tgz"
  "version" "1.0.0"

"parseurl@~1.3.2", "parseurl@~1.3.3":
  "integrity" "sha1-naGee+6NEt/wUT7Vt2lXeTvC6NQ="
  "resolved" "https://registry.npm.taobao.org/parseurl/download/parseurl-1.3.3.tgz"
  "version" "1.3.3"

"pascalcase@^0.1.1":
  "integrity" "sha1-s2PlXoAGym/iF4TS2yK9FdeRfxQ="
  "resolved" "https://registry.npm.taobao.org/pascalcase/download/pascalcase-0.1.1.tgz"
  "version" "0.1.1"

"path-browserify@0.0.1":
  "integrity" "sha1-5sTd1+06onxoogzE5Q4aTug7vEo="
  "resolved" "https://registry.npm.taobao.org/path-browserify/download/path-browserify-0.0.1.tgz"
  "version" "0.0.1"

"path-dirname@^1.0.0":
  "integrity" "sha1-zDPSTVJeCZpTiMAzbG4yuRYGCeA="
  "resolved" "https://registry.npm.taobao.org/path-dirname/download/path-dirname-1.0.2.tgz"
  "version" "1.0.2"

"path-exists@^2.0.0":
  "integrity" "sha1-D+tsZPD8UY2adU3V77YscCJ2H0s="
  "resolved" "https://registry.nlark.com/path-exists/download/path-exists-2.1.0.tgz"
  "version" "2.1.0"
  dependencies:
    "pinkie-promise" "^2.0.0"

"path-exists@^3.0.0":
  "integrity" "sha1-zg6+ql94yxiSXqfYENe1mwEP1RU="
  "resolved" "https://registry.nlark.com/path-exists/download/path-exists-3.0.0.tgz"
  "version" "3.0.0"

"path-exists@^4.0.0":
  "integrity" "sha1-UTvb4tO5XXdi6METfvoZXGxhtbM="
  "resolved" "https://registry.nlark.com/path-exists/download/path-exists-4.0.0.tgz"
  "version" "4.0.0"

"path-is-absolute@^1.0.0":
  "integrity" "sha1-F0uSaHNVNP+8es5r9TpanhtcX18="
  "resolved" "https://registry.npm.taobao.org/path-is-absolute/download/path-is-absolute-1.0.1.tgz"
  "version" "1.0.1"

"path-is-inside@^1.0.2":
  "integrity" "sha1-NlQX3t5EQw0cEa9hAn+s8HS9/FM="
  "resolved" "https://registry.npm.taobao.org/path-is-inside/download/path-is-inside-1.0.2.tgz"
  "version" "1.0.2"

"path-key@^2.0.0", "path-key@^2.0.1":
  "integrity" "sha1-QRyttXTFoUDTpLGRDUDYDMn0C0A="
  "resolved" "https://registry.nlark.com/path-key/download/path-key-2.0.1.tgz"
  "version" "2.0.1"

"path-key@^3.1.0":
  "integrity" "sha1-WB9q3mWMu6ZaDTOA3ndTKVBU83U="
  "resolved" "https://registry.nlark.com/path-key/download/path-key-3.1.1.tgz"
  "version" "3.1.1"

"path-parse@^1.0.6":
  "integrity" "sha1-+8EUtgykKzDZ2vWFjkvWi77bZzU="
  "resolved" "https://registry.nlark.com/path-parse/download/path-parse-1.0.7.tgz"
  "version" "1.0.7"

"path-to-regexp@^1.7.0":
  "integrity" "sha1-iHs7qdhDk+h6CgufTLdWGYtTVIo="
  "resolved" "https://registry.nlark.com/path-to-regexp/download/path-to-regexp-1.8.0.tgz?cache=0&sync_timestamp=1618847046445&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fpath-to-regexp%2Fdownload%2Fpath-to-regexp-1.8.0.tgz"
  "version" "1.8.0"
  dependencies:
    "isarray" "0.0.1"

"path-to-regexp@0.1.7":
  "integrity" "sha1-32BBeABfUi8V60SQ5yR6G/qmf4w="
  "resolved" "https://registry.nlark.com/path-to-regexp/download/path-to-regexp-0.1.7.tgz?cache=0&sync_timestamp=1618847046445&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fpath-to-regexp%2Fdownload%2Fpath-to-regexp-0.1.7.tgz"
  "version" "0.1.7"

"path-type@^1.0.0":
  "integrity" "sha1-WcRPfuSR2nBNpBXaWkBwuk+P5EE="
  "resolved" "https://registry.npm.taobao.org/path-type/download/path-type-1.1.0.tgz?cache=0&sync_timestamp=1611752015315&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fpath-type%2Fdownload%2Fpath-type-1.1.0.tgz"
  "version" "1.1.0"
  dependencies:
    "graceful-fs" "^4.1.2"
    "pify" "^2.0.0"
    "pinkie-promise" "^2.0.0"

"path-type@^3.0.0":
  "integrity" "sha1-zvMdyOCho7sNEFwM2Xzzv0f0428="
  "resolved" "https://registry.npm.taobao.org/path-type/download/path-type-3.0.0.tgz?cache=0&sync_timestamp=1611752015315&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fpath-type%2Fdownload%2Fpath-type-3.0.0.tgz"
  "version" "3.0.0"
  dependencies:
    "pify" "^3.0.0"

"path@^0.12.7":
  "integrity" "sha1-1NwqUGxM4hl+tIHr/NWzbAFAsQ8="
  "resolved" "https://registry.npm.taobao.org/path/download/path-0.12.7.tgz"
  "version" "0.12.7"
  dependencies:
    "process" "^0.11.1"
    "util" "^0.10.3"

"pbkdf2@^3.0.3":
  "integrity" "sha1-3YIqoIh1gOUvGgOdw+2hCO+uMHU="
  "resolved" "https://registry.npm.taobao.org/pbkdf2/download/pbkdf2-3.1.2.tgz?cache=0&sync_timestamp=1617976842723&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fpbkdf2%2Fdownload%2Fpbkdf2-3.1.2.tgz"
  "version" "3.1.2"
  dependencies:
    "create-hash" "^1.1.2"
    "create-hmac" "^1.1.4"
    "ripemd160" "^2.0.1"
    "safe-buffer" "^5.0.1"
    "sha.js" "^2.4.8"

"performance-now@^2.1.0":
  "integrity" "sha1-Ywn04OX6kT7BxpMHrjZLSzd8nns="
  "resolved" "https://registry.nlark.com/performance-now/download/performance-now-2.1.0.tgz"
  "version" "2.1.0"

"picomatch@^2.0.4", "picomatch@^2.2.1":
  "integrity" "sha1-8fBh3o9qS/AiiS4tEoI0+5gwKXI="
  "resolved" "https://registry.nlark.com/picomatch/download/picomatch-2.3.0.tgz?cache=0&sync_timestamp=1621648246651&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fpicomatch%2Fdownload%2Fpicomatch-2.3.0.tgz"
  "version" "2.3.0"

"pify@^2.0.0":
  "integrity" "sha1-7RQaasBDqEnqWISY59yosVMw6Qw="
  "resolved" "https://registry.npm.taobao.org/pify/download/pify-2.3.0.tgz"
  "version" "2.3.0"

"pify@^3.0.0":
  "integrity" "sha1-5aSs0sEB/fPZpNB/DbxNtJ3SgXY="
  "resolved" "https://registry.npm.taobao.org/pify/download/pify-3.0.0.tgz"
  "version" "3.0.0"

"pify@^4.0.1":
  "integrity" "sha1-SyzSXFDVmHNcUCkiJP2MbfQeMjE="
  "resolved" "https://registry.npm.taobao.org/pify/download/pify-4.0.1.tgz"
  "version" "4.0.1"

"pinkie-promise@^2.0.0":
  "integrity" "sha1-ITXW36ejWMBprJsXh3YogihFD/o="
  "resolved" "https://registry.npm.taobao.org/pinkie-promise/download/pinkie-promise-2.0.1.tgz"
  "version" "2.0.1"
  dependencies:
    "pinkie" "^2.0.0"

"pinkie@^2.0.0":
  "integrity" "sha1-clVrgM+g1IqXToDnckjoDtT3+HA="
  "resolved" "https://registry.npm.taobao.org/pinkie/download/pinkie-2.0.4.tgz"
  "version" "2.0.4"

"pkg-dir@^2.0.0":
  "integrity" "sha1-9tXREJ4Z1j7fQo4L1X4Sd3YVM0s="
  "resolved" "https://registry.nlark.com/pkg-dir/download/pkg-dir-2.0.0.tgz"
  "version" "2.0.0"
  dependencies:
    "find-up" "^2.1.0"

"pkg-dir@^3.0.0":
  "integrity" "sha1-J0kCDyOe2ZCIGx9xIQ1R62UjvqM="
  "resolved" "https://registry.nlark.com/pkg-dir/download/pkg-dir-3.0.0.tgz"
  "version" "3.0.0"
  dependencies:
    "find-up" "^3.0.0"

"pkg-dir@^4.1.0":
  "integrity" "sha1-8JkTPfft5CLoHR2ESCcO6z5CYfM="
  "resolved" "https://registry.nlark.com/pkg-dir/download/pkg-dir-4.2.0.tgz"
  "version" "4.2.0"
  dependencies:
    "find-up" "^4.0.0"

"pkg-up@^2.0.0":
  "integrity" "sha1-yBmscoBZpGHKscOImivjxJoATX8="
  "resolved" "https://registry.npm.taobao.org/pkg-up/download/pkg-up-2.0.0.tgz"
  "version" "2.0.0"
  dependencies:
    "find-up" "^2.1.0"

"portfinder@^1.0.26":
  "integrity" "sha1-Z8RiKFK9U3TdHdkA93n1NGL6x3g="
  "resolved" "https://registry.nlark.com/portfinder/download/portfinder-1.0.28.tgz"
  "version" "1.0.28"
  dependencies:
    "async" "^2.6.2"
    "debug" "^3.1.1"
    "mkdirp" "^0.5.5"

"posix-character-classes@^0.1.0":
  "integrity" "sha1-AerA/jta9xoqbAL+q7jB/vfgDqs="
  "resolved" "https://registry.nlark.com/posix-character-classes/download/posix-character-classes-0.1.1.tgz"
  "version" "0.1.1"

"postcss-calc@^7.0.1":
  "integrity" "sha1-+KbpnxLmGcLrwjz2xIb9wVhgkz4="
  "resolved" "https://registry.nlark.com/postcss-calc/download/postcss-calc-7.0.5.tgz"
  "version" "7.0.5"
  dependencies:
    "postcss" "^7.0.27"
    "postcss-selector-parser" "^6.0.2"
    "postcss-value-parser" "^4.0.2"

"postcss-colormin@^4.0.3":
  "integrity" "sha1-rgYLzpPteUrHEmTwgTLVUJVr04E="
  "resolved" "https://registry.nlark.com/postcss-colormin/download/postcss-colormin-4.0.3.tgz"
  "version" "4.0.3"
  dependencies:
    "browserslist" "^4.0.0"
    "color" "^3.0.0"
    "has" "^1.0.0"
    "postcss" "^7.0.0"
    "postcss-value-parser" "^3.0.0"

"postcss-convert-values@^4.0.1":
  "integrity" "sha1-yjgT7U2g+BL51DcDWE5Enr4Ymn8="
  "resolved" "https://registry.nlark.com/postcss-convert-values/download/postcss-convert-values-4.0.1.tgz"
  "version" "4.0.1"
  dependencies:
    "postcss" "^7.0.0"
    "postcss-value-parser" "^3.0.0"

"postcss-discard-comments@^4.0.2":
  "integrity" "sha1-H7q9LCRr/2qq15l7KwkY9NevQDM="
  "resolved" "https://registry.nlark.com/postcss-discard-comments/download/postcss-discard-comments-4.0.2.tgz?cache=0&sync_timestamp=1621449592883&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fpostcss-discard-comments%2Fdownload%2Fpostcss-discard-comments-4.0.2.tgz"
  "version" "4.0.2"
  dependencies:
    "postcss" "^7.0.0"

"postcss-discard-duplicates@^4.0.2":
  "integrity" "sha1-P+EzzTyCKC5VD8myORdqkge3hOs="
  "resolved" "https://registry.nlark.com/postcss-discard-duplicates/download/postcss-discard-duplicates-4.0.2.tgz?cache=0&sync_timestamp=1621449593093&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fpostcss-discard-duplicates%2Fdownload%2Fpostcss-discard-duplicates-4.0.2.tgz"
  "version" "4.0.2"
  dependencies:
    "postcss" "^7.0.0"

"postcss-discard-empty@^4.0.1":
  "integrity" "sha1-yMlR6fc+2UKAGUWERKAq2Qu592U="
  "resolved" "https://registry.nlark.com/postcss-discard-empty/download/postcss-discard-empty-4.0.1.tgz"
  "version" "4.0.1"
  dependencies:
    "postcss" "^7.0.0"

"postcss-discard-overridden@^4.0.1":
  "integrity" "sha1-ZSrvipZybwKfXj4AFG7npOdV/1c="
  "resolved" "https://registry.nlark.com/postcss-discard-overridden/download/postcss-discard-overridden-4.0.1.tgz"
  "version" "4.0.1"
  dependencies:
    "postcss" "^7.0.0"

"postcss-merge-longhand@^4.0.11":
  "integrity" "sha1-YvSaE+Sg7gTnuY9CuxYGLKJUniQ="
  "resolved" "https://registry.nlark.com/postcss-merge-longhand/download/postcss-merge-longhand-4.0.11.tgz"
  "version" "4.0.11"
  dependencies:
    "css-color-names" "0.0.4"
    "postcss" "^7.0.0"
    "postcss-value-parser" "^3.0.0"
    "stylehacks" "^4.0.0"

"postcss-merge-rules@^4.0.3":
  "integrity" "sha1-NivqT/Wh+Y5AdacTxsslrv75plA="
  "resolved" "https://registry.nlark.com/postcss-merge-rules/download/postcss-merge-rules-4.0.3.tgz?cache=0&sync_timestamp=1622234641993&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fpostcss-merge-rules%2Fdownload%2Fpostcss-merge-rules-4.0.3.tgz"
  "version" "4.0.3"
  dependencies:
    "browserslist" "^4.0.0"
    "caniuse-api" "^3.0.0"
    "cssnano-util-same-parent" "^4.0.0"
    "postcss" "^7.0.0"
    "postcss-selector-parser" "^3.0.0"
    "vendors" "^1.0.0"

"postcss-minify-font-values@^4.0.2":
  "integrity" "sha1-zUw0TM5HQ0P6xdgiBqssvLiv1aY="
  "resolved" "https://registry.nlark.com/postcss-minify-font-values/download/postcss-minify-font-values-4.0.2.tgz"
  "version" "4.0.2"
  dependencies:
    "postcss" "^7.0.0"
    "postcss-value-parser" "^3.0.0"

"postcss-minify-gradients@^4.0.2":
  "integrity" "sha1-k7KcL/UJnFNe7NpWxKpuZlpmNHE="
  "resolved" "https://registry.nlark.com/postcss-minify-gradients/download/postcss-minify-gradients-4.0.2.tgz?cache=0&sync_timestamp=1621449598992&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fpostcss-minify-gradients%2Fdownload%2Fpostcss-minify-gradients-4.0.2.tgz"
  "version" "4.0.2"
  dependencies:
    "cssnano-util-get-arguments" "^4.0.0"
    "is-color-stop" "^1.0.0"
    "postcss" "^7.0.0"
    "postcss-value-parser" "^3.0.0"

"postcss-minify-params@^4.0.2":
  "integrity" "sha1-a5zvAwwR41Jh+V9hjJADbWgNuHQ="
  "resolved" "https://registry.nlark.com/postcss-minify-params/download/postcss-minify-params-4.0.2.tgz"
  "version" "4.0.2"
  dependencies:
    "alphanum-sort" "^1.0.0"
    "browserslist" "^4.0.0"
    "cssnano-util-get-arguments" "^4.0.0"
    "postcss" "^7.0.0"
    "postcss-value-parser" "^3.0.0"
    "uniqs" "^2.0.0"

"postcss-minify-selectors@^4.0.2":
  "integrity" "sha1-4uXrQL/uUA0M2SQ1APX46kJi+9g="
  "resolved" "https://registry.nlark.com/postcss-minify-selectors/download/postcss-minify-selectors-4.0.2.tgz?cache=0&sync_timestamp=1621449593365&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fpostcss-minify-selectors%2Fdownload%2Fpostcss-minify-selectors-4.0.2.tgz"
  "version" "4.0.2"
  dependencies:
    "alphanum-sort" "^1.0.0"
    "has" "^1.0.0"
    "postcss" "^7.0.0"
    "postcss-selector-parser" "^3.0.0"

"postcss-modules-extract-imports@^2.0.0":
  "integrity" "sha1-gYcZoa4doyX5gyRGsBE27rSTzX4="
  "resolved" "https://registry.nlark.com/postcss-modules-extract-imports/download/postcss-modules-extract-imports-2.0.0.tgz"
  "version" "2.0.0"
  dependencies:
    "postcss" "^7.0.5"

"postcss-modules-local-by-default@^3.0.2":
  "integrity" "sha1-uxTgzHgnnVBNvcv9fgyiiZP/u7A="
  "resolved" "https://registry.npm.taobao.org/postcss-modules-local-by-default/download/postcss-modules-local-by-default-3.0.3.tgz?cache=0&sync_timestamp=1602587625149&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fpostcss-modules-local-by-default%2Fdownload%2Fpostcss-modules-local-by-default-3.0.3.tgz"
  "version" "3.0.3"
  dependencies:
    "icss-utils" "^4.1.1"
    "postcss" "^7.0.32"
    "postcss-selector-parser" "^6.0.2"
    "postcss-value-parser" "^4.1.0"

"postcss-modules-scope@^2.2.0":
  "integrity" "sha1-OFyuATzHdD9afXYC0Qc6iequYu4="
  "resolved" "https://registry.npm.taobao.org/postcss-modules-scope/download/postcss-modules-scope-2.2.0.tgz"
  "version" "2.2.0"
  dependencies:
    "postcss" "^7.0.6"
    "postcss-selector-parser" "^6.0.0"

"postcss-modules-values@^3.0.0":
  "integrity" "sha1-W1AA1uuuKbQlUwG0o6VFdEI+fxA="
  "resolved" "https://registry.nlark.com/postcss-modules-values/download/postcss-modules-values-3.0.0.tgz"
  "version" "3.0.0"
  dependencies:
    "icss-utils" "^4.0.0"
    "postcss" "^7.0.6"

"postcss-normalize-charset@^4.0.1":
  "integrity" "sha1-izWt067oOhNrBHHg1ZvlilAoXdQ="
  "resolved" "https://registry.nlark.com/postcss-normalize-charset/download/postcss-normalize-charset-4.0.1.tgz?cache=0&sync_timestamp=1621449593655&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fpostcss-normalize-charset%2Fdownload%2Fpostcss-normalize-charset-4.0.1.tgz"
  "version" "4.0.1"
  dependencies:
    "postcss" "^7.0.0"

"postcss-normalize-display-values@^4.0.2":
  "integrity" "sha1-Db4EpM6QY9RmftK+R2u4MMglk1o="
  "resolved" "https://registry.nlark.com/postcss-normalize-display-values/download/postcss-normalize-display-values-4.0.2.tgz?cache=0&sync_timestamp=1621449599414&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fpostcss-normalize-display-values%2Fdownload%2Fpostcss-normalize-display-values-4.0.2.tgz"
  "version" "4.0.2"
  dependencies:
    "cssnano-util-get-match" "^4.0.0"
    "postcss" "^7.0.0"
    "postcss-value-parser" "^3.0.0"

"postcss-normalize-positions@^4.0.2":
  "integrity" "sha1-BfdX+E8mBDc3g2ipH4ky1LECkX8="
  "resolved" "https://registry.nlark.com/postcss-normalize-positions/download/postcss-normalize-positions-4.0.2.tgz"
  "version" "4.0.2"
  dependencies:
    "cssnano-util-get-arguments" "^4.0.0"
    "has" "^1.0.0"
    "postcss" "^7.0.0"
    "postcss-value-parser" "^3.0.0"

"postcss-normalize-repeat-style@^4.0.2":
  "integrity" "sha1-xOu8KJ85kaAo1EdRy90RkYsXkQw="
  "resolved" "https://registry.nlark.com/postcss-normalize-repeat-style/download/postcss-normalize-repeat-style-4.0.2.tgz?cache=0&sync_timestamp=1621449596114&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fpostcss-normalize-repeat-style%2Fdownload%2Fpostcss-normalize-repeat-style-4.0.2.tgz"
  "version" "4.0.2"
  dependencies:
    "cssnano-util-get-arguments" "^4.0.0"
    "cssnano-util-get-match" "^4.0.0"
    "postcss" "^7.0.0"
    "postcss-value-parser" "^3.0.0"

"postcss-normalize-string@^4.0.2":
  "integrity" "sha1-zUTECrB6DHo23F6Zqs4eyk7CaQw="
  "resolved" "https://registry.nlark.com/postcss-normalize-string/download/postcss-normalize-string-4.0.2.tgz?cache=0&sync_timestamp=1621449595099&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fpostcss-normalize-string%2Fdownload%2Fpostcss-normalize-string-4.0.2.tgz"
  "version" "4.0.2"
  dependencies:
    "has" "^1.0.0"
    "postcss" "^7.0.0"
    "postcss-value-parser" "^3.0.0"

"postcss-normalize-timing-functions@^4.0.2":
  "integrity" "sha1-jgCcoqOUnNr4rSPmtquZy159KNk="
  "resolved" "https://registry.nlark.com/postcss-normalize-timing-functions/download/postcss-normalize-timing-functions-4.0.2.tgz"
  "version" "4.0.2"
  dependencies:
    "cssnano-util-get-match" "^4.0.0"
    "postcss" "^7.0.0"
    "postcss-value-parser" "^3.0.0"

"postcss-normalize-unicode@^4.0.1":
  "integrity" "sha1-hBvUj9zzAZrUuqdJOj02O1KuHPs="
  "resolved" "https://registry.nlark.com/postcss-normalize-unicode/download/postcss-normalize-unicode-4.0.1.tgz"
  "version" "4.0.1"
  dependencies:
    "browserslist" "^4.0.0"
    "postcss" "^7.0.0"
    "postcss-value-parser" "^3.0.0"

"postcss-normalize-url@^4.0.1":
  "integrity" "sha1-EOQ3+GvHx+WPe5ZS7YeNqqlfquE="
  "resolved" "https://registry.nlark.com/postcss-normalize-url/download/postcss-normalize-url-4.0.1.tgz"
  "version" "4.0.1"
  dependencies:
    "is-absolute-url" "^2.0.0"
    "normalize-url" "^3.0.0"
    "postcss" "^7.0.0"
    "postcss-value-parser" "^3.0.0"

"postcss-normalize-whitespace@^4.0.2":
  "integrity" "sha1-vx1AcP5Pzqh9E0joJdjMDF+qfYI="
  "resolved" "https://registry.nlark.com/postcss-normalize-whitespace/download/postcss-normalize-whitespace-4.0.2.tgz?cache=0&sync_timestamp=1621449593892&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fpostcss-normalize-whitespace%2Fdownload%2Fpostcss-normalize-whitespace-4.0.2.tgz"
  "version" "4.0.2"
  dependencies:
    "postcss" "^7.0.0"
    "postcss-value-parser" "^3.0.0"

"postcss-ordered-values@^4.1.2":
  "integrity" "sha1-DPdcgg7H1cTSgBiVWeC1ceusDu4="
  "resolved" "https://registry.nlark.com/postcss-ordered-values/download/postcss-ordered-values-4.1.2.tgz"
  "version" "4.1.2"
  dependencies:
    "cssnano-util-get-arguments" "^4.0.0"
    "postcss" "^7.0.0"
    "postcss-value-parser" "^3.0.0"

"postcss-reduce-initial@^4.0.3":
  "integrity" "sha1-f9QuvqXpyBRgljniwuhK4nC6SN8="
  "resolved" "https://registry.nlark.com/postcss-reduce-initial/download/postcss-reduce-initial-4.0.3.tgz?cache=0&sync_timestamp=1621449599206&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fpostcss-reduce-initial%2Fdownload%2Fpostcss-reduce-initial-4.0.3.tgz"
  "version" "4.0.3"
  dependencies:
    "browserslist" "^4.0.0"
    "caniuse-api" "^3.0.0"
    "has" "^1.0.0"
    "postcss" "^7.0.0"

"postcss-reduce-transforms@^4.0.2":
  "integrity" "sha1-F++kBerMbge+NBSlyi0QdGgdTik="
  "resolved" "https://registry.nlark.com/postcss-reduce-transforms/download/postcss-reduce-transforms-4.0.2.tgz"
  "version" "4.0.2"
  dependencies:
    "cssnano-util-get-match" "^4.0.0"
    "has" "^1.0.0"
    "postcss" "^7.0.0"
    "postcss-value-parser" "^3.0.0"

"postcss-selector-parser@^3.0.0":
  "integrity" "sha1-sxD1xMD9r3b5SQK7qjDbaqhPUnA="
  "resolved" "https://registry.nlark.com/postcss-selector-parser/download/postcss-selector-parser-3.1.2.tgz?cache=0&sync_timestamp=1620752924836&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fpostcss-selector-parser%2Fdownload%2Fpostcss-selector-parser-3.1.2.tgz"
  "version" "3.1.2"
  dependencies:
    "dot-prop" "^5.2.0"
    "indexes-of" "^1.0.1"
    "uniq" "^1.0.1"

"postcss-selector-parser@^6.0.0", "postcss-selector-parser@^6.0.2":
  "integrity" "sha1-LFu6gXSsL2mBq2MaQqsO5UrzMuo="
  "resolved" "https://registry.nlark.com/postcss-selector-parser/download/postcss-selector-parser-6.0.6.tgz?cache=0&sync_timestamp=1620752924836&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fpostcss-selector-parser%2Fdownload%2Fpostcss-selector-parser-6.0.6.tgz"
  "version" "6.0.6"
  dependencies:
    "cssesc" "^3.0.0"
    "util-deprecate" "^1.0.2"

"postcss-svgo@^4.0.3":
  "integrity" "sha1-NDos26yVBdQWJD1Jb3JPOIlMlB4="
  "resolved" "https://registry.nlark.com/postcss-svgo/download/postcss-svgo-4.0.3.tgz?cache=0&sync_timestamp=1622234649078&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fpostcss-svgo%2Fdownload%2Fpostcss-svgo-4.0.3.tgz"
  "version" "4.0.3"
  dependencies:
    "postcss" "^7.0.0"
    "postcss-value-parser" "^3.0.0"
    "svgo" "^1.0.0"

"postcss-unique-selectors@^4.0.1":
  "integrity" "sha1-lEaRHzKJv9ZMbWgPBzwDsfnuS6w="
  "resolved" "https://registry.nlark.com/postcss-unique-selectors/download/postcss-unique-selectors-4.0.1.tgz"
  "version" "4.0.1"
  dependencies:
    "alphanum-sort" "^1.0.0"
    "postcss" "^7.0.0"
    "uniqs" "^2.0.0"

"postcss-value-parser@^3.0.0":
  "integrity" "sha1-n/giVH4okyE88cMO+lGsX9G6goE="
  "resolved" "https://registry.nlark.com/postcss-value-parser/download/postcss-value-parser-3.3.1.tgz"
  "version" "3.3.1"

"postcss-value-parser@^4.0.2", "postcss-value-parser@^4.1.0":
  "integrity" "sha1-RD9qIM7WSBor2k+oUypuVdeJoss="
  "resolved" "https://registry.nlark.com/postcss-value-parser/download/postcss-value-parser-4.1.0.tgz"
  "version" "4.1.0"

"postcss@^7.0.0", "postcss@^7.0.1", "postcss@^7.0.14", "postcss@^7.0.27", "postcss@^7.0.32", "postcss@^7.0.5", "postcss@^7.0.6":
  "integrity" "sha1-BW+M/6k5ZiqPWQWVDAfVKFZE38s="
  "resolved" "https://registry.nlark.com/postcss/download/postcss-7.0.36.tgz"
  "version" "7.0.36"
  dependencies:
    "chalk" "^2.4.2"
    "source-map" "^0.6.1"
    "supports-color" "^6.1.0"

"prelude-ls@^1.2.1":
  "integrity" "sha1-3rxkidem5rDnYRiIzsiAM30xY5Y="
  "resolved" "https://registry.npm.taobao.org/prelude-ls/download/prelude-ls-1.2.1.tgz"
  "version" "1.2.1"

"prepend-http@^1.0.0":
  "integrity" "sha1-1PRWKwzjaW5BrFLQ4ALlemNdxtw="
  "resolved" "https://registry.nlark.com/prepend-http/download/prepend-http-1.0.4.tgz"
  "version" "1.0.4"

"prettier-linter-helpers@^1.0.0":
  "integrity" "sha1-0j1B/hN1ZG3i0BBNNFSjAIgCz3s="
  "resolved" "https://registry.npm.taobao.org/prettier-linter-helpers/download/prettier-linter-helpers-1.0.0.tgz"
  "version" "1.0.0"
  dependencies:
    "fast-diff" "^1.1.2"

"prettier@^2.0.5", "prettier@>=1.13.0":
  "integrity" "sha1-7ygKBewlNxLkhiM9tcbyNEHnNC0="
  "resolved" "https://registry.nlark.com/prettier/download/prettier-2.3.2.tgz?cache=0&sync_timestamp=1624696193562&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fprettier%2Fdownload%2Fprettier-2.3.2.tgz"
  "version" "2.3.2"

"pretty-error@^2.0.2":
  "integrity" "sha1-von4LYGxyG7I/fvDhQRYgnJ/k7Y="
  "resolved" "https://registry.nlark.com/pretty-error/download/pretty-error-2.1.2.tgz"
  "version" "2.1.2"
  dependencies:
    "lodash" "^4.17.20"
    "renderkid" "^2.0.4"

"printj@~1.1.0":
  "integrity" "sha1-2Q3rKXWoufYA+zoclOP0xTx4oiI="
  "resolved" "https://registry.npm.taobao.org/printj/download/printj-1.1.2.tgz"
  "version" "1.1.2"

"process-nextick-args@~2.0.0":
  "integrity" "sha1-eCDZsWEgzFXKmud5JoCufbptf+I="
  "resolved" "https://registry.npm.taobao.org/process-nextick-args/download/process-nextick-args-2.0.1.tgz"
  "version" "2.0.1"

"process@^0.11.1", "process@^0.11.10":
  "integrity" "sha1-czIwDoQBYb2j5podHZGn1LwW8YI="
  "resolved" "https://registry.npm.taobao.org/process/download/process-0.11.10.tgz"
  "version" "0.11.10"

"progress-bar-webpack-plugin@^1.12.1":
  "integrity" "sha1-u/OxE3pLokdO6xETd9bBpYDFfdE="
  "resolved" "https://registry.npm.taobao.org/progress-bar-webpack-plugin/download/progress-bar-webpack-plugin-1.12.1.tgz"
  "version" "1.12.1"
  dependencies:
    "chalk" "^1.1.1"
    "object.assign" "^4.0.1"
    "progress" "^1.1.8"

"progress@^1.1.8":
  "integrity" "sha1-4mDHj2Fhzdmw5WzD4Khd4Xx6V74="
  "resolved" "https://registry.nlark.com/progress/download/progress-1.1.8.tgz"
  "version" "1.1.8"

"progress@^2.0.0":
  "integrity" "sha1-foz42PW48jnBvGi+tOt4Vn1XLvg="
  "resolved" "https://registry.nlark.com/progress/download/progress-2.0.3.tgz"
  "version" "2.0.3"

"promise-inflight@^1.0.1":
  "integrity" "sha1-mEcocL8igTL8vdhoEputEsPAKeM="
  "resolved" "https://registry.nlark.com/promise-inflight/download/promise-inflight-1.0.1.tgz"
  "version" "1.0.1"

"promise@^7.1.1":
  "integrity" "sha1-BktyYCsY+Q8pGSuLG8QY/9Hr078="
  "resolved" "https://registry.npm.taobao.org/promise/download/promise-7.3.1.tgz"
  "version" "7.3.1"
  dependencies:
    "asap" "~2.0.3"

"prop-types@^15.0", "prop-types@^15.0.0", "prop-types@^15.5.10", "prop-types@^15.5.4", "prop-types@^15.5.6", "prop-types@^15.5.7", "prop-types@^15.5.8", "prop-types@^15.5.9", "prop-types@^15.6.0", "prop-types@^15.6.1", "prop-types@^15.6.2", "prop-types@^15.7.2", "prop-types@15.x":
  "integrity" "sha1-UsQedbjIfnK52TYOAga5ncv/psU="
  "resolved" "https://registry.npm.taobao.org/prop-types/download/prop-types-15.7.2.tgz"
  "version" "15.7.2"
  dependencies:
    "loose-envify" "^1.4.0"
    "object-assign" "^4.1.1"
    "react-is" "^16.8.1"

"proxy-addr@~2.0.5":
  "integrity" "sha1-8Z/mnOqzEe65S0LnDowgcPm6ECU="
  "resolved" "https://registry.nlark.com/proxy-addr/download/proxy-addr-2.0.7.tgz"
  "version" "2.0.7"
  dependencies:
    "forwarded" "0.2.0"
    "ipaddr.js" "1.9.1"

"prr@~1.0.1":
  "integrity" "sha1-0/wRS6BplaRexok/SEzrHXj19HY="
  "resolved" "https://registry.nlark.com/prr/download/prr-1.0.1.tgz"
  "version" "1.0.1"

"pseudomap@^1.0.2":
  "integrity" "sha1-8FKijacOYYkX7wqKw0wa5aaChrM="
  "resolved" "https://registry.nlark.com/pseudomap/download/pseudomap-1.0.2.tgz"
  "version" "1.0.2"

"psl@^1.1.28":
  "integrity" "sha1-kyb4vPsBOtzABf3/BWrM4CDlHCQ="
  "resolved" "https://registry.npm.taobao.org/psl/download/psl-1.8.0.tgz"
  "version" "1.8.0"

"public-encrypt@^4.0.0":
  "integrity" "sha1-T8ydd6B+SLp1J+fL4N4z0HATMeA="
  "resolved" "https://registry.npm.taobao.org/public-encrypt/download/public-encrypt-4.0.3.tgz"
  "version" "4.0.3"
  dependencies:
    "bn.js" "^4.1.0"
    "browserify-rsa" "^4.0.0"
    "create-hash" "^1.1.0"
    "parse-asn1" "^5.0.0"
    "randombytes" "^2.0.1"
    "safe-buffer" "^5.1.2"

"pump@^2.0.0":
  "integrity" "sha1-Ejma3W5M91Jtlzy8i1zi4pCLOQk="
  "resolved" "https://registry.nlark.com/pump/download/pump-2.0.1.tgz"
  "version" "2.0.1"
  dependencies:
    "end-of-stream" "^1.1.0"
    "once" "^1.3.1"

"pump@^3.0.0":
  "integrity" "sha1-tKIRaBW94vTh6mAjVOjHVWUQemQ="
  "resolved" "https://registry.nlark.com/pump/download/pump-3.0.0.tgz"
  "version" "3.0.0"
  dependencies:
    "end-of-stream" "^1.1.0"
    "once" "^1.3.1"

"pumpify@^1.3.3":
  "integrity" "sha1-NlE74karJ1cLGjdKXOJ4v9dDcM4="
  "resolved" "https://registry.nlark.com/pumpify/download/pumpify-1.5.1.tgz"
  "version" "1.5.1"
  dependencies:
    "duplexify" "^3.6.0"
    "inherits" "^2.0.3"
    "pump" "^2.0.0"

"punycode@^1.2.4":
  "integrity" "sha1-wNWmOycYgArY4esPpSachN1BhF4="
  "resolved" "https://registry.nlark.com/punycode/download/punycode-1.4.1.tgz"
  "version" "1.4.1"

"punycode@^2.1.0", "punycode@^2.1.1":
  "integrity" "sha1-tYsBCsQMIsVldhbI0sLALHv0eew="
  "resolved" "https://registry.nlark.com/punycode/download/punycode-2.1.1.tgz"
  "version" "2.1.1"

"punycode@1.3.2":
  "integrity" "sha1-llOgNvt8HuQjQvIyXM7v6jkmxI0="
  "resolved" "https://registry.nlark.com/punycode/download/punycode-1.3.2.tgz"
  "version" "1.3.2"

"q@^1.1.2":
  "integrity" "sha1-fjL3W0E4EpHQRhHxvxQQmsAGUdc="
  "resolved" "https://registry.nlark.com/q/download/q-1.5.1.tgz"
  "version" "1.5.1"

"qs@~6.5.2":
  "integrity" "sha1-yzroBuh0BERYTvFUzo7pjUA/PjY="
  "resolved" "https://registry.nlark.com/qs/download/qs-6.5.2.tgz"
  "version" "6.5.2"

"qs@6.7.0":
  "integrity" "sha1-QdwaAV49WB8WIXdr4xr7KHapsbw="
  "resolved" "https://registry.nlark.com/qs/download/qs-6.7.0.tgz"
  "version" "6.7.0"

"query-string@^4.1.0":
  "integrity" "sha1-u7aTucqRXCMlFbIosaArYJBD2+s="
  "resolved" "https://registry.nlark.com/query-string/download/query-string-4.3.4.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fquery-string%2Fdownload%2Fquery-string-4.3.4.tgz"
  "version" "4.3.4"
  dependencies:
    "object-assign" "^4.1.0"
    "strict-uri-encode" "^1.0.0"

"querystring-es3@^0.2.0":
  "integrity" "sha1-nsYfeQSYdXB9aUFFlv2Qek1xHnM="
  "resolved" "https://registry.npm.taobao.org/querystring-es3/download/querystring-es3-0.2.1.tgz"
  "version" "0.2.1"

"querystring@0.2.0":
  "integrity" "sha1-sgmEkgO7Jd+CDadW50cAWHhSFiA="
  "resolved" "https://registry.nlark.com/querystring/download/querystring-0.2.0.tgz"
  "version" "0.2.0"

"querystringify@^2.1.1":
  "integrity" "sha1-M0WUG0FTy50ILY7uTNogFqmu9/Y="
  "resolved" "https://registry.npm.taobao.org/querystringify/download/querystringify-2.2.0.tgz"
  "version" "2.2.0"

"raf@^3.4.0", "raf@^3.4.1":
  "integrity" "sha1-B0LpmkplUvRF1z4+4DKK8P8e3jk="
  "resolved" "https://registry.npm.taobao.org/raf/download/raf-3.4.1.tgz"
  "version" "3.4.1"
  dependencies:
    "performance-now" "^2.1.0"

"randombytes@^2.0.0", "randombytes@^2.0.1", "randombytes@^2.0.5", "randombytes@^2.1.0":
  "integrity" "sha1-32+ENy8CcNxlzfYpE0mrekc9Tyo="
  "resolved" "https://registry.nlark.com/randombytes/download/randombytes-2.1.0.tgz"
  "version" "2.1.0"
  dependencies:
    "safe-buffer" "^5.1.0"

"randomfill@^1.0.3":
  "integrity" "sha1-ySGW/IarQr6YPxvzF3giSTHWFFg="
  "resolved" "https://registry.nlark.com/randomfill/download/randomfill-1.0.4.tgz"
  "version" "1.0.4"
  dependencies:
    "randombytes" "^2.0.5"
    "safe-buffer" "^5.1.0"

"range-parser@^1.2.1", "range-parser@~1.2.1":
  "integrity" "sha1-PPNwI9GZ4cJNGlW4SADC8+ZGgDE="
  "resolved" "https://registry.nlark.com/range-parser/download/range-parser-1.2.1.tgz"
  "version" "1.2.1"

"raw-body@2.4.0":
  "integrity" "sha1-oc5vucm8NWylLoklarWQWeE9AzI="
  "resolved" "https://registry.npm.taobao.org/raw-body/download/raw-body-2.4.0.tgz"
  "version" "2.4.0"
  dependencies:
    "bytes" "3.1.0"
    "http-errors" "1.7.2"
    "iconv-lite" "0.4.24"
    "unpipe" "1.0.0"

"rc-align@^2.4.0", "rc-align@^2.4.1":
  "integrity" "sha1-yUGlhvWdEBfyOkKPC0aGY/txAqs="
  "resolved" "https://registry.npm.taobao.org/rc-align/download/rc-align-2.4.5.tgz?cache=0&sync_timestamp=1604568458772&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Frc-align%2Fdownload%2Frc-align-2.4.5.tgz"
  "version" "2.4.5"
  dependencies:
    "babel-runtime" "^6.26.0"
    "dom-align" "^1.7.0"
    "prop-types" "^15.5.8"
    "rc-util" "^4.0.4"

"rc-animate@^2.10.1", "rc-animate@^2.10.2", "rc-animate@^2.3.0", "rc-animate@^2.6.0", "rc-animate@^2.8.2", "rc-animate@2.x":
  "integrity" "sha1-JmbutvHypJWhOyrwniNnEieP2yw="
  "resolved" "https://registry.npm.taobao.org/rc-animate/download/rc-animate-2.11.1.tgz"
  "version" "2.11.1"
  dependencies:
    "babel-runtime" "6.x"
    "classnames" "^2.2.6"
    "css-animation" "^1.3.2"
    "prop-types" "15.x"
    "raf" "^3.4.0"
    "rc-util" "^4.15.3"
    "react-lifecycles-compat" "^3.0.4"

"rc-animate@^3.0.0-rc.1":
  "integrity" "sha1-3v3YY/VoFsIiU05Nxo/t3s0IE4Y="
  "resolved" "https://registry.npm.taobao.org/rc-animate/download/rc-animate-3.1.1.tgz"
  "version" "3.1.1"
  dependencies:
    "@ant-design/css-animation" "^1.7.2"
    "classnames" "^2.2.6"
    "raf" "^3.4.0"
    "rc-util" "^4.15.3"

"rc-calendar@~9.15.7":
  "integrity" "sha1-zh5eqOTXdDW+ZqjHfbEvHw+aNF8="
  "resolved" "https://registry.npm.taobao.org/rc-calendar/download/rc-calendar-9.15.11.tgz"
  "version" "9.15.11"
  dependencies:
    "babel-runtime" "6.x"
    "classnames" "2.x"
    "moment" "2.x"
    "prop-types" "^15.5.8"
    "rc-trigger" "^2.2.0"
    "rc-util" "^4.1.1"
    "react-lifecycles-compat" "^3.0.4"

"rc-cascader@~0.17.4":
  "integrity" "sha1-T96R0jt2CMQgJjw47unAaH+A99w="
  "resolved" "https://registry.nlark.com/rc-cascader/download/rc-cascader-0.17.5.tgz?cache=0&sync_timestamp=1621922431670&other_urls=https%3A%2F%2Fregistry.nlark.com%2Frc-cascader%2Fdownload%2Frc-cascader-0.17.5.tgz"
  "version" "0.17.5"
  dependencies:
    "array-tree-filter" "^2.1.0"
    "prop-types" "^15.5.8"
    "rc-trigger" "^2.2.0"
    "rc-util" "^4.0.4"
    "react-lifecycles-compat" "^3.0.4"
    "shallow-equal" "^1.0.0"
    "warning" "^4.0.1"

"rc-checkbox@~2.1.6":
  "integrity" "sha1-7t2e+cLzr1s7jlzeUlSqia0aiAo="
  "resolved" "https://registry.npm.taobao.org/rc-checkbox/download/rc-checkbox-2.1.8.tgz"
  "version" "2.1.8"
  dependencies:
    "babel-runtime" "^6.23.0"
    "classnames" "2.x"
    "prop-types" "15.x"
    "react-lifecycles-compat" "^3.0.4"

"rc-collapse@~1.11.3":
  "integrity" "sha1-ZqQAidRpUZ6UJACasckn4hQEHYA="
  "resolved" "https://registry.nlark.com/rc-collapse/download/rc-collapse-1.11.8.tgz?cache=0&sync_timestamp=1621999847304&other_urls=https%3A%2F%2Fregistry.nlark.com%2Frc-collapse%2Fdownload%2Frc-collapse-1.11.8.tgz"
  "version" "1.11.8"
  dependencies:
    "classnames" "2.x"
    "css-animation" "1.x"
    "prop-types" "^15.5.6"
    "rc-animate" "2.x"
    "react-is" "^16.7.0"
    "react-lifecycles-compat" "^3.0.4"
    "shallowequal" "^1.1.0"

"rc-dialog@~7.6.0":
  "integrity" "sha1-EVRczAuUWTT6dgeXJuDYU+UtcF8="
  "resolved" "https://registry.nlark.com/rc-dialog/download/rc-dialog-7.6.1.tgz"
  "version" "7.6.1"
  dependencies:
    "babel-runtime" "6.x"
    "rc-animate" "2.x"
    "rc-util" "^4.16.1"

"rc-drawer@~3.1.1":
  "integrity" "sha1-y8sE1MB/C2by7OEdhH9KG9gOoLc="
  "resolved" "https://registry.nlark.com/rc-drawer/download/rc-drawer-3.1.3.tgz"
  "version" "3.1.3"
  dependencies:
    "classnames" "^2.2.6"
    "rc-util" "^4.16.1"
    "react-lifecycles-compat" "^3.0.4"

"rc-dropdown@~2.4.1":
  "integrity" "sha1-qu9us6UVLN2ZgolcKnjZtfBGzew="
  "resolved" "https://registry.nlark.com/rc-dropdown/download/rc-dropdown-2.4.1.tgz"
  "version" "2.4.1"
  dependencies:
    "babel-runtime" "^6.26.0"
    "classnames" "^2.2.6"
    "prop-types" "^15.5.8"
    "rc-trigger" "^2.5.1"
    "react-lifecycles-compat" "^3.0.2"

"rc-editor-core@~0.8.3":
  "integrity" "sha1-byFbxd+cM/+p9sWzDKc6favoq3w="
  "resolved" "https://registry.npm.taobao.org/rc-editor-core/download/rc-editor-core-0.8.10.tgz"
  "version" "0.8.10"
  dependencies:
    "babel-runtime" "^6.26.0"
    "classnames" "^2.2.5"
    "draft-js" "^0.10.0"
    "immutable" "^3.7.4"
    "lodash" "^4.16.5"
    "prop-types" "^15.5.8"
    "setimmediate" "^1.0.5"

"rc-editor-mention@^1.1.13":
  "integrity" "sha1-nxyrEGX4awFSOEAyF5DCqxKsXos="
  "resolved" "https://registry.npm.taobao.org/rc-editor-mention/download/rc-editor-mention-1.1.13.tgz"
  "version" "1.1.13"
  dependencies:
    "babel-runtime" "^6.23.0"
    "classnames" "^2.2.5"
    "dom-scroll-into-view" "^1.2.0"
    "draft-js" "~0.10.0"
    "immutable" "~3.7.4"
    "prop-types" "^15.5.8"
    "rc-animate" "^2.3.0"
    "rc-editor-core" "~0.8.3"

"rc-form@^2.4.10":
  "integrity" "sha1-TuhxHpCiWEuqesJ23pa+4Nmw9fE="
  "resolved" "https://registry.npm.taobao.org/rc-form/download/rc-form-2.4.12.tgz"
  "version" "2.4.12"
  dependencies:
    "async-validator" "~1.11.3"
    "babel-runtime" "6.x"
    "create-react-class" "^15.5.3"
    "dom-scroll-into-view" "1.x"
    "hoist-non-react-statics" "^3.3.0"
    "lodash" "^4.17.4"
    "rc-util" "^4.15.3"
    "react-is" "^16.13.1"
    "warning" "^4.0.3"

"rc-hammerjs@~0.6.0":
  "integrity" "sha1-GDGjvY8hmXAL/MWtayCjVjCuteA="
  "resolved" "https://registry.npm.taobao.org/rc-hammerjs/download/rc-hammerjs-0.6.10.tgz"
  "version" "0.6.10"
  dependencies:
    "babel-runtime" "6.x"
    "hammerjs" "^2.0.8"
    "prop-types" "^15.5.9"

"rc-input-number@~4.5.0":
  "integrity" "sha1-HL9zXiT+I8TrmkMBAxcguV8qPj0="
  "resolved" "https://registry.nlark.com/rc-input-number/download/rc-input-number-4.5.9.tgz?cache=0&sync_timestamp=1623933214707&other_urls=https%3A%2F%2Fregistry.nlark.com%2Frc-input-number%2Fdownload%2Frc-input-number-4.5.9.tgz"
  "version" "4.5.9"
  dependencies:
    "babel-runtime" "6.x"
    "classnames" "^2.2.0"
    "prop-types" "^15.5.7"
    "rc-util" "^4.5.1"
    "rmc-feedback" "^2.0.0"

"rc-mentions@~0.4.0":
  "integrity" "sha1-wYq3Ae+55LdbOFGgwNLdaYZA4kY="
  "resolved" "https://registry.nlark.com/rc-mentions/download/rc-mentions-0.4.2.tgz?cache=0&sync_timestamp=1621867451279&other_urls=https%3A%2F%2Fregistry.nlark.com%2Frc-mentions%2Fdownload%2Frc-mentions-0.4.2.tgz"
  "version" "0.4.2"
  dependencies:
    "@ant-design/create-react-context" "^0.2.4"
    "classnames" "^2.2.6"
    "rc-menu" "^7.4.22"
    "rc-trigger" "^2.6.2"
    "rc-util" "^4.6.0"
    "react-lifecycles-compat" "^3.0.4"

"rc-menu@^7.3.0", "rc-menu@^7.4.22", "rc-menu@~7.5.1":
  "integrity" "sha1-eM3IF9hvw1OhQwuGTT2Wx0iWAMo="
  "resolved" "https://registry.nlark.com/rc-menu/download/rc-menu-7.5.5.tgz"
  "version" "7.5.5"
  dependencies:
    "classnames" "2.x"
    "dom-scroll-into-view" "1.x"
    "mini-store" "^2.0.0"
    "mutationobserver-shim" "^0.3.2"
    "rc-animate" "^2.10.1"
    "rc-trigger" "^2.3.0"
    "rc-util" "^4.13.0"
    "resize-observer-polyfill" "^1.5.0"
    "shallowequal" "^1.1.0"

"rc-notification@~3.3.1":
  "integrity" "sha1-C6o+cPjUCrAVzo+njCYMSQ/HvrQ="
  "resolved" "https://registry.nlark.com/rc-notification/download/rc-notification-3.3.1.tgz"
  "version" "3.3.1"
  dependencies:
    "babel-runtime" "6.x"
    "classnames" "2.x"
    "prop-types" "^15.5.8"
    "rc-animate" "2.x"
    "rc-util" "^4.0.4"

"rc-pagination@~1.20.11":
  "integrity" "sha1-zLTNDpvU5H9y8p6kMsA1C/ez2Ac="
  "resolved" "https://registry.nlark.com/rc-pagination/download/rc-pagination-1.20.15.tgz?cache=0&sync_timestamp=1624592253089&other_urls=https%3A%2F%2Fregistry.nlark.com%2Frc-pagination%2Fdownload%2Frc-pagination-1.20.15.tgz"
  "version" "1.20.15"
  dependencies:
    "babel-runtime" "6.x"
    "classnames" "^2.2.6"
    "prop-types" "^15.5.7"
    "react-lifecycles-compat" "^3.0.4"

"rc-progress@~2.5.0":
  "integrity" "sha1-APAblb2+GFbTpfgiQgUZAui3qOc="
  "resolved" "https://registry.nlark.com/rc-progress/download/rc-progress-2.5.3.tgz?cache=0&sync_timestamp=1621251898787&other_urls=https%3A%2F%2Fregistry.nlark.com%2Frc-progress%2Fdownload%2Frc-progress-2.5.3.tgz"
  "version" "2.5.3"
  dependencies:
    "babel-runtime" "6.x"
    "prop-types" "^15.5.8"

"rc-rate@~2.5.0":
  "integrity" "sha1-Vfxf0j6p3MciULmoiYA0efSEKWE="
  "resolved" "https://registry.nlark.com/rc-rate/download/rc-rate-2.5.1.tgz"
  "version" "2.5.1"
  dependencies:
    "classnames" "^2.2.5"
    "prop-types" "^15.5.8"
    "rc-util" "^4.3.0"
    "react-lifecycles-compat" "^3.0.4"

"rc-resize-observer@^0.1.0":
  "integrity" "sha1-CXGR+cOrGG7ZB7VTum71Zd8Rwkk="
  "resolved" "https://registry.npm.taobao.org/rc-resize-observer/download/rc-resize-observer-0.1.3.tgz"
  "version" "0.1.3"
  dependencies:
    "classnames" "^2.2.1"
    "rc-util" "^4.13.0"
    "resize-observer-polyfill" "^1.5.1"

"rc-select@~9.2.0":
  "integrity" "sha1-ZDQOLW72TovDz8b0aP/ShiVYmsI="
  "resolved" "https://registry.nlark.com/rc-select/download/rc-select-9.2.3.tgz"
  "version" "9.2.3"
  dependencies:
    "babel-runtime" "^6.23.0"
    "classnames" "2.x"
    "component-classes" "1.x"
    "dom-scroll-into-view" "1.x"
    "prop-types" "^15.5.8"
    "raf" "^3.4.0"
    "rc-animate" "2.x"
    "rc-menu" "^7.3.0"
    "rc-trigger" "^2.5.4"
    "rc-util" "^4.0.4"
    "react-lifecycles-compat" "^3.0.2"
    "warning" "^4.0.2"

"rc-slider@~8.7.1":
  "integrity" "sha1-ntBzYtyTSJo45lSyG4EirXD9PEI="
  "resolved" "https://registry.nlark.com/rc-slider/download/rc-slider-8.7.1.tgz"
  "version" "8.7.1"
  dependencies:
    "babel-runtime" "6.x"
    "classnames" "^2.2.5"
    "prop-types" "^15.5.4"
    "rc-tooltip" "^3.7.0"
    "rc-util" "^4.0.4"
    "react-lifecycles-compat" "^3.0.4"
    "shallowequal" "^1.1.0"
    "warning" "^4.0.3"

"rc-steps@~3.5.0":
  "integrity" "sha1-NrKn8fSZB7DZA2OISxhiPK+ftgA="
  "resolved" "https://registry.npm.taobao.org/rc-steps/download/rc-steps-3.5.0.tgz"
  "version" "3.5.0"
  dependencies:
    "babel-runtime" "^6.23.0"
    "classnames" "^2.2.3"
    "lodash" "^4.17.5"
    "prop-types" "^15.5.7"

"rc-switch@~1.9.0":
  "integrity" "sha1-eSHHZkEf6aZCZRDDQpAi1rpN/eI="
  "resolved" "https://registry.npm.taobao.org/rc-switch/download/rc-switch-1.9.2.tgz"
  "version" "1.9.2"
  dependencies:
    "classnames" "^2.2.1"
    "prop-types" "^15.5.6"
    "react-lifecycles-compat" "^3.0.4"

"rc-table@~6.10.5":
  "integrity" "sha1-GB9McMT9dPZX7o8jGW5+sIoDZco="
  "resolved" "https://registry.nlark.com/rc-table/download/rc-table-6.10.15.tgz"
  "version" "6.10.15"
  dependencies:
    "classnames" "^2.2.5"
    "component-classes" "^1.2.6"
    "lodash" "^4.17.5"
    "mini-store" "^2.0.0"
    "prop-types" "^15.5.8"
    "rc-util" "^4.13.0"
    "react-lifecycles-compat" "^3.0.2"
    "shallowequal" "^1.0.2"

"rc-tabs@~9.7.0":
  "integrity" "sha1-rglpW+9ZY9bmTnvBBSHHbf3YRIs="
  "resolved" "https://registry.nlark.com/rc-tabs/download/rc-tabs-9.7.0.tgz?cache=0&sync_timestamp=1621867452307&other_urls=https%3A%2F%2Fregistry.nlark.com%2Frc-tabs%2Fdownload%2Frc-tabs-9.7.0.tgz"
  "version" "9.7.0"
  dependencies:
    "@ant-design/create-react-context" "^0.2.4"
    "babel-runtime" "6.x"
    "classnames" "2.x"
    "lodash" "^4.17.5"
    "prop-types" "15.x"
    "raf" "^3.4.1"
    "rc-hammerjs" "~0.6.0"
    "rc-util" "^4.0.4"
    "react-lifecycles-compat" "^3.0.4"
    "resize-observer-polyfill" "^1.5.1"
    "warning" "^4.0.3"

"rc-time-picker@~3.7.1":
  "integrity" "sha1-ZajekECTJQrpyCsCpJBeD5leI+I="
  "resolved" "https://registry.npm.taobao.org/rc-time-picker/download/rc-time-picker-3.7.3.tgz"
  "version" "3.7.3"
  dependencies:
    "classnames" "2.x"
    "moment" "2.x"
    "prop-types" "^15.5.8"
    "raf" "^3.4.1"
    "rc-trigger" "^2.2.0"
    "react-lifecycles-compat" "^3.0.4"

"rc-tooltip@^3.7.0", "rc-tooltip@~3.7.3":
  "integrity" "sha1-KArsavyqROjf8EgPuv+eh/wArsw="
  "resolved" "https://registry.nlark.com/rc-tooltip/download/rc-tooltip-3.7.3.tgz"
  "version" "3.7.3"
  dependencies:
    "babel-runtime" "6.x"
    "prop-types" "^15.5.8"
    "rc-trigger" "^2.2.2"

"rc-tree-select@~2.9.1":
  "integrity" "sha1-aqeU4fDmXGbEBqoKKg50/QpVewk="
  "resolved" "https://registry.nlark.com/rc-tree-select/download/rc-tree-select-2.9.4.tgz"
  "version" "2.9.4"
  dependencies:
    "classnames" "^2.2.1"
    "dom-scroll-into-view" "^1.2.1"
    "prop-types" "^15.5.8"
    "raf" "^3.4.0"
    "rc-animate" "^2.8.2"
    "rc-tree" "~2.1.0"
    "rc-trigger" "^3.0.0"
    "rc-util" "^4.5.0"
    "react-lifecycles-compat" "^3.0.4"
    "shallowequal" "^1.0.2"
    "warning" "^4.0.1"

"rc-tree@~2.1.0":
  "integrity" "sha1-73WfPnmaIbQ8Hs+ceU6hwU5wtZs="
  "resolved" "https://registry.nlark.com/rc-tree/download/rc-tree-2.1.4.tgz"
  "version" "2.1.4"
  dependencies:
    "@ant-design/create-react-context" "^0.2.4"
    "classnames" "2.x"
    "prop-types" "^15.5.8"
    "rc-animate" "^2.6.0"
    "rc-util" "^4.5.1"
    "react-lifecycles-compat" "^3.0.4"
    "warning" "^4.0.3"

"rc-trigger@^2.2.0", "rc-trigger@^2.2.2", "rc-trigger@^2.3.0", "rc-trigger@^2.5.1", "rc-trigger@^2.5.4", "rc-trigger@^2.6.2":
  "integrity" "sha1-FAqFfPKL0PoBua7LHialCnAOmIU="
  "resolved" "https://registry.nlark.com/rc-trigger/download/rc-trigger-2.6.5.tgz"
  "version" "2.6.5"
  dependencies:
    "babel-runtime" "6.x"
    "classnames" "^2.2.6"
    "prop-types" "15.x"
    "rc-align" "^2.4.0"
    "rc-animate" "2.x"
    "rc-util" "^4.4.0"
    "react-lifecycles-compat" "^3.0.4"

"rc-trigger@^3.0.0":
  "integrity" "sha1-9tmx2oomsrLR2RKgaHbBpIb1mA8="
  "resolved" "https://registry.nlark.com/rc-trigger/download/rc-trigger-3.0.0.tgz"
  "version" "3.0.0"
  dependencies:
    "babel-runtime" "6.x"
    "classnames" "^2.2.6"
    "prop-types" "15.x"
    "raf" "^3.4.0"
    "rc-align" "^2.4.1"
    "rc-animate" "^3.0.0-rc.1"
    "rc-util" "^4.15.7"

"rc-upload@~2.9.1":
  "integrity" "sha1-jjSnOkaNeQf+MZgsOBAORZOFfTI="
  "resolved" "https://registry.nlark.com/rc-upload/download/rc-upload-2.9.4.tgz?cache=0&sync_timestamp=1623401069520&other_urls=https%3A%2F%2Fregistry.nlark.com%2Frc-upload%2Fdownload%2Frc-upload-2.9.4.tgz"
  "version" "2.9.4"
  dependencies:
    "babel-runtime" "6.x"
    "classnames" "^2.2.5"
    "prop-types" "^15.5.7"
    "warning" "4.x"

"rc-util@^4.0.4", "rc-util@^4.1.1", "rc-util@^4.13.0", "rc-util@^4.15.3", "rc-util@^4.15.7", "rc-util@^4.16.1", "rc-util@^4.3.0", "rc-util@^4.4.0", "rc-util@^4.5.0", "rc-util@^4.5.1", "rc-util@^4.6.0":
  "integrity" "sha1-iGAtDDGFAgqhBT2aHnDqwWG+ywU="
  "resolved" "https://registry.nlark.com/rc-util/download/rc-util-4.21.1.tgz?cache=0&sync_timestamp=1625711434684&other_urls=https%3A%2F%2Fregistry.nlark.com%2Frc-util%2Fdownload%2Frc-util-4.21.1.tgz"
  "version" "4.21.1"
  dependencies:
    "add-dom-event-listener" "^1.1.0"
    "prop-types" "^15.5.10"
    "react-is" "^16.12.0"
    "react-lifecycles-compat" "^3.0.4"
    "shallowequal" "^1.1.0"

"react-clipboardjs-copy@^1.3.1":
  "integrity" "sha1-e7MJ1vVTVJuxykAdMeDbLUlYK3Y="
  "resolved" "https://registry.npm.taobao.org/react-clipboardjs-copy/download/react-clipboardjs-copy-1.3.2.tgz"
  "version" "1.3.2"
  dependencies:
    "clipboard" "^2.0.6"

"react-dom@*", "react-dom@^0.14.0 || ^15.0.0-0 || ^16.0.0", "react-dom@^0.14.0 || ^15.0.0-rc || ^16.0.0-rc || ^16.0.0", "react-dom@^0.14.0 || ^15.0.1 || ^16.0.0", "react-dom@^15.0.0 || ^16.0.0 || ^17.0.0 ", "react-dom@^15.0.2 || ^16.0.0-rc || ^16.0.0 || ^17.0.0", "react-dom@^15.0.2|| ^16.0.0-rc || ^16.0.0", "react-dom@^16.0.0", "react-dom@^16.4.1", "react-dom@^16.9.0", "react-dom@>=15.0.0", "react-dom@>=15.x", "react-dom@>=16.0.0":
  "integrity" "sha1-etg47Cmnd/s8dcOhkPZhz5Kri4k="
  "resolved" "https://registry.nlark.com/react-dom/download/react-dom-16.14.0.tgz"
  "version" "16.14.0"
  dependencies:
    "loose-envify" "^1.1.0"
    "object-assign" "^4.1.1"
    "prop-types" "^15.6.2"
    "scheduler" "^0.19.1"

"react-hot-loader@^4.12.11":
  "integrity" "sha1-wn6UCFgcKmePUxbmnAYbIm3GogI="
  "resolved" "https://registry.npm.taobao.org/react-hot-loader/download/react-hot-loader-4.13.0.tgz"
  "version" "4.13.0"
  dependencies:
    "fast-levenshtein" "^2.0.6"
    "global" "^4.3.0"
    "hoist-non-react-statics" "^3.3.0"
    "loader-utils" "^1.1.0"
    "prop-types" "^15.6.1"
    "react-lifecycles-compat" "^3.0.4"
    "shallowequal" "^1.1.0"
    "source-map" "^0.7.3"

"react-is@^16.12.0", "react-is@^16.13.1", "react-is@^16.6.0", "react-is@^16.7.0", "react-is@^16.8.1":
  "integrity" "sha1-eJcppNw23imZ3BVt1sHZwYzqVqQ="
  "resolved" "https://registry.nlark.com/react-is/download/react-is-16.13.1.tgz"
  "version" "16.13.1"

"react-lazy-load@^3.0.13":
  "integrity" "sha1-I2lD92twhMyEWHFtljKhyYU+pc0="
  "resolved" "https://registry.nlark.com/react-lazy-load/download/react-lazy-load-3.1.13.tgz"
  "version" "3.1.13"
  dependencies:
    "eventlistener" "0.0.1"
    "lodash.debounce" "^4.0.0"
    "lodash.throttle" "^4.0.0"
    "prop-types" "^15.5.8"

"react-lifecycles-compat@^3.0.2", "react-lifecycles-compat@^3.0.4":
  "integrity" "sha1-TxonOv38jzSIqMUWv9p4+HI1I2I="
  "resolved" "https://registry.npm.taobao.org/react-lifecycles-compat/download/react-lifecycles-compat-3.0.4.tgz"
  "version" "3.0.4"

"react-redux@^7.1.0":
  "integrity" "sha1-HrtHQDK3LYBt4uBRnNB3YeIi4iU="
  "resolved" "https://registry.nlark.com/react-redux/download/react-redux-7.2.4.tgz"
  "version" "7.2.4"
  dependencies:
    "@babel/runtime" "^7.12.1"
    "@types/react-redux" "^7.1.16"
    "hoist-non-react-statics" "^3.3.2"
    "loose-envify" "^1.4.0"
    "prop-types" "^15.7.2"
    "react-is" "^16.13.1"

"react-router-dom@^5.2.0":
  "integrity" "sha1-nmWk0MReEyieZsexfH4XXQ6hVmI="
  "resolved" "https://registry.nlark.com/react-router-dom/download/react-router-dom-5.2.0.tgz"
  "version" "5.2.0"
  dependencies:
    "@babel/runtime" "^7.1.2"
    "history" "^4.9.0"
    "loose-envify" "^1.3.1"
    "prop-types" "^15.6.2"
    "react-router" "5.2.0"
    "tiny-invariant" "^1.0.2"
    "tiny-warning" "^1.0.0"

"react-router@^5.2.0", "react-router@5.2.0":
  "integrity" "sha1-Qk51ZByodH+/duXsyml4GqN+opM="
  "resolved" "https://registry.nlark.com/react-router/download/react-router-5.2.0.tgz"
  "version" "5.2.0"
  dependencies:
    "@babel/runtime" "^7.1.2"
    "history" "^4.9.0"
    "hoist-non-react-statics" "^3.1.0"
    "loose-envify" "^1.3.1"
    "mini-create-react-context" "^0.4.0"
    "path-to-regexp" "^1.7.0"
    "prop-types" "^15.6.2"
    "react-is" "^16.6.0"
    "tiny-invariant" "^1.0.2"
    "tiny-warning" "^1.0.0"

"react-slick@~0.25.2":
  "integrity" "sha1-VjMbZ9R9i8/i3OtqyrHI/VvR9rw="
  "resolved" "https://registry.npm.taobao.org/react-slick/download/react-slick-0.25.2.tgz?cache=0&sync_timestamp=1615180439221&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Freact-slick%2Fdownload%2Freact-slick-0.25.2.tgz"
  "version" "0.25.2"
  dependencies:
    "classnames" "^2.2.5"
    "enquire.js" "^2.1.6"
    "json2mq" "^0.2.0"
    "lodash.debounce" "^4.0.8"
    "resize-observer-polyfill" "^1.5.0"

"react@*", "react@^0.14.0 || ^15.0.0 || ^16.0.0", "react@^0.14.0 || ^15.0.0 || ^16.0.0 || ^17.0.0", "react@^0.14.0 || ^15.0.0-0 || ^16.0.0", "react@^0.14.0 || ^15.0.0-0 || ^16.0.0 || ^17.0.0", "react@^0.14.0 || ^15.0.0-rc || ^16.0.0-rc || ^16.0.0", "react@^0.14.0 || ^15.0.1 || ^16.0.0", "react@^15.0.0 || ^16.0.0 || ^17.0.0 ", "react@^15.0.2 || ^16.0.0-rc || ^16.0.0 || ^17.0.0", "react@^15.0.2|| ^16.0.0-rc || ^16.0.0", "react@^16.0.0", "react@^16.14.0", "react@^16.4.1", "react@^16.8.3 || ^17", "react@^16.9.0", "react@>=15", "react@>=15.0.0", "react@>=15.x", "react@>=16.0.0", "react@16.x":
  "integrity" "sha1-lNd23dCqo32j7aj8W2sYpMmjEU0="
  "resolved" "https://registry.nlark.com/react/download/react-16.14.0.tgz"
  "version" "16.14.0"
  dependencies:
    "loose-envify" "^1.1.0"
    "object-assign" "^4.1.1"
    "prop-types" "^15.6.2"

"read-pkg-up@^1.0.1":
  "integrity" "sha1-nWPBMnbAZZGNV/ACpX9AobZD+wI="
  "resolved" "https://registry.nlark.com/read-pkg-up/download/read-pkg-up-1.0.1.tgz?cache=0&sync_timestamp=1618846971516&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fread-pkg-up%2Fdownload%2Fread-pkg-up-1.0.1.tgz"
  "version" "1.0.1"
  dependencies:
    "find-up" "^1.0.0"
    "read-pkg" "^1.0.0"

"read-pkg-up@^3.0.0":
  "integrity" "sha1-PtSWaF26D4/hGNBpHcUfSh/5bwc="
  "resolved" "https://registry.nlark.com/read-pkg-up/download/read-pkg-up-3.0.0.tgz?cache=0&sync_timestamp=1618846971516&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fread-pkg-up%2Fdownload%2Fread-pkg-up-3.0.0.tgz"
  "version" "3.0.0"
  dependencies:
    "find-up" "^2.0.0"
    "read-pkg" "^3.0.0"

"read-pkg@^1.0.0":
  "integrity" "sha1-9f+qXs0pyzHAR0vKfXVra7KePyg="
  "resolved" "https://registry.nlark.com/read-pkg/download/read-pkg-1.1.0.tgz"
  "version" "1.1.0"
  dependencies:
    "load-json-file" "^1.0.0"
    "normalize-package-data" "^2.3.2"
    "path-type" "^1.0.0"

"read-pkg@^3.0.0":
  "integrity" "sha1-nLxoaXj+5l0WwA4rGcI3/Pbjg4k="
  "resolved" "https://registry.nlark.com/read-pkg/download/read-pkg-3.0.0.tgz"
  "version" "3.0.0"
  dependencies:
    "load-json-file" "^4.0.0"
    "normalize-package-data" "^2.3.2"
    "path-type" "^3.0.0"

"readable-stream@^2.0.0":
  "integrity" "sha1-Hsoc9xGu+BTAT2IlKjamL2yyO1c="
  "resolved" "https://registry.nlark.com/readable-stream/download/readable-stream-2.3.7.tgz"
  "version" "2.3.7"
  dependencies:
    "core-util-is" "~1.0.0"
    "inherits" "~2.0.3"
    "isarray" "~1.0.0"
    "process-nextick-args" "~2.0.0"
    "safe-buffer" "~5.1.1"
    "string_decoder" "~1.1.1"
    "util-deprecate" "~1.0.1"

"readable-stream@^2.0.1":
  "integrity" "sha1-Hsoc9xGu+BTAT2IlKjamL2yyO1c="
  "resolved" "https://registry.nlark.com/readable-stream/download/readable-stream-2.3.7.tgz"
  "version" "2.3.7"
  dependencies:
    "core-util-is" "~1.0.0"
    "inherits" "~2.0.3"
    "isarray" "~1.0.0"
    "process-nextick-args" "~2.0.0"
    "safe-buffer" "~5.1.1"
    "string_decoder" "~1.1.1"
    "util-deprecate" "~1.0.1"

"readable-stream@^2.0.2":
  "integrity" "sha1-Hsoc9xGu+BTAT2IlKjamL2yyO1c="
  "resolved" "https://registry.nlark.com/readable-stream/download/readable-stream-2.3.7.tgz"
  "version" "2.3.7"
  dependencies:
    "core-util-is" "~1.0.0"
    "inherits" "~2.0.3"
    "isarray" "~1.0.0"
    "process-nextick-args" "~2.0.0"
    "safe-buffer" "~5.1.1"
    "string_decoder" "~1.1.1"
    "util-deprecate" "~1.0.1"

"readable-stream@^2.0.5":
  "integrity" "sha1-Hsoc9xGu+BTAT2IlKjamL2yyO1c="
  "resolved" "https://registry.nlark.com/readable-stream/download/readable-stream-2.3.7.tgz"
  "version" "2.3.7"
  dependencies:
    "core-util-is" "~1.0.0"
    "inherits" "~2.0.3"
    "isarray" "~1.0.0"
    "process-nextick-args" "~2.0.0"
    "safe-buffer" "~5.1.1"
    "string_decoder" "~1.1.1"
    "util-deprecate" "~1.0.1"

"readable-stream@^2.0.6":
  "integrity" "sha1-Hsoc9xGu+BTAT2IlKjamL2yyO1c="
  "resolved" "https://registry.nlark.com/readable-stream/download/readable-stream-2.3.7.tgz"
  "version" "2.3.7"
  dependencies:
    "core-util-is" "~1.0.0"
    "inherits" "~2.0.3"
    "isarray" "~1.0.0"
    "process-nextick-args" "~2.0.0"
    "safe-buffer" "~5.1.1"
    "string_decoder" "~1.1.1"
    "util-deprecate" "~1.0.1"

"readable-stream@^2.1.5":
  "integrity" "sha1-Hsoc9xGu+BTAT2IlKjamL2yyO1c="
  "resolved" "https://registry.nlark.com/readable-stream/download/readable-stream-2.3.7.tgz"
  "version" "2.3.7"
  dependencies:
    "core-util-is" "~1.0.0"
    "inherits" "~2.0.3"
    "isarray" "~1.0.0"
    "process-nextick-args" "~2.0.0"
    "safe-buffer" "~5.1.1"
    "string_decoder" "~1.1.1"
    "util-deprecate" "~1.0.1"

"readable-stream@^2.2.2":
  "integrity" "sha1-Hsoc9xGu+BTAT2IlKjamL2yyO1c="
  "resolved" "https://registry.nlark.com/readable-stream/download/readable-stream-2.3.7.tgz"
  "version" "2.3.7"
  dependencies:
    "core-util-is" "~1.0.0"
    "inherits" "~2.0.3"
    "isarray" "~1.0.0"
    "process-nextick-args" "~2.0.0"
    "safe-buffer" "~5.1.1"
    "string_decoder" "~1.1.1"
    "util-deprecate" "~1.0.1"

"readable-stream@^2.3.3":
  "integrity" "sha1-Hsoc9xGu+BTAT2IlKjamL2yyO1c="
  "resolved" "https://registry.nlark.com/readable-stream/download/readable-stream-2.3.7.tgz"
  "version" "2.3.7"
  dependencies:
    "core-util-is" "~1.0.0"
    "inherits" "~2.0.3"
    "isarray" "~1.0.0"
    "process-nextick-args" "~2.0.0"
    "safe-buffer" "~5.1.1"
    "string_decoder" "~1.1.1"
    "util-deprecate" "~1.0.1"

"readable-stream@^2.3.6":
  "integrity" "sha1-Hsoc9xGu+BTAT2IlKjamL2yyO1c="
  "resolved" "https://registry.nlark.com/readable-stream/download/readable-stream-2.3.7.tgz"
  "version" "2.3.7"
  dependencies:
    "core-util-is" "~1.0.0"
    "inherits" "~2.0.3"
    "isarray" "~1.0.0"
    "process-nextick-args" "~2.0.0"
    "safe-buffer" "~5.1.1"
    "string_decoder" "~1.1.1"
    "util-deprecate" "~1.0.1"

"readable-stream@^3.0.6", "readable-stream@^3.1.1", "readable-stream@^3.4.0", "readable-stream@^3.6.0":
  "integrity" "sha1-M3u9o63AcGvT4CRCaihtS0sskZg="
  "resolved" "https://registry.nlark.com/readable-stream/download/readable-stream-3.6.0.tgz"
  "version" "3.6.0"
  dependencies:
    "inherits" "^2.0.3"
    "string_decoder" "^1.1.1"
    "util-deprecate" "^1.0.1"

"readable-stream@~2.3.6":
  "integrity" "sha1-Hsoc9xGu+BTAT2IlKjamL2yyO1c="
  "resolved" "https://registry.nlark.com/readable-stream/download/readable-stream-2.3.7.tgz"
  "version" "2.3.7"
  dependencies:
    "core-util-is" "~1.0.0"
    "inherits" "~2.0.3"
    "isarray" "~1.0.0"
    "process-nextick-args" "~2.0.0"
    "safe-buffer" "~5.1.1"
    "string_decoder" "~1.1.1"
    "util-deprecate" "~1.0.1"

"readable-stream@1 || 2":
  "integrity" "sha1-Hsoc9xGu+BTAT2IlKjamL2yyO1c="
  "resolved" "https://registry.nlark.com/readable-stream/download/readable-stream-2.3.7.tgz"
  "version" "2.3.7"
  dependencies:
    "core-util-is" "~1.0.0"
    "inherits" "~2.0.3"
    "isarray" "~1.0.0"
    "process-nextick-args" "~2.0.0"
    "safe-buffer" "~5.1.1"
    "string_decoder" "~1.1.1"
    "util-deprecate" "~1.0.1"

"readdir-glob@^1.0.0":
  "integrity" "sha1-8OELt797+n4K3Yuv/cVMP32+5sQ="
  "resolved" "https://registry.npm.taobao.org/readdir-glob/download/readdir-glob-1.1.1.tgz"
  "version" "1.1.1"
  dependencies:
    "minimatch" "^3.0.4"

"readdirp@^2.2.1":
  "integrity" "sha1-DodiKjMlqjPokihcr4tOhGUppSU="
  "resolved" "https://registry.npm.taobao.org/readdirp/download/readdirp-2.2.1.tgz"
  "version" "2.2.1"
  dependencies:
    "graceful-fs" "^4.1.11"
    "micromatch" "^3.1.10"
    "readable-stream" "^2.0.2"

"readdirp@~3.6.0":
  "integrity" "sha1-dKNwvYVxFuJFspzJc0DNQxoCpsc="
  "resolved" "https://registry.npm.taobao.org/readdirp/download/readdirp-3.6.0.tgz"
  "version" "3.6.0"
  dependencies:
    "picomatch" "^2.2.1"

"redent@^1.0.0":
  "integrity" "sha1-z5Fqsf1fHxbfsggi3W7H9zDCr94="
  "resolved" "https://registry.nlark.com/redent/download/redent-1.0.0.tgz?cache=0&sync_timestamp=1620069702182&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fredent%2Fdownload%2Fredent-1.0.0.tgz"
  "version" "1.0.0"
  dependencies:
    "indent-string" "^2.1.0"
    "strip-indent" "^1.0.1"

"redux-logger@^3.0.6":
  "integrity" "sha1-91VZZvMJjzyIYExEnPC69XeCdL8="
  "resolved" "https://registry.npm.taobao.org/redux-logger/download/redux-logger-3.0.6.tgz"
  "version" "3.0.6"
  dependencies:
    "deep-diff" "^0.3.5"

"redux-thunk@^2.3.0":
  "integrity" "sha1-UcLBmhhe1Rh6qpotCLZm0NZGdiI="
  "resolved" "https://registry.nlark.com/redux-thunk/download/redux-thunk-2.3.0.tgz"
  "version" "2.3.0"

"redux@^4.0.0", "redux@^4.0.4":
  "integrity" "sha1-6wSWefL1I8N58a/zRchhLylMiNQ="
  "resolved" "https://registry.nlark.com/redux/download/redux-4.1.0.tgz?cache=0&sync_timestamp=1619286844146&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fredux%2Fdownload%2Fredux-4.1.0.tgz"
  "version" "4.1.0"
  dependencies:
    "@babel/runtime" "^7.9.2"

"regenerate-unicode-properties@^8.2.0":
  "integrity" "sha1-5d5xEdZV57pgwFfb6f83yH5lzew="
  "resolved" "https://registry.npm.taobao.org/regenerate-unicode-properties/download/regenerate-unicode-properties-8.2.0.tgz"
  "version" "8.2.0"
  dependencies:
    "regenerate" "^1.4.0"

"regenerate@^1.4.0":
  "integrity" "sha1-uTRtiCfo9aMve6KWN9OYtpAUhIo="
  "resolved" "https://registry.npm.taobao.org/regenerate/download/regenerate-1.4.2.tgz"
  "version" "1.4.2"

"regenerator-runtime@^0.11.0":
  "integrity" "sha1-vgWtf5v30i4Fb5cmzuUBf78Z4uk="
  "resolved" "https://registry.nlark.com/regenerator-runtime/download/regenerator-runtime-0.11.1.tgz"
  "version" "0.11.1"

"regenerator-runtime@^0.13.3", "regenerator-runtime@^0.13.4":
  "integrity" "sha1-ysLazIoepnX+qrrriugziYrkb1U="
  "resolved" "https://registry.nlark.com/regenerator-runtime/download/regenerator-runtime-0.13.7.tgz"
  "version" "0.13.7"

"regenerator-transform@^0.14.2":
  "integrity" "sha1-yY2hVGg2ccnE3LFuznNlF+G3/rQ="
  "resolved" "https://registry.nlark.com/regenerator-transform/download/regenerator-transform-0.14.5.tgz"
  "version" "0.14.5"
  dependencies:
    "@babel/runtime" "^7.8.4"

"regex-not@^1.0.0", "regex-not@^1.0.2":
  "integrity" "sha1-H07OJ+ALC2XgJHpoEOaoXYOldSw="
  "resolved" "https://registry.nlark.com/regex-not/download/regex-not-1.0.2.tgz"
  "version" "1.0.2"
  dependencies:
    "extend-shallow" "^3.0.2"
    "safe-regex" "^1.1.0"

"regexp.prototype.flags@^1.2.0", "regexp.prototype.flags@^1.3.1":
  "integrity" "sha1-fvNSro0VnnWMDq3Kb4/LTu8HviY="
  "resolved" "https://registry.nlark.com/regexp.prototype.flags/download/regexp.prototype.flags-1.3.1.tgz"
  "version" "1.3.1"
  dependencies:
    "call-bind" "^1.0.2"
    "define-properties" "^1.1.3"

"regexpp@^3.0.0", "regexpp@^3.1.0":
  "integrity" "sha1-BCWido2PI7rXDKS5BGH6LxIT4bI="
  "resolved" "https://registry.nlark.com/regexpp/download/regexpp-3.2.0.tgz?cache=0&sync_timestamp=1623668872577&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fregexpp%2Fdownload%2Fregexpp-3.2.0.tgz"
  "version" "3.2.0"

"regexpu-core@^4.7.1":
  "integrity" "sha1-LepamgcjMpj78NuR+pq8TG4PitY="
  "resolved" "https://registry.nlark.com/regexpu-core/download/regexpu-core-4.7.1.tgz"
  "version" "4.7.1"
  dependencies:
    "regenerate" "^1.4.0"
    "regenerate-unicode-properties" "^8.2.0"
    "regjsgen" "^0.5.1"
    "regjsparser" "^0.6.4"
    "unicode-match-property-ecmascript" "^1.0.4"
    "unicode-match-property-value-ecmascript" "^1.2.0"

"regjsgen@^0.5.1":
  "integrity" "sha1-kv8pX7He7L9uzaslQ9IH6RqjNzM="
  "resolved" "https://registry.nlark.com/regjsgen/download/regjsgen-0.5.2.tgz"
  "version" "0.5.2"

"regjsparser@^0.6.4":
  "integrity" "sha1-tInu98mizkNydicBFCnPgzpxg+Y="
  "resolved" "https://registry.npm.taobao.org/regjsparser/download/regjsparser-0.6.9.tgz?cache=0&sync_timestamp=1616545067196&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fregjsparser%2Fdownload%2Fregjsparser-0.6.9.tgz"
  "version" "0.6.9"
  dependencies:
    "jsesc" "~0.5.0"

"relateurl@0.2.x":
  "integrity" "sha1-VNvzd+UUQKypCkzSdGANP/LYiKk="
  "resolved" "https://registry.nlark.com/relateurl/download/relateurl-0.2.7.tgz"
  "version" "0.2.7"

"remove-trailing-separator@^1.0.1":
  "integrity" "sha1-wkvOKig62tW8P1jg1IJJuSN52O8="
  "resolved" "https://registry.nlark.com/remove-trailing-separator/download/remove-trailing-separator-1.1.0.tgz"
  "version" "1.1.0"

"renderkid@^2.0.4":
  "integrity" "sha1-Rk8namvc7mBvShWZP5sp/HTKhgk="
  "resolved" "https://registry.nlark.com/renderkid/download/renderkid-2.0.7.tgz?cache=0&sync_timestamp=1623343659191&other_urls=https%3A%2F%2Fregistry.nlark.com%2Frenderkid%2Fdownload%2Frenderkid-2.0.7.tgz"
  "version" "2.0.7"
  dependencies:
    "css-select" "^4.1.3"
    "dom-converter" "^0.2.0"
    "htmlparser2" "^6.1.0"
    "lodash" "^4.17.21"
    "strip-ansi" "^3.0.1"

"repeat-element@^1.1.2":
  "integrity" "sha1-vmgVIIR6tYx1aKx1+/rSjtQtOek="
  "resolved" "https://registry.npm.taobao.org/repeat-element/download/repeat-element-1.1.4.tgz?cache=0&sync_timestamp=1617838546743&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Frepeat-element%2Fdownload%2Frepeat-element-1.1.4.tgz"
  "version" "1.1.4"

"repeat-string@^1.6.1":
  "integrity" "sha1-jcrkcOHIirwtYA//Sndihtp15jc="
  "resolved" "https://registry.nlark.com/repeat-string/download/repeat-string-1.6.1.tgz"
  "version" "1.6.1"

"repeating@^2.0.0":
  "integrity" "sha1-UhTFOpJtNVJwdSf7q0FdvAjQbdo="
  "resolved" "https://registry.nlark.com/repeating/download/repeating-2.0.1.tgz"
  "version" "2.0.1"
  dependencies:
    "is-finite" "^1.0.0"

"request@^2.87.0", "request@^2.88.0":
  "integrity" "sha1-1zyRhzHLWofaBH4gcjQUb2ZNErM="
  "resolved" "https://registry.npm.taobao.org/request/download/request-2.88.2.tgz"
  "version" "2.88.2"
  dependencies:
    "aws-sign2" "~0.7.0"
    "aws4" "^1.8.0"
    "caseless" "~0.12.0"
    "combined-stream" "~1.0.6"
    "extend" "~3.0.2"
    "forever-agent" "~0.6.1"
    "form-data" "~2.3.2"
    "har-validator" "~5.1.3"
    "http-signature" "~1.2.0"
    "is-typedarray" "~1.0.0"
    "isstream" "~0.1.2"
    "json-stringify-safe" "~5.0.1"
    "mime-types" "~2.1.19"
    "oauth-sign" "~0.9.0"
    "performance-now" "^2.1.0"
    "qs" "~6.5.2"
    "safe-buffer" "^5.1.2"
    "tough-cookie" "~2.5.0"
    "tunnel-agent" "^0.6.0"
    "uuid" "^3.3.2"

"require-directory@^2.1.1":
  "integrity" "sha1-jGStX9MNqxyXbiNE/+f3kqam30I="
  "resolved" "https://registry.nlark.com/require-directory/download/require-directory-2.1.1.tgz"
  "version" "2.1.1"

"require-from-string@^2.0.2":
  "integrity" "sha1-iaf92TgmEmcxjq/hT5wy5ZjDaQk="
  "resolved" "https://registry.nlark.com/require-from-string/download/require-from-string-2.0.2.tgz"
  "version" "2.0.2"

"require-main-filename@^2.0.0":
  "integrity" "sha1-0LMp7MfMD2Fkn2IhW+aa9UqomJs="
  "resolved" "https://registry.npm.taobao.org/require-main-filename/download/require-main-filename-2.0.0.tgz"
  "version" "2.0.0"

"requires-port@^1.0.0":
  "integrity" "sha1-kl0mAdOaxIXgkc8NpcbmlNw9yv8="
  "resolved" "https://registry.nlark.com/requires-port/download/requires-port-1.0.0.tgz"
  "version" "1.0.0"

"resize-observer-polyfill@^1.5.0", "resize-observer-polyfill@^1.5.1":
  "integrity" "sha1-DpAg3T0hAkRY1OvSfiPkAmmBBGQ="
  "resolved" "https://registry.nlark.com/resize-observer-polyfill/download/resize-observer-polyfill-1.5.1.tgz?cache=0&sync_timestamp=1618846967396&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fresize-observer-polyfill%2Fdownload%2Fresize-observer-polyfill-1.5.1.tgz"
  "version" "1.5.1"

"resolve-cwd@^2.0.0":
  "integrity" "sha1-AKn3OHVW4nA46uIyyqNypqWbZlo="
  "resolved" "https://registry.nlark.com/resolve-cwd/download/resolve-cwd-2.0.0.tgz"
  "version" "2.0.0"
  dependencies:
    "resolve-from" "^3.0.0"

"resolve-dir@^1.0.0", "resolve-dir@^1.0.1":
  "integrity" "sha1-eaQGRMNivoLybv/nOcm7U4IEb0M="
  "resolved" "https://registry.npm.taobao.org/resolve-dir/download/resolve-dir-1.0.1.tgz"
  "version" "1.0.1"
  dependencies:
    "expand-tilde" "^2.0.0"
    "global-modules" "^1.0.0"

"resolve-from@^3.0.0":
  "integrity" "sha1-six699nWiBvItuZTM17rywoYh0g="
  "resolved" "https://registry.npm.taobao.org/resolve-from/download/resolve-from-3.0.0.tgz"
  "version" "3.0.0"

"resolve-from@^4.0.0":
  "integrity" "sha1-SrzYUq0y3Xuqv+m0DgCjbbXzkuY="
  "resolved" "https://registry.npm.taobao.org/resolve-from/download/resolve-from-4.0.0.tgz"
  "version" "4.0.0"

"resolve-pathname@^3.0.0":
  "integrity" "sha1-mdAiJNPPJjaJvsuzk7xWAxMCXc0="
  "resolved" "https://registry.npm.taobao.org/resolve-pathname/download/resolve-pathname-3.0.0.tgz"
  "version" "3.0.0"

"resolve-url@^0.2.1":
  "integrity" "sha1-LGN/53yJOv0qZj/iGqkIAGjiBSo="
  "resolved" "https://registry.npm.taobao.org/resolve-url/download/resolve-url-0.2.1.tgz"
  "version" "0.2.1"

"resolve@^1.10.0", "resolve@^1.13.1", "resolve@^1.14.2", "resolve@^1.20.0":
  "integrity" "sha1-YpoBP7P3B1XW8LeTXMHCxTeLGXU="
  "resolved" "https://registry.npm.taobao.org/resolve/download/resolve-1.20.0.tgz"
  "version" "1.20.0"
  dependencies:
    "is-core-module" "^2.2.0"
    "path-parse" "^1.0.6"

"resolve@^2.0.0-next.3":
  "integrity" "sha1-1BAWKT1KhYajnKXZtfFcvqH1XkY="
  "resolved" "https://registry.npm.taobao.org/resolve/download/resolve-2.0.0-next.3.tgz"
  "version" "2.0.0-next.3"
  dependencies:
    "is-core-module" "^2.2.0"
    "path-parse" "^1.0.6"

"ret@~0.1.10":
  "integrity" "sha1-uKSCXVvbH8P29Twrwz+BOIaBx7w="
  "resolved" "https://registry.nlark.com/ret/download/ret-0.1.15.tgz"
  "version" "0.1.15"

"retry@^0.12.0":
  "integrity" "sha1-G0KmJmoh8HQh0bC1S33BZ7AcATs="
  "resolved" "https://registry.nlark.com/retry/download/retry-0.12.0.tgz"
  "version" "0.12.0"

"rgb-regex@^1.0.1":
  "integrity" "sha1-wODWiC3w4jviVKR16O3UGRX+rrE="
  "resolved" "https://registry.npm.taobao.org/rgb-regex/download/rgb-regex-1.0.1.tgz"
  "version" "1.0.1"

"rgba-regex@^1.0.0":
  "integrity" "sha1-QzdOLiyglosO8VI0YLfXMP8i7rM="
  "resolved" "https://registry.nlark.com/rgba-regex/download/rgba-regex-1.0.0.tgz"
  "version" "1.0.0"

"rimraf@^2.5.4":
  "integrity" "sha1-NXl/E6f9rcVmFCwp1PB8ytSD4+w="
  "resolved" "https://registry.npm.taobao.org/rimraf/download/rimraf-2.7.1.tgz"
  "version" "2.7.1"
  dependencies:
    "glob" "^7.1.3"

"rimraf@^2.6.3":
  "integrity" "sha1-NXl/E6f9rcVmFCwp1PB8ytSD4+w="
  "resolved" "https://registry.npm.taobao.org/rimraf/download/rimraf-2.7.1.tgz"
  "version" "2.7.1"
  dependencies:
    "glob" "^7.1.3"

"rimraf@^2.7.1":
  "integrity" "sha1-NXl/E6f9rcVmFCwp1PB8ytSD4+w="
  "resolved" "https://registry.npm.taobao.org/rimraf/download/rimraf-2.7.1.tgz"
  "version" "2.7.1"
  dependencies:
    "glob" "^7.1.3"

"rimraf@^3.0.0", "rimraf@^3.0.2":
  "integrity" "sha1-8aVAK6YiCtUswSgrrBrjqkn9Bho="
  "resolved" "https://registry.npm.taobao.org/rimraf/download/rimraf-3.0.2.tgz"
  "version" "3.0.2"
  dependencies:
    "glob" "^7.1.3"

"rimraf@2":
  "integrity" "sha1-NXl/E6f9rcVmFCwp1PB8ytSD4+w="
  "resolved" "https://registry.npm.taobao.org/rimraf/download/rimraf-2.7.1.tgz"
  "version" "2.7.1"
  dependencies:
    "glob" "^7.1.3"

"ripemd160@^2.0.0", "ripemd160@^2.0.1":
  "integrity" "sha1-ocGm9iR1FXe6XQeRTLyShQWFiQw="
  "resolved" "https://registry.npm.taobao.org/ripemd160/download/ripemd160-2.0.2.tgz"
  "version" "2.0.2"
  dependencies:
    "hash-base" "^3.0.0"
    "inherits" "^2.0.1"

"rmc-feedback@^2.0.0":
  "integrity" "sha1-y8bLOuY8emNe7w4l5PuvWsNm7qo="
  "resolved" "https://registry.npm.taobao.org/rmc-feedback/download/rmc-feedback-2.0.0.tgz"
  "version" "2.0.0"
  dependencies:
    "babel-runtime" "6.x"
    "classnames" "^2.2.5"

"run-queue@^1.0.0", "run-queue@^1.0.3":
  "integrity" "sha1-6Eg5bwV9Ij8kOGkkYY4laUFh7Ec="
  "resolved" "https://registry.npm.taobao.org/run-queue/download/run-queue-1.0.3.tgz"
  "version" "1.0.3"
  dependencies:
    "aproba" "^1.1.1"

"rxjs@^6.6.3":
  "integrity" "sha512-hTdwr+7yYNIT5n4AMYp85KA6yw2Va0FLa3Rguvbpa4W3I5xynaBZo41cM3XM+4Q6fRMj3sBYIR1VAmZMXYJvRQ=="
  "resolved" "https://registry.npmmirror.com/rxjs/-/rxjs-6.6.7.tgz"
  "version" "6.6.7"
  dependencies:
    "tslib" "^1.9.0"

"safe-buffer@^5.0.1", "safe-buffer@^5.1.0", "safe-buffer@^5.1.1", "safe-buffer@^5.1.2", "safe-buffer@>=5.1.0", "safe-buffer@~5.1.0", "safe-buffer@~5.1.1", "safe-buffer@5.1.2":
  "integrity" "sha1-mR7GnSluAxN0fVm9/St0XDX4go0="
  "resolved" "https://registry.nlark.com/safe-buffer/download/safe-buffer-5.1.2.tgz"
  "version" "5.1.2"

"safe-buffer@^5.2.0":
  "integrity" "sha1-Hq+fqb2x/dTsdfWPnNtOa3gn7sY="
  "resolved" "https://registry.nlark.com/safe-buffer/download/safe-buffer-5.2.1.tgz"
  "version" "5.2.1"

"safe-regex@^1.1.0":
  "integrity" "sha1-QKNmnzsHfR6UPURinhV91IAjvy4="
  "resolved" "https://registry.npm.taobao.org/safe-regex/download/safe-regex-1.1.0.tgz"
  "version" "1.1.0"
  dependencies:
    "ret" "~0.1.10"

"safer-buffer@^2.0.2", "safer-buffer@^2.1.0", "safer-buffer@>= 2.1.2 < 3", "safer-buffer@>= 2.1.2 < 3.0.0", "safer-buffer@~2.1.0":
  "integrity" "sha1-RPoWGwGHuVSd2Eu5GAL5vYOFzWo="
  "resolved" "https://registry.nlark.com/safer-buffer/download/safer-buffer-2.1.2.tgz"
  "version" "2.1.2"

"sass-graph@2.2.5":
  "integrity" "sha1-qYHIdEa4MZ2W3OBnHkh4eb0kwug="
  "resolved" "https://registry.npm.taobao.org/sass-graph/download/sass-graph-2.2.5.tgz"
  "version" "2.2.5"
  dependencies:
    "glob" "^7.0.0"
    "lodash" "^4.0.0"
    "scss-tokenizer" "^0.2.3"
    "yargs" "^13.3.2"

"sass-loader@^7.2.0":
  "integrity" "sha1-pb9ooEvOocE/+ELXRxUPerfQ0j8="
  "resolved" "https://registry.nlark.com/sass-loader/download/sass-loader-7.3.1.tgz"
  "version" "7.3.1"
  dependencies:
    "clone-deep" "^4.0.1"
    "loader-utils" "^1.0.1"
    "neo-async" "^2.5.0"
    "pify" "^4.0.1"
    "semver" "^6.3.0"

"sax@~1.2.4":
  "integrity" "sha1-KBYjTiN4vdxOU1T6tcqold9xANk="
  "resolved" "https://registry.nlark.com/sax/download/sax-1.2.4.tgz"
  "version" "1.2.4"

"saxes@^5.0.1":
  "integrity" "sha1-7rq5U/o7dgjb6U5drbFciI+maW0="
  "resolved" "https://registry.nlark.com/saxes/download/saxes-5.0.1.tgz"
  "version" "5.0.1"
  dependencies:
    "xmlchars" "^2.2.0"

"scheduler@^0.19.1":
  "integrity" "sha1-Tz4u0sGn1laB9MhU+oxaHMtA8ZY="
  "resolved" "https://registry.nlark.com/scheduler/download/scheduler-0.19.1.tgz?cache=0&sync_timestamp=1624897084172&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fscheduler%2Fdownload%2Fscheduler-0.19.1.tgz"
  "version" "0.19.1"
  dependencies:
    "loose-envify" "^1.1.0"
    "object-assign" "^4.1.1"

"schema-utils@^1.0.0":
  "integrity" "sha1-C3mpMgTXtgDUsoUNH2bCo0lRx3A="
  "resolved" "https://registry.nlark.com/schema-utils/download/schema-utils-1.0.0.tgz"
  "version" "1.0.0"
  dependencies:
    "ajv" "^6.1.0"
    "ajv-errors" "^1.0.0"
    "ajv-keywords" "^3.1.0"

"schema-utils@^2.6.1", "schema-utils@^2.6.5", "schema-utils@^2.7.0":
  "integrity" "sha1-HKTzLRskxZDCA7jnpQvw6kzTlNc="
  "resolved" "https://registry.nlark.com/schema-utils/download/schema-utils-2.7.1.tgz"
  "version" "2.7.1"
  dependencies:
    "@types/json-schema" "^7.0.5"
    "ajv" "^6.12.4"
    "ajv-keywords" "^3.5.2"

"scss-tokenizer@^0.2.3":
  "integrity" "sha1-jrBtualyMzOCTT9VMGQRSYR85dE="
  "resolved" "https://registry.npm.taobao.org/scss-tokenizer/download/scss-tokenizer-0.2.3.tgz"
  "version" "0.2.3"
  dependencies:
    "js-base64" "^2.1.8"
    "source-map" "^0.4.2"

"select-hose@^2.0.0":
  "integrity" "sha1-Yl2GWPhlr0Psliv8N2o3NZpJlMo="
  "resolved" "https://registry.npm.taobao.org/select-hose/download/select-hose-2.0.0.tgz"
  "version" "2.0.0"

"select@^1.1.2":
  "integrity" "sha1-DnNQrN7ICxEIUoeG7B1EGNEbOW0="
  "resolved" "https://registry.nlark.com/select/download/select-1.1.2.tgz"
  "version" "1.1.2"

"selfsigned@^1.10.8":
  "integrity" "sha1-JJKc2Qb+D0S20B+yOZmnOVN6y+k="
  "resolved" "https://registry.nlark.com/selfsigned/download/selfsigned-1.10.11.tgz"
  "version" "1.10.11"
  dependencies:
    "node-forge" "^0.10.0"

"semver@^5.5.0":
  "integrity" "sha1-qVT5Ma66UI0we78Gnv8MAclhFvc="
  "resolved" "https://registry.nlark.com/semver/download/semver-5.7.1.tgz?cache=0&sync_timestamp=1618846864940&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fsemver%2Fdownload%2Fsemver-5.7.1.tgz"
  "version" "5.7.1"

"semver@^5.6.0":
  "integrity" "sha1-qVT5Ma66UI0we78Gnv8MAclhFvc="
  "resolved" "https://registry.nlark.com/semver/download/semver-5.7.1.tgz?cache=0&sync_timestamp=1618846864940&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fsemver%2Fdownload%2Fsemver-5.7.1.tgz"
  "version" "5.7.1"

"semver@^6.0.0", "semver@^6.1.1", "semver@^6.1.2", "semver@^6.3.0":
  "integrity" "sha1-7gpkyK9ejO6mdoexM3YeG+y9HT0="
  "resolved" "https://registry.nlark.com/semver/download/semver-6.3.0.tgz?cache=0&sync_timestamp=1618846864940&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fsemver%2Fdownload%2Fsemver-6.3.0.tgz"
  "version" "6.3.0"

"semver@^7.2.1":
  "integrity" "sha1-C2Ich5NI2JmOSw5L6Us/EuYBjvc="
  "resolved" "https://registry.nlark.com/semver/download/semver-7.3.5.tgz?cache=0&sync_timestamp=1618846864940&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fsemver%2Fdownload%2Fsemver-7.3.5.tgz"
  "version" "7.3.5"
  dependencies:
    "lru-cache" "^6.0.0"

"semver@^7.3.2":
  "integrity" "sha1-C2Ich5NI2JmOSw5L6Us/EuYBjvc="
  "resolved" "https://registry.nlark.com/semver/download/semver-7.3.5.tgz?cache=0&sync_timestamp=1618846864940&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fsemver%2Fdownload%2Fsemver-7.3.5.tgz"
  "version" "7.3.5"
  dependencies:
    "lru-cache" "^6.0.0"

"semver@~5.3.0":
  "integrity" "sha1-myzl094C0XxgEq0yaqa00M9U+U8="
  "resolved" "https://registry.nlark.com/semver/download/semver-5.3.0.tgz?cache=0&sync_timestamp=1618846864940&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fsemver%2Fdownload%2Fsemver-5.3.0.tgz"
  "version" "5.3.0"

"semver@2 || 3 || 4 || 5":
  "integrity" "sha1-qVT5Ma66UI0we78Gnv8MAclhFvc="
  "resolved" "https://registry.nlark.com/semver/download/semver-5.7.1.tgz?cache=0&sync_timestamp=1618846864940&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fsemver%2Fdownload%2Fsemver-5.7.1.tgz"
  "version" "5.7.1"

"semver@7.0.0":
  "integrity" "sha1-XzyjV2HkfgWyBsba/yz4FPAxa44="
  "resolved" "https://registry.nlark.com/semver/download/semver-7.0.0.tgz?cache=0&sync_timestamp=1618846864940&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fsemver%2Fdownload%2Fsemver-7.0.0.tgz"
  "version" "7.0.0"

"send@0.17.1":
  "integrity" "sha1-wdiwWfeQD3Rm3Uk4vcROEd2zdsg="
  "resolved" "https://registry.nlark.com/send/download/send-0.17.1.tgz?cache=0&sync_timestamp=1618846901933&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fsend%2Fdownload%2Fsend-0.17.1.tgz"
  "version" "0.17.1"
  dependencies:
    "debug" "2.6.9"
    "depd" "~1.1.2"
    "destroy" "~1.0.4"
    "encodeurl" "~1.0.2"
    "escape-html" "~1.0.3"
    "etag" "~1.8.1"
    "fresh" "0.5.2"
    "http-errors" "~1.7.2"
    "mime" "1.6.0"
    "ms" "2.1.1"
    "on-finished" "~2.3.0"
    "range-parser" "~1.2.1"
    "statuses" "~1.5.0"

"serialize-error@^2.1.0":
  "integrity" "sha1-ULZ51WNc34Rme9yOWa9OW4HV9go="
  "resolved" "https://registry.nlark.com/serialize-error/download/serialize-error-2.1.0.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fserialize-error%2Fdownload%2Fserialize-error-2.1.0.tgz"
  "version" "2.1.0"

"serialize-javascript@^2.1.2":
  "integrity" "sha1-7OxTsOAxe9yV73arcHS3OEeF+mE="
  "resolved" "https://registry.nlark.com/serialize-javascript/download/serialize-javascript-2.1.2.tgz?cache=0&sync_timestamp=1624284111898&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fserialize-javascript%2Fdownload%2Fserialize-javascript-2.1.2.tgz"
  "version" "2.1.2"

"serialize-javascript@^4.0.0":
  "integrity" "sha1-tSXhI4SJpez8Qq+sw/6Z5mb0sao="
  "resolved" "https://registry.nlark.com/serialize-javascript/download/serialize-javascript-4.0.0.tgz?cache=0&sync_timestamp=1624284111898&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fserialize-javascript%2Fdownload%2Fserialize-javascript-4.0.0.tgz"
  "version" "4.0.0"
  dependencies:
    "randombytes" "^2.1.0"

"serve-index@^1.9.1":
  "integrity" "sha1-03aNabHn2C5c4FD/9bRTvqEqkjk="
  "resolved" "https://registry.npm.taobao.org/serve-index/download/serve-index-1.9.1.tgz"
  "version" "1.9.1"
  dependencies:
    "accepts" "~1.3.4"
    "batch" "0.6.1"
    "debug" "2.6.9"
    "escape-html" "~1.0.3"
    "http-errors" "~1.6.2"
    "mime-types" "~2.1.17"
    "parseurl" "~1.3.2"

"serve-static@1.14.1":
  "integrity" "sha1-Zm5jbcTwEPfvKZcKiKZ0MgiYsvk="
  "resolved" "https://registry.nlark.com/serve-static/download/serve-static-1.14.1.tgz"
  "version" "1.14.1"
  dependencies:
    "encodeurl" "~1.0.2"
    "escape-html" "~1.0.3"
    "parseurl" "~1.3.3"
    "send" "0.17.1"

"set-blocking@^2.0.0", "set-blocking@~2.0.0":
  "integrity" "sha1-BF+XgtARrppoA93TgrJDkrPYkPc="
  "resolved" "https://registry.nlark.com/set-blocking/download/set-blocking-2.0.0.tgz"
  "version" "2.0.0"

"set-immediate-shim@~1.0.1":
  "integrity" "sha1-SysbJ+uAip+NzEgaWOXlb1mfP2E="
  "resolved" "https://registry.npm.taobao.org/set-immediate-shim/download/set-immediate-shim-1.0.1.tgz"
  "version" "1.0.1"

"set-value@^2.0.0", "set-value@^2.0.1":
  "integrity" "sha1-oY1AUw5vB95CKMfe/kInr4ytAFs="
  "resolved" "https://registry.nlark.com/set-value/download/set-value-2.0.1.tgz"
  "version" "2.0.1"
  dependencies:
    "extend-shallow" "^2.0.1"
    "is-extendable" "^0.1.1"
    "is-plain-object" "^2.0.3"
    "split-string" "^3.0.1"

"setimmediate@^1.0.4", "setimmediate@^1.0.5", "setimmediate@~1.0.4":
  "integrity" "sha1-KQy7Iy4waULX1+qbg3Mqt4VvgoU="
  "resolved" "https://registry.nlark.com/setimmediate/download/setimmediate-1.0.5.tgz"
  "version" "1.0.5"

"setprototypeof@1.1.0":
  "integrity" "sha1-0L2FU2iHtv58DYGMuWLZ2RxU5lY="
  "resolved" "https://registry.nlark.com/setprototypeof/download/setprototypeof-1.1.0.tgz"
  "version" "1.1.0"

"setprototypeof@1.1.1":
  "integrity" "sha1-fpWsskqpL1iF4KvvW6ExMw1K5oM="
  "resolved" "https://registry.nlark.com/setprototypeof/download/setprototypeof-1.1.1.tgz"
  "version" "1.1.1"

"sha.js@^2.4.0", "sha.js@^2.4.8":
  "integrity" "sha1-N6XPC4HsvGlD3hCbopYNGyZYSuc="
  "resolved" "https://registry.npm.taobao.org/sha.js/download/sha.js-2.4.11.tgz"
  "version" "2.4.11"
  dependencies:
    "inherits" "^2.0.1"
    "safe-buffer" "^5.0.1"

"shallow-clone@^3.0.0":
  "integrity" "sha1-jymBrZJTH1UDWwH7IwdppA4C76M="
  "resolved" "https://registry.nlark.com/shallow-clone/download/shallow-clone-3.0.1.tgz"
  "version" "3.0.1"
  dependencies:
    "kind-of" "^6.0.2"

"shallow-equal@^1.0.0":
  "integrity" "sha1-TBar+lYEOqINBQMk76aJQLDaedo="
  "resolved" "https://registry.npm.taobao.org/shallow-equal/download/shallow-equal-1.2.1.tgz"
  "version" "1.2.1"

"shallowequal@^1.0.2", "shallowequal@^1.1.0":
  "integrity" "sha1-GI1SHelbkIdAT9TctosT3wrk5/g="
  "resolved" "https://registry.npm.taobao.org/shallowequal/download/shallowequal-1.1.0.tgz"
  "version" "1.1.0"

"shebang-command@^1.2.0":
  "integrity" "sha1-RKrGW2lbAzmJaMOfNj/uXer98eo="
  "resolved" "https://registry.nlark.com/shebang-command/download/shebang-command-1.2.0.tgz"
  "version" "1.2.0"
  dependencies:
    "shebang-regex" "^1.0.0"

"shebang-command@^2.0.0":
  "integrity" "sha1-zNCvT4g1+9wmW4JGGq8MNmY/NOo="
  "resolved" "https://registry.nlark.com/shebang-command/download/shebang-command-2.0.0.tgz"
  "version" "2.0.0"
  dependencies:
    "shebang-regex" "^3.0.0"

"shebang-regex@^1.0.0":
  "integrity" "sha1-2kL0l0DAtC2yypcoVxyxkMmO/qM="
  "resolved" "https://registry.npm.taobao.org/shebang-regex/download/shebang-regex-1.0.0.tgz"
  "version" "1.0.0"

"shebang-regex@^3.0.0":
  "integrity" "sha1-rhbxZE2HPsrYQ7AwexQzYtTEIXI="
  "resolved" "https://registry.npm.taobao.org/shebang-regex/download/shebang-regex-3.0.0.tgz"
  "version" "3.0.0"

"side-channel@^1.0.4":
  "integrity" "sha1-785cj9wQTudRslxY1CkAEfpeos8="
  "resolved" "https://registry.npm.taobao.org/side-channel/download/side-channel-1.0.4.tgz"
  "version" "1.0.4"
  dependencies:
    "call-bind" "^1.0.0"
    "get-intrinsic" "^1.0.2"
    "object-inspect" "^1.9.0"

"signal-exit@^3.0.0":
  "integrity" "sha1-oUEMLt2PB3sItOJTyOrPyvBXRhw="
  "resolved" "https://registry.npm.taobao.org/signal-exit/download/signal-exit-3.0.3.tgz?cache=0&sync_timestamp=1592843131591&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fsignal-exit%2Fdownload%2Fsignal-exit-3.0.3.tgz"
  "version" "3.0.3"

"simple-swizzle@^0.2.2":
  "integrity" "sha1-pNprY1/8zMoz9w0Xy5JZLeleVXo="
  "resolved" "https://registry.npm.taobao.org/simple-swizzle/download/simple-swizzle-0.2.2.tgz"
  "version" "0.2.2"
  dependencies:
    "is-arrayish" "^0.3.1"

"slash@^1.0.0":
  "integrity" "sha1-xB8vbDn8FtHNF61LXYlhFK5HDVU="
  "resolved" "https://registry.nlark.com/slash/download/slash-1.0.0.tgz"
  "version" "1.0.0"

"slice-ansi@^4.0.0":
  "integrity" "sha1-UA6N0P1VsFgVCGJVsxla3ypF/ms="
  "resolved" "https://registry.npm.taobao.org/slice-ansi/download/slice-ansi-4.0.0.tgz?cache=0&sync_timestamp=1618554984144&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fslice-ansi%2Fdownload%2Fslice-ansi-4.0.0.tgz"
  "version" "4.0.0"
  dependencies:
    "ansi-styles" "^4.0.0"
    "astral-regex" "^2.0.0"
    "is-fullwidth-code-point" "^3.0.0"

"snapdragon-node@^2.0.1":
  "integrity" "sha1-bBdfhv8UvbByRWPo88GwIaKGhTs="
  "resolved" "https://registry.nlark.com/snapdragon-node/download/snapdragon-node-2.1.1.tgz"
  "version" "2.1.1"
  dependencies:
    "define-property" "^1.0.0"
    "isobject" "^3.0.0"
    "snapdragon-util" "^3.0.1"

"snapdragon-util@^3.0.1":
  "integrity" "sha1-+VZHlIbyrNeXAGk/b3uAXkWrVuI="
  "resolved" "https://registry.nlark.com/snapdragon-util/download/snapdragon-util-3.0.1.tgz"
  "version" "3.0.1"
  dependencies:
    "kind-of" "^3.2.0"

"snapdragon@^0.8.1":
  "integrity" "sha1-ZJIufFZbDhQgS6GqfWlkJ40lGC0="
  "resolved" "https://registry.npm.taobao.org/snapdragon/download/snapdragon-0.8.2.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fsnapdragon%2Fdownload%2Fsnapdragon-0.8.2.tgz"
  "version" "0.8.2"
  dependencies:
    "base" "^0.11.1"
    "debug" "^2.2.0"
    "define-property" "^0.2.5"
    "extend-shallow" "^2.0.1"
    "map-cache" "^0.2.2"
    "source-map" "^0.5.6"
    "source-map-resolve" "^0.5.0"
    "use" "^3.1.0"

"sockjs-client@^1.5.0":
  "integrity" "sha1-JWkI9tWt+5Tau9vQLGY2LMoPnqY="
  "resolved" "https://registry.npm.taobao.org/sockjs-client/download/sockjs-client-1.5.1.tgz?cache=0&sync_timestamp=1616686612247&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fsockjs-client%2Fdownload%2Fsockjs-client-1.5.1.tgz"
  "version" "1.5.1"
  dependencies:
    "debug" "^3.2.6"
    "eventsource" "^1.0.7"
    "faye-websocket" "^0.11.3"
    "inherits" "^2.0.4"
    "json3" "^3.3.3"
    "url-parse" "^1.5.1"

"sockjs@^0.3.21":
  "integrity" "sha1-s0/7mOeWkwtgoM+hGQTWozmn1Bc="
  "resolved" "https://registry.npm.taobao.org/sockjs/download/sockjs-0.3.21.tgz"
  "version" "0.3.21"
  dependencies:
    "faye-websocket" "^0.11.3"
    "uuid" "^3.4.0"
    "websocket-driver" "^0.7.4"

"sort-keys@^1.0.0":
  "integrity" "sha1-RBttTTRnmPG05J6JIK37oOVD+a0="
  "resolved" "https://registry.nlark.com/sort-keys/download/sort-keys-1.1.2.tgz"
  "version" "1.1.2"
  dependencies:
    "is-plain-obj" "^1.0.0"

"source-list-map@^2.0.0":
  "integrity" "sha1-OZO9hzv8SEecyp6jpUeDXHwVSzQ="
  "resolved" "https://registry.npm.taobao.org/source-list-map/download/source-list-map-2.0.1.tgz"
  "version" "2.0.1"

"source-map-loader@^0.2.4":
  "integrity" "sha1-wYsNxuI79m9nkkN1V8VpoR4HInE="
  "resolved" "https://registry.nlark.com/source-map-loader/download/source-map-loader-0.2.4.tgz"
  "version" "0.2.4"
  dependencies:
    "async" "^2.5.0"
    "loader-utils" "^1.1.0"

"source-map-resolve@^0.5.0":
  "integrity" "sha1-GQhmvs51U+H48mei7oLGBrVQmho="
  "resolved" "https://registry.nlark.com/source-map-resolve/download/source-map-resolve-0.5.3.tgz"
  "version" "0.5.3"
  dependencies:
    "atob" "^2.1.2"
    "decode-uri-component" "^0.2.0"
    "resolve-url" "^0.2.1"
    "source-map-url" "^0.4.0"
    "urix" "^0.1.0"

"source-map-support@~0.5.12":
  "integrity" "sha1-qYti+G3K9PZzmWSMCFKRq56P7WE="
  "resolved" "https://registry.npm.taobao.org/source-map-support/download/source-map-support-0.5.19.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fsource-map-support%2Fdownload%2Fsource-map-support-0.5.19.tgz"
  "version" "0.5.19"
  dependencies:
    "buffer-from" "^1.0.0"
    "source-map" "^0.6.0"

"source-map-url@^0.4.0":
  "integrity" "sha1-CvZmBadFpaL5HPG7+KevvCg97FY="
  "resolved" "https://registry.npm.taobao.org/source-map-url/download/source-map-url-0.4.1.tgz"
  "version" "0.4.1"

"source-map@^0.4.2":
  "integrity" "sha1-66T12pwNyZneaAMti092FzZSA2s="
  "resolved" "https://registry.nlark.com/source-map/download/source-map-0.4.4.tgz"
  "version" "0.4.4"
  dependencies:
    "amdefine" ">=0.0.4"

"source-map@^0.5.0":
  "integrity" "sha1-igOdLRAh0i0eoUyA2OpGi6LvP8w="
  "resolved" "https://registry.nlark.com/source-map/download/source-map-0.5.7.tgz"
  "version" "0.5.7"

"source-map@^0.5.6":
  "integrity" "sha1-igOdLRAh0i0eoUyA2OpGi6LvP8w="
  "resolved" "https://registry.nlark.com/source-map/download/source-map-0.5.7.tgz"
  "version" "0.5.7"

"source-map@^0.6.0":
  "integrity" "sha1-dHIq8y6WFOnCh6jQu95IteLxomM="
  "resolved" "https://registry.nlark.com/source-map/download/source-map-0.6.1.tgz"
  "version" "0.6.1"

"source-map@^0.6.1":
  "integrity" "sha1-dHIq8y6WFOnCh6jQu95IteLxomM="
  "resolved" "https://registry.nlark.com/source-map/download/source-map-0.6.1.tgz"
  "version" "0.6.1"

"source-map@^0.7.3":
  "integrity" "sha1-UwL4FpAxc1ImVECS5kmB91F1A4M="
  "resolved" "https://registry.nlark.com/source-map/download/source-map-0.7.3.tgz"
  "version" "0.7.3"

"source-map@~0.6.0":
  "integrity" "sha1-dHIq8y6WFOnCh6jQu95IteLxomM="
  "resolved" "https://registry.nlark.com/source-map/download/source-map-0.6.1.tgz"
  "version" "0.6.1"

"source-map@~0.6.1":
  "integrity" "sha1-dHIq8y6WFOnCh6jQu95IteLxomM="
  "resolved" "https://registry.nlark.com/source-map/download/source-map-0.6.1.tgz"
  "version" "0.6.1"

"spawn-command@^0.0.2-1":
  "integrity" "sha512-n98l9E2RMSJ9ON1AKisHzz7V42VDiBQGY6PB1BwRglz99wpVsSuGzQ+jOi6lFXBGVTCrRpltvjm+/XA+tpeJrg=="
  "resolved" "https://registry.npmmirror.com/spawn-command/-/spawn-command-0.0.2-1.tgz"
  "version" "0.0.2-1"

"spdx-correct@^3.0.0":
  "integrity" "sha1-3s6BrJweZxPl99G28X1Gj6U9iak="
  "resolved" "https://registry.npm.taobao.org/spdx-correct/download/spdx-correct-3.1.1.tgz"
  "version" "3.1.1"
  dependencies:
    "spdx-expression-parse" "^3.0.0"
    "spdx-license-ids" "^3.0.0"

"spdx-exceptions@^2.1.0":
  "integrity" "sha1-PyjOGnegA3JoPq3kpDMYNSeiFj0="
  "resolved" "https://registry.nlark.com/spdx-exceptions/download/spdx-exceptions-2.3.0.tgz"
  "version" "2.3.0"

"spdx-expression-parse@^3.0.0":
  "integrity" "sha1-z3D1BILu/cmOPOCmgz5KU87rpnk="
  "resolved" "https://registry.npm.taobao.org/spdx-expression-parse/download/spdx-expression-parse-3.0.1.tgz"
  "version" "3.0.1"
  dependencies:
    "spdx-exceptions" "^2.1.0"
    "spdx-license-ids" "^3.0.0"

"spdx-license-ids@^3.0.0":
  "integrity" "sha1-illRNd75WSvaaXCUdPHL7qfCRn8="
  "resolved" "https://registry.nlark.com/spdx-license-ids/download/spdx-license-ids-3.0.9.tgz?cache=0&sync_timestamp=1621652583280&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fspdx-license-ids%2Fdownload%2Fspdx-license-ids-3.0.9.tgz"
  "version" "3.0.9"

"spdy-transport@^3.0.0":
  "integrity" "sha1-ANSGOmQArXXfkzYaFghgXl3NzzE="
  "resolved" "https://registry.nlark.com/spdy-transport/download/spdy-transport-3.0.0.tgz"
  "version" "3.0.0"
  dependencies:
    "debug" "^4.1.0"
    "detect-node" "^2.0.4"
    "hpack.js" "^2.1.6"
    "obuf" "^1.1.2"
    "readable-stream" "^3.0.6"
    "wbuf" "^1.7.3"

"spdy@^4.0.2":
  "integrity" "sha1-t09GYgOj7aRSwCSSuR+56EonZ3s="
  "resolved" "https://registry.nlark.com/spdy/download/spdy-4.0.2.tgz"
  "version" "4.0.2"
  dependencies:
    "debug" "^4.1.0"
    "handle-thing" "^2.0.0"
    "http-deceiver" "^1.2.7"
    "select-hose" "^2.0.0"
    "spdy-transport" "^3.0.0"

"split-string@^3.0.1", "split-string@^3.0.2":
  "integrity" "sha1-fLCd2jqGWFcFxks5pkZgOGguj+I="
  "resolved" "https://registry.nlark.com/split-string/download/split-string-3.1.0.tgz"
  "version" "3.1.0"
  dependencies:
    "extend-shallow" "^3.0.0"

"sprintf-js@~1.0.2":
  "integrity" "sha1-BOaSb2YolTVPPdAVIDYzuFcpfiw="
  "resolved" "https://registry.nlark.com/sprintf-js/download/sprintf-js-1.0.3.tgz"
  "version" "1.0.3"

"sshpk@^1.7.0":
  "integrity" "sha1-+2YcC+8ps520B2nuOfpwCT1vaHc="
  "resolved" "https://registry.npm.taobao.org/sshpk/download/sshpk-1.16.1.tgz"
  "version" "1.16.1"
  dependencies:
    "asn1" "~0.2.3"
    "assert-plus" "^1.0.0"
    "bcrypt-pbkdf" "^1.0.0"
    "dashdash" "^1.12.0"
    "ecc-jsbn" "~0.1.1"
    "getpass" "^0.1.1"
    "jsbn" "~0.1.0"
    "safer-buffer" "^2.0.2"
    "tweetnacl" "~0.14.0"

"ssri@^6.0.1":
  "integrity" "sha1-FXk5E08gRk5zAd26PpD/qPdyisU="
  "resolved" "https://registry.nlark.com/ssri/download/ssri-6.0.2.tgz?cache=0&sync_timestamp=1621364668574&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fssri%2Fdownload%2Fssri-6.0.2.tgz"
  "version" "6.0.2"
  dependencies:
    "figgy-pudding" "^3.5.1"

"ssri@^7.0.0":
  "integrity" "sha1-M+RPiWqWcVjjxjRo5H7EZhO5W18="
  "resolved" "https://registry.nlark.com/ssri/download/ssri-7.1.1.tgz?cache=0&sync_timestamp=1621364668574&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fssri%2Fdownload%2Fssri-7.1.1.tgz"
  "version" "7.1.1"
  dependencies:
    "figgy-pudding" "^3.5.1"
    "minipass" "^3.1.1"

"stable@^0.1.8":
  "integrity" "sha1-g26zyDgv4pNv6vVEYxAXzn1Ho88="
  "resolved" "https://registry.npm.taobao.org/stable/download/stable-0.1.8.tgz"
  "version" "0.1.8"

"stackframe@^1.1.1":
  "integrity" "sha1-UkKUktY8YuuYmATBFVLj0i53kwM="
  "resolved" "https://registry.npm.taobao.org/stackframe/download/stackframe-1.2.0.tgz"
  "version" "1.2.0"

"static-extend@^0.1.1":
  "integrity" "sha1-YICcOcv/VTNyJv1eC1IPNB8ftcY="
  "resolved" "https://registry.nlark.com/static-extend/download/static-extend-0.1.2.tgz"
  "version" "0.1.2"
  dependencies:
    "define-property" "^0.2.5"
    "object-copy" "^0.1.0"

"statuses@>= 1.4.0 < 2", "statuses@>= 1.5.0 < 2", "statuses@~1.5.0":
  "integrity" "sha1-Fhx9rBd2Wf2YEfQ3cfqZOBR4Yow="
  "resolved" "https://registry.npm.taobao.org/statuses/download/statuses-1.5.0.tgz?cache=0&sync_timestamp=1609654090567&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fstatuses%2Fdownload%2Fstatuses-1.5.0.tgz"
  "version" "1.5.0"

"stdout-stream@^1.4.0":
  "integrity" "sha1-WsF0zdXNcmEEqgwLK9g4FdjVNd4="
  "resolved" "https://registry.npm.taobao.org/stdout-stream/download/stdout-stream-1.4.1.tgz"
  "version" "1.4.1"
  dependencies:
    "readable-stream" "^2.0.1"

"stream-browserify@^2.0.1":
  "integrity" "sha1-h1IdOKRKp+6RzhzSpH3wy0ndZgs="
  "resolved" "https://registry.nlark.com/stream-browserify/download/stream-browserify-2.0.2.tgz"
  "version" "2.0.2"
  dependencies:
    "inherits" "~2.0.1"
    "readable-stream" "^2.0.2"

"stream-each@^1.1.0":
  "integrity" "sha1-6+J6DDibBPvMIzZClS4Qcxr6m64="
  "resolved" "https://registry.nlark.com/stream-each/download/stream-each-1.2.3.tgz"
  "version" "1.2.3"
  dependencies:
    "end-of-stream" "^1.1.0"
    "stream-shift" "^1.0.0"

"stream-http@^2.7.2":
  "integrity" "sha1-stJCRpKIpaJ+xP6JM6z2I95lFPw="
  "resolved" "https://registry.npm.taobao.org/stream-http/download/stream-http-2.8.3.tgz?cache=0&sync_timestamp=1618430770209&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fstream-http%2Fdownload%2Fstream-http-2.8.3.tgz"
  "version" "2.8.3"
  dependencies:
    "builtin-status-codes" "^3.0.0"
    "inherits" "^2.0.1"
    "readable-stream" "^2.3.6"
    "to-arraybuffer" "^1.0.0"
    "xtend" "^4.0.0"

"stream-shift@^1.0.0":
  "integrity" "sha1-1wiCgVWasneEJCebCHfaPDktWj0="
  "resolved" "https://registry.nlark.com/stream-shift/download/stream-shift-1.0.1.tgz"
  "version" "1.0.1"

"strict-uri-encode@^1.0.0":
  "integrity" "sha1-J5siXfHVgrH1TmWt3UNS4Y+qBxM="
  "resolved" "https://registry.npm.taobao.org/strict-uri-encode/download/strict-uri-encode-1.1.0.tgz"
  "version" "1.1.0"

"string_decoder@^1.0.0", "string_decoder@^1.1.1", "string_decoder@~1.1.1":
  "integrity" "sha1-nPFhG6YmhdcDCunkujQUnDrwP8g="
  "resolved" "https://registry.nlark.com/string_decoder/download/string_decoder-1.1.1.tgz"
  "version" "1.1.1"
  dependencies:
    "safe-buffer" "~5.1.0"

"string-convert@^0.2.0":
  "integrity" "sha1-aYLMMEn7tM2F+LJFaLnZvznu/5c="
  "resolved" "https://registry.nlark.com/string-convert/download/string-convert-0.2.1.tgz"
  "version" "0.2.1"

"string-width@^1.0.1":
  "integrity" "sha1-EYvfW4zcUaKn5w0hHgfisLmxB9M="
  "resolved" "https://registry.nlark.com/string-width/download/string-width-1.0.2.tgz"
  "version" "1.0.2"
  dependencies:
    "code-point-at" "^1.0.0"
    "is-fullwidth-code-point" "^1.0.0"
    "strip-ansi" "^3.0.0"

"string-width@^1.0.2 || 2":
  "integrity" "sha1-q5Pyeo3BPSjKyBXEYhQ6bZASrp4="
  "resolved" "https://registry.nlark.com/string-width/download/string-width-2.1.1.tgz"
  "version" "2.1.1"
  dependencies:
    "is-fullwidth-code-point" "^2.0.0"
    "strip-ansi" "^4.0.0"

"string-width@^2.0.0":
  "integrity" "sha1-q5Pyeo3BPSjKyBXEYhQ6bZASrp4="
  "resolved" "https://registry.nlark.com/string-width/download/string-width-2.1.1.tgz"
  "version" "2.1.1"
  dependencies:
    "is-fullwidth-code-point" "^2.0.0"
    "strip-ansi" "^4.0.0"

"string-width@^3.0.0":
  "integrity" "sha1-InZ74htirxCBV0MG9prFG2IgOWE="
  "resolved" "https://registry.nlark.com/string-width/download/string-width-3.1.0.tgz"
  "version" "3.1.0"
  dependencies:
    "emoji-regex" "^7.0.1"
    "is-fullwidth-code-point" "^2.0.0"
    "strip-ansi" "^5.1.0"

"string-width@^3.1.0":
  "integrity" "sha1-InZ74htirxCBV0MG9prFG2IgOWE="
  "resolved" "https://registry.nlark.com/string-width/download/string-width-3.1.0.tgz"
  "version" "3.1.0"
  dependencies:
    "emoji-regex" "^7.0.1"
    "is-fullwidth-code-point" "^2.0.0"
    "strip-ansi" "^5.1.0"

"string-width@^4.1.0", "string-width@^4.2.0":
  "integrity" "sha1-2v1PlVmnWFz7pSnGoKT3NIjr1MU="
  "resolved" "https://registry.nlark.com/string-width/download/string-width-4.2.2.tgz"
  "version" "4.2.2"
  dependencies:
    "emoji-regex" "^8.0.0"
    "is-fullwidth-code-point" "^3.0.0"
    "strip-ansi" "^6.0.0"

"string.prototype.matchall@^4.0.5":
  "integrity" "sha1-WTcGROHbfkwMBFJ3aQz3sBIDxNo="
  "resolved" "https://registry.nlark.com/string.prototype.matchall/download/string.prototype.matchall-4.0.5.tgz?cache=0&sync_timestamp=1621973430160&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fstring.prototype.matchall%2Fdownload%2Fstring.prototype.matchall-4.0.5.tgz"
  "version" "4.0.5"
  dependencies:
    "call-bind" "^1.0.2"
    "define-properties" "^1.1.3"
    "es-abstract" "^1.18.2"
    "get-intrinsic" "^1.1.1"
    "has-symbols" "^1.0.2"
    "internal-slot" "^1.0.3"
    "regexp.prototype.flags" "^1.3.1"
    "side-channel" "^1.0.4"

"string.prototype.trimend@^1.0.4":
  "integrity" "sha1-51rpDClCxjUEaGwYsoe0oLGkX4A="
  "resolved" "https://registry.npm.taobao.org/string.prototype.trimend/download/string.prototype.trimend-1.0.4.tgz"
  "version" "1.0.4"
  dependencies:
    "call-bind" "^1.0.2"
    "define-properties" "^1.1.3"

"string.prototype.trimstart@^1.0.4":
  "integrity" "sha1-s2OZr0qymZtMnGSL16P7K7Jv7u0="
  "resolved" "https://registry.npm.taobao.org/string.prototype.trimstart/download/string.prototype.trimstart-1.0.4.tgz?cache=0&sync_timestamp=1614127232940&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fstring.prototype.trimstart%2Fdownload%2Fstring.prototype.trimstart-1.0.4.tgz"
  "version" "1.0.4"
  dependencies:
    "call-bind" "^1.0.2"
    "define-properties" "^1.1.3"

"strip-ansi@^3.0.0", "strip-ansi@^3.0.1":
  "integrity" "sha1-ajhfuIU9lS1f8F0Oiq+UJ43GPc8="
  "resolved" "https://registry.nlark.com/strip-ansi/download/strip-ansi-3.0.1.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fstrip-ansi%2Fdownload%2Fstrip-ansi-3.0.1.tgz"
  "version" "3.0.1"
  dependencies:
    "ansi-regex" "^2.0.0"

"strip-ansi@^3.0.1":
  "integrity" "sha1-ajhfuIU9lS1f8F0Oiq+UJ43GPc8="
  "resolved" "https://registry.nlark.com/strip-ansi/download/strip-ansi-3.0.1.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fstrip-ansi%2Fdownload%2Fstrip-ansi-3.0.1.tgz"
  "version" "3.0.1"
  dependencies:
    "ansi-regex" "^2.0.0"

"strip-ansi@^4.0.0":
  "integrity" "sha1-qEeQIusaw2iocTibY1JixQXuNo8="
  "resolved" "https://registry.nlark.com/strip-ansi/download/strip-ansi-4.0.0.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fstrip-ansi%2Fdownload%2Fstrip-ansi-4.0.0.tgz"
  "version" "4.0.0"
  dependencies:
    "ansi-regex" "^3.0.0"

"strip-ansi@^5.0.0", "strip-ansi@^5.1.0", "strip-ansi@^5.2.0":
  "integrity" "sha1-jJpTb+tq/JYr36WxBKUJHBrZwK4="
  "resolved" "https://registry.nlark.com/strip-ansi/download/strip-ansi-5.2.0.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fstrip-ansi%2Fdownload%2Fstrip-ansi-5.2.0.tgz"
  "version" "5.2.0"
  dependencies:
    "ansi-regex" "^4.1.0"

"strip-ansi@^6.0.0":
  "integrity" "sha1-CxVx3XZpzNTz4G4U7x7tJiJa5TI="
  "resolved" "https://registry.nlark.com/strip-ansi/download/strip-ansi-6.0.0.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fstrip-ansi%2Fdownload%2Fstrip-ansi-6.0.0.tgz"
  "version" "6.0.0"
  dependencies:
    "ansi-regex" "^5.0.0"

"strip-bom@^2.0.0":
  "integrity" "sha1-YhmoVhZSBJHzV4i9vxRHqZx+aw4="
  "resolved" "https://registry.nlark.com/strip-bom/download/strip-bom-2.0.0.tgz"
  "version" "2.0.0"
  dependencies:
    "is-utf8" "^0.2.0"

"strip-bom@^3.0.0":
  "integrity" "sha1-IzTBjpx1n3vdVv3vfprj1YjmjtM="
  "resolved" "https://registry.nlark.com/strip-bom/download/strip-bom-3.0.0.tgz"
  "version" "3.0.0"

"strip-eof@^1.0.0":
  "integrity" "sha1-u0P/VZim6wXYm1n80SnJgzE2Br8="
  "resolved" "https://registry.npm.taobao.org/strip-eof/download/strip-eof-1.0.0.tgz"
  "version" "1.0.0"

"strip-indent@^1.0.1":
  "integrity" "sha1-DHlipq3vp7vUrDZkYKY4VSrhoKI="
  "resolved" "https://registry.nlark.com/strip-indent/download/strip-indent-1.0.1.tgz"
  "version" "1.0.1"
  dependencies:
    "get-stdin" "^4.0.1"

"strip-json-comments@^3.1.0", "strip-json-comments@^3.1.1":
  "integrity" "sha1-MfEoGzgyYwQ0gxwxDAHMzajL4AY="
  "resolved" "https://registry.npm.taobao.org/strip-json-comments/download/strip-json-comments-3.1.1.tgz?cache=0&sync_timestamp=1594571796132&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fstrip-json-comments%2Fdownload%2Fstrip-json-comments-3.1.1.tgz"
  "version" "3.1.1"

"style-loader@^1.0.0":
  "integrity" "sha1-gotKOzt+eqWEfOe66eh0USEUJJ4="
  "resolved" "https://registry.nlark.com/style-loader/download/style-loader-1.3.0.tgz?cache=0&sync_timestamp=1624560213944&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fstyle-loader%2Fdownload%2Fstyle-loader-1.3.0.tgz"
  "version" "1.3.0"
  dependencies:
    "loader-utils" "^2.0.0"
    "schema-utils" "^2.7.0"

"stylehacks@^4.0.0":
  "integrity" "sha1-Zxj8r00eB9ihMYaQiB6NlnJqcdU="
  "resolved" "https://registry.nlark.com/stylehacks/download/stylehacks-4.0.3.tgz?cache=0&sync_timestamp=1621449595596&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fstylehacks%2Fdownload%2Fstylehacks-4.0.3.tgz"
  "version" "4.0.3"
  dependencies:
    "browserslist" "^4.0.0"
    "postcss" "^7.0.0"
    "postcss-selector-parser" "^3.0.0"

"supports-color@^2.0.0":
  "integrity" "sha1-U10EXOa2Nj+kARcIRimZXp3zJMc="
  "resolved" "https://registry.nlark.com/supports-color/download/supports-color-2.0.0.tgz?cache=0&sync_timestamp=1622293579301&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fsupports-color%2Fdownload%2Fsupports-color-2.0.0.tgz"
  "version" "2.0.0"

"supports-color@^5.3.0":
  "integrity" "sha1-4uaaRKyHcveKHsCzW2id9lMO/I8="
  "resolved" "https://registry.nlark.com/supports-color/download/supports-color-5.5.0.tgz?cache=0&sync_timestamp=1622293579301&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fsupports-color%2Fdownload%2Fsupports-color-5.5.0.tgz"
  "version" "5.5.0"
  dependencies:
    "has-flag" "^3.0.0"

"supports-color@^6.1.0":
  "integrity" "sha1-B2Srxpxj1ayELdSGfo0CXogN+PM="
  "resolved" "https://registry.nlark.com/supports-color/download/supports-color-6.1.0.tgz?cache=0&sync_timestamp=1622293579301&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fsupports-color%2Fdownload%2Fsupports-color-6.1.0.tgz"
  "version" "6.1.0"
  dependencies:
    "has-flag" "^3.0.0"

"supports-color@^7.1.0":
  "integrity" "sha1-G33NyzK4E4gBs+R4umpRyqiWSNo="
  "resolved" "https://registry.nlark.com/supports-color/download/supports-color-7.2.0.tgz?cache=0&sync_timestamp=1622293579301&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fsupports-color%2Fdownload%2Fsupports-color-7.2.0.tgz"
  "version" "7.2.0"
  dependencies:
    "has-flag" "^4.0.0"

"supports-color@^8.1.0":
  "integrity" "sha512-MpUEN2OodtUzxvKQl72cUF7RQ5EiHsGvSsVG0ia9c5RbWGL2CI4C7EpPS8UTBIplnlzZiNuV56w+FuNxy3ty2Q=="
  "resolved" "https://registry.npmmirror.com/supports-color/-/supports-color-8.1.1.tgz"
  "version" "8.1.1"
  dependencies:
    "has-flag" "^4.0.0"

"svgo@^1.0.0":
  "integrity" "sha1-ttxRHAYzRsnkFbgeQ0ARRbltQWc="
  "resolved" "https://registry.nlark.com/svgo/download/svgo-1.3.2.tgz"
  "version" "1.3.2"
  dependencies:
    "chalk" "^2.4.1"
    "coa" "^2.0.2"
    "css-select" "^2.0.0"
    "css-select-base-adapter" "^0.1.1"
    "css-tree" "1.0.0-alpha.37"
    "csso" "^4.0.2"
    "js-yaml" "^3.13.1"
    "mkdirp" "~0.5.1"
    "object.values" "^1.1.0"
    "sax" "~1.2.4"
    "stable" "^0.1.8"
    "unquote" "~1.1.1"
    "util.promisify" "~1.0.0"

"table@^6.0.9":
  "integrity" "sha1-7gVZK3FDgxqMlPPO5qrkwczvM+I="
  "resolved" "https://registry.nlark.com/table/download/table-6.7.1.tgz"
  "version" "6.7.1"
  dependencies:
    "ajv" "^8.0.1"
    "lodash.clonedeep" "^4.5.0"
    "lodash.truncate" "^4.4.2"
    "slice-ansi" "^4.0.0"
    "string-width" "^4.2.0"
    "strip-ansi" "^6.0.0"

"tapable@^1.0.0", "tapable@^1.1.3":
  "integrity" "sha1-ofzMBrWNth/XpF2i2kT186Pme6I="
  "resolved" "https://registry.npm.taobao.org/tapable/download/tapable-1.1.3.tgz?cache=0&sync_timestamp=1607088859616&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Ftapable%2Fdownload%2Ftapable-1.1.3.tgz"
  "version" "1.1.3"

"tar-stream@^2.2.0":
  "integrity" "sha1-rK2EwoQTawYNw/qmRHSqmuvXcoc="
  "resolved" "https://registry.npm.taobao.org/tar-stream/download/tar-stream-2.2.0.tgz"
  "version" "2.2.0"
  dependencies:
    "bl" "^4.0.3"
    "end-of-stream" "^1.4.1"
    "fs-constants" "^1.0.0"
    "inherits" "^2.0.3"
    "readable-stream" "^3.1.1"

"tar@^2.0.0":
  "integrity" "sha1-DKiEhWLHKZuLRG/2pNYM27I+3EA="
  "resolved" "https://registry.nlark.com/tar/download/tar-2.2.2.tgz?cache=0&sync_timestamp=1618847020118&other_urls=https%3A%2F%2Fregistry.nlark.com%2Ftar%2Fdownload%2Ftar-2.2.2.tgz"
  "version" "2.2.2"
  dependencies:
    "block-stream" "*"
    "fstream" "^1.0.12"
    "inherits" "2"

"terser-webpack-plugin@^1.4.1", "terser-webpack-plugin@^1.4.3":
  "integrity" "sha1-oheu+uozDnNP+sthIOwfoxLWBAs="
  "resolved" "https://registry.nlark.com/terser-webpack-plugin/download/terser-webpack-plugin-1.4.5.tgz?cache=0&sync_timestamp=1624624486956&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fterser-webpack-plugin%2Fdownload%2Fterser-webpack-plugin-1.4.5.tgz"
  "version" "1.4.5"
  dependencies:
    "cacache" "^12.0.2"
    "find-cache-dir" "^2.1.0"
    "is-wsl" "^1.1.0"
    "schema-utils" "^1.0.0"
    "serialize-javascript" "^4.0.0"
    "source-map" "^0.6.1"
    "terser" "^4.1.2"
    "webpack-sources" "^1.4.0"
    "worker-farm" "^1.7.0"

"terser@^4.1.2":
  "integrity" "sha1-YwVjQ9fHC7KfOvZlhlpG/gOg3xc="
  "resolved" "https://registry.nlark.com/terser/download/terser-4.8.0.tgz?cache=0&sync_timestamp=1624884102109&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fterser%2Fdownload%2Fterser-4.8.0.tgz"
  "version" "4.8.0"
  dependencies:
    "commander" "^2.20.0"
    "source-map" "~0.6.1"
    "source-map-support" "~0.5.12"

"text-table@^0.2.0":
  "integrity" "sha1-f17oI66AUgfACvLfSoTsP8+lcLQ="
  "resolved" "https://registry.npm.taobao.org/text-table/download/text-table-0.2.0.tgz"
  "version" "0.2.0"

"through2@^2.0.0":
  "integrity" "sha1-AcHjnrMdB8t9A6lqcIIyYLIxMs0="
  "resolved" "https://registry.npm.taobao.org/through2/download/through2-2.0.5.tgz?cache=0&sync_timestamp=1593480386934&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fthrough2%2Fdownload%2Fthrough2-2.0.5.tgz"
  "version" "2.0.5"
  dependencies:
    "readable-stream" "~2.3.6"
    "xtend" "~4.0.1"

"thunky@^1.0.2":
  "integrity" "sha1-Wrr3FKlAXbBQRzK7zNLO3Z75U30="
  "resolved" "https://registry.nlark.com/thunky/download/thunky-1.1.0.tgz"
  "version" "1.1.0"

"timers-browserify@^2.0.4":
  "integrity" "sha1-RKRcEfv0B/NPl7zNFXfGUjYbAO4="
  "resolved" "https://registry.nlark.com/timers-browserify/download/timers-browserify-2.0.12.tgz"
  "version" "2.0.12"
  dependencies:
    "setimmediate" "^1.0.4"

"timsort@^0.3.0":
  "integrity" "sha1-QFQRqOfmM5/mTbmiNN4R3DHgK9Q="
  "resolved" "https://registry.npm.taobao.org/timsort/download/timsort-0.3.0.tgz"
  "version" "0.3.0"

"tiny-emitter@^2.0.0":
  "integrity" "sha1-HRpW7fxRxD6GPLtTgqcjMONVVCM="
  "resolved" "https://registry.npm.taobao.org/tiny-emitter/download/tiny-emitter-2.1.0.tgz"
  "version" "2.1.0"

"tiny-invariant@^1.0.2":
  "integrity" "sha1-Y0xfjv3CdxS384bDXmdgmR0jCHU="
  "resolved" "https://registry.npm.taobao.org/tiny-invariant/download/tiny-invariant-1.1.0.tgz"
  "version" "1.1.0"

"tiny-warning@^1.0.0", "tiny-warning@^1.0.3":
  "integrity" "sha1-lKMNtFPfTGQ9D9VmBg1gqHXYR1Q="
  "resolved" "https://registry.npm.taobao.org/tiny-warning/download/tiny-warning-1.0.3.tgz"
  "version" "1.0.3"

"tinycolor2@^1.4.1":
  "integrity" "sha1-P2pNEHGtB2dtf6Ry4frECnGdiAM="
  "resolved" "https://registry.nlark.com/tinycolor2/download/tinycolor2-1.4.2.tgz"
  "version" "1.4.2"

"tmp@^0.2.0":
  "integrity" "sha1-hFf8MDfc9HGcJRNnoa9lAO4czxQ="
  "resolved" "https://registry.nlark.com/tmp/download/tmp-0.2.1.tgz"
  "version" "0.2.1"
  dependencies:
    "rimraf" "^3.0.0"

"to-arraybuffer@^1.0.0":
  "integrity" "sha1-fSKbH8xjfkZsoIEYCDanqr/4P0M="
  "resolved" "https://registry.npm.taobao.org/to-arraybuffer/download/to-arraybuffer-1.0.1.tgz"
  "version" "1.0.1"

"to-fast-properties@^2.0.0":
  "integrity" "sha1-3F5pjL0HkmW8c+A3doGk5Og/YW4="
  "resolved" "https://registry.npm.taobao.org/to-fast-properties/download/to-fast-properties-2.0.0.tgz"
  "version" "2.0.0"

"to-object-path@^0.3.0":
  "integrity" "sha1-KXWIt7Dn4KwI4E5nL4XB9JmeF68="
  "resolved" "https://registry.npm.taobao.org/to-object-path/download/to-object-path-0.3.0.tgz"
  "version" "0.3.0"
  dependencies:
    "kind-of" "^3.0.2"

"to-regex-range@^2.1.0":
  "integrity" "sha1-fIDBe53+vlmeJzZ+DU3VWQFB2zg="
  "resolved" "https://registry.nlark.com/to-regex-range/download/to-regex-range-2.1.1.tgz"
  "version" "2.1.1"
  dependencies:
    "is-number" "^3.0.0"
    "repeat-string" "^1.6.1"

"to-regex-range@^5.0.1":
  "integrity" "sha1-FkjESq58jZiKMmAY7XL1tN0DkuQ="
  "resolved" "https://registry.nlark.com/to-regex-range/download/to-regex-range-5.0.1.tgz"
  "version" "5.0.1"
  dependencies:
    "is-number" "^7.0.0"

"to-regex@^3.0.1", "to-regex@^3.0.2":
  "integrity" "sha1-E8/dmzNlUvMLUfM6iuG0Knp1mc4="
  "resolved" "https://registry.npm.taobao.org/to-regex/download/to-regex-3.0.2.tgz"
  "version" "3.0.2"
  dependencies:
    "define-property" "^2.0.2"
    "extend-shallow" "^3.0.2"
    "regex-not" "^1.0.2"
    "safe-regex" "^1.1.0"

"toggle-selection@^1.0.6":
  "integrity" "sha1-bkWxJj8gF/oKzH2J14sVuL932jI="
  "resolved" "https://registry.npm.taobao.org/toggle-selection/download/toggle-selection-1.0.6.tgz"
  "version" "1.0.6"

"toidentifier@1.0.0":
  "integrity" "sha1-fhvjRw8ed5SLxD2Uo8j013UrpVM="
  "resolved" "https://registry.npm.taobao.org/toidentifier/download/toidentifier-1.0.0.tgz"
  "version" "1.0.0"

"toposort@^1.0.0":
  "integrity" "sha1-LmhELZ9k7HILjMieZEOsbKqVACk="
  "resolved" "https://registry.npm.taobao.org/toposort/download/toposort-1.0.7.tgz"
  "version" "1.0.7"

"tough-cookie@~2.5.0":
  "integrity" "sha1-zZ+yoKodWhK0c72fuW+j3P9lreI="
  "resolved" "https://registry.npm.taobao.org/tough-cookie/download/tough-cookie-2.5.0.tgz"
  "version" "2.5.0"
  dependencies:
    "psl" "^1.1.28"
    "punycode" "^2.1.1"

"traverse@>=0.3.0 <0.4":
  "integrity" "sha1-cXuPIgzAu3tE5AUUwisui7xw2Lk="
  "resolved" "https://registry.npm.taobao.org/traverse/download/traverse-0.3.9.tgz"
  "version" "0.3.9"

"tree-kill@^1.2.2":
  "integrity" "sha512-L0Orpi8qGpRG//Nd+H90vFB+3iHnue1zSSGmNOOCh1GLJ7rUKVwV2HvijphGQS2UmhUZewS9VgvxYIdgr+fG1A=="
  "resolved" "https://registry.npmmirror.com/tree-kill/-/tree-kill-1.2.2.tgz"
  "version" "1.2.2"

"trim-newlines@^1.0.0":
  "integrity" "sha1-WIeWa7WCpFA6QetST301ARgVphM="
  "resolved" "https://registry.nlark.com/trim-newlines/download/trim-newlines-1.0.0.tgz"
  "version" "1.0.0"

"true-case-path@^1.0.2":
  "integrity" "sha1-+BO1qMhrQNpZYGcisUTjIleZ9H0="
  "resolved" "https://registry.npm.taobao.org/true-case-path/download/true-case-path-1.0.3.tgz"
  "version" "1.0.3"
  dependencies:
    "glob" "^7.1.2"

"tsconfig-paths@^3.9.0":
  "integrity" "sha1-CYVHpsREiAfo/Ljq4IEGTumjyQs="
  "resolved" "https://registry.npm.taobao.org/tsconfig-paths/download/tsconfig-paths-3.9.0.tgz"
  "version" "3.9.0"
  dependencies:
    "@types/json5" "^0.0.29"
    "json5" "^1.0.1"
    "minimist" "^1.2.0"
    "strip-bom" "^3.0.0"

"tslib@^1.10.0", "tslib@^1.8.1", "tslib@^1.9.0":
  "integrity" "sha1-zy04vcNKE0vK8QkcQfZhni9nLQA="
  "resolved" "https://registry.nlark.com/tslib/download/tslib-1.14.1.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.nlark.com%2Ftslib%2Fdownload%2Ftslib-1.14.1.tgz"
  "version" "1.14.1"

"tsutils@^3.17.1":
  "integrity" "sha1-tIcX05TOpsHglpg+7Vjp1hcVtiM="
  "resolved" "https://registry.npm.taobao.org/tsutils/download/tsutils-3.21.0.tgz"
  "version" "3.21.0"
  dependencies:
    "tslib" "^1.8.1"

"tty-browserify@0.0.0":
  "integrity" "sha1-oVe6QC2iTpv5V/mqadUk7tQpAaY="
  "resolved" "https://registry.nlark.com/tty-browserify/download/tty-browserify-0.0.0.tgz"
  "version" "0.0.0"

"tunnel-agent@^0.6.0":
  "integrity" "sha1-J6XeoGs2sEoKmWZ3SykIaPD8QP0="
  "resolved" "https://registry.nlark.com/tunnel-agent/download/tunnel-agent-0.6.0.tgz"
  "version" "0.6.0"
  dependencies:
    "safe-buffer" "^5.0.1"

"tweetnacl@^0.14.3", "tweetnacl@~0.14.0":
  "integrity" "sha1-WuaBd/GS1EViadEIr6k/+HQ/T2Q="
  "resolved" "https://registry.npm.taobao.org/tweetnacl/download/tweetnacl-0.14.5.tgz"
  "version" "0.14.5"

"type-check@^0.4.0", "type-check@~0.4.0":
  "integrity" "sha1-B7ggO/pwVsBlcFDjzNLDdzC6uPE="
  "resolved" "https://registry.nlark.com/type-check/download/type-check-0.4.0.tgz"
  "version" "0.4.0"
  dependencies:
    "prelude-ls" "^1.2.1"

"type-fest@^0.20.2":
  "integrity" "sha1-G/IH9LKPkVg2ZstfvTJ4hzAc1fQ="
  "resolved" "https://registry.nlark.com/type-fest/download/type-fest-0.20.2.tgz?cache=0&sync_timestamp=1623872995368&other_urls=https%3A%2F%2Fregistry.nlark.com%2Ftype-fest%2Fdownload%2Ftype-fest-0.20.2.tgz"
  "version" "0.20.2"

"type-is@~1.6.17", "type-is@~1.6.18":
  "integrity" "sha1-TlUs0F3wlGfcvE73Od6J8s83wTE="
  "resolved" "https://registry.nlark.com/type-is/download/type-is-1.6.18.tgz"
  "version" "1.6.18"
  dependencies:
    "media-typer" "0.3.0"
    "mime-types" "~2.1.24"

"typedarray@^0.0.6":
  "integrity" "sha1-hnrHTjhkGHsdPUfZlqeOxciDB3c="
  "resolved" "https://registry.nlark.com/typedarray/download/typedarray-0.0.6.tgz"
  "version" "0.0.6"

"typescript@^3.5.3", "typescript@>=2.8.0 || >= 3.2.0-dev || >= 3.3.0-dev || >= 3.4.0-dev || >= 3.5.0-dev || >= 3.6.0-dev || >= 3.6.0-beta || >= 3.7.0-dev || >= 3.7.0-beta":
  "integrity" "sha1-cPORCselHta+952ngAaQsZv3eLg="
  "resolved" "https://registry.nlark.com/typescript/download/typescript-3.9.10.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.nlark.com%2Ftypescript%2Fdownload%2Ftypescript-3.9.10.tgz"
  "version" "3.9.10"

"ua-parser-js@^0.7.18":
  "integrity" "sha1-i6BOZT81ziECOcZGYWhb+RId7DE="
  "resolved" "https://registry.nlark.com/ua-parser-js/download/ua-parser-js-0.7.28.tgz"
  "version" "0.7.28"

"uglify-js@3.4.x":
  "integrity" "sha1-mtlWPY6zrN+404WX0q8dgV9qdV8="
  "resolved" "https://registry.nlark.com/uglify-js/download/uglify-js-3.4.10.tgz?cache=0&sync_timestamp=1624812339222&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fuglify-js%2Fdownload%2Fuglify-js-3.4.10.tgz"
  "version" "3.4.10"
  dependencies:
    "commander" "~2.19.0"
    "source-map" "~0.6.1"

"unbox-primitive@^1.0.1":
  "integrity" "sha1-CF4hViXsMWJXTciFmr7nilmxRHE="
  "resolved" "https://registry.npm.taobao.org/unbox-primitive/download/unbox-primitive-1.0.1.tgz?cache=0&sync_timestamp=1616706302651&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Funbox-primitive%2Fdownload%2Funbox-primitive-1.0.1.tgz"
  "version" "1.0.1"
  dependencies:
    "function-bind" "^1.1.1"
    "has-bigints" "^1.0.1"
    "has-symbols" "^1.0.2"
    "which-boxed-primitive" "^1.0.2"

"unicode-canonical-property-names-ecmascript@^1.0.4":
  "integrity" "sha1-JhmADEyCWADv3YNDr33Zkzy+KBg="
  "resolved" "https://registry.nlark.com/unicode-canonical-property-names-ecmascript/download/unicode-canonical-property-names-ecmascript-1.0.4.tgz"
  "version" "1.0.4"

"unicode-match-property-ecmascript@^1.0.4":
  "integrity" "sha1-jtKjJWmWG86SJ9Cc0/+7j+1fAgw="
  "resolved" "https://registry.nlark.com/unicode-match-property-ecmascript/download/unicode-match-property-ecmascript-1.0.4.tgz"
  "version" "1.0.4"
  dependencies:
    "unicode-canonical-property-names-ecmascript" "^1.0.4"
    "unicode-property-aliases-ecmascript" "^1.0.4"

"unicode-match-property-value-ecmascript@^1.2.0":
  "integrity" "sha1-DZH2AO7rMJaqlisdb8iIduZOpTE="
  "resolved" "https://registry.nlark.com/unicode-match-property-value-ecmascript/download/unicode-match-property-value-ecmascript-1.2.0.tgz"
  "version" "1.2.0"

"unicode-property-aliases-ecmascript@^1.0.4":
  "integrity" "sha1-3Vepn2IHvt/0Yoq++5TFDblByPQ="
  "resolved" "https://registry.npm.taobao.org/unicode-property-aliases-ecmascript/download/unicode-property-aliases-ecmascript-1.1.0.tgz"
  "version" "1.1.0"

"union-value@^1.0.0":
  "integrity" "sha1-C2/nuDWuzaYcbqTU8CwUIh4QmEc="
  "resolved" "https://registry.npm.taobao.org/union-value/download/union-value-1.0.1.tgz"
  "version" "1.0.1"
  dependencies:
    "arr-union" "^3.1.0"
    "get-value" "^2.0.6"
    "is-extendable" "^0.1.1"
    "set-value" "^2.0.1"

"uniq@^1.0.1":
  "integrity" "sha1-sxxa6CVIRKOoKBVBzisEuGWnNP8="
  "resolved" "https://registry.npm.taobao.org/uniq/download/uniq-1.0.1.tgz"
  "version" "1.0.1"

"uniqs@^2.0.0":
  "integrity" "sha1-/+3ks2slKQaW5uFl1KWe25mOawI="
  "resolved" "https://registry.nlark.com/uniqs/download/uniqs-2.0.0.tgz"
  "version" "2.0.0"

"unique-filename@^1.1.1":
  "integrity" "sha1-HWl2k2mtoFgxA6HmrodoG1ZXMjA="
  "resolved" "https://registry.nlark.com/unique-filename/download/unique-filename-1.1.1.tgz"
  "version" "1.1.1"
  dependencies:
    "unique-slug" "^2.0.0"

"unique-slug@^2.0.0":
  "integrity" "sha1-uqvOkQg/xk6UWw861hPiZPfNTmw="
  "resolved" "https://registry.nlark.com/unique-slug/download/unique-slug-2.0.2.tgz"
  "version" "2.0.2"
  dependencies:
    "imurmurhash" "^0.1.4"

"unpipe@~1.0.0", "unpipe@1.0.0":
  "integrity" "sha1-sr9O6FFKrmFltIF4KdIbLvSZBOw="
  "resolved" "https://registry.npm.taobao.org/unpipe/download/unpipe-1.0.0.tgz"
  "version" "1.0.0"

"unquote@~1.1.1":
  "integrity" "sha1-j97XMk7G6IoP+LkF58CYzcCG1UQ="
  "resolved" "https://registry.npm.taobao.org/unquote/download/unquote-1.1.1.tgz"
  "version" "1.1.1"

"unset-value@^1.0.0":
  "integrity" "sha1-g3aHP30jNRef+x5vw6jtDfyKtVk="
  "resolved" "https://registry.nlark.com/unset-value/download/unset-value-1.0.0.tgz"
  "version" "1.0.0"
  dependencies:
    "has-value" "^0.3.1"
    "isobject" "^3.0.0"

"unzipper@^0.10.11":
  "integrity" "sha1-C0mRRGRyy9uS7nQDkJ8mwkGceC4="
  "resolved" "https://registry.npm.taobao.org/unzipper/download/unzipper-0.10.11.tgz"
  "version" "0.10.11"
  dependencies:
    "big-integer" "^1.6.17"
    "binary" "~0.3.0"
    "bluebird" "~3.4.1"
    "buffer-indexof-polyfill" "~1.0.0"
    "duplexer2" "~0.1.4"
    "fstream" "^1.0.12"
    "graceful-fs" "^4.2.2"
    "listenercount" "~1.0.1"
    "readable-stream" "~2.3.6"
    "setimmediate" "~1.0.4"

"upath@^1.1.1":
  "integrity" "sha1-j2bbzVWog6za5ECK+LA1pQRMGJQ="
  "resolved" "https://registry.nlark.com/upath/download/upath-1.2.0.tgz"
  "version" "1.2.0"

"upper-case@^1.1.1":
  "integrity" "sha1-9rRQHC7EzdJrp4vnIilh3ndiFZg="
  "resolved" "https://registry.npm.taobao.org/upper-case/download/upper-case-1.1.3.tgz"
  "version" "1.1.3"

"uri-js@^4.2.2":
  "integrity" "sha1-mxpSWVIlhZ5V9mnZKPiMbFfyp34="
  "resolved" "https://registry.npm.taobao.org/uri-js/download/uri-js-4.4.1.tgz?cache=0&sync_timestamp=1610237641463&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Furi-js%2Fdownload%2Furi-js-4.4.1.tgz"
  "version" "4.4.1"
  dependencies:
    "punycode" "^2.1.0"

"urix@^0.1.0":
  "integrity" "sha1-2pN/emLiH+wf0Y1Js1wpNQZ6bHI="
  "resolved" "https://registry.nlark.com/urix/download/urix-0.1.0.tgz"
  "version" "0.1.0"

"url-parse@^1.4.3", "url-parse@^1.5.1":
  "integrity" "sha1-1fqYkK+KXh8nSiyYN2UQ9kJfbjs="
  "resolved" "https://registry.nlark.com/url-parse/download/url-parse-1.5.1.tgz"
  "version" "1.5.1"
  dependencies:
    "querystringify" "^2.1.1"
    "requires-port" "^1.0.0"

"url@^0.11.0":
  "integrity" "sha1-ODjpfPxgUh63PFJajlW/3Z4uKPE="
  "resolved" "https://registry.nlark.com/url/download/url-0.11.0.tgz?cache=0&sync_timestamp=1618846952693&other_urls=https%3A%2F%2Fregistry.nlark.com%2Furl%2Fdownload%2Furl-0.11.0.tgz"
  "version" "0.11.0"
  dependencies:
    "punycode" "1.3.2"
    "querystring" "0.2.0"

"use@^3.1.0":
  "integrity" "sha1-1QyMrHmhn7wg8pEfVuuXP04QBw8="
  "resolved" "https://registry.nlark.com/use/download/use-3.1.1.tgz"
  "version" "3.1.1"

"util-deprecate@^1.0.1", "util-deprecate@^1.0.2", "util-deprecate@~1.0.1":
  "integrity" "sha1-RQ1Nyfpw3nMnYvvS1KKJgUGaDM8="
  "resolved" "https://registry.npm.taobao.org/util-deprecate/download/util-deprecate-1.0.2.tgz"
  "version" "1.0.2"

"util.promisify@~1.0.0", "util.promisify@1.0.0":
  "integrity" "sha1-RA9xZaRZyaFtwUXrjnLzVocJcDA="
  "resolved" "https://registry.npm.taobao.org/util.promisify/download/util.promisify-1.0.0.tgz?cache=0&sync_timestamp=1610159975962&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Futil.promisify%2Fdownload%2Futil.promisify-1.0.0.tgz"
  "version" "1.0.0"
  dependencies:
    "define-properties" "^1.1.2"
    "object.getownpropertydescriptors" "^2.0.3"

"util@^0.10.3":
  "integrity" "sha1-OqASW/5mikZy3liFfTrOJ+y3aQE="
  "resolved" "https://registry.nlark.com/util/download/util-0.10.4.tgz"
  "version" "0.10.4"
  dependencies:
    "inherits" "2.0.3"

"util@^0.11.0":
  "integrity" "sha1-MjZzNyDsZLsn9uJvQhqqLhtYjWE="
  "resolved" "https://registry.nlark.com/util/download/util-0.11.1.tgz"
  "version" "0.11.1"
  dependencies:
    "inherits" "2.0.3"

"util@0.10.3":
  "integrity" "sha1-evsa/lCAUkZInj23/g7TeTNqwPk="
  "resolved" "https://registry.nlark.com/util/download/util-0.10.3.tgz"
  "version" "0.10.3"
  dependencies:
    "inherits" "2.0.1"

"utila@~0.4":
  "integrity" "sha1-ihagXURWV6Oupe7MWxKk+lN5dyw="
  "resolved" "https://registry.npm.taobao.org/utila/download/utila-0.4.0.tgz"
  "version" "0.4.0"

"utils-merge@1.0.1":
  "integrity" "sha1-n5VxD1CiZ5R7LMwSR0HBAoQn5xM="
  "resolved" "https://registry.npm.taobao.org/utils-merge/download/utils-merge-1.0.1.tgz"
  "version" "1.0.1"

"uuid@^3.3.2", "uuid@^3.4.0":
  "integrity" "sha1-sj5DWK+oogL+ehAK8fX4g/AgB+4="
  "resolved" "https://registry.nlark.com/uuid/download/uuid-3.4.0.tgz"
  "version" "3.4.0"

"uuid@^8.3.0":
  "integrity" "sha1-gNW1ztJxu5r2xEXyGhoExgbO++I="
  "resolved" "https://registry.nlark.com/uuid/download/uuid-8.3.2.tgz"
  "version" "8.3.2"

"v8-compile-cache@^2.0.3", "v8-compile-cache@^2.1.1":
  "integrity" "sha1-LeGWGMZtwkfc+2+ZM4A12CRaLO4="
  "resolved" "https://registry.nlark.com/v8-compile-cache/download/v8-compile-cache-2.3.0.tgz"
  "version" "2.3.0"

"validate-npm-package-license@^3.0.1":
  "integrity" "sha1-/JH2uce6FchX9MssXe/uw51PQQo="
  "resolved" "https://registry.npm.taobao.org/validate-npm-package-license/download/validate-npm-package-license-3.0.4.tgz"
  "version" "3.0.4"
  dependencies:
    "spdx-correct" "^3.0.0"
    "spdx-expression-parse" "^3.0.0"

"value-equal@^1.0.1":
  "integrity" "sha1-Hgt5THNMXAyt4XnEN9NW2TGjTWw="
  "resolved" "https://registry.npm.taobao.org/value-equal/download/value-equal-1.0.1.tgz"
  "version" "1.0.1"

"vary@~1.1.2":
  "integrity" "sha1-IpnwLG3tMNSllhsLn3RSShj2NPw="
  "resolved" "https://registry.nlark.com/vary/download/vary-1.1.2.tgz"
  "version" "1.1.2"

"vendors@^1.0.0":
  "integrity" "sha1-4rgApT56Kbk1BsPPQRANFsTErY4="
  "resolved" "https://registry.nlark.com/vendors/download/vendors-1.0.4.tgz"
  "version" "1.0.4"

"verror@1.10.0":
  "integrity" "sha1-OhBcoXBTr1XW4nDB+CiGguGNpAA="
  "resolved" "https://registry.nlark.com/verror/download/verror-1.10.0.tgz"
  "version" "1.10.0"
  dependencies:
    "assert-plus" "^1.0.0"
    "core-util-is" "1.0.2"
    "extsprintf" "^1.2.0"

"vm-browserify@^1.0.1":
  "integrity" "sha1-eGQcSIuObKkadfUR56OzKobl3aA="
  "resolved" "https://registry.nlark.com/vm-browserify/download/vm-browserify-1.1.2.tgz"
  "version" "1.1.2"

"warning@^4.0.1", "warning@^4.0.2", "warning@^4.0.3", "warning@~4.0.3", "warning@4.x":
  "integrity" "sha1-Fungd+uKhtavfWSqHgX9hbRnjKM="
  "resolved" "https://registry.nlark.com/warning/download/warning-4.0.3.tgz"
  "version" "4.0.3"
  dependencies:
    "loose-envify" "^1.0.0"

"watchpack-chokidar2@^2.0.1":
  "integrity" "sha1-OFAAcu5uzmbzdpk2lQ6hdxvhyVc="
  "resolved" "https://registry.nlark.com/watchpack-chokidar2/download/watchpack-chokidar2-2.0.1.tgz"
  "version" "2.0.1"
  dependencies:
    "chokidar" "^2.1.8"

"watchpack@^1.7.4":
  "integrity" "sha1-EmfmxV4Lm1vkTCAjrtVDeiwmxFM="
  "resolved" "https://registry.nlark.com/watchpack/download/watchpack-1.7.5.tgz"
  "version" "1.7.5"
  dependencies:
    "graceful-fs" "^4.1.2"
    "neo-async" "^2.5.0"
  optionalDependencies:
    "chokidar" "^3.4.1"
    "watchpack-chokidar2" "^2.0.1"

"wbuf@^1.1.0", "wbuf@^1.7.3":
  "integrity" "sha1-wdjRSTFtPqhShIiVy2oL/oh7h98="
  "resolved" "https://registry.nlark.com/wbuf/download/wbuf-1.7.3.tgz"
  "version" "1.7.3"
  dependencies:
    "minimalistic-assert" "^1.0.0"

"webpack-cli@^3.3.6":
  "integrity" "sha1-lOmtoIFFPNCqYJyZ5QABL9OtLUo="
  "resolved" "https://registry.nlark.com/webpack-cli/download/webpack-cli-3.3.12.tgz"
  "version" "3.3.12"
  dependencies:
    "chalk" "^2.4.2"
    "cross-spawn" "^6.0.5"
    "enhanced-resolve" "^4.1.1"
    "findup-sync" "^3.0.0"
    "global-modules" "^2.0.0"
    "import-local" "^2.0.0"
    "interpret" "^1.4.0"
    "loader-utils" "^1.4.0"
    "supports-color" "^6.1.0"
    "v8-compile-cache" "^2.1.1"
    "yargs" "^13.3.2"

"webpack-dev-middleware@^3.7.2":
  "integrity" "sha1-Bjk3KxQyYuK4SrldO5GnWXBhwsU="
  "resolved" "https://registry.nlark.com/webpack-dev-middleware/download/webpack-dev-middleware-3.7.3.tgz"
  "version" "3.7.3"
  dependencies:
    "memory-fs" "^0.4.1"
    "mime" "^2.4.4"
    "mkdirp" "^0.5.1"
    "range-parser" "^1.2.1"
    "webpack-log" "^2.0.0"

"webpack-dev-server@^3.8.0":
  "integrity" "sha1-aV687Xakkp8NXef9c/r+GF/jNwg="
  "resolved" "https://registry.nlark.com/webpack-dev-server/download/webpack-dev-server-3.11.2.tgz"
  "version" "3.11.2"
  dependencies:
    "ansi-html" "0.0.7"
    "bonjour" "^3.5.0"
    "chokidar" "^2.1.8"
    "compression" "^1.7.4"
    "connect-history-api-fallback" "^1.6.0"
    "debug" "^4.1.1"
    "del" "^4.1.1"
    "express" "^4.17.1"
    "html-entities" "^1.3.1"
    "http-proxy-middleware" "0.19.1"
    "import-local" "^2.0.0"
    "internal-ip" "^4.3.0"
    "ip" "^1.1.5"
    "is-absolute-url" "^3.0.3"
    "killable" "^1.0.1"
    "loglevel" "^1.6.8"
    "opn" "^5.5.0"
    "p-retry" "^3.0.1"
    "portfinder" "^1.0.26"
    "schema-utils" "^1.0.0"
    "selfsigned" "^1.10.8"
    "semver" "^6.3.0"
    "serve-index" "^1.9.1"
    "sockjs" "^0.3.21"
    "sockjs-client" "^1.5.0"
    "spdy" "^4.0.2"
    "strip-ansi" "^3.0.1"
    "supports-color" "^6.1.0"
    "url" "^0.11.0"
    "webpack-dev-middleware" "^3.7.2"
    "webpack-log" "^2.0.0"
    "ws" "^6.2.1"
    "yargs" "^13.3.2"

"webpack-log@^2.0.0":
  "integrity" "sha1-W3ko4GN1k/EZ0y9iJ8HgrDHhtH8="
  "resolved" "https://registry.nlark.com/webpack-log/download/webpack-log-2.0.0.tgz"
  "version" "2.0.0"
  dependencies:
    "ansi-colors" "^3.0.0"
    "uuid" "^3.3.2"

"webpack-merge@^4.2.1":
  "integrity" "sha1-onxS6ng9E5iv0gh/VH17nS9DY00="
  "resolved" "https://registry.nlark.com/webpack-merge/download/webpack-merge-4.2.2.tgz"
  "version" "4.2.2"
  dependencies:
    "lodash" "^4.17.15"

"webpack-sources@^1.0.1", "webpack-sources@^1.1.0", "webpack-sources@^1.4.0", "webpack-sources@^1.4.1":
  "integrity" "sha1-7t2OwLko+/HL/plOItLYkPMwqTM="
  "resolved" "https://registry.nlark.com/webpack-sources/download/webpack-sources-1.4.3.tgz"
  "version" "1.4.3"
  dependencies:
    "source-list-map" "^2.0.0"
    "source-map" "~0.6.1"

"webpack@*", "webpack@^1.0.0 || ^2.0.0 || ^3.0.0 || ^4.0.0", "webpack@^1.3.0 || ^2 || ^3 || ^4", "webpack@^2.0.0 || ^3.0.0 || ^4.0.0", "webpack@^3.0.0 || ^4.0.0", "webpack@^4.0.0", "webpack@^4.0.0 || ^5.0.0", "webpack@^4.3.0 || ^5.0.0", "webpack@^4.39.1", "webpack@^4.4.0", "webpack@>=2", "webpack@4.x.x":
  "integrity" "sha1-v5tEBOogoHNgXgoBHRiNd8tq1UI="
  "resolved" "https://registry.nlark.com/webpack/download/webpack-4.46.0.tgz?cache=0&sync_timestamp=1624897469465&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fwebpack%2Fdownload%2Fwebpack-4.46.0.tgz"
  "version" "4.46.0"
  dependencies:
    "@webassemblyjs/ast" "1.9.0"
    "@webassemblyjs/helper-module-context" "1.9.0"
    "@webassemblyjs/wasm-edit" "1.9.0"
    "@webassemblyjs/wasm-parser" "1.9.0"
    "acorn" "^6.4.1"
    "ajv" "^6.10.2"
    "ajv-keywords" "^3.4.1"
    "chrome-trace-event" "^1.0.2"
    "enhanced-resolve" "^4.5.0"
    "eslint-scope" "^4.0.3"
    "json-parse-better-errors" "^1.0.2"
    "loader-runner" "^2.4.0"
    "loader-utils" "^1.2.3"
    "memory-fs" "^0.4.1"
    "micromatch" "^3.1.10"
    "mkdirp" "^0.5.3"
    "neo-async" "^2.6.1"
    "node-libs-browser" "^2.2.1"
    "schema-utils" "^1.0.0"
    "tapable" "^1.1.3"
    "terser-webpack-plugin" "^1.4.3"
    "watchpack" "^1.7.4"
    "webpack-sources" "^1.4.1"

"websocket-driver@^0.7.4", "websocket-driver@>=0.5.1":
  "integrity" "sha1-ia1Slbv2S0gKvLox5JU6ynBvV2A="
  "resolved" "https://registry.npm.taobao.org/websocket-driver/download/websocket-driver-0.7.4.tgz"
  "version" "0.7.4"
  dependencies:
    "http-parser-js" ">=0.5.1"
    "safe-buffer" ">=5.1.0"
    "websocket-extensions" ">=0.1.1"

"websocket-extensions@>=0.1.1":
  "integrity" "sha1-f4RzvIOd/YdgituV1+sHUhFXikI="
  "resolved" "https://registry.nlark.com/websocket-extensions/download/websocket-extensions-0.1.4.tgz"
  "version" "0.1.4"

"whatwg-fetch@>=0.10.0":
  "integrity" "sha1-3O0k838mJO0CgXJdUdDi4/5nf4w="
  "resolved" "https://registry.nlark.com/whatwg-fetch/download/whatwg-fetch-3.6.2.tgz"
  "version" "3.6.2"

"which-boxed-primitive@^1.0.2":
  "integrity" "sha1-E3V7yJsgmwSf5dhkMOIc9AqJqOY="
  "resolved" "https://registry.nlark.com/which-boxed-primitive/download/which-boxed-primitive-1.0.2.tgz"
  "version" "1.0.2"
  dependencies:
    "is-bigint" "^1.0.1"
    "is-boolean-object" "^1.1.0"
    "is-number-object" "^1.0.4"
    "is-string" "^1.0.5"
    "is-symbol" "^1.0.3"

"which-module@^2.0.0":
  "integrity" "sha1-2e8H3Od7mQK4o6j6SzHD4/fm6Ho="
  "resolved" "https://registry.nlark.com/which-module/download/which-module-2.0.0.tgz"
  "version" "2.0.0"

"which@^1.2.14", "which@^1.2.9", "which@^1.3.1", "which@1":
  "integrity" "sha1-pFBD1U9YBTFtqNYvn1CRjT2nCwo="
  "resolved" "https://registry.nlark.com/which/download/which-1.3.1.tgz"
  "version" "1.3.1"
  dependencies:
    "isexe" "^2.0.0"

"which@^2.0.1":
  "integrity" "sha1-fGqN0KY2oDJ+ELWckobu6T8/UbE="
  "resolved" "https://registry.nlark.com/which/download/which-2.0.2.tgz"
  "version" "2.0.2"
  dependencies:
    "isexe" "^2.0.0"

"wide-align@^1.1.0":
  "integrity" "sha1-rgdOa9wMFKQx6ATmJFScYzsABFc="
  "resolved" "https://registry.npm.taobao.org/wide-align/download/wide-align-1.1.3.tgz"
  "version" "1.1.3"
  dependencies:
    "string-width" "^1.0.2 || 2"

"word-wrap@^1.2.3":
  "integrity" "sha1-YQY29rH3A4kb00dxzLF/uTtHB5w="
  "resolved" "https://registry.npm.taobao.org/word-wrap/download/word-wrap-1.2.3.tgz"
  "version" "1.2.3"

"worker-farm@^1.7.0":
  "integrity" "sha1-JqlMU5G7ypJhUgAvabhKS/dy5ag="
  "resolved" "https://registry.npm.taobao.org/worker-farm/download/worker-farm-1.7.0.tgz"
  "version" "1.7.0"
  dependencies:
    "errno" "~0.1.7"

"wrap-ansi@^5.1.0":
  "integrity" "sha1-H9H2cjXVttD+54EFYAG/tpTAOwk="
  "resolved" "https://registry.nlark.com/wrap-ansi/download/wrap-ansi-5.1.0.tgz"
  "version" "5.1.0"
  dependencies:
    "ansi-styles" "^3.2.0"
    "string-width" "^3.0.0"
    "strip-ansi" "^5.0.0"

"wrap-ansi@^7.0.0":
  "integrity" "sha512-YVGIj2kamLSTxw6NsZjoBxfSwsn0ycdesmc4p+Q21c5zPuZ1pl+NfxVdxPtdHvmNVOQ6XSYG4AUtyt/Fi7D16Q=="
  "resolved" "https://registry.npmmirror.com/wrap-ansi/-/wrap-ansi-7.0.0.tgz"
  "version" "7.0.0"
  dependencies:
    "ansi-styles" "^4.0.0"
    "string-width" "^4.1.0"
    "strip-ansi" "^6.0.0"

"wrappy@1":
  "integrity" "sha1-tSQ9jz7BqjXxNkYFvA0QNuMKtp8="
  "resolved" "https://registry.nlark.com/wrappy/download/wrappy-1.0.2.tgz?cache=0&sync_timestamp=1619133505879&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fwrappy%2Fdownload%2Fwrappy-1.0.2.tgz"
  "version" "1.0.2"

"ws@^6.2.1":
  "integrity" "sha1-3Vzb1XqZeZFgl2UtePHMX66gwy4="
  "resolved" "https://registry.nlark.com/ws/download/ws-6.2.2.tgz"
  "version" "6.2.2"
  dependencies:
    "async-limiter" "~1.0.0"

"xmlchars@^2.2.0":
  "integrity" "sha1-Bg/hvLf5x2/ioX24apvDq4lCEMs="
  "resolved" "https://registry.npm.taobao.org/xmlchars/download/xmlchars-2.2.0.tgz"
  "version" "2.2.0"

"xtend@^4.0.0", "xtend@~4.0.1":
  "integrity" "sha1-u3J3n1+kZRhrH0OPZ0+jR/2121Q="
  "resolved" "https://registry.npm.taobao.org/xtend/download/xtend-4.0.2.tgz"
  "version" "4.0.2"

"y18n@^4.0.0":
  "integrity" "sha1-tfJZyCzW4zaSHv17/Yv1YN6e7t8="
  "resolved" "https://registry.nlark.com/y18n/download/y18n-4.0.3.tgz"
  "version" "4.0.3"

"y18n@^5.0.5":
  "integrity" "sha512-0pfFzegeDWJHJIAmTLRP2DwHjdF5s7jo9tuztdQxAhINCdvS+3nGINqPd00AphqJR/0LhANUS6/+7SCb98YOfA=="
  "resolved" "https://registry.npmmirror.com/y18n/-/y18n-5.0.8.tgz"
  "version" "5.0.8"

"yallist@^2.1.2":
  "integrity" "sha1-HBH5IY8HYImkfdUS+TxmmaaoHVI="
  "resolved" "https://registry.npm.taobao.org/yallist/download/yallist-2.1.2.tgz"
  "version" "2.1.2"

"yallist@^3.0.2":
  "integrity" "sha1-27fa+b/YusmrRev2ArjLrQ1dCP0="
  "resolved" "https://registry.npm.taobao.org/yallist/download/yallist-3.1.1.tgz"
  "version" "3.1.1"

"yallist@^4.0.0":
  "integrity" "sha1-m7knkNnA7/7GO+c1GeEaNQGaOnI="
  "resolved" "https://registry.npm.taobao.org/yallist/download/yallist-4.0.0.tgz"
  "version" "4.0.0"

"yargs-parser@^13.1.2":
  "integrity" "sha1-Ew8JcC667vJlDVTObj5XBvek+zg="
  "resolved" "https://registry.nlark.com/yargs-parser/download/yargs-parser-13.1.2.tgz?cache=0&sync_timestamp=1624233514145&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fyargs-parser%2Fdownload%2Fyargs-parser-13.1.2.tgz"
  "version" "13.1.2"
  dependencies:
    "camelcase" "^5.0.0"
    "decamelize" "^1.2.0"

"yargs-parser@^20.2.2":
  "integrity" "sha512-y11nGElTIV+CT3Zv9t7VKl+Q3hTQoT9a1Qzezhhl6Rp21gJ/IVTW7Z3y9EWXhuUBC2Shnf+DX0antecpAwSP8w=="
  "resolved" "https://registry.npmmirror.com/yargs-parser/-/yargs-parser-20.2.9.tgz"
  "version" "20.2.9"

"yargs@^13.3.2":
  "integrity" "sha1-rX/+/sGqWVZayRX4Lcyzipwxot0="
  "resolved" "https://registry.nlark.com/yargs/download/yargs-13.3.2.tgz?cache=0&sync_timestamp=1620086644940&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fyargs%2Fdownload%2Fyargs-13.3.2.tgz"
  "version" "13.3.2"
  dependencies:
    "cliui" "^5.0.0"
    "find-up" "^3.0.0"
    "get-caller-file" "^2.0.1"
    "require-directory" "^2.1.1"
    "require-main-filename" "^2.0.0"
    "set-blocking" "^2.0.0"
    "string-width" "^3.0.0"
    "which-module" "^2.0.0"
    "y18n" "^4.0.0"
    "yargs-parser" "^13.1.2"

"yargs@^16.2.0":
  "integrity" "sha512-D1mvvtDG0L5ft/jGWkLpG1+m0eQxOfaBvTNELraWj22wSVUMWxZUvYgJYcKh6jGGIkJFhH4IZPQhR4TKpc8mBw=="
  "resolved" "https://registry.npmmirror.com/yargs/-/yargs-16.2.0.tgz"
  "version" "16.2.0"
  dependencies:
    "cliui" "^7.0.2"
    "escalade" "^3.1.1"
    "get-caller-file" "^2.0.5"
    "require-directory" "^2.1.1"
    "string-width" "^4.2.0"
    "y18n" "^5.0.5"
    "yargs-parser" "^20.2.2"

"zip-stream@^4.1.0":
  "integrity" "sha1-Ud0yZXFUTjaqP3VkMLMTV23I/Hk="
  "resolved" "https://registry.npm.taobao.org/zip-stream/download/zip-stream-4.1.0.tgz"
  "version" "4.1.0"
  dependencies:
    "archiver-utils" "^2.1.0"
    "compress-commons" "^4.1.0"
    "readable-stream" "^3.6.0"
